(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8877],{20549:function(){},43290:function(){},5362:function(e,t,n){"use strict";n.d(t,{F9:function(){return C},Ne:function(){return E},Nl:function(){return y},OJ:function(){return f},WE:function(){return w},uC:function(){return _}});var r=n(30947),i=n(8073),o=n(45144),a=n(15969),s=n(5068),u=n(39155),c=n(77930),l=n(7653),d=n(8571),g=n(53500),p=n(68571);function f(){let{applicationType:e}=(0,d.m)();return(0,l.useCallback)(async function(t){var n,r;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return await fetch(t,{...o,headers:{"anthropic-client-sha":null!==(n=p.env.RELEASE_SHA)&&void 0!==n?n:"unknown","anthropic-client-version":null!==(r=p.env.RELEASE_VERSION)&&void 0!==r?r:"unknown",...(0,i.F)(e),"anthropic-anonymous-id":g.u.anonymousId,...o.headers},credentials:"include"})},[e])}function m(e,t){let n=f();return(0,l.useCallback)(async(i,a)=>{let s=await n(i,a),u=await s.text(),c={error:{type:"api_error",message:u}};try{("DELETE"!==a.method||u.length>0)&&(c=JSON.parse(u))}catch(e){(0,o.uT)("Got invalid JSON from NextJS response",{extra:{text:u,status:s.status}})}if(!s.ok&&t!==s.status)throw(0,r.fT)(s.status,c,e,s.headers);return c},[n,t,e])}let _=(e,t,n)=>{let{transformVariables:r,enabled:i=!0,...o}=n||{},s=m("Mutation error"),{mutate:u,mutateAsync:c,...d}=(0,a.useMutation)({mutationFn:async n=>{var i;let o="function"==typeof e?e(n):e,a=null!==(i=null==r?void 0:r(n))&&void 0!==i?i:n,u=a?JSON.stringify(a):void 0;return await s(o,{method:t,headers:{"Content-Type":"application/json"},body:u})},...o});return{mutate:(0,l.useCallback)(e=>i?u(e):void 0,[u,i]),mutateAsync:(0,l.useCallback)((e,t)=>i?c(e,t):(()=>{throw Error("You can't use mutateAsync with the `enabled` feature of useApiMutation. Use mutate instead.")})(),[c,i]),...d}},h={staleTime:3e5,refetchOnWindowFocus:!1,retry:!1},v=(e,t,n)=>async()=>{let r=await e(t,{method:"GET",headers:{"Content-Type":"application/json"}});return n?n(r):r},w=(e,t,n)=>{let r=m("Query error",null==t?void 0:t.additionalPermittedStatusCode);return(0,s.useQuery)({queryKey:[e,n],queryFn:v(r,e,n),...h,...t})},y=(e,t)=>{let n=m("Query error",null==t?void 0:t.additionalPermittedStatusCode),r=(0,l.useMemo)(()=>{let{queryKey:r,...i}=t||{};return e.map((e,t)=>{var o;return{queryKey:null!==(o=null==r?void 0:r[t])&&void 0!==o?o:[e],queryFn:async()=>await n(e,{method:"GET",headers:{"Content-Type":"application/json"}}),staleTime:3e5,...i}})},[n,e,t]),i=(0,u.useQueries)({queries:r});return(0,l.useMemo)(()=>i,[JSON.stringify(i)])},C=e=>(0,l.useMemo)(()=>e.filter(e=>!e.isError&&!e.isLoading).map(e=>e.data).filter(e=>void 0!==e),[e]);function E(e,t,n,r){let i=(0,c.useQueryClient)(),{queryKey:o,...a}=r;return _(e,t,{async onMutate(e){await i.cancelQueries({queryKey:o});let t=i.getQueryData(o);return i.setQueryData(o,t=>n(e,t)),{previousValue:t}},onError(e,t,n){(null==n?void 0:n.previousValue)&&i.setQueryData(o,n.previousValue)},...a})}},8571:function(e,t,n){"use strict";n.d(t,{ConfigurationProvider:function(){return a},Z:function(){return u},m:function(){return s}});var r=n(27573),i=n(7653);let o=(0,i.createContext)(void 0),a=e=>{let{config:t,children:n}=e;return(0,r.jsx)(o.Provider,{value:t,children:n})},s=()=>{let e=(0,i.useContext)(o);if(!e)throw Error("useConfiguration must be called from within ConfigurationProvider");return e},u=()=>{let{applicationType:e}=s();return"claude-dot"===e}},95407:function(e,t,n){"use strict";var r,i;n.d(t,{DF:function(){return a},iw:function(){return o}}),(i=r||(r={})).GoogleTagManager="google_tag_manager",i.GoogleEnhancedConversions="google_enhanced_conversions";let o={analytics:!1,marketing:!1},a={analytics:!0,marketing:!0}},40287:function(e,t,n){"use strict";n.d(t,{SSRCookiesProvider:function(){return c},U:function(){return u},f:function(){return l}});var r=n(27573),i=n(7653);function o(e){return e.endsWith(".anthropic.com")?".anthropic.com":"claude.ai"===e||e.endsWith(".claude.ai")?".claude.ai":"claude-ai.staging.ant.dev"===e||"preview.claude-ai.staging.ant.dev"===e?".staging.ant.dev":"console.staging.ant.dev"===e?".console.staging.ant.dev":void 0}let a=(0,i.createContext)(null),s="Thu, 01 Jan 1970 00:00:01 GMT",u={get:e=>{let t=document.cookie.split(";");for(let n=0;n<t.length;n++){let[r,i]=t[n].trim().split("=");if(e===r)return decodeURIComponent(i)}},set:function(e,t){var n;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=["".concat(e,"=").concat(encodeURIComponent(t)),"max-age=".concat(null!==(n=r.maxAgeSeconds)&&void 0!==n?n:31536e3),"samesite=lax","secure","path=/"],a=o(window.location.hostname);a&&(document.cookie="".concat(e,"=[removed]; expires=").concat(s,"; samesite=lax; secure; path=/"),i.push("domain=".concat(a))),document.cookie=i.join("; ")},delete:e=>{let t=["".concat(e,"=[removed]"),"expires=".concat(s),"samesite=lax","secure"],n=o(window.location.hostname);n&&t.push("domain=".concat(n)),document.cookie=t.join("; ")}},c=e=>{let{value:t,children:n}=e,o=(0,i.useMemo)(()=>{if(!t)return{get:()=>void 0,set:()=>void 0,delete:()=>void 0};let e=Object.fromEntries(t.map(e=>[e.name,e.value]));return{get:t=>e[t],set:e=>{throw Error("Cannot set cookies in server-rendered client component. Attempting to set ".concat(e))},delete:e=>{throw Error("Cannot delete cookies in server-rendered client component. Attempting to delete ".concat(e))}}},[t]);return(0,r.jsx)(a.Provider,{value:o,children:n})},l=()=>((0,i.useContext)(a),u)},27218:function(e,t,n){"use strict";n.d(t,{Cf:function(){return A},CurrentAccountProvider:function(){return v},IS:function(){return L},Sl:function(){return T},ZJ:function(){return b},Zd:function(){return P},b9:function(){return O},cg:function(){return D},cs:function(){return I},dr:function(){return y},gQ:function(){return M},rW:function(){return k},t:function(){return C},uh:function(){return x},w7:function(){return S},z6:function(){return N}});var r=n(27573),i=n(30947),o=n(95622),a=n(6385),s=n(31825),u=n(14448),c=n(13262),l=n(92841),d=n(86971),g=n.n(d),p=n(7653),f=n(5362),m=n(8571),_=n(40287);let h=(0,p.createContext)(null),v=e=>{let{children:t}=e,n=(0,_.f)(),[u,c]=(0,p.useState)(),{data:l,error:d,isFetching:f,refetch:v}=y();(0,p.useEffect)(()=>{u&&n.set(a.cn.LAST_ACTIVE_ORG,u)},[u,n]);let w=(0,m.Z)(),C=(0,p.useMemo)(()=>{let e;if(!(null==l?void 0:l.account))return;let t=w?"chat":"api",r=l.account.memberships.map(e=>e.organization).filter(e=>e.capabilities.includes(t));if(u){let e=r.find(e=>e.uuid===u);if(e)return e}if(!e){let t=n.get(a.cn.LAST_ACTIVE_ORG);e=r.find(e=>e.uuid===t)}return e||(e=g()(r,"name")),e&&c(e.uuid),e},[l,u,w,n]),E=(0,p.useMemo)(()=>{var e;let t=d instanceof i.Hx&&403===d.statusCode||!(null==l?void 0:l.account);return{account:null==l?void 0:l.account,activeOrganization:C,statsig:null==l?void 0:l.statsig,intercomAccountHash:null==l?void 0:l.intercom_account_hash,messageLimit:null==l?void 0:null===(e=l.messageLimits)||void 0===e?void 0:e[(null==C?void 0:C.uuid)||""],setActiveOrganizationUUID:c,isLoading:f,isLoggedOut:!f&&t,refetch:async()=>{var e;return null===(e=(await v()).data)||void 0===e?void 0:e.account}}},[l,d,C,f,v]);return(0,o.id)(s.C.Account,{getAccountDetails:()=>{var e;return{account:null!==(e=E.account)&&void 0!==e?e:void 0,activeOrganization:E.activeOrganization,isLoggedOut:E.isLoggedOut}}},[E.account,E.activeOrganization,E.isLoggedOut]),(0,r.jsx)(h.Provider,{value:E,children:t})},w=e=>e&&"type"in e?null:e,y=()=>{let{data:e,refetch:t,...n}=(0,f.WE)("/api/bootstrap",{queryKey:[c.aY],staleTime:1/0,retryOnMount:!1,additionalPermittedStatusCode:403}),r=(0,p.useCallback)(async()=>{let e=await t();return{...e,data:w(e.data)}},[t]);return{...n,data:w(e),refetch:r}},C=()=>{let e=(0,p.useContext)(h);if(!e)throw Error("Must call inside CurrentAccountProvider");return e},E=e=>{let{activeOrganization:t}=C();return!!t&&t.capabilities.includes(e)},b=()=>E("claude_pro"),A=()=>E("raven"),O=()=>{let e=A(),t=b();return e?"raven":t?"claude_pro":"free"},k=()=>{switch(O()){case"claude_pro":case"raven":return!0;case"free":return!1}},S=()=>"free"===O(),N=()=>{let e=O(),t=T();switch(e){case"free":case"claude_pro":return t;case"raven":return!1}},x=()=>{let{value:e}=(0,u.F)("read_only_mode");return e},I=()=>{let{account:e,activeOrganization:t}=C(),n=(0,l.Di)(e,t,l.YB.BillingManage);return!A()||n},T=()=>{var e;let{account:t}=C(),{data:n,isLoading:r}=(0,f.WE)("/api/account/raven_eligible",{queryKey:[c.LJ,null==t?void 0:t.uuid],enabled:!!t});return{data:null!==(e=null==n?void 0:n.eligible)&&void 0!==e&&e,isLoading:r}},M=e=>{let{activeOrganization:t}=C();return(null==(e=e||t)?void 0:e.raven_type)==="enterprise"},D=e=>{let{activeOrganization:t}=C();return(null==(e=e||t)?void 0:e.raven_type)==="team"},L=()=>{let{account:e,activeOrganization:t}=C();return null==e?void 0:e.memberships.find(e=>e.organization.uuid===(null==t?void 0:t.uuid))},P=()=>(0,u.F)("claude_ai_ember").value},53500:function(e,t,n){"use strict";n.d(t,{u:function(){return m}});var r=n(95407),i=n(40287),o=n(6385),a=n(44475),s=n(91099),u=n(45144),c=n(35919),l=n.n(c),d=n(74717);class g{constructor(){this.name="RemoveUserAgent",this.version="1.0.0",this.type="before",this.isLoaded=()=>!0,this.load=()=>Promise.resolve(),this.redactPiiFromContext=e=>{var t;return(null==e?void 0:null===(t=e.event)||void 0===t?void 0:t.context)&&(delete e.event.context.userAgent,delete e.event.context.email,e.event.context.ip="REDACTED"),e},this.track=this.redactPiiFromContext,this.identify=this.redactPiiFromContext,this.page=this.redactPiiFromContext,this.group=this.redactPiiFromContext,this.alias=this.redactPiiFromContext,this.screen=this.redactPiiFromContext}}let p=e=>({marketing:e.marketing,analytics:e.analytics,necessary:!0});class f{constructor(){this.consentChangedCallback=l(),this.preferences=p(r.iw),this.analyticsBrowser=null,this.anonymousId=(0,d.Z)(),this.loadIfNecessary=e=>{let{segmentKey:t,segmentCdnHost:n,segmentApiHost:r,requiresExplicitConsent:i}=e;return this.analyticsBrowser||(this.analyticsBrowser=new s.b,this.loadInitialConsentPreferences(i),this.wrapAnalyticsWithConsent(this.analyticsBrowser).load({writeKey:t,cdnURL:"https://".concat(n),plugins:[new g]},{disableClientPersistence:!0,integrations:{"Segment.io":{apiHost:"".concat(r,"/v1"),protocol:"https"}}}).catch(e=>{(0,u.Tb)(e,{extra:{message:"Failed to load analytics"}})}),this.syncAnonymousId()),this.analyticsBrowser},this.registerOnConsentChanged=e=>{this.consentChangedCallback=t=>{t.analytics?this.syncAnonymousId():this.deleteCookie(),e(t)}},this.getCategories=()=>this.preferences,this.loadInitialConsentPreferences=e=>{let t;try{t=i.U.get(o.cn.CONSENT_PREFERENCES)}catch(e){(0,u.Tb)(e,{extra:{message:"Failed to get consent preferences cookie"}})}if(t)try{let e=JSON.parse(t);this.preferences=p(e)}catch(e){console.warn("Malformed consent preferences cookie",t)}else e||(this.preferences=p(r.DF))},this.syncAnonymousId=()=>{this.analyticsBrowser&&this.anonymousId&&(this.analyticsBrowser.setAnonymousId(this.anonymousId).catch(l()),this.preferences.analytics&&i.U.set(o.cn.SEGMENT_ANONYMOUS_ID,this.anonymousId))},this.deleteCookie=()=>{i.U.delete(o.cn.SEGMENT_ANONYMOUS_ID)},this.reset=()=>{var e;this.deleteCookie(),this.anonymousId=(0,d.Z)(),null===(e=this.analyticsBrowser)||void 0===e||e.reset()},this.updateCategories=e=>{this.preferences=p(e),this.consentChangedCallback(this.preferences)},this.wrapAnalyticsWithConsent=e=>(0,a.K)({registerOnConsentChanged:this.registerOnConsentChanged,getCategories:this.getCategories})(e);{let e=i.U.get(o.cn.SEGMENT_ANONYMOUS_ID);e&&(this.anonymousId=e)}}}let m=new f},95622:function(e,t,n){"use strict";n.d(t,{H9:function(){return s},LT:function(){return i},id:function(){return u}});var r,i,o=n(7653),a=n(88755);function s(e,t){let n=(0,a.useIsClaudeApp)();(0,o.useEffect)(()=>{var r;if(n)return null===(r=window.claudeAppBindings)||void 0===r||r.registerBinding(e,t),()=>{var t;null===(t=window.claudeAppBindings)||void 0===t||t.unregisterBinding(e)}},[e,t,n])}function u(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=(0,a.useIsClaudeApp)();(0,o.useEffect)(()=>{if(r&&window.registerDesktopApi)try{return window.registerDesktopApi(e,t)}catch(e){console.error("Failed to register desktop API",e)}},[...n,e,t])}(r=i||(i={})).cmdK="cmdK",r.googleAuthCode="googleAuthCode"},85069:function(e,t,n){"use strict";n.d(t,{D0:function(){return s},Gd:function(){return u},Gg:function(){return o},Rx:function(){return a},eM:function(){return i},t6:function(){return c}});var r=n(66947);let i="!h-[52px] !border-0",o="shadow-[0px_1px_0px_0px_theme(colors.border-300)]";function a(e){let{version:t,platform:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.toLowerCase(),o=i.match(/claude(?:nest)?\/([^ ]+)/i),a=o?o[1]:null;if(!a)return!1;let s=!t||(0,r.satisfies)(a,t,{includePrerelease:!0}),u=!n||"mac"===n&&i.includes("macintosh")||"windows"===n&&i.includes("windows");return s&&u&&!!window.claudeAppBindings}let s={version:">=0.3.7"},u={version:">=0.9.0"},c={...u,platform:"mac"}},30947:function(e,t,n){"use strict";n.d(t,{Hx:function(){return r},fT:function(){return i}});class r extends Error{constructor(e,t,n,r,i){super(e),this.type=t,this.statusCode=n,this.extra=r,this.errorCode=i}}function i(e,t,n,i){if(t&&"object"==typeof t&&"error"in t){let{message:n,type:o,details:a,...s}=t.error;s.headers=i,s.details=a;let u=null;a&&"object"==typeof a&&"error_code"in a&&"string"==typeof a.error_code&&(u=a.error_code);let c=n;return!c&&"error_description"in t&&"string"==typeof t.error_description&&(c=t.error_description),new r(c,o,e,s,u)}return new r(n,"api_error",e,t||{})}},6385:function(e,t,n){"use strict";var r,i;n.d(t,{_R:function(){return l},cn:function(){return r},dS:function(){return p},gV:function(){return s},jC:function(){return c},jW:function(){return d},m7:function(){return a},ox:function(){return u},pz:function(){return o},r6:function(){return g}});let o="https://support.anthropic.com/en/articles/8602283-does-claude-ai-have-any-message-limits",a="https://support.anthropic.com/en/articles/8325612-does-claude-pro-have-any-usage-limits",s="https://support.anthropic.com/en/articles/8324991-about-claude-pro-usage",u="https://support.anthropic.com/en/articles/9266767-what-is-the-claude-team-plan",c="https://support.anthropic.com/en/articles/7996848-how-large-is-claude-s-context-window",l="https://support.anthropic.com/en/articles/7996856-what-is-the-maximum-prompt-length",d="https://support.anthropic.com/en/articles/8106465-our-approach-to-user-safety";(i=r||(r={})).LAST_ACTIVE_ORG="lastActiveOrg",i.COLOR_MODE="CH-prefers-color-scheme",i.CONSENT_PREFERENCES="anthropic-consent-preferences",i.SIDEBAR_PINNED="user-sidebar-pinned",i.SIDEBAR_VISIBLE_ON_LOAD="user-sidebar-visible-on-load",i.CONSOLE_SIDEBAR_EXPANDED="console-sidebar-expanded",i.SKIP_HARMONY_INFO_MODAL="skip-harmony-info-modal",i.RETURN_TO="return-to",i.JOIN_TOKEN="join-token",i.LEGAL_ACCEPTANCES="legal-acceptances",i.SESSION_KEY="sessionKey",i.PENDING_LOGIN="pendingLogin",i.SSO_STATE="ssoState",i.LOCALE="locale",i.SEGMENT_ANONYMOUS_ID="ajs_anonymous_id";let g=4,p=3.2},31825:function(e,t,n){"use strict";var r,i;n.d(t,{C:function(){return r}}),(i=r||(r={})).Account="Account",i.Toast="Toast"},14448:function(e,t,n){"use strict";n.d(t,{F:function(){return i}});var r=n(15992);let i=e=>(0,r.useGate)(e)},8073:function(e,t,n){"use strict";n.d(t,{F:function(){return o}});var r=n(85069),i=n(98649);let o=e=>{let t=i.Bf.UNKNOWN;return(0,r.Rx)(window.navigator.userAgent)?t=i.Bf.DESKTOP_APP:"claude-dot"===e?t=i.Bf.WEB_CLAUDE_AI:"console"===e&&(t=i.Bf.WEB_CONSOLE),{"anthropic-client-platform":t}}},13262:function(e,t,n){"use strict";n.d(t,{$T:function(){return E},AI:function(){return x},BB:function(){return f},CA:function(){return R},CN:function(){return C},Dx:function(){return z},E0:function(){return K},Ej:function(){return ec},FU:function(){return U},Fd:function(){return ea},Gj:function(){return L},HI:function(){return J},I8:function(){return s},I_:function(){return m},LJ:function(){return et},Nr:function(){return ed},Os:function(){return V},PB:function(){return M},QU:function(){return w},Qn:function(){return p},RK:function(){return d},Rz:function(){return W},Sp:function(){return q},Su:function(){return el},UD:function(){return B},VH:function(){return P},Wj:function(){return ee},XC:function(){return $},Xg:function(){return I},Y9:function(){return es},Yp:function(){return H},Yu:function(){return F},Yw:function(){return Y},aY:function(){return c},cF:function(){return ei},dY:function(){return eo},eW:function(){return v},ec:function(){return r},eo:function(){return _},eu:function(){return X},gi:function(){return b},gs:function(){return l},it:function(){return eu},jb:function(){return T},je:function(){return g},l3:function(){return j},l8:function(){return i},ld:function(){return D},lx:function(){return k},mm:function(){return h},n$:function(){return O},ph:function(){return a},q2:function(){return Z},qB:function(){return en},s8:function(){return Q},sB:function(){return A},sM:function(){return y},sf:function(){return u},tt:function(){return S},tv:function(){return o},wD:function(){return G},x5:function(){return er},zy:function(){return N}});let r="account_profile",i="account_claude_code_waitlist",o="chat_conversation_list",a="chat_conversation",s="chat_conversation_tree",u="chat_failed_stream_retry",c="current_account",l="current_account_deletable",d="invoice_list",g="upcoming_invoice",p="model_config",f="org_invites",m="org_members",_="org_profile",h="org",v="account_statsig",w="subscription_details",y="subscription_status",C="payment_intent",E="project",b="project_list",A="artifacts_list",O="top_projects",k="project_list_conversations",S="project_doc",N="project_doc_list",x="project_sync_list",I="project_sync_auth",T="project_files_list",M="chat_snapshot_list_all",D="chat_snapshot",L="chat_snapshot_latest",P="activity_feed",j="project_accounts_list",B="members_limit",R="enterprise_auth/domains",z="enterprise_auth/domain_memberships",F="enterprise_auth/v2/sso_settings",U="sync_org_settings",V="sync_gh_auth_status",G="sync_gh_repos_for_current_user",W="sync_gh_repo",K="sync_gh_repo_tree_json",Y="sync_gd_auth_status",H="sync_gd_document",J="sync_gd_document_metadata",Q="sync_gd_recents",q="sync_outline_auth_status",Z="sync_outline_document",X="public_projects_enabled",$="org_feature_settings",ee="artifact_feedback_eligibility",et="raven_eligibility",en="shared_artifact_version",er="artifact_visibility",ei="chat_sync",eo="sync_progress",ea="parent_organization",es="parent_organization_join_proposal",eu="custom_styles_list",ec="file_preview",el=[V,G,W,K,Y,H,J,Q,q,Z,ei,eo,x,I],ed="get_spotlight"},92841:function(e,t,n){"use strict";var r,i,o,a;n.d(t,{Di:function(){return u},Fs:function(){return c},YB:function(){return r}}),(o=r||(r={})).MembersView="members:view",o.MembersManage="members:manage",o.ApiView="api:view",o.ApiManage="api:manage",o.IntegrationsManage="integrations:manage",o.BillingView="billing:view",o.BillingManage="billing:manage",o.OrganizationManage="organization:manage",o.InvoicesView="invoices:view",o.UsageView="usage:view",o.ExportData="export:data",o.OwnersManage="owners:manage",o.WorkspacesView="workspaces:view",o.WorkspacesManage="workspaces:manage",o.EnterpriseAuthManage="enterprise_auth:manage",o.LimitsView="limits:view",o.MembershipAdminsManage="membership_admins:manage",o.ExportAuditLogs="export:audit_logs",o.SecurityKeysManage="security_keys:manage",(a=i||(i={})).MembersView="workspace:members:view",a.MembersManage="workspace:members:manage",a.ApiView="workspace:api:view",a.ApiManage="workspace:api:manage",a.WorkspaceView="workspace:workspace:view",a.WorkspaceManage="workspace:workspace:manage",a.UsageView="workspace:usage:view",a.CostView="workspace:cost:view",a.LimitsView="workspace:limits:view",a.LimitsManage="workspace:limits:manage";let s={user:["members:view","workspaces:view"],membership_admin:["members:view","members:manage"],developer:["members:view","api:view","api:manage","usage:view","workspaces:view","limits:view","security_keys:manage"],billing:["members:view","billing:view","billing:manage","usage:view","invoices:view","workspaces:view","limits:view"],admin:["members:view","members:manage","api:view","api:manage","billing:view","billing:manage","usage:view","invoices:view","organization:manage","export:data","workspaces:view","workspaces:manage","limits:view","security_keys:manage"],owner:["members:view","members:manage","api:view","api:manage","integrations:manage","billing:view","billing:manage","usage:view","invoices:view","organization:manage","owners:manage","workspaces:view","workspaces:manage","enterprise_auth:manage","limits:view","membership_admins:manage","export:audit_logs"],primary_owner:["members:view","members:manage","api:view","api:manage","integrations:manage","billing:view","billing:manage","usage:view","invoices:view","organization:manage","export:data","owners:manage","workspaces:view","workspaces:manage","enterprise_auth:manage","limits:view","membership_admins:manage","export:audit_logs"]},u=(e,t,n)=>!!e&&!!t&&c(e,t,n),c=(e,t,n)=>{var r;let i=null===(r=e.memberships.find(e=>e.organization.uuid===t.uuid))||void 0===r?void 0:r.role;return!!i&&s[i].includes(n)}},43965:function(e,t,n){"use strict";n.d(t,{ServerUserAgentProvider:function(){return a},q:function(){return s}});var r=n(27573),i=n(7653);let o=(0,i.createContext)(null);function a(e){let{userAgent:t,children:n}=e;return(0,r.jsx)(o.Provider,{value:t,children:n})}function s(){return(0,i.useContext)(o)}},98649:function(e,t,n){"use strict";n.d(t,{B0:function(){return g},Bf:function(){return l},Ig:function(){return p},JX:function(){return u},Mm:function(){return s},_J:function(){return c},d2:function(){return m},h3:function(){return f}});var r,i,o,a,s,u,c,l,d=n(41270);(r=s||(s={})).UPLOAD="upload",r.ADD_SCREENSHOT="addScreenshot",r.PROJECTS="projects",r.SEARCH_AND_TOOLS="searchAndTools",r.EXTENDED_THINKING="extendedThinking",r.DRIVE="drive",r.GITHUB="github",r.STYLES="styles",r.COMPASS="compass";let g=["Product Management","Engineering","Human Resources","Finance","Marketing","Sales","Operations","Data Science","Design","Legal","Other"],p=["coding_development","learning_studying","writing_content_creation","business_strategy","design_creativity","life_stuff","claudes_choice"],f=["coding_development","learning_studying","writing_content_creation","business_strategy","design_creativity","career_chat","claudes_choice"];(i=u||(u={})).CHAT="chat",i.PROJECT="project",i.ALL="all",(o=c||(c={})).DAY="day",o.MONTH="month",o.INDEFINITE="indefinite";let m=d.z.object({type:d.z.literal("knowledge"),title:d.z.string(),url:d.z.string().nullable(),metadata:d.z.discriminatedUnion("type",[d.z.object({type:d.z.literal("google_doc_metadata"),doc_uuid:d.z.string(),owner:d.z.string().nullable()}),d.z.object({type:d.z.literal("webpage_metadata"),favicon_url:d.z.string().nullable(),site_name:d.z.string().nullable(),site_domain:d.z.string()})])});(a=l||(l={})).UNKNOWN="unknown",a.ANDROID="android",a.IOS="ios",a.DESKTOP_APP="desktop_app",a.WEB_CLAUDE_AI="web_claude_ai",a.WEB_CONSOLE="web_console"},88755:function(e,t,n){"use strict";n.d(t,{useIsClaudeApp:function(){return a}});var r=n(7653),i=n(85069),o=n(43965);function a(){let{version:e,platform:t}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,o.q)();let n=window.navigator.userAgent;return(0,r.useMemo)(()=>!!n&&(0,i.Rx)(n,{version:e,platform:t}),[n,e,t])}}}]);