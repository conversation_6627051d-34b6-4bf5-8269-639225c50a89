#!/usr/bin/env python3
"""
Bot Functionality Test - Test each bot's core functionality
Tests the actual working capabilities of each bot
"""

import os
import sys
import time
import logging
from datetime import datetime
from typing import Dict, Any

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BotFunctionalityTest:
    """Test each bot's core functionality"""
    
    def __init__(self):
        self.test_results = {}
        self.test_account_id = 5  # Use account 5 for testing
        
        # Import required modules
        try:
            from core.instagram_manager import InstagramManager
            from core.database import DatabaseManager
            self.instagram_manager = InstagramManager()
            self.db_manager = DatabaseManager()
        except ImportError as e:
            logger.error(f"Failed to import required modules: {e}")
            raise
    
    def test_bio_scanner_functionality(self) -> Dict[str, Any]:
        """Test Bio Scanner Bot functionality"""
        logger.info("Testing Bio Scanner Bot functionality...")
        
        try:
            from bots.bio_scanner_bot import BioScannerBot
            
            # Create test CSV file
            test_csv_path = "C:/files/test_bio_functionality.csv"
            test_csv_content = "username,id1,id3,title\ninstagram,cristiano,leomessi,test_user"
            
            os.makedirs("C:/files", exist_ok=True)
            with open(test_csv_path, 'w', encoding='utf-8') as f:
                f.write(test_csv_content)
            
            # Initialize bot
            bot = BioScannerBot(account_id=self.test_account_id)
            
            # Test CSV loading
            users_to_process = bot.load_input_data()
            if users_to_process:
                logger.info(f"CSV loaded successfully with {len(users_to_process)} users")
                
                # Test user processing (just first user)
                test_user = users_to_process[0]
                logger.info(f"Testing with user: {test_user}")
                
                # Test user info retrieval
                user_info = bot.get_user_profile_data(test_user[0], test_user[1], test_user[2], test_user[3])  # username, id1, id3, title
                if user_info:
                    logger.info(f"User info retrieved: {user_info.get('username', 'N/A')}")
                    
                    # Test database save
                    save_result = self.db_manager.save_user_data(
                        username=user_info.get('username', ''),
                        user_id=user_info.get('user_id', None),
                        full_name=user_info.get('full_name', ''),
                        biography=user_info.get('biography', ''),
                        followers_count=user_info.get('follower_count', 0),
                        following_count=user_info.get('following_count', 0),
                        profile_pic_url=user_info.get('profile_pic_url', ''),
                        is_verified=user_info.get('is_verified', False),
                        is_private=user_info.get('is_private', False),
                        external_url=user_info.get('external_url', ''),
                        account_type=user_info.get('account_type', ''),
                        business_category_name=user_info.get('business_category_name', ''),
                        category_name=user_info.get('category_name', ''),
                        category=user_info.get('category', ''),
                        public_phone_number=user_info.get('public_phone_number', ''),
                        public_email=user_info.get('public_email', ''),
                        city_name=user_info.get('city_name', ''),
                        address_street=user_info.get('address_street', ''),
                        zip=user_info.get('zip', ''),
                        latitude=user_info.get('latitude', None),
                        longitude=user_info.get('longitude', None),
                        contact_phone_number=user_info.get('contact_phone_number', ''),
                        contact_email=user_info.get('contact_email', ''),
                        website=user_info.get('website', ''),
                        scan_date=datetime.now(),
                        account_id=self.test_account_id
                    )
                    
                    if save_result:
                        logger.info("User data saved to database successfully")
                        result = {
                            'success': True,
                            'message': 'Bio Scanner functionality test passed',
                            'users_processed': 1,
                            'data_saved': True
                        }
                    else:
                        logger.warning("Failed to save user data to database")
                        result = {
                            'success': False,
                            'error': 'Database save failed',
                            'message': 'Bio Scanner functionality test failed at database save'
                        }
                else:
                    logger.warning("Failed to retrieve user info")
                    result = {
                        'success': False,
                        'error': 'User info retrieval failed',
                        'message': 'Bio Scanner functionality test failed at user info retrieval'
                    }
            else:
                logger.warning("No users loaded from CSV")
                result = {
                    'success': False,
                    'error': 'CSV loading failed',
                    'message': 'Bio Scanner functionality test failed at CSV loading'
                }
            
            # Cleanup test file
            if os.path.exists(test_csv_path):
                os.remove(test_csv_path)
            
            return result
            
        except Exception as e:
            logger.error(f"Bio Scanner functionality test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Bio Scanner functionality test failed with exception'
            }
    
    def test_data_retriever_functionality(self) -> Dict[str, Any]:
        """Test Data Retriever Bot functionality"""
        logger.info("Testing Data Retriever Bot functionality...")
        
        try:
            from bots.data_retriever_bot import DataRetrieverBot
            
            # Initialize bot
            bot = DataRetrieverBot(account_id=self.test_account_id)
            
            # Test user search (using Instagram Manager instead)
            search_results = self.instagram_manager.search_users(self.test_account_id, "instagram", 5)
            if search_results:
                logger.info(f"User search successful, found {len(search_results)} users")
                
                # Test getting user info
                if search_results:
                    test_user = search_results[0]
                    user_info = self.instagram_manager.get_user_info(self.test_account_id, test_user['username'])
                    if user_info:
                        logger.info(f"User info retrieved: {user_info.get('username', 'N/A')}")
                        
                        # Test getting user media (using Instagram Manager instead)
                        media_info = self.instagram_manager.get_user_media(self.test_account_id, test_user['username'], 3)
                        if media_info:
                            logger.info(f"Media info retrieved: {len(media_info)} posts")
                            result = {
                                'success': True,
                                'message': 'Data Retriever functionality test passed',
                                'users_searched': len(search_results),
                                'user_info_retrieved': True,
                                'media_retrieved': len(media_info)
                            }
                        else:
                            logger.warning("Failed to retrieve media info")
                            result = {
                                'success': False,
                                'error': 'Media retrieval failed',
                                'message': 'Data Retriever functionality test failed at media retrieval'
                            }
                    else:
                        logger.warning("Failed to retrieve user info")
                        result = {
                            'success': False,
                            'error': 'User info retrieval failed',
                            'message': 'Data Retriever functionality test failed at user info retrieval'
                        }
                else:
                    result = {
                        'success': False,
                        'error': 'No search results',
                        'message': 'Data Retriever functionality test failed - no search results'
                    }
            else:
                logger.warning("User search failed")
                result = {
                    'success': False,
                    'error': 'User search failed',
                    'message': 'Data Retriever functionality test failed at user search'
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Data Retriever functionality test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Data Retriever functionality test failed with exception'
            }
    
    def test_message_bot_functionality(self) -> Dict[str, Any]:
        """Test Message Bot functionality"""
        logger.info("Testing Message Bot functionality...")
        
        try:
            from bots.message_bot import MessageBot
            
            # Initialize bot
            bot = MessageBot(
                account_id=self.test_account_id,
                instagram_manager=self.instagram_manager,
                db_manager=self.db_manager
            )
            
            # Test message template loading
            messages = self.db_manager.get_all_messages()
            if messages:
                logger.info(f"Found {len(messages)} message templates")
                
                # Test message sending preparation (without actually sending)
                test_message = messages[0] if messages else None
                if test_message:
                    logger.info(f"Testing with message template: {test_message.get('title', 'N/A')}")
                    
                    # Test message formatting
                    formatted_message = bot.format_message(test_message.get('content', test_message.get('message', '')), {
                        'username': 'test_user',
                        'full_name': 'Test User'
                    })
                    
                    if formatted_message:
                        logger.info("Message formatting successful")
                        result = {
                            'success': True,
                            'message': 'Message Bot functionality test passed',
                            'templates_loaded': len(messages),
                            'message_formatting': True
                        }
                    else:
                        logger.warning("Message formatting failed")
                        result = {
                            'success': False,
                            'error': 'Message formatting failed',
                            'message': 'Message Bot functionality test failed at message formatting'
                        }
                else:
                    result = {
                        'success': False,
                        'error': 'No message templates available',
                        'message': 'Message Bot functionality test failed - no templates'
                    }
            else:
                logger.warning("No message templates found")
                result = {
                    'success': False,
                    'error': 'No message templates',
                    'message': 'Message Bot functionality test failed - no templates in database'
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Message Bot functionality test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Message Bot functionality test failed with exception'
            }
    
    def test_account_warmup_functionality(self) -> Dict[str, Any]:
        """Test Account Warmup Bot functionality"""
        logger.info("Testing Account Warmup Bot functionality...")
        
        try:
            from bots.account_warmup_bot import AccountWarmupBot
            
            # Initialize bot
            bot = AccountWarmupBot(
                account_id=self.test_account_id,
                instagram_manager=self.instagram_manager,
                db_manager=self.db_manager
            )
            
            # Test bot initialization and basic methods
            if bot:
                # Test action selection
                action = bot.choose_action()
                if action in bot.action_weights:
                    logger.info(f"Action selection successful: {action}")
                    
                    # Test action weights
                    weights_sum = sum(bot.action_weights.values())
                    if abs(weights_sum - 1.0) < 0.01:  # Should sum to 1.0
                        logger.info("Action weights configuration correct")
                        
                        result = {
                            'success': True,
                            'message': 'Account Warmup Bot functionality test passed',
                            'action_selection': True,
                            'weights_configuration': True,
                            'max_actions': bot.max_actions_per_session,
                            'session_duration': bot.session_duration
                        }
                    else:
                        logger.warning("Action weights configuration incorrect")
                        result = {
                            'success': False,
                            'error': 'Action weights configuration incorrect',
                            'message': 'Account Warmup Bot functionality test failed at weights configuration'
                        }
                else:
                    logger.warning("Action selection failed")
                    result = {
                        'success': False,
                        'error': 'Action selection failed',
                        'message': 'Account Warmup Bot functionality test failed at action selection'
                    }
            else:
                result = {
                    'success': False,
                    'error': 'Bot initialization failed',
                    'message': 'Account Warmup Bot functionality test failed at initialization'
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Account Warmup Bot functionality test failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': 'Account Warmup Bot functionality test failed with exception'
            }
    
    def run_all_bot_tests(self) -> Dict[str, Any]:
        """Run all bot functionality tests"""
        logger.info("Starting bot functionality tests...")
        start_time = datetime.now()
        
        # Test each bot
        self.test_results['bio_scanner'] = self.test_bio_scanner_functionality()
        self.test_results['data_retriever'] = self.test_data_retriever_functionality()
        self.test_results['message_bot'] = self.test_message_bot_functionality()
        self.test_results['account_warmup'] = self.test_account_warmup_functionality()
        
        # Calculate summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        total_tests = len(self.test_results)
        
        self.test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': (passed_tests / total_tests) * 100,
            'total_duration': duration,
            'test_timestamp': start_time.isoformat()
        }
        
        return self.test_results
    
    def print_results(self):
        """Print formatted test results"""
        print("\n" + "="*60)
        print("BOT FUNCTIONALITY TEST RESULTS")
        print("="*60)
        
        # Print individual bot test results
        for bot_name, result in self.test_results.items():
            if bot_name != 'summary':
                status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
                print(f"{bot_name.replace('_', ' ').title()}: {status}")
                
                if not result.get('success', False) and 'error' in result:
                    print(f"  Error: {result['error']}")
                
                if 'message' in result:
                    print(f"  Message: {result['message']}")
                
                # Print additional details
                for key, value in result.items():
                    if key not in ['success', 'error', 'message']:
                        print(f"  {key}: {value}")
        
        # Print summary
        if 'summary' in self.test_results:
            summary = self.test_results['summary']
            print("\n" + "-"*60)
            print("TEST SUMMARY")
            print("-"*60)
            print(f"Total Tests: {summary['total_tests']}")
            print(f"Passed: {summary['passed_tests']}")
            print(f"Failed: {summary['failed_tests']}")
            print(f"Success Rate: {summary['success_rate']:.1f}%")
            print(f"Duration: {summary['total_duration']:.2f} seconds")
            print(f"Timestamp: {summary['test_timestamp']}")
        
        print("\n" + "="*60)

def main():
    """Main test execution"""
    print("Starting Bot Functionality Tests...")
    print("This will test each bot's core functionality")
    print("="*60)
    
    try:
        # Create test instance
        tester = BotFunctionalityTest()
        
        # Run all tests
        results = tester.run_all_bot_tests()
        
        # Print results
        tester.print_results()
        
        # Return exit code based on results
        if 'summary' in results:
            failed_tests = results['summary']['failed_tests']
            if failed_tests == 0:
                print("\n🎉 All bot functionality tests passed!")
                return 0
            else:
                print(f"\n⚠️  {failed_tests} bot functionality tests failed.")
                return 1
        else:
            print("\n❌ Bot functionality test execution failed.")
            return 1
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error during bot functionality testing: {e}")
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
