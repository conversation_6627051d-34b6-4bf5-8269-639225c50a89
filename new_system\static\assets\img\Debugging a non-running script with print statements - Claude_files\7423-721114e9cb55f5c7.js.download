(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7423],{44475:function(t,e,n){"use strict";n.d(e,{K:function(){return m}});class r extends Error{constructor(t,e){super(e),this.name=t}}class i extends r{constructor(t,e){2==arguments.length&&(t+=` (Received: ${JSON.stringify(e)})`),super("ValidationError",`[Validation] ${t}`)}}function o(t,e){if("function"!=typeof t)throw new i(`${e} is not a function`,t)}function s(t,e){if(null===t||"object"!=typeof t)throw new i(`${e} is not an object`,t)}function a(t){let e=!0;if(t&&"object"==typeof t&&!Array.isArray(t)){for(let n in e=!1,t)if("boolean"!=typeof t[n]){e=!0;break}}if(e)throw new i("Consent Categories should be {[categoryName: string]: boolean}",t)}let u=(t,...e)=>{let n=e.reduce((t,e)=>n=>e(t(n)),t=>t);return(...e)=>n(t(...e))};class c extends r{constructor(t){super("AbortLoadError",""),this.loadSegmentNormally=t}}class l{abort(t){if("object"!=typeof t)throw new i("arg should be an object",t);throw new c(t.loadSegmentNormally)}}let p=t=>async({payload:e,next:n})=>{let r=await t();a(r),e.obj.context.consent={...e.obj.context.consent,categoryPreferences:r},n(e)},f=t=>Array.from(new Set(t)),d=(t,e)=>e.reduce((e,n)=>(n in t&&(e[n]=t[n]),e),{}),h=async(t,e,n)=>{let r;if(!(r=n?f(Object.values(n).reduce((t,e)=>t.concat(e))):e.consentSettings?.allCategories||[]).length)throw new i("Invariant: No consent categories defined in Segment",[]);return d(await t(),r)};class v{get analytics(){return y(this._uninitializedAnalytics)}constructor(t){!function(t){if(s(t,"analytics"),!("load"in t)||!("on"in t)||!("addSourceMiddleware"in t))throw new i("analytics is not an Analytics instance",t)}(t),this._uninitializedAnalytics=t,this.loadNormally=t.load.bind(this._uninitializedAnalytics),this.cdnSettings=new Promise(t=>this.analytics.on("initialize",t))}replaceLoadMethod(t){this.analytics.load=t}page(){this.analytics.page()}configureConsentStampingMiddleware({getCategories:t,pruneUnmappedCategories:e,integrationCategoryMappings:n}){let r=p(async()=>e?h(t,await this.cdnSettings,n):t());return this.analytics.addSourceMiddleware(r)}consentChange(t){try{a(t)}catch(t){return console.error(t)}this.analytics.track("Segment Consent Preference",void 0,{consent:{categoryPreferences:t}})}}let y=t=>{if(Array.isArray(t)){let e=t._loadOptions??{},n=window[e?.globalAnalyticsKey??"analytics"];if(n?.initialized)return n}return t},g=(t,e)=>!!e&&!e.hasUnmappedDestinations&&Object.keys(t).filter(e=>t[e]).every(t=>!e.allCategories.includes(t)),m=(...[t])=>{!function(t){if("object"!=typeof t||!t)throw new i("Options should be an object",t);o(t.getCategories,"getCategories"),t.shouldLoadSegment&&o(t.shouldLoadSegment,"shouldLoadSegment"),t.shouldEnableIntegration&&o(t.shouldEnableIntegration,"shouldEnableIntegration"),t.shouldDisableSegment&&o(t.shouldDisableSegment,"shouldDisableSegment"),t.integrationCategoryMappings&&s(t.integrationCategoryMappings,"integrationCategoryMappings"),t.registerOnConsentChanged&&o(t.registerOnConsentChanged,"registerOnConsentChanged")}(t);let{shouldDisableSegment:e,getCategories:n,shouldLoadSegment:r,integrationCategoryMappings:p,pruneUnmappedCategories:f,shouldEnableIntegration:d,registerOnConsentChanged:h,shouldLoadWrapper:y}=t;return t=>{let i=new v(t),o=y?.()||Promise.resolve();o.then(()=>{h?.(t=>i.consentChange(t))});let s=async(t,s)=>{let h;if(s?.initialPageview&&(i.page(),s={...s,initialPageview:!1}),!await e?.()){try{await o,h=await r?.(new l)||await n()}catch(e){if(e instanceof c){!0===e.loadSegmentNormally&&i.loadNormally(t,s);return}throw e}return a(h),i.configureConsentStampingMiddleware({getCategories:n,integrationCategoryMappings:p,pruneUnmappedCategories:f}),i.loadNormally(t,{...s,updateCDNSettings:u(t=>t.remotePlugins?w(t,h,p,d):t,s?.updateCDNSettings||(t=>t)),disable:_(h,s?.disable)})}};return i.replaceLoadMethod(s),t}},b=t=>{if(t&&"object"==typeof t&&"consentSettings"in t&&"object"==typeof t.consentSettings&&t.consentSettings&&"categories"in t.consentSettings&&Array.isArray(t.consentSettings.categories))return t.consentSettings.categories||void 0},w=(t,e,n,r)=>{let{remotePlugins:i,integrations:o}=t,s=t=>{let i=n?n[t]:b(o[t]);return r?r(i||[],e,{creationName:t}):!i||!i.length||i.every(t=>e[t])};return Object.keys(o).reduce((t,e)=>(s(e)||(t.remotePlugins=t.remotePlugins&&t.remotePlugins.filter(t=>t.creationName!==e),delete t.integrations[e]),t),{...t,remotePlugins:i,integrations:{...o}})},_=(t,e)=>!0===e||(n=>g(t,n.consentSettings)||"function"==typeof e&&e(n))},52203:function(t,e,n){"use strict";function r(t,e){return new Promise(function(n,r){var i=setTimeout(function(){r(Error("Promise timed out"))},e);t.then(function(t){return clearTimeout(i),n(t)}).catch(r)})}function i(t,e,n){var i=function(){try{return Promise.resolve(e(t))}catch(t){return Promise.reject(t)}};return new Promise(function(t){return setTimeout(t,n)}).then(function(){return r(i(),1e3)}).catch(function(e){null==t||t.log("warn","Callback Error",{error:e}),null==t||t.stats.increment("callback_error")}).then(function(){return t})}n.d(e,{FJ:function(){return r},UI:function(){return i}})},55114:function(t,e,n){"use strict";n.d(e,{Y:function(){return u},_:function(){return c}});var r=n(15156),i=n(59681),o=n(83780),s=function(){function t(){this._logs=[]}return t.prototype.log=function(t,e,n){var r=new Date;this._logs.push({level:t,message:e,time:r,extras:n})},Object.defineProperty(t.prototype,"logs",{get:function(){return this._logs},enumerable:!1,configurable:!0}),t.prototype.flush=function(){if(this.logs.length>1){var t=this._logs.reduce(function(t,e){var n,r,i,s=(0,o.pi)((0,o.pi)({},e),{json:JSON.stringify(e.extras,null," "),extras:e.extras});delete s.time;var a=null!==(i=null===(r=e.time)||void 0===r?void 0:r.toISOString())&&void 0!==i?i:"";return t[a]&&(a="".concat(a,"-").concat(Math.random())),(0,o.pi)((0,o.pi)({},t),((n={})[a]=s,n))},{});console.table?console.table(t):console.log(t)}else this.logs.forEach(function(t){var e=t.level,n=t.message,r=t.extras;"info"===e||"debug"===e?console.log(n,null!=r?r:""):console[e](n,null!=r?r:"")});this._logs=[]},t}(),a=n(75174),u=function(t){var e,n,r;this.retry=null===(e=t.retry)||void 0===e||e,this.type=null!==(n=t.type)&&void 0!==n?n:"plugin Error",this.reason=null!==(r=t.reason)&&void 0!==r?r:""},c=function(){function t(t,e,n,i){void 0===e&&(e=(0,r.v4)()),void 0===n&&(n=new a.i),void 0===i&&(i=new s),this.attempts=0,this.event=t,this._id=e,this.logger=i,this.stats=n}return t.system=function(){},t.prototype.isSame=function(t){return t.id===this.id},t.prototype.cancel=function(t){if(t)throw t;throw new u({reason:"Context Cancel"})},t.prototype.log=function(t,e,n){this.logger.log(t,e,n)},Object.defineProperty(t.prototype,"id",{get:function(){return this._id},enumerable:!1,configurable:!0}),t.prototype.updateEvent=function(t,e){var n;if("integrations"===t.split(".")[0]){var r=t.split(".")[1];if((null===(n=this.event.integrations)||void 0===n?void 0:n[r])===!1)return this.event}return(0,i.N)(this.event,t,e),this.event},t.prototype.failedDelivery=function(){return this._failedDelivery},t.prototype.setFailedDelivery=function(t){this._failedDelivery=t},t.prototype.logs=function(){return this.logger.logs},t.prototype.flush=function(){this.logger.flush(),this.stats.flush()},t.prototype.toJSON=function(){return{id:this._id,event:this.event,logs:this.logger.logs,metrics:this.stats.metrics}},t}()},32593:function(t,e,n){"use strict";n.d(e,{M:function(){return o},Z:function(){return s}});var r=n(83780),i=n(97480),o="onRemoveFromFuture",s=function(t){function e(e,n,r){var i=t.call(this)||this;return i.future=[],i.maxAttempts=e,i.queue=n,i.seen=null!=r?r:{},i}return(0,r.ZT)(e,t),e.prototype.push=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=e.map(function(e){return!(t.updateAttempts(e)>t.maxAttempts||t.includes(e))&&(t.queue.push(e),!0)});return this.queue=this.queue.sort(function(e,n){return t.getAttempts(e)-t.getAttempts(n)}),r},e.prototype.pushWithBackoff=function(t){var e,n,r,i,s=this;if(0===this.getAttempts(t))return this.push(t)[0];var a=this.updateAttempts(t);return!(a>this.maxAttempts||this.includes(t))&&(setTimeout(function(){s.queue.push(t),s.future=s.future.filter(function(e){return e.id!==t.id}),s.emit(o)},(n=(e={attempt:a-1}).minTimeout,Math.min((Math.random()+1)*(void 0===n?500:n)*Math.pow(void 0===(r=e.factor)?2:r,e.attempt),void 0===(i=e.maxTimeout)?1/0:i))),this.future.push(t),!0)},e.prototype.getAttempts=function(t){var e;return null!==(e=this.seen[t.id])&&void 0!==e?e:0},e.prototype.updateAttempts=function(t){return this.seen[t.id]=this.getAttempts(t)+1,this.getAttempts(t)},e.prototype.includes=function(t){return this.queue.includes(t)||this.future.includes(t)||!!this.queue.find(function(e){return e.id===t.id})||!!this.future.find(function(e){return e.id===t.id})},e.prototype.pop=function(){return this.queue.shift()},Object.defineProperty(e.prototype,"length",{get:function(){return this.queue.length},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"todo",{get:function(){return this.queue.length+this.future.length},enumerable:!1,configurable:!0}),e}(i.Q)},12265:function(t,e,n){"use strict";n.d(e,{a:function(){return o},z:function(){return s}});var r=n(83780),i=n(55114);function o(t,e){t.log("debug","plugin",{plugin:e.name});var n=new Date().getTime(),o=e[t.event.type];return void 0===o?Promise.resolve(t):(function(t){return(0,r.mG)(this,void 0,void 0,function(){return(0,r.Jh)(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,t()];case 1:return[2,e.sent()];case 2:return[2,Promise.reject(e.sent())];case 3:return[2]}})})})(function(){return o.apply(e,[t])}).then(function(t){var r=new Date().getTime()-n;return t.stats.gauge("plugin_time",r,["plugin:".concat(e.name)]),t}).catch(function(n){if(n instanceof i.Y&&"middleware_cancellation"===n.type)throw n;return n instanceof i.Y?t.log("warn",n.type,{plugin:e.name,error:n}):(t.log("error","plugin Error",{plugin:e.name,error:n}),t.stats.increment("plugin_error",1,["plugin:".concat(e.name)])),n})}function s(t,e){return o(t,e).then(function(e){if(e instanceof i._)return e;t.log("debug","Context canceled"),t.stats.increment("context_canceled"),t.cancel(e)})}},75174:function(t,e,n){"use strict";n.d(e,{i:function(){return o},s:function(){return i}});var r=n(83780),i=function(){function t(){this.metrics=[]}return t.prototype.increment=function(t,e,n){void 0===e&&(e=1),this.metrics.push({metric:t,value:e,tags:null!=n?n:[],type:"counter",timestamp:Date.now()})},t.prototype.gauge=function(t,e,n){this.metrics.push({metric:t,value:e,tags:null!=n?n:[],type:"gauge",timestamp:Date.now()})},t.prototype.flush=function(){var t=this.metrics.map(function(t){return(0,r.pi)((0,r.pi)({},t),{tags:t.tags.join(",")})});console.table?console.table(t):console.log(t),this.metrics=[]},t.prototype.serialize=function(){return this.metrics.map(function(t){return{m:t.metric,v:t.value,t:t.tags,k:({gauge:"g",counter:"c"})[t.type],e:t.timestamp}})},t}(),o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,r.ZT)(e,t),e.prototype.gauge=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},e.prototype.increment=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},e.prototype.flush=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},e.prototype.serialize=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return[]},e}(i)},36971:function(t,e,n){"use strict";function r(t){return"string"==typeof t}function i(t){return"number"==typeof t}function o(t){return"function"==typeof t}function s(t){return null!=t}function a(t){return"object"===Object.prototype.toString.call(t).slice(8,-1).toLowerCase()}n.d(e,{Gg:function(){return s},HD:function(){return r},PO:function(){return a},hj:function(){return i},mf:function(){return o}})},16638:function(t,e,n){"use strict";n.d(e,{d:function(){return r}});var r=function(){var t,e,n=!1,r=new Promise(function(r,i){t=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];n=!0,r.apply(void 0,t)},e=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];n=!0,i.apply(void 0,t)}});return{resolve:t,reject:e,promise:r,isSettled:function(){return n}}}},97480:function(t,e,n){"use strict";n.d(e,{Q:function(){return r}});var r=function(){function t(t){var e;this.callbacks={},this.warned=!1,this.maxListeners=null!==(e=null==t?void 0:t.maxListeners)&&void 0!==e?e:10}return t.prototype.warnIfPossibleMemoryLeak=function(t){!this.warned&&this.maxListeners&&this.callbacks[t].length>this.maxListeners&&(console.warn("Event Emitter: Possible memory leak detected; ".concat(String(t)," has exceeded ").concat(this.maxListeners," listeners.")),this.warned=!0)},t.prototype.on=function(t,e){return this.callbacks[t]?(this.callbacks[t].push(e),this.warnIfPossibleMemoryLeak(t)):this.callbacks[t]=[e],this},t.prototype.once=function(t,e){var n=this,r=function(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];n.off(t,r),e.apply(n,i)};return this.on(t,r),this},t.prototype.off=function(t,e){var n,r=(null!==(n=this.callbacks[t])&&void 0!==n?n:[]).filter(function(t){return t!==e});return this.callbacks[t]=r,this},t.prototype.emit=function(t){for(var e,n=this,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return(null!==(e=this.callbacks[t])&&void 0!==e?e:[]).forEach(function(t){t.apply(n,r)}),this},t}()},91099:function(t,e,n){"use strict";n.d(e,{b:function(){return tR}});var r,i,o=n(83780),s=n(68571);function a(){return void 0!==s&&s.env?s.env:{}}var u=n(73515),c=n(74008),l=n(36971);function p(t,e,n,r){var i,o=[t,e,n,r],s=(0,l.PO)(t)?t.event:t;if(!s||!(0,l.HD)(s))throw Error("Event missing");var a=(0,l.PO)(t)?null!==(i=t.properties)&&void 0!==i?i:{}:(0,l.PO)(e)?e:{},u={};return(0,l.mf)(n)||(u=null!=n?n:{}),(0,l.PO)(t)&&!(0,l.mf)(e)&&(u=null!=e?e:{}),[s,a,u,o.find(l.mf)]}function f(t,e,n,r,i){var o,s,a=null,u=null,c=[t,e,n,r,i],p=c.filter(l.HD);void 0!==p[0]&&void 0!==p[1]&&(a=p[0],u=p[1]),1===p.length&&(a=null,u=p[0]);var f=c.find(l.mf),d=c.filter(function(t){return null===u?(0,l.PO)(t):(0,l.PO)(t)||null===t}),h=null!==(o=d[0])&&void 0!==o?o:{},v=null!==(s=d[1])&&void 0!==s?s:{};return[a,u,h,v,f]}var d=function(t){return function(){for(var e,n,r,i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];for(var s={},a=["callback","options","traits","id"],u=0;u<i.length;u++){var c=i[u],p=a.pop();if("id"===p){if((0,l.HD)(c)||(0,l.hj)(c)){s.id=c.toString();continue}if(null==c)continue;p=a.pop()}if(("traits"===p||"options"===p)&&(null==c||(0,l.PO)(c))&&(s[p]=c),(0,l.mf)(c)){s.callback=c;break}}return[null!==(e=s.id)&&void 0!==e?e:t.id(),null!==(n=s.traits)&&void 0!==n?n:{},null!==(r=s.options)&&void 0!==r?r:{},s.callback]}};function h(t,e,n,r){(0,l.hj)(t)&&(t=t.toString()),(0,l.hj)(e)&&(e=e.toString());var i=[t,e,n,r],o=i.filter(l.HD),s=o[0],a=void 0===s?t:s,u=o[1],c=i.filter(l.PO)[0];return[a,void 0===u?null:u,void 0===c?{}:c,i.find(l.mf)]}var v=n(86417),y=n(15116),g=n(52203),m=n(97480),b=n(15156),w=function(t,e,n,r,i,o){return{__t:"bpc",c:e,p:r,u:t,s:n,t:i,r:o}},_=Object.keys(w("","","","","","")),x=function(t){var e=t.indexOf("#");return -1===e?t:t.slice(0,e)},S=function(t){try{return new URL(t).pathname}catch(e){return"/"===t[0]?t:"/"+t}},I=function(t){var e=t.c,n=t.p,r=t.s,i=t.u,o=t.r,s=t.t,a=e?S(e):n,u=e?e.indexOf("?")>-1?e:e+r:x(i);return{path:a,referrer:o,search:r,title:s,url:u}},j=function(){var t=document.querySelector("link[rel='canonical']");return w(location.href,t&&t.getAttribute("href")||void 0,location.search,location.pathname,document.title,document.referrer)},O=function(t,e){void 0===e&&(e=I(j()));var n,r,i,s=t.context;"page"===t.type&&(i=t.properties&&(n=t.properties,r=Object.keys(e),Object.assign.apply(Object,(0,o.ev)([{}],r.map(function(t){var e;if(n&&Object.prototype.hasOwnProperty.call(n,t))return(e={})[t]=n[t],e}),!1))),t.properties=(0,o.pi)((0,o.pi)((0,o.pi)({},e),t.properties),t.name?{name:t.name}:{})),s.page=(0,o.pi)((0,o.pi)((0,o.pi)({},e),i),s.page)},k=n(59681),P=function(t){function e(e,n){var r=t.call(this,"".concat(e," ").concat(n))||this;return r.field=e,r}return(0,o.ZT)(e,t),e}(Error),M="is not a string",E="is not an object",A=function(t){var e,n;this.settings=t,this.createMessageId=t.createMessageId,this.onEventMethodCall=null!==(e=t.onEventMethodCall)&&void 0!==e?e:function(){},this.onFinishedEvent=null!==(n=t.onFinishedEvent)&&void 0!==n?n:function(){}},D=function(t){function e(e){var n=t.call(this,{createMessageId:function(){return"ajs-next-".concat(Date.now(),"-").concat((0,b.v4)())},onEventMethodCall:function(t){var e=t.options;n.maybeUpdateAnonId(e)},onFinishedEvent:function(t){return n.addIdentity(t),t}})||this;return n.user=e,n}return(0,o.ZT)(e,t),e.prototype.maybeUpdateAnonId=function(t){(null==t?void 0:t.anonymousId)&&this.user.anonymousId(t.anonymousId)},e.prototype.addIdentity=function(t){this.user.id()&&(t.userId=this.user.id()),this.user.anonymousId()&&(t.anonymousId=this.user.anonymousId())},e.prototype.track=function(e,n,r,i,o){var s=t.prototype.track.call(this,e,n,r,i);return O(s,o),s},e.prototype.page=function(e,n,r,i,o,s){var a=t.prototype.page.call(this,e,n,r,i,o);return O(a,s),a},e.prototype.screen=function(e,n,r,i,o,s){var a=t.prototype.screen.call(this,e,n,r,i,o);return O(a,s),a},e.prototype.identify=function(e,n,r,i,o){var s=t.prototype.identify.call(this,e,n,r,i);return O(s,o),s},e.prototype.group=function(e,n,r,i,o){var s=t.prototype.group.call(this,e,n,r,i);return O(s,o),s},e.prototype.alias=function(e,n,r,i,o){var s=t.prototype.alias.call(this,e,n,r,i);return O(s,o),s},e}(function(){function t(t){this.settings=new A(t)}return t.prototype.track=function(t,e,n,r){return this.settings.onEventMethodCall({type:"track",options:n}),this.normalize((0,o.pi)((0,o.pi)({},this.baseEvent()),{event:t,type:"track",properties:null!=e?e:{},options:(0,o.pi)({},n),integrations:(0,o.pi)({},r)}))},t.prototype.page=function(t,e,n,r,i){this.settings.onEventMethodCall({type:"page",options:r});var s,a={type:"page",properties:(0,o.pi)({},n),options:(0,o.pi)({},r),integrations:(0,o.pi)({},i)};return null!==t&&(a.category=t,a.properties=null!==(s=a.properties)&&void 0!==s?s:{},a.properties.category=t),null!==e&&(a.name=e),this.normalize((0,o.pi)((0,o.pi)({},this.baseEvent()),a))},t.prototype.screen=function(t,e,n,r,i){this.settings.onEventMethodCall({type:"screen",options:r});var s={type:"screen",properties:(0,o.pi)({},n),options:(0,o.pi)({},r),integrations:(0,o.pi)({},i)};return null!==t&&(s.category=t),null!==e&&(s.name=e),this.normalize((0,o.pi)((0,o.pi)({},this.baseEvent()),s))},t.prototype.identify=function(t,e,n,r){return this.settings.onEventMethodCall({type:"identify",options:n}),this.normalize((0,o.pi)((0,o.pi)({},this.baseEvent()),{type:"identify",userId:t,traits:null!=e?e:{},options:(0,o.pi)({},n),integrations:r}))},t.prototype.group=function(t,e,n,r){return this.settings.onEventMethodCall({type:"group",options:n}),this.normalize((0,o.pi)((0,o.pi)({},this.baseEvent()),{type:"group",traits:null!=e?e:{},options:(0,o.pi)({},n),integrations:(0,o.pi)({},r),groupId:t}))},t.prototype.alias=function(t,e,n,r){this.settings.onEventMethodCall({type:"alias",options:n});var i={userId:t,type:"alias",options:(0,o.pi)({},n),integrations:(0,o.pi)({},r)};return(null!==e&&(i.previousId=e),void 0===t)?this.normalize((0,o.pi)((0,o.pi)({},i),this.baseEvent())):this.normalize((0,o.pi)((0,o.pi)({},this.baseEvent()),i))},t.prototype.baseEvent=function(){return{integrations:{},options:{}}},t.prototype.context=function(t){var e,n=["userId","anonymousId","timestamp","messageId"];delete t.integrations;var r=Object.keys(t),i=null!==(e=t.context)&&void 0!==e?e:{},o={};return r.forEach(function(e){"context"!==e&&(n.includes(e)?(0,k.N)(o,e,t[e]):(0,k.N)(i,e,t[e]))}),[i,o]},t.prototype.normalize=function(t){var e,n,r,i,s=Object.keys(null!==(r=t.integrations)&&void 0!==r?r:{}).reduce(function(e,n){var r,i;return(0,o.pi)((0,o.pi)({},e),((r={})[n]=!!(null===(i=t.integrations)||void 0===i?void 0:i[n]),r))},{});t.options=(e=t.options||{},n=function(t,e){return void 0!==e},Object.keys(e).filter(function(t){return n(t,e[t])}).reduce(function(t,n){return t[n]=e[n],t},{}));var a=(0,o.pi)((0,o.pi)({},s),null===(i=t.options)||void 0===i?void 0:i.integrations),u=t.options?this.context(t.options):[],c=u[0],p=u[1],f=t.options,d=(0,o._T)(t,["options"]),h=(0,o.pi)((0,o.pi)((0,o.pi)((0,o.pi)({timestamp:new Date},d),{context:c,integrations:a}),p),{messageId:f.messageId||this.settings.createMessageId()});return this.settings.onFinishedEvent(h),function(t){if(!(0,l.Gg)(t))throw new P("Event","is nil");if("object"!=typeof t)throw new P("Event",E)}(h),function(t){if(!(0,l.HD)(t.type))throw new P(".type",M)}(h),function(t){if(!(0,l.HD)(t.messageId))throw new P(".messageId",M)}(h),"track"===h.type&&(function(t){if(!(0,l.HD)(t.event))throw new P(".event",M)}(h),function(t){if(!(0,l.PO)(t.properties))throw new P(".properties",E)}(h)),["group","identify"].includes(h.type)&&function(t){if(!(0,l.PO)(t.traits))throw new P(".traits",E)}(h),h},t}()),C=function(t){return"addMiddleware"in t&&"destination"===t.type},F=n(10700),N=n(32593),J=n(55114),T=function(){var t,e,n=0;return{done:function(){return t},run:function(r){var i=r();return"object"==typeof i&&null!==i&&"then"in i&&"function"==typeof i.then&&(1==++n&&(t=new Promise(function(t){return e=t})),i.finally(function(){return 0==--n&&e()})),i}}},G=n(12265),q=function(t){function e(e){return t.call(this,"string"==typeof e?new F.$(4,e):e)||this}return(0,o.ZT)(e,t),e.prototype.flush=function(){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(e){return(0,v.s)()?[2,[]]:[2,t.prototype.flush.call(this)]})})},e}(function(t){function e(e){var n=t.call(this)||this;return n.criticalTasks=T(),n.plugins=[],n.failedInitializations=[],n.flushing=!1,n.queue=e,n.queue.on(N.M,function(){n.scheduleFlush(0)}),n}return(0,o.ZT)(e,t),e.prototype.register=function(t,e,n){return(0,o.mG)(this,void 0,void 0,function(){var r,i,s=this;return(0,o.Jh)(this,function(o){switch(o.label){case 0:if(this.plugins.push(e),r=function(n){s.failedInitializations.push(e.name),s.emit("initialization_failure",e),console.warn(e.name,n),t.log("warn","Failed to load destination",{plugin:e.name,error:n}),s.plugins=s.plugins.filter(function(t){return t!==e})},!("destination"===e.type&&"Segment.io"!==e.name))return[3,1];return e.load(t,n).catch(r),[3,4];case 1:return o.trys.push([1,3,,4]),[4,e.load(t,n)];case 2:return o.sent(),[3,4];case 3:return i=o.sent(),r(i),[3,4];case 4:return[2]}})})},e.prototype.deregister=function(t,e,n){return(0,o.mG)(this,void 0,void 0,function(){var r;return(0,o.Jh)(this,function(i){switch(i.label){case 0:if(i.trys.push([0,3,,4]),!e.unload)return[3,2];return[4,Promise.resolve(e.unload(t,n))];case 1:i.sent(),i.label=2;case 2:return this.plugins=this.plugins.filter(function(t){return t.name!==e.name}),[3,4];case 3:return r=i.sent(),t.log("warn","Failed to unload destination",{plugin:e.name,error:r}),[3,4];case 4:return[2]}})})},e.prototype.dispatch=function(t){return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(n){return t.log("debug","Dispatching"),t.stats.increment("message_dispatched"),this.queue.push(t),e=this.subscribeToDelivery(t),this.scheduleFlush(0),[2,e]})})},e.prototype.subscribeToDelivery=function(t){return(0,o.mG)(this,void 0,void 0,function(){var e=this;return(0,o.Jh)(this,function(n){return[2,new Promise(function(n){var r=function(i,o){i.isSame(t)&&(e.off("flush",r),n(i))};e.on("flush",r)})]})})},e.prototype.dispatchSingle=function(t){return(0,o.mG)(this,void 0,void 0,function(){var e=this;return(0,o.Jh)(this,function(n){return t.log("debug","Dispatching"),t.stats.increment("message_dispatched"),this.queue.updateAttempts(t),t.attempts=1,[2,this.deliver(t).catch(function(n){return e.enqueuRetry(n,t)?e.subscribeToDelivery(t):(t.setFailedDelivery({reason:n}),t)})]})})},e.prototype.isEmpty=function(){return 0===this.queue.length},e.prototype.scheduleFlush=function(t){var e=this;void 0===t&&(t=500),this.flushing||(this.flushing=!0,setTimeout(function(){e.flush().then(function(){setTimeout(function(){e.flushing=!1,e.queue.length&&e.scheduleFlush(0)},0)})},t))},e.prototype.deliver=function(t){return(0,o.mG)(this,void 0,void 0,function(){var e,n,r,i;return(0,o.Jh)(this,function(o){switch(o.label){case 0:return[4,this.criticalTasks.done()];case 1:o.sent(),e=Date.now(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,this.flushOne(t)];case 3:return t=o.sent(),n=Date.now()-e,this.emit("delivery_success",t),t.stats.gauge("delivered",n),t.log("debug","Delivered",t.event),[2,t];case 4:throw i=r=o.sent(),t.log("error","Failed to deliver",i),this.emit("delivery_failure",t,i),t.stats.increment("delivery_failed"),r;case 5:return[2]}})})},e.prototype.enqueuRetry=function(t,e){return(!(t instanceof J.Y)||!!t.retry)&&this.queue.pushWithBackoff(e)},e.prototype.flush=function(){return(0,o.mG)(this,void 0,void 0,function(){var t,e;return(0,o.Jh)(this,function(n){switch(n.label){case 0:if(0===this.queue.length||!(t=this.queue.pop()))return[2,[]];t.attempts=this.queue.getAttempts(t),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.deliver(t)];case 2:return t=n.sent(),this.emit("flush",t,!0),[3,4];case 3:return e=n.sent(),this.enqueuRetry(e,t)||(t.setFailedDelivery({reason:e}),this.emit("flush",t,!1)),[2,[]];case 4:return[2,[t]]}})})},e.prototype.isReady=function(){return!0},e.prototype.availableExtensions=function(t){var e,n,r=(e="type",n={},this.plugins.filter(function(e){if("destination"!==e.type&&"Segment.io"!==e.name)return!0;var n,r,i,o=void 0;return null===(n=e.alternativeNames)||void 0===n||n.forEach(function(e){void 0!==t[e]&&(o=t[e])}),null!==(i=null!==(r=t[e.name])&&void 0!==r?r:o)&&void 0!==i?i:!1!==("Segment.io"===e.name||t.All)}).forEach(function(t){var r,i=void 0;if("string"==typeof e){var s=t[e];i="string"!=typeof s?JSON.stringify(s):s}else e instanceof Function&&(i=e(t));void 0!==i&&(n[i]=(0,o.ev)((0,o.ev)([],null!==(r=n[i])&&void 0!==r?r:[],!0),[t],!1))}),n),i=r.before,s=r.enrichment,a=r.destination,u=r.after;return{before:void 0===i?[]:i,enrichment:void 0===s?[]:s,destinations:void 0===a?[]:a,after:void 0===u?[]:u}},e.prototype.flushOne=function(t){var e,n;return(0,o.mG)(this,void 0,void 0,function(){var r,i,s,a,u,c,l,p,f,d,h,v,y;return(0,o.Jh)(this,function(o){switch(o.label){case 0:if(!this.isReady())throw Error("Not ready");t.attempts>1&&this.emit("delivery_retry",t),i=(r=this.availableExtensions(null!==(e=t.event.integrations)&&void 0!==e?e:{})).before,s=r.enrichment,a=0,u=i,o.label=1;case 1:if(!(a<u.length))return[3,4];return c=u[a],[4,(0,G.z)(t,c)];case 2:(l=o.sent())instanceof J._&&(t=l),this.emit("message_enriched",t,c),o.label=3;case 3:return a++,[3,1];case 4:p=0,f=s,o.label=5;case 5:if(!(p<f.length))return[3,8];return d=f[p],[4,(0,G.a)(t,d)];case 6:(l=o.sent())instanceof J._&&(t=l),this.emit("message_enriched",t,d),o.label=7;case 7:return p++,[3,5];case 8:return v=(h=this.availableExtensions(null!==(n=t.event.integrations)&&void 0!==n?n:{})).destinations,y=h.after,[4,new Promise(function(e,n){setTimeout(function(){Promise.all(v.map(function(e){return(0,G.a)(t,e)})).then(e).catch(n)},0)})];case 9:return o.sent(),t.stats.increment("message_delivered"),this.emit("message_delivered",t),[4,Promise.all(y.map(function(e){return(0,G.a)(t,e)}))];case 10:return o.sent(),[2,t]}})})},e}(m.Q));function L(t){for(var e=t.constructor.prototype,n=0,r=Object.getOwnPropertyNames(e);n<r.length;n++){var i=r[n];if("constructor"!==i){var o=Object.getOwnPropertyDescriptor(t.constructor.prototype,i);o&&"function"==typeof o.value&&(t[i]=t[i].bind(t))}}return t}var z={Cookie:"cookie",LocalStorage:"localStorage",Memory:"memory"},R=function(t,e,n,r){console.warn("".concat(t.constructor.name,": Can't ").concat(e,' key "').concat(n,'" | Err: ').concat(r))},U=function(){function t(t){this.stores=t}return t.prototype.get=function(t){for(var e=null,n=0,r=this.stores;n<r.length;n++){var i=r[n];try{if(null!=(e=i.get(t)))return e}catch(e){R(i,"get",t,e)}}return null},t.prototype.set=function(t,e){this.stores.forEach(function(n){try{n.set(t,e)}catch(e){R(n,"set",t,e)}})},t.prototype.clear=function(t){this.stores.forEach(function(e){try{e.remove(t)}catch(n){R(e,"remove",t,n)}})},t.prototype.getAndSync=function(t){var e=this.get(t),n="number"==typeof e?e.toString():e;return this.set(t,n),n},t}(),K=function(){function t(){this.cache={}}return t.prototype.get=function(t){var e;return null!==(e=this.cache[t])&&void 0!==e?e:null},t.prototype.set=function(t,e){this.cache[t]=e},t.prototype.remove=function(t){delete this.cache[t]},t}();function B(t){return t&&t.stores&&Array.isArray(t.stores)&&t.stores.every(function(t){return Object.values(z).includes(t)})}function Z(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)t[r]=n[r]}return t}var V=function t(e,n){function r(t,r,i){if("undefined"!=typeof document){"number"==typeof(i=Z({},n,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var s in i)i[s]&&(o+="; "+s,!0!==i[s]&&(o+="="+i[s].split(";")[0]));return document.cookie=t+"="+e.write(r,t)+o}}return Object.create({set:r,get:function(t){if("undefined"!=typeof document&&(!arguments.length||t)){for(var n=document.cookie?document.cookie.split("; "):[],r={},i=0;i<n.length;i++){var o=n[i].split("="),s=o.slice(1).join("=");try{var a=decodeURIComponent(o[0]);if(r[a]=e.read(s,a),t===a)break}catch(t){}}return t?r[t]:r}},remove:function(t,e){r(t,"",Z({},e,{expires:-1}))},withAttributes:function(e){return t(this.converter,Z({},this.attributes,e))},withConverter:function(e){return t(Z({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(e)}})}({read:function(t){return'"'===t[0]&&(t=t.slice(1,-1)),t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(t){return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"});function H(t){var e=function(t){try{return new URL(t)}catch(t){return}}(t);if(e)for(var n=function(t){var e=t.hostname.split("."),n=e[e.length-1],r=[];if(4===e.length&&parseInt(n,10)>0||e.length<=1)return r;for(var i=e.length-2;i>=0;--i)r.push(e.slice(i).join("."));return r}(e),r=0;r<n.length;++r){var i="__tld__",o=n[r],s={domain:"."+o};try{if(V.set(i,"1",s),V.get(i))return V.remove(i,s),o}catch(t){return}}}var W=function(){function t(e){void 0===e&&(e=t.defaults),this.options=(0,o.pi)((0,o.pi)({},t.defaults),e)}return Object.defineProperty(t,"defaults",{get:function(){return{maxage:365,domain:H(window.location.href),path:"/",sameSite:"Lax"}},enumerable:!1,configurable:!0}),t.prototype.opts=function(){return{sameSite:this.options.sameSite,expires:this.options.maxage,domain:this.options.domain,path:this.options.path,secure:this.options.secure}},t.prototype.get=function(t){var e;try{var n=V.get(t);if(null==n)return null;try{return null!==(e=JSON.parse(n))&&void 0!==e?e:null}catch(t){return null!=n?n:null}}catch(t){return null}},t.prototype.set=function(t,e){"string"==typeof e?V.set(t,e,this.opts()):null===e?V.remove(t,this.opts()):V.set(t,JSON.stringify(e),this.opts())},t.prototype.remove=function(t){return V.remove(t,this.opts())},t}(),Q=function(){function t(){}return t.prototype.localStorageWarning=function(t,e){console.warn("Unable to access ".concat(t,", localStorage may be ").concat(e))},t.prototype.get=function(t){var e;try{var n=localStorage.getItem(t);if(null===n)return null;try{return null!==(e=JSON.parse(n))&&void 0!==e?e:null}catch(t){return null!=n?n:null}}catch(e){return this.localStorageWarning(t,"unavailable"),null}},t.prototype.set=function(t,e){try{localStorage.setItem(t,JSON.stringify(e))}catch(e){this.localStorageWarning(t,"full")}},t.prototype.remove=function(t){try{return localStorage.removeItem(t)}catch(e){this.localStorageWarning(t,"unavailable")}},t}();function $(t){return t.map(function(t){var e,n;switch("object"==typeof t&&void 0!==t.name?(e=t.name,n=t.settings):e=t,e){case z.Cookie:return new W(n);case z.LocalStorage:return new Q;case z.Memory:return new K;default:throw Error("Unknown Store Type: ".concat(t))}})}function Y(t,e){return t.map(function(t){return e&&t===z.Cookie?{name:t,settings:e}:t})}var X={persist:!0,cookie:{key:"ajs_user_id",oldKey:"ajs_user"},localStorage:{key:"ajs_user_traits"}},tt=function(){function t(t,e){void 0===t&&(t=X);var n,r,i,s,a=this;this.options={},this.id=function(t){if(a.options.disable)return null;var e=a.identityStore.getAndSync(a.idKey);void 0!==t&&(a.identityStore.set(a.idKey,t),t!==e&&null!==e&&null!==t&&a.anonymousId(null));var n=a.identityStore.getAndSync(a.idKey);if(n)return n;var r=a.legacyUserStore.get(X.cookie.oldKey);return r?"object"==typeof r?r.id:r:null},this.anonymousId=function(t){var e,n;if(a.options.disable)return null;if(void 0===t){var r=null!==(e=a.identityStore.getAndSync(a.anonKey))&&void 0!==e?e:null===(n=a.legacySIO())||void 0===n?void 0:n[0];if(r)return r}return null===t?a.identityStore.set(a.anonKey,null):a.identityStore.set(a.anonKey,null!=t?t:(0,b.v4)()),a.identityStore.getAndSync(a.anonKey)},this.traits=function(t){var e;if(!a.options.disable)return null===t&&(t={}),t&&a.traitsStore.set(a.traitsKey,null!=t?t:{}),null!==(e=a.traitsStore.get(a.traitsKey))&&void 0!==e?e:{}},this.options=(0,o.pi)((0,o.pi)({},X),t),this.cookieOptions=e,this.idKey=null!==(r=null===(n=t.cookie)||void 0===n?void 0:n.key)&&void 0!==r?r:X.cookie.key,this.traitsKey=null!==(s=null===(i=t.localStorage)||void 0===i?void 0:i.key)&&void 0!==s?s:X.localStorage.key,this.anonKey="ajs_anonymous_id",this.identityStore=this.createStorage(this.options,e),this.legacyUserStore=this.createStorage(this.options,e,function(t){return t===z.Cookie}),this.traitsStore=this.createStorage(this.options,e,function(t){return t!==z.Cookie});var u=this.legacyUserStore.get(X.cookie.oldKey);u&&"object"==typeof u&&(u.id&&this.id(u.id),u.traits&&this.traits(u.traits)),L(this)}return t.prototype.legacySIO=function(){var t=this.legacyUserStore.get("_sio");if(!t)return null;var e=t.split("----");return[e[0],e[1]]},t.prototype.identify=function(t,e){if(!this.options.disable){e=null!=e?e:{};var n=this.id();(null===n||n===t)&&(e=(0,o.pi)((0,o.pi)({},this.traits()),e)),t&&this.id(t),this.traits(e)}},t.prototype.logout=function(){this.anonymousId(null),this.id(null),this.traits({})},t.prototype.reset=function(){this.logout(),this.identityStore.clear(this.idKey),this.identityStore.clear(this.anonKey),this.traitsStore.clear(this.traitsKey)},t.prototype.load=function(){return new t(this.options,this.cookieOptions)},t.prototype.save=function(){return!0},t.prototype.createStorage=function(t,e,n){var r=[z.LocalStorage,z.Cookie,z.Memory];return t.disable?new U([]):t.persist?(void 0!==t.storage&&null!==t.storage&&B(t.storage)&&(r=t.storage.stores),t.localStorageFallbackDisabled&&(r=r.filter(function(t){return t!==z.LocalStorage})),n&&(r=r.filter(n)),new U($(Y(r,e)))):new U([new K])},t.defaults=X,t}(),te={persist:!0,cookie:{key:"ajs_group_id"},localStorage:{key:"ajs_group_properties"}},tn=function(t){function e(e,n){void 0===e&&(e=te);var r=t.call(this,(0,o.pi)((0,o.pi)({},te),e),n)||this;return r.anonymousId=function(t){},L(r),r}return(0,o.ZT)(e,t),e}(tt),tr=n(65712),ti=n(94701),to=n(77013),ts=function(t,e,n){n.getCalls(t).forEach(function(t){th(e,t).catch(console.error)})},ta=ts.bind(void 0,"on"),tu=ts.bind(void 0,"setAnonymousId"),tc=function(t,e){e.toArray().forEach(function(e){setTimeout(function(){th(t,e).catch(console.error)},0)})},tl=function(t){if(tp(t))return I(t.pop())},tp=function(t){return function(t){if(!(0,l.PO)(t)||"bpc"!==t.__t)return!1;for(var e in t)if(!_.includes(e))return!1;return!0}(t[t.length-1])},tf=function(t,e,n,r){void 0===n&&(n=function(){}),void 0===r&&(r=console.error),this.method=t,this.resolve=n,this.reject=r,this.called=!1,this.args=e},td=function(){function t(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._callMap={},this.push.apply(this,t)}return Object.defineProperty(t.prototype,"calls",{get:function(){return this._pushSnippetWindowBuffer(),this._callMap},set:function(t){this._callMap=t},enumerable:!1,configurable:!0}),t.prototype.getCalls=function(t){var e;return null!==(e=this.calls[t])&&void 0!==e?e:[]},t.prototype.push=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];e.forEach(function(e){["track","screen","alias","group","page","identify"].includes(e.method)&&!tp(e.args)&&(e.args=(0,o.ev)((0,o.ev)([],e.args,!0),[j()],!1)),t.calls[e.method]?t.calls[e.method].push(e):t.calls[e.method]=[e]})},t.prototype.clear=function(){this._pushSnippetWindowBuffer(),this.calls={}},t.prototype.toArray=function(){var t;return(t=[]).concat.apply(t,Object.values(this.calls))},t.prototype._pushSnippetWindowBuffer=function(){var t=(0,to.wI)();if(Array.isArray(t)){var e=t.splice(0,t.length).map(function(t){return new tf(t[0],t.slice(1))});this.push.apply(this,e)}},t}();function th(t,e){return(0,o.mG)(this,void 0,void 0,function(){var n,r;return(0,o.Jh)(this,function(i){switch(i.label){case 0:var o;if(i.trys.push([0,3,,4]),e.called)return[2,void 0];if(e.called=!0,!("object"==typeof(o=n=t[e.method].apply(t,e.args))&&null!==o&&"then"in o&&"function"==typeof o.then))return[3,2];return[4,n];case 1:i.sent(),i.label=2;case 2:return e.resolve(n),[3,4];case 3:return r=i.sent(),e.reject(r),[3,4];case 4:return[2]}})})}var tv=function(){function t(t){var e=this;this.trackSubmit=this._createMethod("trackSubmit"),this.trackClick=this._createMethod("trackClick"),this.trackLink=this._createMethod("trackLink"),this.pageView=this._createMethod("pageview"),this.identify=this._createMethod("identify"),this.reset=this._createMethod("reset"),this.group=this._createMethod("group"),this.track=this._createMethod("track"),this.ready=this._createMethod("ready"),this.alias=this._createMethod("alias"),this.debug=this._createChainableMethod("debug"),this.page=this._createMethod("page"),this.once=this._createChainableMethod("once"),this.off=this._createChainableMethod("off"),this.on=this._createChainableMethod("on"),this.addSourceMiddleware=this._createMethod("addSourceMiddleware"),this.setAnonymousId=this._createMethod("setAnonymousId"),this.addDestinationMiddleware=this._createMethod("addDestinationMiddleware"),this.screen=this._createMethod("screen"),this.register=this._createMethod("register"),this.deregister=this._createMethod("deregister"),this.user=this._createMethod("user"),this.VERSION=tr.i,this._preInitBuffer=new td,this._promise=t(this._preInitBuffer),this._promise.then(function(t){var n=t[0],r=t[1];e.instance=n,e.ctx=r}).catch(function(){})}return t.prototype.then=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this._promise).then.apply(t,e)},t.prototype.catch=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this._promise).catch.apply(t,e)},t.prototype.finally=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(t=this._promise).finally.apply(t,e)},t.prototype._createMethod=function(t){var e=this;return function(){for(var n,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return e.instance?Promise.resolve((n=e.instance)[t].apply(n,r)):new Promise(function(n,i){e._preInitBuffer.push(new tf(t,r,n,i))})}},t.prototype._createChainableMethod=function(t){var e=this;return function(){for(var n,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];return e.instance?(n=e.instance)[t].apply(n,r):e._preInitBuffer.push(new tf(t,r)),e}},t}(),ty="This is being deprecated and will be not be available in future releases of Analytics JS",tg=(0,ti.R)(),tm=null==tg?void 0:tg.analytics;function tb(){console.warn(ty)}var tw=function(t){function e(e,n,r,i,s){var a,u,c,l,p,f,d,h=this;(h=t.call(this)||this)._debug=!1,h.initialized=!1,h.user=function(){return h._user},h.init=h.initialize.bind(h),h.log=tb,h.addIntegrationMiddleware=tb,h.listeners=tb,h.addEventListener=tb,h.removeAllListeners=tb,h.removeListener=tb,h.removeEventListener=tb,h.hasListeners=tb,h.add=tb,h.addIntegration=tb;var v=null==n?void 0:n.cookie,y=null!==(p=null==n?void 0:n.disableClientPersistence)&&void 0!==p&&p;h.settings=e,h.settings.timeout=null!==(f=h.settings.timeout)&&void 0!==f?f:300,h.queue=null!=r?r:(a="".concat(e.writeKey,":event-queue"),void 0===(u=null==n?void 0:n.retryQueue)&&(u=!1),void 0===(c=y)&&(c=!1),l=u?10:1,new q(c?new N.Z(l,[]):new F.$(l,a)));var g=null==n?void 0:n.storage;return h._universalStorage=h.createStore(y,g,v),h._user=null!=i?i:new tt((0,o.pi)({persist:!y,storage:null==n?void 0:n.storage},null==n?void 0:n.user),v).load(),h._group=null!=s?s:new tn((0,o.pi)({persist:!y,storage:null==n?void 0:n.storage},null==n?void 0:n.group),v).load(),h.eventFactory=new D(h._user),h.integrations=null!==(d=null==n?void 0:n.integrations)&&void 0!==d?d:{},h.options=null!=n?n:{},L(h),h}return(0,o.ZT)(e,t),e.prototype.createStore=function(t,e,n){return new U(t?[new K]:e&&B(e)?$(Y(e.stores,n)):$([z.LocalStorage,{name:z.Cookie,settings:n},z.Memory]))},Object.defineProperty(e.prototype,"storage",{get:function(){return this._universalStorage},enumerable:!1,configurable:!0}),e.prototype.track=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n,r,i,s,a,u,c=this;return(0,o.Jh)(this,function(o){return e=tl(t),r=(n=p.apply(void 0,t))[0],i=n[1],s=n[2],a=n[3],u=this.eventFactory.track(r,i,s,this.integrations,e),[2,this._dispatch(u,a).then(function(t){return c.emit("track",r,t.event.properties,t.event.options),t})]})})},e.prototype.page=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n,r,i,s,a,u,c,l=this;return(0,o.Jh)(this,function(o){return e=tl(t),r=(n=f.apply(void 0,t))[0],i=n[1],s=n[2],a=n[3],u=n[4],c=this.eventFactory.page(r,i,s,a,this.integrations,e),[2,this._dispatch(c,u).then(function(t){return l.emit("page",r,i,t.event.properties,t.event.options),t})]})})},e.prototype.identify=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n,r,i,s,a,u,c=this;return(0,o.Jh)(this,function(o){return e=tl(t),r=(n=d(this._user).apply(void 0,t))[0],i=n[1],s=n[2],a=n[3],this._user.identify(r,i),u=this.eventFactory.identify(this._user.id(),this._user.traits(),s,this.integrations,e),[2,this._dispatch(u,a).then(function(t){return c.emit("identify",t.event.userId,t.event.traits,t.event.options),t})]})})},e.prototype.group=function(){for(var t=this,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=tl(e);if(0===e.length)return this._group;var i=d(this._group).apply(void 0,e),o=i[0],s=i[1],a=i[2],u=i[3];this._group.identify(o,s);var c=this._group.id(),l=this._group.traits(),p=this.eventFactory.group(c,l,a,this.integrations,r);return this._dispatch(p,u).then(function(e){return t.emit("group",e.event.groupId,e.event.traits,e.event.options),e})},e.prototype.alias=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n,r,i,s,a,u,c=this;return(0,o.Jh)(this,function(o){return e=tl(t),r=(n=h.apply(void 0,t))[0],i=n[1],s=n[2],a=n[3],u=this.eventFactory.alias(r,i,s,this.integrations,e),[2,this._dispatch(u,a).then(function(t){return c.emit("alias",r,i,t.event.options),t})]})})},e.prototype.screen=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n,r,i,s,a,u,c,l=this;return(0,o.Jh)(this,function(o){return e=tl(t),r=(n=f.apply(void 0,t))[0],i=n[1],s=n[2],a=n[3],u=n[4],c=this.eventFactory.screen(r,i,s,a,this.integrations,e),[2,this._dispatch(c,u).then(function(t){return l.emit("screen",r,i,t.event.properties,t.event.options),t})]})})},e.prototype.trackClick=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return[4,n.e(8119).then(n.bind(n,40640))];case 1:return[2,(e=r.sent().link).call.apply(e,(0,o.ev)([this],t,!1))]}})})},e.prototype.trackLink=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return[4,n.e(8119).then(n.bind(n,40640))];case 1:return[2,(e=r.sent().link).call.apply(e,(0,o.ev)([this],t,!1))]}})})},e.prototype.trackSubmit=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return[4,n.e(8119).then(n.bind(n,40640))];case 1:return[2,(e=r.sent().form).call.apply(e,(0,o.ev)([this],t,!1))]}})})},e.prototype.trackForm=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return[4,n.e(8119).then(n.bind(n,40640))];case 1:return[2,(e=r.sent().form).call.apply(e,(0,o.ev)([this],t,!1))]}})})},e.prototype.register=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n=this;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return e=y._.system(),[4,Promise.all(t.map(function(t){return n.queue.register(e,t,n)}))];case 1:return r.sent(),[2,e]}})})},e.prototype.deregister=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return(0,o.mG)(this,void 0,void 0,function(){var e,n=this;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return e=y._.system(),[4,Promise.all(t.map(function(t){var r=n.queue.plugins.find(function(e){return e.name===t});if(r)return n.queue.deregister(e,r,n);e.log("warn","plugin ".concat(t," not found"))}))];case 1:return r.sent(),[2,e]}})})},e.prototype.debug=function(t){return!1===t&&localStorage.getItem("debug")&&localStorage.removeItem("debug"),this._debug=t,this},e.prototype.reset=function(){this._user.reset(),this._group.reset(),this.emit("reset")},e.prototype.timeout=function(t){this.settings.timeout=t},e.prototype._dispatch=function(t,e){return(0,o.mG)(this,void 0,void 0,function(){var n;return(0,o.Jh)(this,function(r){return(n=new y._(t),(0,v.s)()&&!this.options.retryQueue)?[2,n]:[2,function(t,e,n,r){return(0,o.mG)(this,void 0,void 0,function(){var i,s;return(0,o.Jh)(this,function(o){switch(o.label){case 0:if(n.emit("dispatch_start",t),i=Date.now(),!e.isEmpty())return[3,2];return[4,e.dispatchSingle(t)];case 1:return s=o.sent(),[3,4];case 2:return[4,e.dispatch(t)];case 3:s=o.sent(),o.label=4;case 4:var a,u;if(!(null==r?void 0:r.callback))return[3,6];return[4,(0,g.UI)(s,r.callback,(a=i,Math.max((null!=(u=r.timeout)?u:300)-(Date.now()-a),0)))];case 5:s=o.sent(),o.label=6;case 6:return(null==r?void 0:r.debug)&&s.flush(),[2,s]}})})}(n,this.queue,this,{callback:e,debug:this._debug,timeout:this.settings.timeout})]})})},e.prototype.addSourceMiddleware=function(t){return(0,o.mG)(this,void 0,void 0,function(){var e=this;return(0,o.Jh)(this,function(r){switch(r.label){case 0:return[4,this.queue.criticalTasks.run(function(){return(0,o.mG)(e,void 0,void 0,function(){var e,r,i;return(0,o.Jh)(this,function(o){switch(o.label){case 0:return[4,Promise.resolve().then(n.bind(n,99860))];case 1:return e=o.sent().sourceMiddlewarePlugin,r={},this.queue.plugins.forEach(function(t){if("destination"===t.type)return r[t.name]=!0}),i=e(t,r),[4,this.register(i)];case 2:return o.sent(),[2]}})})})];case 1:return r.sent(),[2,this]}})})},e.prototype.addDestinationMiddleware=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return this.queue.plugins.filter(C).forEach(function(n){("*"===t||n.name.toLowerCase()===t.toLowerCase())&&n.addMiddleware.apply(n,e)}),Promise.resolve(this)},e.prototype.setAnonymousId=function(t){return this._user.anonymousId(t)},e.prototype.queryString=function(t){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(e){switch(e.label){case 0:if(!1===this.options.useQueryString)return[2,[]];return[4,n.e(3096).then(n.bind(n,79868))];case 1:return[2,(0,e.sent().queryString)(this,t)]}})})},e.prototype.use=function(t){return t(this),this},e.prototype.ready=function(t){return void 0===t&&(t=function(t){return t}),(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(e){return[2,Promise.all(this.queue.plugins.map(function(t){return t.ready?t.ready():Promise.resolve()})).then(function(e){return t(e),e})]})})},e.prototype.noConflict=function(){return console.warn(ty),(0,to.ql)(null!=tm?tm:this),this},e.prototype.normalize=function(t){return console.warn(ty),this.eventFactory.normalize(t)},Object.defineProperty(e.prototype,"failedInitializations",{get:function(){return console.warn(ty),this.queue.failedInitializations},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"VERSION",{get:function(){return tr.i},enumerable:!1,configurable:!0}),e.prototype.initialize=function(t,e){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(t){return console.warn(ty),[2,Promise.resolve(this)]})})},e.prototype.pageview=function(t){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(e){switch(e.label){case 0:return console.warn(ty),[4,this.page({path:t})];case 1:return e.sent(),[2,this]}})})},Object.defineProperty(e.prototype,"plugins",{get:function(){var t;return console.warn(ty),null!==(t=this._plugins)&&void 0!==t?t:{}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"Integrations",{get:function(){return console.warn(ty),this.queue.plugins.filter(function(t){return"destination"===t.type}).reduce(function(t,e){var n="".concat(e.name.toLowerCase().replace(".","").split(" ").join("-"),"Integration"),r=window[n];if(!r)return t;var i=r.Integration;return i?t[e.name]=i:t[e.name]=r,t},{})},enumerable:!1,configurable:!0}),e.prototype.push=function(t){var e=t.shift();(!e||this[e])&&this[e].apply(this,t)},e}(m.Q),t_=function(t){function e(){var e=t.call(this,{writeKey:""},{disableClientPersistence:!0})||this;return e.initialized=!0,e}return(0,o.ZT)(e,t),e}(tw),tx=n(44997),tS=n(16638),tI=n(83181),tj=n(9676),tO=function(t){try{var e=new URLSearchParams;return Object.entries(t).forEach(function(t){var n=t[0],r=t[1];Array.isArray(r)?r.forEach(function(t){return e.append(n,t)}):e.append(n,r)}),e.toString()}catch(t){return""}},tk=new function(){var t=this;this.name="Page Enrichment",this.type="before",this.version="0.1.0",this.isLoaded=function(){return!0},this.load=function(e,n){return(0,o.mG)(t,void 0,void 0,function(){var t;return(0,o.Jh)(this,function(e){switch(e.label){case 0:this.instance=n,e.label=1;case 1:return e.trys.push([1,3,,4]),t=this,[4,function(t){return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(n){return(e=navigator.userAgentData)?t?[2,e.getHighEntropyValues(t).catch(function(){return e.toJSON()})]:[2,e.toJSON()]:[2,void 0]})})}(this.instance.options.highEntropyValuesClientHints)];case 2:return t.userAgentData=e.sent(),[3,4];case 3:return e.sent(),[3,4];case 4:return[2,Promise.resolve()]}})})},this.enrich=function(e){var n,i,s,a,u,c,l,p=e.event.context,f=p.page.search||"",d="object"==typeof f?tO(f):f;p.userAgent=navigator.userAgent,p.userAgentData=t.userAgentData;var h=navigator.userLanguage||navigator.language;void 0===p.locale&&void 0!==h&&(p.locale=h),null!==(c=p.library)&&void 0!==c||(p.library={name:"analytics.js",version:"".concat("web"===(0,tI.B)()?"next":"npm:next","-").concat(tr.i)}),d&&!p.campaign&&(p.campaign=((u=d).startsWith("?")&&(u=u.substring(1)),(u=u.replace(/\?/g,"&")).split("&").reduce(function(t,e){var n=e.split("="),r=n[0],i=n[1];if(r.includes("utm_")&&r.length>4){var o=r.slice(4);"campaign"===o&&(o="name"),t[o]=(0,tj.a)(void 0===i?"":i)}return t},{})));var v=function(){var t=V.get("_ga");if(t&&t.startsWith("amp"))return t}();v&&(p.amp={id:v}),s=(i=new U(null!==(l=t.instance.options.disableClientPersistence)&&void 0!==l&&l?[]:[new W(function(){if(r)return r;var t=H(window.location.href);return r={expires:31536e6,secure:!1,path:"/"},t&&(r.domain=t),r}())])).get("s:context.referrer"),(a=null!==(n=function(t){var e={btid:"dataxu",urid:"millennial-media"};t.startsWith("?")&&(t=t.substring(1));for(var n=(t=t.replace(/\?/g,"&")).split("&"),r=0;r<n.length;r++){var i=n[r].split("="),o=i[0],s=i[1];if(e[o])return{id:s,type:e[o]}}}(d))&&void 0!==n?n:s)&&(p&&(p.referrer=(0,o.pi)((0,o.pi)({},p.referrer),a)),i.set("s:context.referrer",a));try{p.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone}catch(t){}return e},this.track=this.enrich,this.identify=this.enrich,this.page=this.enrich,this.group=this.enrich,this.alias=this.enrich,this.screen=this.enrich},tP=n(41213),tM=n(99860),tE=n(12156),tA=function(){function t(t,e){this.version="1.0.0",this.alternativeNames=[],this.loadPromise=(0,tS.d)(),this.middleware=[],this.alias=this._createMethod("alias"),this.group=this._createMethod("group"),this.identify=this._createMethod("identify"),this.page=this._createMethod("page"),this.screen=this._createMethod("screen"),this.track=this._createMethod("track"),this.action=e,this.name=t,this.type=e.type,this.alternativeNames.push(e.name)}return t.prototype.addMiddleware=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];"destination"===this.type&&(t=this.middleware).push.apply(t,e)},t.prototype.transform=function(t){return(0,o.mG)(this,void 0,void 0,function(){var e;return(0,o.Jh)(this,function(n){switch(n.label){case 0:return[4,(0,tM.applyDestinationMiddleware)(this.name,t.event,this.middleware)];case 1:return null===(e=n.sent())&&t.cancel(new J.Y({retry:!1,reason:"dropped by destination middleware"})),[2,new y._(e)]}})})},t.prototype._createMethod=function(t){var e=this;return function(n){return(0,o.mG)(e,void 0,void 0,function(){var e,r;return(0,o.Jh)(this,function(i){switch(i.label){case 0:if(!this.action[t])return[2,n];if(e=n,"destination"!==this.type)return[3,2];return[4,this.transform(n)];case 1:e=i.sent(),i.label=2;case 2:return i.trys.push([2,5,,6]),[4,this.ready()];case 3:if(!i.sent())throw Error("Something prevented the destination from getting ready");return(0,tE.z)(n,{integrationName:this.action.name,methodName:t,type:"action"}),[4,this.action[t](e)];case 4:return i.sent(),[3,6];case 5:throw r=i.sent(),(0,tE.z)(n,{integrationName:this.action.name,methodName:t,type:"action",didError:!0}),r;case 6:return[2,n]}})})}},t.prototype.isLoaded=function(){return this.action.isLoaded()},t.prototype.ready=function(){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.loadPromise.promise];case 1:return t.sent(),[2,!0];case 2:return t.sent(),[2,!1];case 3:return[2]}})})},t.prototype.load=function(t,e){return(0,o.mG)(this,void 0,void 0,function(){var n,r,i,s;return(0,o.Jh)(this,function(o){switch(o.label){case 0:if(this.loadPromise.isSettled())return[2,this.loadPromise.promise];o.label=1;case 1:return o.trys.push([1,3,,4]),(0,tE.z)(t,{integrationName:this.action.name,methodName:"load",type:"action"}),n=this.action.load(t,e),i=(r=this.loadPromise).resolve,[4,n];case 2:return i.apply(r,[o.sent()]),[2,n];case 3:throw s=o.sent(),(0,tE.z)(t,{integrationName:this.action.name,methodName:"load",type:"action",didError:!0}),this.loadPromise.reject(s),s;case 4:return[2]}})})},t.prototype.unload=function(t,e){var n,r;return null===(r=(n=this.action).unload)||void 0===r?void 0:r.call(n,t,e)},t}(),tD=n(50498),tC=function(t){var e=!1;window.addEventListener("pagehide",function(){e||t(e=!0)}),document.addEventListener("visibilitychange",function(){if("hidden"==document.visibilityState){if(e)return;e=!0}else e=!1;t(e)})};function tF(t){return(encodeURI(JSON.stringify(t)).split(/%..|./).length-1)/1024}var tN=n(97256);function tJ(t,e,n,r){var i=this;t||setTimeout(function(){return(0,o.mG)(i,void 0,void 0,function(){var t,i;return(0,o.Jh)(this,function(s){switch(s.label){case 0:return t=!0,[4,function(t,e){return(0,o.mG)(this,void 0,void 0,function(){var n,r=this;return(0,o.Jh)(this,function(i){switch(i.label){case 0:if(n=[],(0,v.s)())return[2,e];return[4,(0,tN.x)(function(){return e.length>0&&!(0,v.s)()},function(){return(0,o.mG)(r,void 0,void 0,function(){var r;return(0,o.Jh)(this,function(i){switch(i.label){case 0:if(!(r=e.pop()))return[2];return[4,(0,G.a)(r,t)];case 1:return i.sent() instanceof y._||n.push(r),[2]}})})})];case 1:return i.sent(),n.map(function(t){return e.pushWithBackoff(t)}),[2,e]}})})}(n,e)];case 1:return i=s.sent(),t=!1,e.todo>0&&r(t,i,n,r),[2]}})})},5e3*Math.random())}var tT=n(41554),tG=(0,ti.R)(),tq=null!==(i=tG.__SEGMENT_INSPECTOR__)&&void 0!==i?i:tG.__SEGMENT_INSPECTOR__={},tL=function(t){var e;return null===(e=tq.attach)||void 0===e?void 0:e.call(tq,t)},tz=n(42051),tR=function(t){function e(){var e=this,r=(0,tS.d)(),i=r.promise,s=r.resolve;return(e=t.call(this,function(t){return i.then(function(e){return function(t,e,r){var i,s,l,p,f,d,h,g,m,b;return void 0===e&&(e={}),(0,o.mG)(this,void 0,void 0,function(){var w,_,x,S,I,j,O,k,P,M,E;return(0,o.Jh)(this,function(A){var D,C,J,T;switch(A.label){case 0:if(!0===e.disable)return[2,[new t_,y._.system()]];if(e.globalAnalyticsKey&&(0,to.jV)(e.globalAnalyticsKey),t.cdnURL&&(0,u.UH)(t.cdnURL),e.initialPageview&&r.push(new tf("page",[])),!(null!==(i=t.cdnSettings)&&void 0!==i))return[3,1];return _=i,[3,3];case 1:return[4,(D=t.writeKey,J=null!=(C=t.cdnURL)?C:(0,u.Vl)(),(0,c.h)("".concat(J,"/v1/projects/").concat(D,"/settings")).then(function(t){return t.ok?t.json():t.text().then(function(t){throw Error(t)})}).catch(function(t){throw console.error(t.message),t}))];case 2:_=A.sent(),A.label=3;case 3:if(w=_,e.updateCDNSettings&&(w=e.updateCDNSettings(w)),"function"!=typeof e.disable)return[3,5];return[4,e.disable(w)];case 4:if(A.sent())return[2,[new t_,y._.system()]];A.label=5;case 5:return x=null===(l=null===(s=w.integrations["Segment.io"])||void 0===s?void 0:s.retryQueue)||void 0===l||l,tL(S=new tw(t,e=(0,o.pi)({retryQueue:x},e))),I=null!==(p=t.plugins)&&void 0!==p?p:[],j=null!==(f=t.classicIntegrations)&&void 0!==f?f:[],O=null===(d=e.integrations)||void 0===d?void 0:d["Segment.io"],tz.j.initRemoteMetrics((0,o.pi)((0,o.pi)({},w.metrics),{host:null!==(h=null==O?void 0:O.apiHost)&&void 0!==h?h:null===(g=w.metrics)||void 0===g?void 0:g.host,protocol:null==O?void 0:O.protocol})),tu(T=S,r),ta(T,r),[4,function(t,e,r,i,s,l){var p,f,d;return void 0===s&&(s=[]),(0,o.mG)(this,void 0,void 0,function(){var h,y,g,m,b,w,_,x,S,I,j,O,k,P,M=this;return(0,o.Jh)(this,function(E){var A,D,C;switch(E.label){case 0:if(h=null==s?void 0:s.filter(function(t){return"object"==typeof t}),y=null==s?void 0:s.filter(function(t){return"function"==typeof t&&"string"==typeof t.pluginName}),"test"===a().NODE_ENV||!((null!==(C=null===(D=null===(A=e.middlewareSettings)||void 0===A?void 0:A.routingRules)||void 0===D?void 0:D.length)&&void 0!==C?C:0)>0))return[3,2];return[4,n.e(4604).then(n.bind(n,38283)).then(function(t){return t.tsubMiddleware(e.middlewareSettings.routingRules)})];case 1:return m=E.sent(),[3,3];case 2:m=void 0,E.label=3;case 3:if(g=m,("test"===a().NODE_ENV||!(Object.keys(e.integrations).length>1))&&!(l.length>0))return[3,5];return[4,n.e(9464).then(n.bind(n,52014)).then(function(n){return n.ajsDestinations(t,e,r.integrations,i,g,l)})];case 4:return w=E.sent(),[3,6];case 5:w=[],E.label=6;case 6:if(b=w,!e.legacyVideoPluginsEnabled)return[3,8];return[4,n.e(8150).then(n.bind(n,46023)).then(function(t){return t.loadLegacyVideoPlugins(r)})];case 7:E.sent(),E.label=8;case 8:if(!(null===(p=i.plan)||void 0===p?void 0:p.track))return[3,10];return[4,n.e(7493).then(n.bind(n,76673)).then(function(t){var n;return t.schemaFilter(null===(n=i.plan)||void 0===n?void 0:n.track,e)})];case 9:return x=E.sent(),[3,11];case 10:x=void 0,E.label=11;case 11:return _=x,S=(0,tx.o)(e,i),[4,(function(t,e,n,r,i,s){var a,c,l;return(0,o.mG)(this,void 0,void 0,function(){var p,f,d=this;return(0,o.Jh)(this,function(h){switch(h.label){case 0:return p=[],f=null!==(c=null===(a=t.middlewareSettings)||void 0===a?void 0:a.routingRules)&&void 0!==c?c:[],[4,Promise.all((null!==(l=t.remotePlugins)&&void 0!==l?l:[]).map(function(t){return(0,o.mG)(d,void 0,void 0,function(){var a,c,l,d,h;return(0,o.Jh)(this,function(v){switch(v.label){case 0:var y,g;if(y=e[t.creationName],g=e[t.name],!1===e.All&&!y&&!g||!1===y||!1===g)return[2];v.label=1;case 1:if(v.trys.push([1,6,,7]),c=null==s?void 0:s.find(function(e){return e.pluginName===t.name}))return[3,3];return[4,function(t,e){return(0,o.mG)(this,void 0,void 0,function(){var n,r,i,s,a;return(0,o.Jh)(this,function(o){switch(o.label){case 0:if(n=RegExp("https://cdn.segment.(com|build)"),r=(0,u.Vl)(),!e)return[3,6];s=(i=t.url.split("/"))[i.length-2],a=t.url.replace(s,btoa(s).replace(/=/g,"")),o.label=1;case 1:return o.trys.push([1,3,,5]),[4,(0,tP.v)(a.replace(n,r))];case 2:case 4:return o.sent(),[3,5];case 3:return o.sent(),[4,(0,tP.v)(t.url.replace(n,r))];case 5:return[3,8];case 6:return[4,(0,tP.v)(t.url.replace(n,r))];case 7:o.sent(),o.label=8;case 8:if("function"==typeof window[t.libraryName])return[2,window[t.libraryName]];return[2]}})})}(t,null==r?void 0:r.obfuscate)];case 2:c=v.sent(),v.label=3;case 3:if(!(a=c))return[3,5];return[4,a((0,o.pi)((0,o.pi)({},t.settings),n[t.name]))];case 4:(function(t){if(!Array.isArray(t))throw Error("Not a valid list of plugins");var e=["load","isLoaded","name","version","type"];t.forEach(function(t){e.forEach(function(e){var n;if(void 0===t[e])throw Error("Plugin: ".concat(null!==(n=t.name)&&void 0!==n?n:"unknown"," missing required function ").concat(e))})})})(d=Array.isArray(l=v.sent())?l:[l]),h=f.filter(function(e){return e.destinationName===t.creationName}),d.forEach(function(e){var n=new tA(t.creationName,e);h.length&&i&&n.addMiddleware(i),p.push(n)}),v.label=5;case 5:return[3,7];case 6:return console.warn("Failed to load Remote Plugin",v.sent()),[3,7];case 7:return[2]}})})}))];case 1:return h.sent(),[2,p.filter(Boolean)]}})})})(e,r.integrations,S,i,g,y).catch(function(){return[]})];case 12:if(I=E.sent(),j=(0,o.ev)((0,o.ev)((0,o.ev)([tk],h,!0),b,!0),I,!0),_&&j.push(_),(null===(f=i.integrations)||void 0===f?void 0:f.All)===!1&&!i.integrations["Segment.io"]||i.integrations&&!1===i.integrations["Segment.io"])return[3,14];return k=(O=j).push,[4,function(t,e,n){window.addEventListener("pagehide",function(){l.push.apply(l,Array.from(p)),p.clear()});var r,i,s,a,u=null!==(i=null==e?void 0:e.apiKey)&&void 0!==i?i:"",l=t.options.disableClientPersistence?new N.Z(t.queue.queue.maxAttempts,[]):new F.$(t.queue.queue.maxAttempts,"".concat(u,":dest-Segment.io")),p=new Set,f=null!==(s=null==e?void 0:e.apiHost)&&void 0!==s?s:tT.U,d=null!==(a=null==e?void 0:e.protocol)&&void 0!==a?a:"https",h="".concat(d,"://").concat(f),y=null==e?void 0:e.deliveryStrategy,g=(null==y?void 0:y.strategy)==="batching"?function(t,e){var n,r,i,s=[],a=!1,u=null!==(n=null==e?void 0:e.size)&&void 0!==n?n:10,l=null!==(r=null==e?void 0:e.timeout)&&void 0!==r?r:5e3;function p(e){if(0!==e.length){var n,r=null===(n=e[0])||void 0===n?void 0:n.writeKey,i=e.map(function(t){return t.sentAt,(0,o._T)(t,["sentAt"])});return(0,c.h)("https://".concat(t,"/b"),{keepalive:a,headers:{"Content-Type":"text/plain"},method:"post",body:JSON.stringify({writeKey:r,batch:i,sentAt:new Date().toISOString()})})}}function f(){return(0,o.mG)(this,void 0,void 0,function(){var t;return(0,o.Jh)(this,function(e){return s.length?(t=s,s=[],[2,p(t)]):[2]})})}return tC(function(t){if((a=t)&&s.length){var e,n;Promise.all((e=[],n=0,s.forEach(function(t){tF(e[n])>=64&&n++,e[n]?e[n].push(t):e[n]=[t]}),e).map(p)).catch(console.error)}}),{dispatch:function(t,e){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(t){return s.push(e),[2,s.length>=u||tF(s)>=450||a?f():void(i||(i=setTimeout(function(){i=void 0,f().catch(console.error)},l)))]})})}}}(f,y.config):(r=null==y?void 0:y.config,{dispatch:function(t,e){return(0,c.h)(t,{keepalive:null==r?void 0:r.keepalive,headers:{"Content-Type":"text/plain"},method:"post",body:JSON.stringify(e)})}});function m(r){return(0,o.mG)(this,void 0,void 0,function(){var i,s;return(0,o.Jh)(this,function(a){if((0,v.s)())return l.push(r),tJ(!1,l,b,tJ),[2,r];if(p.add(r),i=r.event.type.charAt(0),s=(0,tD.D)(r.event).json(),"track"===r.event.type&&delete s.traits,"alias"===r.event.type){var u,c,f,d,y,m;u=s,m=t.user(),u.previousId=null!==(d=null!==(f=null!==(c=u.previousId)&&void 0!==c?c:u.from)&&void 0!==f?f:m.id())&&void 0!==d?d:m.anonymousId(),u.userId=null!==(y=u.userId)&&void 0!==y?y:u.to,delete u.from,delete u.to,s=u}return[2,g.dispatch("".concat(h,"/").concat(i),function(t,e,n,r){var i,s=t.user();delete e.options,e.writeKey=null==n?void 0:n.apiKey,e.userId=e.userId||s.id(),e.anonymousId=e.anonymousId||s.anonymousId(),e.sentAt=new Date;var a=t.queue.failedInitializations||[];a.length>0&&(e._metadata={failedInitializations:a});var u=[],c=[];for(var l in r){var p=r[l];"Segment.io"===l&&u.push(l),"bundled"===p.bundlingStatus&&u.push(l),"unbundled"===p.bundlingStatus&&c.push(l)}for(var f=0,d=(null==n?void 0:n.unbundledIntegrations)||[];f<d.length;f++){var h=d[f];c.includes(h)||c.push(h)}var v=null!==(i=null==n?void 0:n.maybeBundledConfigIds)&&void 0!==i?i:{},y=[];return u.sort().forEach(function(t){var e;(null!==(e=v[t])&&void 0!==e?e:[]).forEach(function(t){y.push(t)})}),(null==n?void 0:n.addBundledMetadata)!==!1&&(e._metadata=(0,o.pi)((0,o.pi)({},e._metadata),{bundled:u.sort(),unbundled:c.sort(),bundledIds:y})),e}(t,s,e,n)).then(function(){return r}).catch(function(){return l.pushWithBackoff(r),tJ(!1,l,b,tJ),r}).finally(function(){p.delete(r)})]})})}var b={name:"Segment.io",type:"destination",version:"0.1.0",isLoaded:function(){return!0},load:function(){return Promise.resolve()},track:m,identify:m,page:m,alias:m,group:m,screen:m};return l.todo&&tJ(!1,l,b,tJ),b}(r,S["Segment.io"],e.integrations)];case 13:k.apply(O,[E.sent()]),E.label=14;case 14:return[4,r.register.apply(r,j)];case 15:if(P=E.sent(),!Object.entries(null!==(d=e.enabledMiddleware)&&void 0!==d?d:{}).some(function(t){return t[1]}))return[3,17];return[4,n.e(9214).then(n.bind(n,53005)).then(function(t){var n=t.remoteMiddlewares;return(0,o.mG)(M,void 0,void 0,function(){return(0,o.Jh)(this,function(t){switch(t.label){case 0:return[4,n(P,e,i.obfuscate)];case 1:return[2,Promise.all(t.sent().map(function(t){return r.addSourceMiddleware(t)}))]}})})})];case 16:E.sent(),E.label=17;case 17:return[2,P]}})})}(t.writeKey,w,S,e,I,j)];case 6:if(k=A.sent(),P=null!==(m=window.location.search)&&void 0!==m?m:"",M=null!==(b=window.location.hash)&&void 0!==b?b:"",!(E=P.length?P:M.replace(/(?=#).*(?=\?)/,"")).includes("ajs_"))return[3,8];return[4,S.queryString(E).catch(console.error)];case 7:A.sent(),A.label=8;case 8:return S.initialized=!0,S.emit("initialize",t,e),[4,function(t,e){return(0,o.mG)(this,void 0,void 0,function(){return(0,o.Jh)(this,function(n){switch(n.label){case 0:return[4,(0,o.mG)(void 0,void 0,void 0,function(){var n,r;return(0,o.Jh)(this,function(i){switch(i.label){case 0:n=0,r=e.getCalls("addSourceMiddleware"),i.label=1;case 1:if(!(n<r.length))return[3,4];return[4,th(t,r[n]).catch(console.error)];case 2:i.sent(),i.label=3;case 3:return n++,[3,1];case 4:return[2]}})})];case 1:return n.sent(),tc(t,e),e.clear(),[2]}})})}(S,r)];case 9:return A.sent(),[2,[S,k]]}})})}(e[0],e[1],t)})})||this)._resolveLoadStart=function(t,e){return s([t,e])},e}return(0,o.ZT)(e,t),e.prototype.load=function(t,e){return void 0===e&&(e={}),this._resolveLoadStart(t,e),this},e.load=function(t,n){return void 0===n&&(n={}),new e().load(t,n)},e.standalone=function(t,n){return e.load({writeKey:t},n).then(function(t){return t[0]})},e}(tv)},86417:function(t,e,n){"use strict";n.d(e,{G:function(){return i},s:function(){return o}});var r=n(70610);function i(){return!(0,r.j)()||window.navigator.onLine}function o(){return!i()}},41554:function(t,e,n){"use strict";n.d(e,{U:function(){return r}});var r="api.segment.io/v1"},15116:function(t,e,n){"use strict";n.d(e,{_:function(){return s}});var r=n(83780),i=n(55114),o=n(42051),s=function(t){function e(e,n){return t.call(this,e,n,new o.j)||this}return(0,r.ZT)(e,t),e.system=function(){return new this({type:"track",event:"system"})},e}(i._)},70610:function(t,e,n){"use strict";function r(){return"undefined"!=typeof window}function i(){return!r()}n.d(e,{j:function(){return r},s:function(){return i}})},9676:function(t,e,n){"use strict";function r(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(e){return t}}n.d(e,{a:function(){return r}})},42051:function(t,e,n){"use strict";n.d(e,{j:function(){return d}});var r,i=n(83780),o=n(75174),s=n(74008),a=n(65712),u=n(83181),c=n(41554),l=function(t,e,n){var r=e.reduce(function(t,e){var n=e.split(":"),r=n[0],i=n[1];return t[r]=i,t},{});return{type:"Counter",metric:t,value:1,tags:(0,i.pi)((0,i.pi)({},r),{library:"analytics.js",library_version:"web"===n?"next-".concat(a.i):"npm:next-".concat(a.i)})}};function p(t){console.error("Error sending segment performance metrics",t)}var f=function(){function t(t){var e,n,r,i,o,s=this;if(this.host=null!==(e=null==t?void 0:t.host)&&void 0!==e?e:c.U,this.sampleRate=null!==(n=null==t?void 0:t.sampleRate)&&void 0!==n?n:1,this.flushTimer=null!==(r=null==t?void 0:t.flushTimer)&&void 0!==r?r:3e4,this.maxQueueSize=null!==(i=null==t?void 0:t.maxQueueSize)&&void 0!==i?i:20,this.protocol=null!==(o=null==t?void 0:t.protocol)&&void 0!==o?o:"https",this.queue=[],this.sampleRate>0){var a=!1,u=function(){a||(a=!0,s.flush().catch(p),a=!1,setTimeout(u,s.flushTimer))};u()}}return t.prototype.increment=function(t,e){if(!(!t.includes("analytics_js.")||0===e.length||Math.random()>this.sampleRate)&&!(this.queue.length>=this.maxQueueSize)){var n=l(t,e,(0,u.B)());this.queue.push(n),t.includes("error")&&this.flush().catch(p)}},t.prototype.flush=function(){return(0,i.mG)(this,void 0,void 0,function(){var t=this;return(0,i.Jh)(this,function(e){switch(e.label){case 0:if(this.queue.length<=0)return[2];return[4,this.send().catch(function(e){p(e),t.sampleRate=0})];case 1:return e.sent(),[2]}})})},t.prototype.send=function(){return(0,i.mG)(this,void 0,void 0,function(){var t,e,n;return(0,i.Jh)(this,function(r){return t={series:this.queue},this.queue=[],e={"Content-Type":"text/plain"},n="".concat(this.protocol,"://").concat(this.host,"/m"),[2,(0,s.h)(n,{headers:e,body:JSON.stringify(t),method:"POST"})]})})},t}(),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return(0,i.ZT)(e,t),e.initRemoteMetrics=function(t){r=new f(t)},e.prototype.increment=function(e,n,i){t.prototype.increment.call(this,e,n,i),null==r||r.increment(e,null!=i?i:[])},e}(o.s)},12156:function(t,e,n){"use strict";function r(t,e){var n=e.methodName,r=e.integrationName,i=e.type,o=e.didError;t.stats.increment("analytics_js.integration.invoke".concat(void 0!==o&&o?".error":""),1,["method:".concat(n),"integration_name:".concat(r),"type:".concat(i)])}n.d(e,{z:function(){return r}})},65712:function(t,e,n){"use strict";n.d(e,{i:function(){return r}});var r="1.69.0"},74008:function(t,e,n){"use strict";n.d(e,{h:function(){return s}});var r=n(33836),i=n.n(r),o=n(94701),s=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=(0,o.R)();return(n&&n.fetch||i()).apply(void 0,t)}},94701:function(t,e,n){"use strict";n.d(e,{R:function(){return r}});var r=function(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:null}},77013:function(t,e,n){"use strict";n.d(e,{jV:function(){return o},ql:function(){return s},wI:function(){return i}});var r="analytics";function i(){return window[r]}function o(t){r=t}function s(t){window[r]=t}},41213:function(t,e,n){"use strict";function r(t){return Array.prototype.slice.call(window.document.querySelectorAll("script")).find(function(e){return e.src===t})}function i(t,e){var n=r(t);if(void 0!==n){var i=null==n?void 0:n.getAttribute("status");if("loaded"===i)return Promise.resolve(n);if("loading"===i)return new Promise(function(t,e){n.addEventListener("load",function(){return t(n)}),n.addEventListener("error",function(t){return e(t)})})}return new Promise(function(n,r){var i,o=window.document.createElement("script");o.type="text/javascript",o.src=t,o.async=!0,o.setAttribute("status","loading");for(var s=0,a=Object.entries(null!=e?e:{});s<a.length;s++){var u=a[s],c=u[0],l=u[1];o.setAttribute(c,l)}o.onload=function(){o.onerror=o.onload=null,o.setAttribute("status","loaded"),n(o)},o.onerror=function(){o.onerror=o.onload=null,o.setAttribute("status","error"),r(Error("Failed to load ".concat(t)))};var p=window.document.getElementsByTagName("script")[0];null===(i=p.parentElement)||void 0===i||i.insertBefore(o,p)})}function o(t){var e=r(t);return void 0!==e&&e.remove(),Promise.resolve()}n.d(e,{t:function(){return o},v:function(){return i}})},44997:function(t,e,n){"use strict";n.d(e,{o:function(){return i}});var r=n(83780);function i(t,e){var n,i=Object.entries(null!==(n=e.integrations)&&void 0!==n?n:{}).reduce(function(t,e){var n,i,o=e[0],s=e[1];return"object"==typeof s?(0,r.pi)((0,r.pi)({},t),((n={})[o]=s,n)):(0,r.pi)((0,r.pi)({},t),((i={})[o]={},i))},{});return Object.entries(t.integrations).reduce(function(t,e){var n,o=e[0],s=e[1];return(0,r.pi)((0,r.pi)({},t),((n={})[o]=(0,r.pi)((0,r.pi)({},s),i[o]),n))},{})}},97256:function(t,e,n){"use strict";n.d(e,{x:function(){return i}});var r=n(83780),i=function(t,e){return(0,r.mG)(void 0,void 0,void 0,function(){var n;return(0,r.Jh)(this,function(i){return[2,(n=function(i){return(0,r.mG)(void 0,void 0,void 0,function(){var o;return(0,r.Jh)(this,function(r){switch(r.label){case 0:if(!t(i))return[3,2];return o=n,[4,e()];case 1:return[2,o.apply(void 0,[r.sent()])];case 2:return[2]}})})})(void 0)]})})}},73515:function(t,e,n){"use strict";n.d(e,{Kg:function(){return l},UH:function(){return u},Vl:function(){return c}});var r,i=n(77013),o=/(https:\/\/.*)\/analytics\.js\/v1\/(?:.*?)\/(?:platform|analytics.*)?/,s=function(){var t;return Array.prototype.slice.call(document.querySelectorAll("script")).forEach(function(e){var n,r=null!==(n=e.getAttribute("src"))&&void 0!==n?n:"",i=o.exec(r);i&&i[1]&&(t=i[1])}),t},a=function(){var t;return null!=r?r:null===(t=(0,i.wI)())||void 0===t?void 0:t._cdn},u=function(t){var e=(0,i.wI)();e&&(e._cdn=t),r=t},c=function(){var t=a();return t?t:s()||"https://cdn.segment.com"},l=function(){var t=c();return"".concat(t,"/next-integrations")}},10700:function(t,e,n){"use strict";n.d(e,{$:function(){return f}});var r=n(83780),i=n(32593),o=n(15116),s=n(70610),a={getItem:function(){},setItem:function(){},removeItem:function(){}};try{a=(0,s.j)()&&window.localStorage?window.localStorage:a}catch(t){console.warn("Unable to access localStorage",t)}function u(t){var e=a.getItem(t);return(e?JSON.parse(e):[]).map(function(t){return new o._(t.event,t.id)})}function c(t){var e=a.getItem(t);return e?JSON.parse(e):{}}function l(t){a.removeItem(t)}function p(t,e,n){void 0===n&&(n=0);var r="persisted-queue:v1:".concat(t,":lock"),i=a.getItem(r),o=i?JSON.parse(i):null,s=null===o||new Date().getTime()>o;if(s){a.setItem(r,JSON.stringify(new Date().getTime()+50)),e(),a.removeItem(r);return}!s&&n<3?setTimeout(function(){p(t,e,n+1)},50):console.error("Unable to retrieve lock")}var f=function(t){function e(e,n){var i=t.call(this,e,[])||this,o="persisted-queue:v1:".concat(n,":items"),s="persisted-queue:v1:".concat(n,":seen"),f=[],d={};return p(n,function(){try{f=u(o),d=c(s),l(o),l(s),i.queue=(0,r.ev)((0,r.ev)([],f,!0),i.queue,!0),i.seen=(0,r.pi)((0,r.pi)({},d),i.seen)}catch(t){console.error(t)}}),window.addEventListener("pagehide",function(){if(i.todo>0){var t=(0,r.ev)((0,r.ev)([],i.queue,!0),i.future,!0);try{p(n,function(){var e,n,l,p;e=u(o),n=(0,r.ev)((0,r.ev)([],t,!0),e,!0).reduce(function(t,e){var n;return(0,r.pi)((0,r.pi)({},t),((n={})[e.id]=e,n))},{}),a.setItem(o,JSON.stringify(Object.values(n))),l=i.seen,p=c(s),a.setItem(s,JSON.stringify((0,r.pi)((0,r.pi)({},p),l)))})}catch(t){console.error(t)}}}),i}return(0,r.ZT)(e,t),e}(i.Z)},50498:function(t,e,n){"use strict";n.d(e,{D:function(){return i}});var r=n(53433);function i(t,e){var n=new r.Facade(t,e);return"track"===t.type&&(n=new r.Track(t,e)),"identify"===t.type&&(n=new r.Identify(t,e)),"page"===t.type&&(n=new r.Page(t,e)),"alias"===t.type&&(n=new r.Alias(t,e)),"group"===t.type&&(n=new r.Group(t,e)),"screen"===t.type&&(n=new r.Screen(t,e)),Object.defineProperty(n,"obj",{value:t,writable:!0}),n}},83181:function(t,e,n){"use strict";function r(){return"npm"}n.d(e,{B:function(){return r}})},99860:function(t,e,n){"use strict";n.r(e),n.d(e,{applyDestinationMiddleware:function(){return s},sourceMiddlewarePlugin:function(){return a}});var r=n(83780),i=n(55114),o=n(50498);function s(t,e,n){return(0,r.mG)(this,void 0,void 0,function(){var i,s,a,u,c;return(0,r.Jh)(this,function(l){switch(l.label){case 0:i=(0,o.D)(e,{clone:!0,traverse:!1}).rawEvent(),s=0,a=n,l.label=1;case 1:if(!(s<a.length))return[3,4];return u=a[s],[4,function(e,n){return(0,r.mG)(this,void 0,void 0,function(){var i,s,a;return(0,r.Jh)(this,function(u){switch(u.label){case 0:return i=!1,s=null,[4,n({payload:(0,o.D)(e,{clone:!0,traverse:!1}),integration:t,next:function(t){i=!0,null===t&&(s=null),t&&(s=t.obj)}})];case 1:return u.sent(),i||null===s||(s.integrations=(0,r.pi)((0,r.pi)({},e.integrations),((a={})[t]=!1,a))),[2,s]}})})}(i,u)];case 2:if(null===(c=l.sent()))return[2,null];i=c,l.label=3;case 3:return s++,[3,1];case 4:return[2,i]}})})}function a(t,e){function n(n){return(0,r.mG)(this,void 0,void 0,function(){var s;return(0,r.Jh)(this,function(r){switch(r.label){case 0:return s=!1,[4,t({payload:(0,o.D)(n.event,{clone:!0,traverse:!1}),integrations:null!=e?e:{},next:function(t){s=!0,t&&(n.event=t.obj)}})];case 1:if(r.sent(),!s)throw new i.Y({retry:!1,type:"middleware_cancellation",reason:"Middleware `next` function skipped"});return[2,n]}})})}return{name:"Source Middleware ".concat(t.name),type:"before",version:"0.1.0",isLoaded:function(){return!0},load:function(t){return Promise.resolve(t)},track:n,page:n,identify:n,alias:n,group:n}}},77185:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});var i=r(n(2560));function o(t,e){return function(){var n=this.traits(),r=this.properties?this.properties():{};return i.default(n,"address."+t)||i.default(n,t)||(e?i.default(n,"address."+e):null)||(e?i.default(n,e):null)||i.default(r,"address."+t)||i.default(r,t)||(e?i.default(r,"address."+e):null)||(e?i.default(r,e):null)}}e.default=function(t){t.zip=o("postalCode","zip"),t.country=o("country"),t.street=o("street"),t.state=o("state"),t.city=o("city"),t.region=o("region")}},15938:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Alias=void 0;var i=r(n(2461)),o=n(52280);function s(t,e){o.Facade.call(this,t,e)}e.Alias=s,i.default(s,o.Facade),s.prototype.action=function(){return"alias"},s.prototype.type=s.prototype.action,s.prototype.previousId=function(){return this.field("previousId")||this.field("from")},s.prototype.from=s.prototype.previousId,s.prototype.userId=function(){return this.field("userId")||this.field("to")},s.prototype.to=s.prototype.userId},90151:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.clone=void 0,e.clone=function t(e){if("object"!=typeof e)return e;if("[object Object]"===Object.prototype.toString.call(e)){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=t(e[r]));return n}return Array.isArray(e)?e.map(t):e}},26041:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Delete=void 0;var i=r(n(2461)),o=n(52280);function s(t,e){o.Facade.call(this,t,e)}e.Delete=s,i.default(s,o.Facade),s.prototype.type=function(){return"delete"}},52280:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Facade=void 0;var i=r(n(77185)),o=n(90151),s=r(n(53499)),a=r(n(88377)),u=r(n(2560)),c=r(n(85922));function l(t,e){e=e||{},this.raw=o.clone(t),"clone"in e||(e.clone=!0),e.clone&&(t=o.clone(t)),"traverse"in e||(e.traverse=!0),"timestamp"in t?t.timestamp=a.default(t.timestamp):t.timestamp=new Date,e.traverse&&c.default(t),this.opts=e,this.obj=t}e.Facade=l;var p=l.prototype;function f(t){return o.clone(t)}p.proxy=function(t){var e,n=t.split("."),r=this[t=n.shift()]||this.obj[t];return r?("function"==typeof r&&(r=r.call(this)||{}),0===n.length||(r=u.default(r,n.join("."))),this.opts.clone?(e=r,o.clone(e)):r):r},p.field=function(t){var e,n=this.obj[t];return this.opts.clone?(e=n,o.clone(e)):n},l.proxy=function(t){return function(){return this.proxy(t)}},l.field=function(t){return function(){return this.field(t)}},l.multi=function(t){return function(){var e=this.proxy(t+"s");if(Array.isArray(e))return e;var n=this.proxy(t);return n&&(n=[this.opts.clone?o.clone(n):n]),n||[]}},l.one=function(t){return function(){var e=this.proxy(t);if(e)return e;var n=this.proxy(t+"s");if(Array.isArray(n))return n[0]}},p.json=function(){var t=this.opts.clone?o.clone(this.obj):this.obj;return this.type&&(t.type=this.type()),t},p.rawEvent=function(){return this.raw},p.options=function(t){var e=this.obj.options||this.obj.context||{},n=this.opts.clone?o.clone(e):e;if(!t)return n;if(this.enabled(t)){var r=this.integrations(),i=r[t]||u.default(r,t);return"object"!=typeof i&&(i=u.default(this.options(),t)),"object"==typeof i?i:{}}},p.context=p.options,p.enabled=function(t){var e=this.proxy("options.providers.all");"boolean"!=typeof e&&(e=this.proxy("options.all")),"boolean"!=typeof e&&(e=this.proxy("integrations.all")),"boolean"!=typeof e&&(e=!0);var n=e&&s.default(t),r=this.integrations();if(r.providers&&r.providers.hasOwnProperty(t)&&(n=r.providers[t]),r.hasOwnProperty(t)){var i=r[t];n="boolean"!=typeof i||i}return!!n},p.integrations=function(){return this.obj.integrations||this.proxy("options.providers")||this.options()},p.active=function(){var t=this.proxy("options.active");return null==t&&(t=!0),t},p.anonymousId=function(){return this.field("anonymousId")||this.field("sessionId")},p.sessionId=p.anonymousId,p.groupId=l.proxy("options.groupId"),p.traits=function(t){var e=this.proxy("options.traits")||{},n=this.userId();for(var r in t=t||{},n&&(e.id=n),t)if(Object.prototype.hasOwnProperty.call(t,r)){var i=null==this[r]?this.proxy("options.traits."+r):this[r]();if(null==i)continue;e[t[r]]=i,delete e[r]}return e},p.library=function(){var t=this.proxy("options.library");return t?"string"==typeof t?{name:t,version:null}:t:{name:"unknown",version:null}},p.device=function(){var t=this.proxy("context.device");("object"!=typeof t||null===t)&&(t={});var e=this.library().name;return t.type||(e.indexOf("ios")>-1&&(t.type="ios"),e.indexOf("android")>-1&&(t.type="android")),t},p.userAgent=l.proxy("context.userAgent"),p.timezone=l.proxy("context.timezone"),p.timestamp=l.field("timestamp"),p.channel=l.field("channel"),p.ip=l.proxy("context.ip"),p.userId=l.field("userId"),i.default(p)},51137:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Group=void 0;var i=r(n(2461)),o=r(n(51846)),s=r(n(88377)),a=n(52280);function u(t,e){a.Facade.call(this,t,e)}e.Group=u,i.default(u,a.Facade);var c=u.prototype;c.action=function(){return"group"},c.type=c.action,c.groupId=a.Facade.field("groupId"),c.created=function(){var t=this.proxy("traits.createdAt")||this.proxy("traits.created")||this.proxy("properties.createdAt")||this.proxy("properties.created");if(t)return s.default(t)},c.email=function(){var t=this.proxy("traits.email");if(t)return t;var e=this.groupId();if(o.default(e))return e},c.traits=function(t){var e=this.properties(),n=this.groupId();for(var r in t=t||{},n&&(e.id=n),t)if(Object.prototype.hasOwnProperty.call(t,r)){var i=null==this[r]?this.proxy("traits."+r):this[r]();if(null==i)continue;e[t[r]]=i,delete e[r]}return e},c.name=a.Facade.proxy("traits.name"),c.industry=a.Facade.proxy("traits.industry"),c.employees=a.Facade.proxy("traits.employees"),c.properties=function(){return this.field("traits")||this.field("properties")||{}}},8835:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Identify=void 0;var i=n(52280),o=r(n(2560)),s=r(n(2461)),a=r(n(51846)),u=r(n(88377)),c=function(t){return t.trim()};function l(t,e){i.Facade.call(this,t,e)}e.Identify=l,s.default(l,i.Facade);var p=l.prototype;p.action=function(){return"identify"},p.type=p.action,p.traits=function(t){var e=this.field("traits")||{},n=this.userId();for(var r in t=t||{},n&&(e.id=n),t){var i=null==this[r]?this.proxy("traits."+r):this[r]();null!=i&&(e[t[r]]=i,r!==t[r]&&delete e[r])}return e},p.email=function(){var t=this.proxy("traits.email");if(t)return t;var e=this.userId();if(a.default(e))return e},p.created=function(){var t=this.proxy("traits.created")||this.proxy("traits.createdAt");if(t)return u.default(t)},p.companyCreated=function(){var t=this.proxy("traits.company.created")||this.proxy("traits.company.createdAt");if(t)return u.default(t)},p.companyName=function(){return this.proxy("traits.company.name")},p.name=function(){var t=this.proxy("traits.name");if("string"==typeof t)return c(t);var e=this.firstName(),n=this.lastName();if(e&&n)return c(e+" "+n)},p.firstName=function(){var t=this.proxy("traits.firstName");if("string"==typeof t)return c(t);var e=this.proxy("traits.name");if("string"==typeof e)return c(e).split(" ")[0]},p.lastName=function(){var t=this.proxy("traits.lastName");if("string"==typeof t)return c(t);var e=this.proxy("traits.name");if("string"==typeof e){var n=c(e).indexOf(" ");if(-1!==n)return c(e.substr(n+1))}},p.uid=function(){return this.userId()||this.username()||this.email()},p.description=function(){return this.proxy("traits.description")||this.proxy("traits.background")},p.age=function(){var t=this.birthday(),e=o.default(this.traits(),"age");return null!=e?e:t instanceof Date?new Date().getFullYear()-t.getFullYear():void 0},p.avatar=function(){var t=this.traits();return o.default(t,"avatar")||o.default(t,"photoUrl")||o.default(t,"avatarUrl")},p.position=function(){var t=this.traits();return o.default(t,"position")||o.default(t,"jobTitle")},p.username=i.Facade.proxy("traits.username"),p.website=i.Facade.one("traits.website"),p.websites=i.Facade.multi("traits.website"),p.phone=i.Facade.one("traits.phone"),p.phones=i.Facade.multi("traits.phone"),p.address=i.Facade.proxy("traits.address"),p.gender=i.Facade.proxy("traits.gender"),p.birthday=i.Facade.proxy("traits.birthday")},53433:function(t,e,n){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.Delete=e.Screen=e.Page=e.Track=e.Identify=e.Group=e.Alias=e.Facade=void 0;var i=n(52280);Object.defineProperty(e,"Facade",{enumerable:!0,get:function(){return i.Facade}});var o=n(15938);Object.defineProperty(e,"Alias",{enumerable:!0,get:function(){return o.Alias}});var s=n(51137);Object.defineProperty(e,"Group",{enumerable:!0,get:function(){return s.Group}});var a=n(8835);Object.defineProperty(e,"Identify",{enumerable:!0,get:function(){return a.Identify}});var u=n(20154);Object.defineProperty(e,"Track",{enumerable:!0,get:function(){return u.Track}});var c=n(91566);Object.defineProperty(e,"Page",{enumerable:!0,get:function(){return c.Page}});var l=n(22744);Object.defineProperty(e,"Screen",{enumerable:!0,get:function(){return l.Screen}});var p=n(26041);Object.defineProperty(e,"Delete",{enumerable:!0,get:function(){return p.Delete}}),e.default=r(r({},i.Facade),{Alias:o.Alias,Group:s.Group,Identify:a.Identify,Track:u.Track,Page:c.Page,Screen:l.Screen,Delete:p.Delete})},51846:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=/.+\@.+\..+/;e.default=function(t){return n.test(t)}},53499:function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={Salesforce:!0};e.default=function(t){return!n[t]}},91566:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Page=void 0;var i=r(n(2461)),o=n(52280),s=n(20154),a=r(n(51846));function u(t,e){o.Facade.call(this,t,e)}e.Page=u,i.default(u,o.Facade);var c=u.prototype;c.action=function(){return"page"},c.type=c.action,c.category=o.Facade.field("category"),c.name=o.Facade.field("name"),c.title=o.Facade.proxy("properties.title"),c.path=o.Facade.proxy("properties.path"),c.url=o.Facade.proxy("properties.url"),c.referrer=function(){return this.proxy("context.referrer.url")||this.proxy("context.page.referrer")||this.proxy("properties.referrer")},c.properties=function(t){var e=this.field("properties")||{},n=this.category(),r=this.name();for(var i in t=t||{},n&&(e.category=n),r&&(e.name=r),t)if(Object.prototype.hasOwnProperty.call(t,i)){var o=null==this[i]?this.proxy("properties."+i):this[i]();if(null==o)continue;e[t[i]]=o,i!==t[i]&&delete e[i]}return e},c.email=function(){var t=this.proxy("context.traits.email")||this.proxy("properties.email");if(t)return t;var e=this.userId();if(a.default(e))return e},c.fullName=function(){var t=this.category(),e=this.name();return e&&t?t+" "+e:e},c.event=function(t){return t?"Viewed "+t+" Page":"Loaded a Page"},c.track=function(t){var e=this.json();return e.event=this.event(t),e.timestamp=this.timestamp(),e.properties=this.properties(),new s.Track(e,this.opts)}},22744:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Screen=void 0;var i=r(n(2461)),o=n(91566),s=n(20154);function a(t,e){o.Page.call(this,t,e)}e.Screen=a,i.default(a,o.Page),a.prototype.action=function(){return"screen"},a.prototype.type=a.prototype.action,a.prototype.event=function(t){return t?"Viewed "+t+" Screen":"Loaded a Screen"},a.prototype.track=function(t){var e=this.json();return e.event=this.event(t),e.timestamp=this.timestamp(),e.properties=this.properties(),new s.Track(e,this.opts)}},20154:function(t,e,n){"use strict";var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.Track=void 0;var i=r(n(2461)),o=n(52280),s=n(8835),a=r(n(51846)),u=r(n(2560));function c(t,e){o.Facade.call(this,t,e)}e.Track=c,i.default(c,o.Facade);var l=c.prototype;l.action=function(){return"track"},l.type=l.action,l.event=o.Facade.field("event"),l.value=o.Facade.proxy("properties.value"),l.category=o.Facade.proxy("properties.category"),l.id=o.Facade.proxy("properties.id"),l.productId=function(){return this.proxy("properties.product_id")||this.proxy("properties.productId")},l.promotionId=function(){return this.proxy("properties.promotion_id")||this.proxy("properties.promotionId")},l.cartId=function(){return this.proxy("properties.cart_id")||this.proxy("properties.cartId")},l.checkoutId=function(){return this.proxy("properties.checkout_id")||this.proxy("properties.checkoutId")},l.paymentId=function(){return this.proxy("properties.payment_id")||this.proxy("properties.paymentId")},l.couponId=function(){return this.proxy("properties.coupon_id")||this.proxy("properties.couponId")},l.wishlistId=function(){return this.proxy("properties.wishlist_id")||this.proxy("properties.wishlistId")},l.reviewId=function(){return this.proxy("properties.review_id")||this.proxy("properties.reviewId")},l.orderId=function(){return this.proxy("properties.id")||this.proxy("properties.order_id")||this.proxy("properties.orderId")},l.sku=o.Facade.proxy("properties.sku"),l.tax=o.Facade.proxy("properties.tax"),l.name=o.Facade.proxy("properties.name"),l.price=o.Facade.proxy("properties.price"),l.total=o.Facade.proxy("properties.total"),l.repeat=o.Facade.proxy("properties.repeat"),l.coupon=o.Facade.proxy("properties.coupon"),l.shipping=o.Facade.proxy("properties.shipping"),l.discount=o.Facade.proxy("properties.discount"),l.shippingMethod=function(){return this.proxy("properties.shipping_method")||this.proxy("properties.shippingMethod")},l.paymentMethod=function(){return this.proxy("properties.payment_method")||this.proxy("properties.paymentMethod")},l.description=o.Facade.proxy("properties.description"),l.plan=o.Facade.proxy("properties.plan"),l.subtotal=function(){var t=u.default(this.properties(),"subtotal"),e=this.total()||this.revenue();if(t)return t;if(!e)return 0;if(this.total()){var n=this.tax();n&&(e-=n),(n=this.shipping())&&(e-=n),(n=this.discount())&&(e+=n)}return e},l.products=function(){var t=this.properties(),e=u.default(t,"products");return Array.isArray(e)?e.filter(function(t){return null!==t}):[]},l.quantity=function(){return(this.obj.properties||{}).quantity||1},l.currency=function(){return(this.obj.properties||{}).currency||"USD"},l.referrer=function(){return this.proxy("context.referrer.url")||this.proxy("context.page.referrer")||this.proxy("properties.referrer")},l.query=o.Facade.proxy("options.query"),l.properties=function(t){var e=this.field("properties")||{};for(var n in t=t||{})if(Object.prototype.hasOwnProperty.call(t,n)){var r=null==this[n]?this.proxy("properties."+n):this[n]();if(null==r)continue;e[t[n]]=r,delete e[n]}return e},l.username=function(){return this.proxy("traits.username")||this.proxy("properties.username")||this.userId()||this.sessionId()},l.email=function(){var t=this.proxy("traits.email")||this.proxy("properties.email")||this.proxy("options.traits.email");if(t)return t;var e=this.userId();if(a.default(e))return e},l.revenue=function(){var t=this.proxy("properties.revenue"),e=this.event();return!t&&e&&e.match(/^[ _]?completed[ _]?order[ _]?|^[ _]?order[ _]?completed[ _]?$/i)&&(t=this.proxy("properties.total")),function(t){if(t&&("number"==typeof t||"string"==typeof t&&!isNaN(t=parseFloat(t=t.replace(/\$/g,"")))))return t}(t)},l.cents=function(){var t=this.revenue();return"number"!=typeof t?this.value()||0:100*t},l.identify=function(){var t=this.json();return t.traits=this.traits(),new s.Identify(t,this.opts)}},85922:function(t,e,n){"use strict";var r=n(17754);t.exports=function t(e,n){var i,o;return(void 0===n&&(n=!0),e&&"object"==typeof e)?(i=n,Object.keys(e).forEach(function(n){e[n]=t(e[n],i)}),e):Array.isArray(e)?(o=n,e.forEach(function(n,r){e[r]=t(n,o)}),e):r.is(e,n)?r.parse(e):e}},17754:function(t,e){"use strict";var n=/^(\d{4})(?:-?(\d{2})(?:-?(\d{2}))?)?(?:([ T])(\d{2}):?(\d{2})(?::?(\d{2})(?:[,\.](\d{1,}))?)?(?:(Z)|([+\-])(\d{2})(?::?(\d{2}))?)?)?$/;e.parse=function(t){var e=[1,5,6,7,11,12],r=n.exec(t),i=0;if(!r)return new Date(t);for(var o,s=0;o=e[s];s++)r[o]=parseInt(r[o],10)||0;return r[2]=parseInt(r[2],10)||1,r[3]=parseInt(r[3],10)||1,r[2]--,r[8]=r[8]?(r[8]+"00").substring(0,3):0," "===r[4]?i=new Date().getTimezoneOffset():"Z"!==r[9]&&r[10]&&(i=60*r[11]+r[12],"+"===r[10]&&(i=0-i)),new Date(Date.UTC(r[1],r[2],r[3],r[5],r[6]+i,r[7],r[8]))},e.is=function(t,e){return"string"==typeof t&&(!e||!1!==/^\d{4}-\d{2}-\d{2}/.test(t))&&n.test(t)}},2461:function(t){"function"==typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}}},35919:function(t){t.exports=function(){}},88377:function(t,e,n){"use strict";var r=n(17754),i=n(7288),o=n(35653),s=Object.prototype.toString;t.exports=function(t){return"[object Date]"===s.call(t)?t:"[object Number]"===s.call(t)?new Date(t<315576e5?1e3*t:t):r.is(t)?r.parse(t):i.is(t)?i.parse(t):o.is(t)?o.parse(t):new Date(t)}},7288:function(t,e){"use strict";var n=/\d{13}/;e.is=function(t){return n.test(t)},e.parse=function(t){return new Date(t=parseInt(t,10))}},35653:function(t,e){"use strict";var n=/\d{10}/;e.is=function(t){return n.test(t)},e.parse=function(t){return new Date(1e3*parseInt(t,10))}},33836:function(t){"use strict";let e=self.fetch.bind(self);t.exports=e,t.exports.default=t.exports},2560:function(t){function e(t){return function(e,n,r,o){var s,a=o&&"function"==typeof o.normalizer?o.normalizer:i;n=a(n);for(var u=!1;!u;)!function(){for(s in e){var t=a(s);if(0===n.indexOf(t)){var r=n.substr(t.length);if("."===r.charAt(0)||0===r.length){n=r.substr(1);var i=e[s];if(null==i||!n.length){u=!0;return}e=i;return}}}s=void 0,u=!0}();if(s)return null==e?e:t(e,s,r)}}function n(t,e){return t.hasOwnProperty(e)&&delete t[e],t}function r(t,e,n){return t.hasOwnProperty(e)&&(t[e]=n),t}function i(t){return t.replace(/[^a-zA-Z0-9\.]+/g,"").toLowerCase()}t.exports=e(function(t,e){if(t.hasOwnProperty(e))return t[e]}),t.exports.find=t.exports,t.exports.replace=function(t,n,i,o){return e(r).call(this,t,n,i,o),t},t.exports.del=function(t,r,i){return e(n).call(this,t,r,null,i),t}},15156:function(t,e,n){"use strict";n.d(e,{v4:function(){return s}});for(var r,i=256,o=[];i--;)o[i]=(i+256).toString(16).substring(1);function s(){var t,e=0,n="";if(!r||i+16>256){for(r=Array(e=256);e--;)r[e]=256*Math.random()|0;e=i=0}for(;e<16;e++)t=r[i+e],6==e?n+=o[15&t|64]:8==e?n+=o[63&t|128]:n+=o[t],1&e&&e>1&&e<11&&(n+="-");return i++,n}},59681:function(t,e,n){"use strict";function r(t,e,n){e.split&&(e=e.split("."));for(var r,i,o=0,s=e.length,a=t;o<s&&"__proto__"!=(i=""+e[o++])&&"constructor"!==i&&"prototype"!==i;)a=a[i]=o===s?n:typeof(r=a[i])==typeof e?r:0*e[o]!=0||~(""+e[o]).indexOf(".")?{}:[]}n.d(e,{N:function(){return r}})}}]);