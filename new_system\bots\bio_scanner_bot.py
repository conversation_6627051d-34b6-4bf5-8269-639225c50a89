#!/usr/bin/env python3
"""
Bio Scanner Bot - Instagram Profile Information Scanner
Replaces the original bio*.py functionality using instagrapi
"""

import os
import sys
import time
import json
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
import logging
import concurrent.futures
from pathlib import Path

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/bio_scanner_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BioScannerBot:
    """Instagram Profile Bio Scanner using instagrapi"""
    
    def __init__(self, account_id: int):
        self.account_id = account_id
        self.instagram_manager = InstagramManager()
        self.db_manager = DatabaseManager()
        self.config = Config()
        
        # File paths
        self.input_csv = f"C:/files/account_data_{account_id}.csv"
        self.output_csv = f"C:/files/instagram_profiles_data_{account_id}.csv"
        self.final_csv = f"C:/files/bio_{account_id}.csv"
        self.progress_file = f"C:/files/bio_{account_id}_progress.json"
        
        # Processing settings
        self.batch_size = 20  # Users per batch (like original)
        self.max_batches = 100  # Maximum batches per run
        self.max_workers = 10  # Concurrent threads for profile fetching
        
        # Proxy settings (using Evomi residential proxies)
        self.countries = self.config.PROXY_CONFIG.get('countries', [
            'US', 'UK', 'CA', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL'
        ])
        self.country_index = 0
        
        # Statistics
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.batch_count = 0
        
        logger.info(f"Bio Scanner Bot initialized for account {account_id}")
    
    def get_account_credentials(self) -> Tuple[str, str, str]:
        """Get account credentials from database"""
        try:
            account = self.db_manager.get_account_by_id(self.account_id)
            if not account:
                raise Exception(f"Account {self.account_id} not found in database")
            
            username = account['username']
            password = account['password']
            secret_key = account.get('secretkey', '').replace(" ", "")
            
            logger.info(f"Retrieved credentials for account: {username}")
            return username, password, secret_key
            
        except Exception as e:
            logger.error(f"Failed to get credentials: {e}")
            raise
    
    def get_next_proxy_country(self) -> str:
        """Get next country for proxy rotation"""
        country = self.countries[self.country_index]
        self.country_index = (self.country_index + 1) % len(self.countries)
        return country
    
    def setup_instagram_client(self) -> bool:
        """Setup Instagram client with fresh login"""
        try:
            # Get credentials
            username, password, secret_key = self.get_account_credentials()
            
            logger.info(f"Setting up fresh login for account {username}")
            
            # Force fresh login using instagram_manager
            success = self.instagram_manager.add_account(
                account_id=self.account_id,
                username=username,
                password=password,
                secret_key=secret_key
            )
            
            if success:
                # Now login the account
                login_success = self.instagram_manager.login_account(self.account_id)
                if login_success:
                    logger.info(f"Fresh login successful for account {username}")
                    return True
                else:
                    logger.error(f"Fresh login failed for account {username}")
                    return False
            else:
                logger.error(f"Failed to add account {username} to manager")
                return False
                
        except Exception as e:
            logger.error(f"Failed to setup Instagram client: {e}")
            return False
    
    def load_input_data(self) -> List[Tuple[str, str, str, str]]:
        """Load input data from CSV file"""
        try:
            if not os.path.exists(self.input_csv):
                logger.error(f"Input CSV file not found: {self.input_csv}")
                return []
            
            # Load CSV
            df = pd.read_csv(self.input_csv)
            logger.info(f"Loaded {len(df)} rows from input CSV")
            
            # Check required columns
            required_cols = ['username', 'id1', 'id3', 'title']
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                logger.info(f"Available columns: {df.columns.tolist()}")
                return []
            
            # Filter out rows with missing usernames
            valid_data = df.dropna(subset=['username'])
            logger.info(f"Valid data rows: {len(valid_data)}")
            
            # Create list of tuples (username, id1, id3, title)
            user_data = []
            for _, row in valid_data.iterrows():
                username = str(row['username']).strip()
                id1 = str(row.get('id1', ''))
                id3 = str(row.get('id3', ''))
                title = str(row.get('title', ''))
                
                if username:  # Only add if username is not empty
                    user_data.append((username, id1, id3, title))
            
            logger.info(f"Total users to process: {len(user_data)}")
            return user_data
            
        except Exception as e:
            logger.error(f"Error loading input data: {e}")
            return []
    
    def load_progress(self) -> Tuple[int, set]:
        """Load progress from previous runs"""
        start_index = 0
        processed_usernames = set()
        
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    start_index = progress_data.get('last_index', 0)
                    processed_usernames = set(progress_data.get('processed_usernames', []))
                    logger.info(f"Resuming from index {start_index}")
                    logger.info(f"Already processed {len(processed_usernames)} usernames")
            except Exception as e:
                logger.warning(f"Could not load progress file: {e}")
        
        # Also load existing results if available
        if os.path.exists(self.output_csv):
            try:
                existing_df = pd.read_csv(self.output_csv)
                if 'username' in existing_df.columns:
                    existing_usernames = existing_df['username'].tolist()
                    processed_usernames.update(existing_usernames)
                    logger.info(f"Loaded {len(existing_df)} existing results")
            except Exception as e:
                logger.warning(f"Could not load existing output file: {e}")
        
        return start_index, processed_usernames
    
    def save_progress(self, last_index: int, processed_usernames: set):
        """Save progress to file"""
        try:
            progress_data = {
                'last_index': last_index,
                'processed_usernames': list(processed_usernames),
                'timestamp': datetime.now().isoformat()
            }
            
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2)
                
            logger.debug(f"Progress saved: index {last_index}, total processed: {len(processed_usernames)}")
            
        except Exception as e:
            logger.error(f"Error saving progress: {e}")
    
    def get_user_profile_data(self, username: str, id1: str, id3: str, title: str) -> Dict[str, Any]:
        """Get user profile data using instagrapi"""
        try:
            # Get user info using Instagram manager
            user_info = self.instagram_manager.get_user_info(self.account_id, username)
            
            if user_info:
                result = {
                    'username': username,
                    'id1': id1,
                    'id3': id3,
                    'title': title,
                    'bio': user_info.get('biography', ''),
                    'posts': user_info.get('media_count', 0),
                    'followers': user_info.get('follower_count', 0),
                    'following': user_info.get('following_count', 0),
                    'is_verified': user_info.get('is_verified', False),
                    'is_private': user_info.get('is_private', False),
                    'full_name': user_info.get('full_name', '')
                }
                
                # Save to database
                try:
                    self.db_manager.save_user_data(self.account_id, {
                        'user_id': user_info.get('user_id'),
                        'username': username,
                        'full_name': user_info.get('full_name', ''),
                        'biography': user_info.get('biography', ''),
                        'follower_count': user_info.get('follower_count', 0),
                        'following_count': user_info.get('following_count', 0),
                        'media_count': user_info.get('media_count', 0),
                        'is_private': user_info.get('is_private', False),
                        'is_verified': user_info.get('is_verified', False),
                        'profile_pic_url': user_info.get('profile_pic_url', '')
                    })
                    logger.debug(f"Saved {username} to database")
                except Exception as e:
                    logger.error(f"Failed to save {username} to database: {e}")
                
                self.success_count += 1
                logger.info(f"Profile retrieved: {username} | Followers: {result['followers']}")
                return result
                
            else:
                # Profile not found or error
                self.error_count += 1
                logger.warning(f"Profile not found: {username}")
                return self._create_empty_profile(username, id1, id3, title)
                
        except Exception as e:
            self.error_count += 1
            logger.error(f"Error getting profile for {username}: {e}")
            return self._create_empty_profile(username, id1, id3, title)
    
    def _create_empty_profile(self, username: str, id1: str, id3: str, title: str) -> Dict[str, Any]:
        """Create empty profile data structure"""
        return {
            'username': username,
            'id1': id1,
            'id3': id3,
            'title': title,
            'bio': '',
            'posts': None,
            'followers': None,
            'following': None,
            'is_verified': False,
            'is_private': None,
            'full_name': ''
        }
    
    def process_batch_with_instagrapi(self, batch: List[Tuple[str, str, str, str]]) -> List[Dict[str, Any]]:
        """Process a batch of users using instagrapi (single account, sequential)"""
        results = []
        
        for username, id1, id3, title in batch:
            try:
                # Add small delay to avoid rate limiting
                time.sleep(1)
                
                # Get profile data
                profile_data = self.get_user_profile_data(username, id1, id3, title)
                results.append(profile_data)
                self.processed_count += 1
                
            except Exception as e:
                logger.error(f"Error processing {username}: {e}")
                results.append(self._create_empty_profile(username, id1, id3, title))
                self.error_count += 1
        
        return results
    
    def process_batch_with_proxy(self, batch: List[Tuple[str, str, str, str]], country_code: str) -> List[Dict[str, Any]]:
        """Process a batch of users using direct API calls with proxy (fallback method)"""
        results = []
        
        # Setup proxy for this batch
        proxy_config = self.config.PROXY_CONFIG
        proxy_url = f"http://{proxy_config['user_base']}-{country_code}:{proxy_config['password']}@{proxy_config['host']}:{proxy_config['port']}"
        
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'X-IG-App-ID': '936619743392459',
            'Referer': 'https://www.instagram.com/',
            'Origin': 'https://www.instagram.com',
        }
        
        for username, id1, id3, title in batch:
            try:
                import requests
                
                url = f"https://www.instagram.com/api/v1/users/web_profile_info/?username={username}"
                
                response = requests.get(url, headers=headers, proxies=proxies, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    user_data = data.get('data', {}).get('user', {})
                    
                    if user_data:
                        result = {
                            'username': username,
                            'id1': id1,
                            'id3': id3,
                            'title': title,
                            'bio': user_data.get('biography', ''),
                            'posts': user_data.get('edge_owner_to_timeline_media', {}).get('count'),
                            'followers': user_data.get('edge_followed_by', {}).get('count'),
                            'following': user_data.get('edge_follow', {}).get('count'),
                            'is_verified': user_data.get('is_verified', False),
                            'is_private': user_data.get('is_private', False),
                            'full_name': user_data.get('full_name', '')
                        }
                        self.success_count += 1
                        logger.info(f"Profile retrieved (proxy): {username} | Followers: {result['followers']}")
                    else:
                        result = self._create_empty_profile(username, id1, id3, title)
                        self.error_count += 1
                else:
                    result = self._create_empty_profile(username, id1, id3, title)
                    self.error_count += 1
                    logger.warning(f"HTTP Error {response.status_code} for {username}")
                
                results.append(result)
                self.processed_count += 1
                
                # Small delay between requests
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error processing {username} with proxy: {e}")
                results.append(self._create_empty_profile(username, id1, id3, title))
                self.error_count += 1
        
        return results
    
    def save_results(self, results: List[Dict[str, Any]]):
        """Save results to CSV files"""
        try:
            if not results:
                return
            
            # Load existing results if file exists
            if os.path.exists(self.output_csv):
                try:
                    existing_df = pd.read_csv(self.output_csv)
                    results_df = existing_df
                except Exception as e:
                    logger.warning(f"Could not load existing output file: {e}")
                    results_df = pd.DataFrame()
            else:
                results_df = pd.DataFrame()
            
            # Add new results
            new_df = pd.DataFrame(results)
            if not results_df.empty:
                results_df = pd.concat([results_df, new_df], ignore_index=True)
            else:
                results_df = new_df
            
            # Remove duplicates
            results_df = results_df.drop_duplicates(subset=['username'], keep='last')
            
            # Save raw results
            results_df.to_csv(self.output_csv, index=False, encoding='utf-8')
            logger.info(f"Saved {len(results)} new results to {self.output_csv}")
            
            # Create final processed file (combine stats into bio like original)
            self.create_final_output(results_df)
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def create_final_output(self, results_df: pd.DataFrame):
        """Create final output file with combined bio data"""
        try:
            if results_df.empty:
                return
            
            final_df = results_df.copy()
            
            # Combine fields into bio (like original script)
            for index, row in final_df.iterrows():
                bio_text = str(row['bio']) if pd.notna(row['bio']) else ""
                
                # Format the stats directly in bio as requested
                stats_text = ""
                if (pd.notna(row['posts']) and pd.notna(row['followers']) and 
                    pd.notna(row['following'])):
                    stats_text = f"\nposts{row['posts']}followers{row['followers']}following{row['following']}"
                
                # Append stats to bio
                final_df.at[index, 'bio'] = bio_text + stats_text
            
            # Keep only required columns
            final_df = final_df[['username', 'id1', 'id3', 'title', 'bio']]
            final_df.to_csv(self.final_csv, index=False, encoding='utf-8')
            
            logger.info(f"Final processed data saved to: {self.final_csv}")
            
        except Exception as e:
            logger.error(f"Error creating final output: {e}")
    
    def run(self, use_proxy_fallback: bool = True) -> Dict[str, Any]:
        """Run the bio scanner bot"""
        try:
            start_time = time.time()
            logger.info(f"Starting Bio Scanner Bot for account {self.account_id}")
            
            # Load input data
            user_data = self.load_input_data()
            if not user_data:
                raise Exception("No user data to process")
            
            # Load progress
            start_index, processed_usernames = self.load_progress()
            
            # Filter out already processed users
            user_data = [
                (username, id1, id3, title) for username, id1, id3, title in user_data
                if username not in processed_usernames
            ]
            
            logger.info(f"Users remaining to process: {len(user_data)}")
            
            if not user_data:
                logger.info("All users already processed")
                return {
                    'account_id': self.account_id,
                    'status': 'completed',
                    'message': 'All users already processed'
                }
            
            # Setup Instagram client (primary method)
            instagram_available = self.setup_instagram_client()
            
            # Process users in batches
            last_processed_index = start_index
            results_batch = []
            
            for i in range(0, len(user_data), self.batch_size):
                if self.batch_count >= self.max_batches:
                    logger.info(f"Reached maximum of {self.max_batches} batches, stopping")
                    break
                
                batch = user_data[i:i + self.batch_size]
                self.batch_count += 1
                
                logger.info(f"Processing batch {self.batch_count}/{self.max_batches} ({len(batch)} profiles)")
                
                try:
                    # Primary method: Use instagrapi if available
                    if instagram_available:
                        batch_results = self.process_batch_with_instagrapi(batch)
                    
                    # Fallback method: Use proxy with direct API calls
                    elif use_proxy_fallback:
                        country_code = self.get_next_proxy_country()
                        logger.info(f"Using proxy fallback with country: {country_code}")
                        batch_results = self.process_batch_with_proxy(batch, country_code)
                    
                    else:
                        logger.error("No processing method available")
                        break
                    
                    # Collect results
                    results_batch.extend(batch_results)
                    
                    # Update processed usernames
                    for result in batch_results:
                        processed_usernames.add(result['username'])
                    
                    # Save progress and results after each batch
                    last_processed_index = start_index + i + self.batch_size
                    self.save_progress(last_processed_index, processed_usernames)
                    self.save_results(batch_results)
                    
                    logger.info(f"Batch {self.batch_count} completed - Success: {self.success_count}, Errors: {self.error_count}")
                    
                    # Brief pause between batches
                    if i + self.batch_size < len(user_data):
                        time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"Error processing batch {self.batch_count}: {e}")
                    continue
            
            # Calculate final statistics
            total_time = time.time() - start_time
            
            # Clean up progress file if all done
            if last_processed_index >= len(user_data):
                try:
                    if os.path.exists(self.progress_file):
                        os.remove(self.progress_file)
                        logger.info("Progress file removed - all users processed")
                except Exception as e:
                    logger.warning(f"Could not remove progress file: {e}")
            
            results = {
                'account_id': self.account_id,
                'status': 'completed',
                'processing_time': f"{total_time:.2f} seconds",
                'batches_processed': self.batch_count,
                'total_profiles_processed': self.processed_count,
                'successful_profiles': self.success_count,
                'failed_profiles': self.error_count,
                'success_rate': f"{(self.success_count / max(self.processed_count, 1) * 100):.1f}%",
                'output_files': {
                    'raw_data': self.output_csv,
                    'final_bio_data': self.final_csv
                }
            }
            
            logger.info("=== Bio Scanning Complete ===")
            logger.info(f"Total time: {total_time:.2f} seconds")
            logger.info(f"Batches processed: {self.batch_count}")
            logger.info(f"Profiles processed: {self.processed_count}")
            logger.info(f"Successful: {self.success_count}")
            logger.info(f"Failed: {self.error_count}")
            
            return results
            
        except Exception as e:
            logger.error(f"Critical error in bio scanner bot: {e}")
            return {
                'account_id': self.account_id,
                'status': 'error',
                'error': str(e),
                'profiles_processed': self.processed_count,
                'successful_profiles': self.success_count,
                'failed_profiles': self.error_count
            }

def main():
    """Main function for standalone execution"""
    if len(sys.argv) < 2:
        print("Usage: python bio_scanner_bot.py <account_id> [use_proxy_fallback]")
        print("Example: python bio_scanner_bot.py 1")
        print("Example: python bio_scanner_bot.py 1 true")
        sys.exit(1)
    
    try:
        account_id = int(sys.argv[1])
        use_proxy_fallback = len(sys.argv) > 2 and sys.argv[2].lower() == 'true'
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Run bio scanner bot
        bot = BioScannerBot(account_id)
        results = bot.run(use_proxy_fallback)
        
        print("\n=== Final Results ===")
        for key, value in results.items():
            print(f"{key}: {value}")
        
    except ValueError:
        print("Account ID must be a number")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()










