(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7879],{44713:function(e,t,n){var r=0/0,i=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,c=/^0o[0-7]+$/i,a=parseInt,s="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,l="object"==typeof self&&self&&self.Object===Object&&self,f=s||l||Function("return this")(),d=Object.prototype.toString,v=Math.max,g=Math.min,w=function(){return f.Date.now()};function h(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function y(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==d.call(t))return r;if(h(e)){var t,n="function"==typeof e.valueOf?e.valueOf():e;e=h(n)?n+"":n}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var s=u.test(e);return s||c.test(e)?a(e.slice(2),s?2:8):o.test(e)?r:+e}e.exports=function(e,t,n){var r,i,o,u,c,a,s=0,l=!1,f=!1,d=!0;if("function"!=typeof e)throw TypeError("Expected a function");function b(t){var n=r,o=i;return r=i=void 0,s=t,u=e.apply(o,n)}function S(e){var n=e-a,r=e-s;return void 0===a||n>=t||n<0||f&&r>=o}function p(){var e,n,r,i=w();if(S(i))return m(i);c=setTimeout(p,(e=i-a,n=i-s,r=t-e,f?g(r,o-n):r))}function m(e){return(c=void 0,d&&r)?b(e):(r=i=void 0,u)}function E(){var e,n=w(),o=S(n);if(r=arguments,i=this,a=n,o){if(void 0===c)return s=e=a,c=setTimeout(p,t),l?b(e):u;if(f)return c=setTimeout(p,t),b(a)}return void 0===c&&(c=setTimeout(p,t)),u}return t=y(t)||0,h(n)&&(l=!!n.leading,o=(f="maxWait"in n)?v(y(n.maxWait)||0,t):o,d="trailing"in n?!!n.trailing:d),E.cancel=function(){void 0!==c&&clearTimeout(c),s=0,r=a=i=c=void 0},E.flush=function(){return void 0===c?u:m(w())},E}},77879:function(e,t,n){"use strict";n.d(t,{Cf:function(){return s},KS:function(){return x},OR:function(){return c},O_:function(){return S},S1:function(){return b},Xs:function(){return O},Yz:function(){return l},_:function(){return v},ac:function(){return w},g4:function(){return R},iP:function(){return T},kt:function(){return o},m9:function(){return a},nj:function(){return y},t$:function(){return m},tm:function(){return p},yU:function(){return k}});var r=n(7653),i=n(44713);function o(e=!1){if("boolean"!=typeof e)throw Error("defaultValue must be `true` or `false`");let[t,n]=(0,r.useState)(e),i=(0,r.useCallback)(()=>{n(!0)},[]),o=(0,r.useCallback)(()=>{n(!1)},[]),u=(0,r.useCallback)(()=>{n(e=>!e)},[]);return{value:t,setValue:n,setTrue:i,setFalse:o,toggle:u}}var u="undefined"!=typeof window?r.useLayoutEffect:r.useEffect;function c(e,t,n,i){let o=(0,r.useRef)(t);u(()=>{o.current=t},[t]),(0,r.useEffect)(()=>{let t=(null==n?void 0:n.current)??window;if(!(t&&t.addEventListener))return;let r=e=>{o.current(e)};return t.addEventListener(e,r,i),()=>{t.removeEventListener(e,r,i)}},[e,n,i])}function a(){let[e,t]=(0,r.useState)(null);return[e,(0,r.useCallback)(async e=>{if(!(null==navigator?void 0:navigator.clipboard))return console.warn("Clipboard not supported"),!1;try{return await navigator.clipboard.writeText(e),t(e),!0}catch(e){return console.warn("Copy failed",e),t(null),!1}},[])]}function s(e){let[t,n]=(0,r.useState)(e??0);return{count:t,increment:(0,r.useCallback)(()=>{n(e=>e+1)},[]),decrement:(0,r.useCallback)(()=>{n(e=>e-1)},[]),reset:(0,r.useCallback)(()=>{n(e??0)},[e]),setCount:n}}function l(e,t){let n=(0,r.useRef)(e);u(()=>{n.current=e},[e]),(0,r.useEffect)(()=>{if(null===t)return;let e=setInterval(()=>{n.current()},t);return()=>{clearInterval(e)}},[t])}function f(e){let t=(0,r.useRef)(()=>{throw Error("Cannot call an event handler while rendering.")});return u(()=>{t.current=e},[e]),(0,r.useCallback)((...e)=>{var n;return null==(n=t.current)?void 0:n.call(t,...e)},[t])}var d="undefined"==typeof window;function v(e,t,n={}){let{initializeWithValue:i=!0}=n,o=(0,r.useCallback)(e=>n.serializer?n.serializer(e):JSON.stringify(e),[n]),u=(0,r.useCallback)(e=>{let r;if(n.deserializer)return n.deserializer(e);if("undefined"===e)return;let i=t instanceof Function?t():t;try{r=JSON.parse(e)}catch(e){return console.error("Error parsing JSON:",e),i}return r},[n,t]),a=(0,r.useCallback)(()=>{let n=t instanceof Function?t():t;if(d)return n;try{let t=window.localStorage.getItem(e);return t?u(t):n}catch(t){return console.warn(`Error reading localStorage key \u201C${e}\u201D:`,t),n}},[t,e,u]),[s,l]=(0,r.useState)(()=>i?a():t instanceof Function?t():t),v=f(t=>{d&&console.warn(`Tried setting localStorage key \u201C${e}\u201D even though environment is not a client`);try{let n=t instanceof Function?t(a()):t;window.localStorage.setItem(e,o(n)),l(n),window.dispatchEvent(new StorageEvent("local-storage",{key:e}))}catch(t){console.warn(`Error setting localStorage key \u201C${e}\u201D:`,t)}}),g=f(()=>{d&&console.warn(`Tried removing localStorage key \u201C${e}\u201D even though environment is not a client`);let n=t instanceof Function?t():t;window.localStorage.removeItem(e),l(n),window.dispatchEvent(new StorageEvent("local-storage",{key:e}))});(0,r.useEffect)(()=>{l(a())},[e]);let w=(0,r.useCallback)(t=>{t.key&&t.key!==e||l(a())},[e,a]);return c("storage",w),c("local-storage",w),[s,v,g]}var g="undefined"==typeof window;function w(e,{defaultValue:t=!1,initializeWithValue:n=!0}={}){let i=e=>g?t:window.matchMedia(e).matches,[o,c]=(0,r.useState)(()=>n?i(e):t);function a(){c(i(e))}return u(()=>{let t=window.matchMedia(e);return a(),t.addListener?t.addListener(a):t.addEventListener("change",a),()=>{t.removeListener?t.removeListener(a):t.removeEventListener("change",a)}},[e]),o}function h(e,t=500,n){let o=(0,r.useRef)();!function(e){let t=(0,r.useRef)(e);t.current=e,(0,r.useEffect)(()=>()=>{t.current()},[])}(()=>{o.current&&o.current.cancel()});let u=(0,r.useMemo)(()=>{let r=i(e,t,n),u=(...e)=>r(...e);return u.cancel=()=>{r.cancel()},u.isPending=()=>!!o.current,u.flush=()=>r.flush(),u},[e,t,n]);return(0,r.useEffect)(()=>{o.current=i(e,t,n)},[e,t,n]),u}function y(e,t,n){let i=(null==n?void 0:n.equalityFn)??((e,t)=>e===t),o=e instanceof Function?e():e,[u,c]=(0,r.useState)(o),a=(0,r.useRef)(o),s=h(c,t,n);return i(a.current,o)||(s(o),a.current=o),[u,s]}function b({threshold:e=0,root:t=null,rootMargin:n="0%",freezeOnceVisible:i=!1,initialIsIntersecting:o=!1,onChange:u}={}){var c;let[a,s]=(0,r.useState)(null),[l,f]=(0,r.useState)(()=>({isIntersecting:o,entry:void 0})),d=(0,r.useRef)();d.current=u;let v=(null==(c=l.entry)?void 0:c.isIntersecting)&&i;(0,r.useEffect)(()=>{let r;if(!a||!("IntersectionObserver"in window)||v)return;let o=new IntersectionObserver(e=>{let t=Array.isArray(o.thresholds)?o.thresholds:[o.thresholds];e.forEach(e=>{let n=e.isIntersecting&&t.some(t=>e.intersectionRatio>=t);f({isIntersecting:n,entry:e}),d.current&&d.current(n,e),n&&i&&r&&(r(),r=void 0)})},{threshold:e,root:t,rootMargin:n});return o.observe(a),()=>{o.disconnect()}},[a,JSON.stringify(e),t,n,v,i]);let g=(0,r.useRef)(null);(0,r.useEffect)(()=>{var e;a||null==(e=l.entry)||!e.target||i||v||g.current===l.entry.target||(g.current=l.entry.target,f({isIntersecting:o,entry:void 0}))},[a,l.entry,i,v,o]);let w=[s,!!l.isIntersecting,l.entry];return w.ref=w[0],w.isIntersecting=w[1],w.entry=w[2],w}function S(){let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{t(!0)},[]),e}function p(){let e=(0,r.useRef)(!1);return(0,r.useEffect)(()=>(e.current=!0,()=>{e.current=!1}),[]),(0,r.useCallback)(()=>e.current,[])}function m(e,t,n="mousedown",r={}){c(n,n=>{let r=n.target;r&&r.isConnected&&(Array.isArray(e)?e.filter(e=>!!e.current).every(e=>e.current&&!e.current.contains(r)):e.current&&!e.current.contains(r))&&t(n)},void 0,r)}var E={width:void 0,height:void 0};function k(e){let{ref:t,box:n="content-box"}=e,[{width:i,height:o},u]=(0,r.useState)(E),c=p(),a=(0,r.useRef)({...E}),s=(0,r.useRef)(void 0);return s.current=e.onResize,(0,r.useEffect)(()=>{if(!t.current||"undefined"==typeof window||!("ResizeObserver"in window))return;let e=new ResizeObserver(([e])=>{let t="border-box"===n?"borderBoxSize":"device-pixel-content-box"===n?"devicePixelContentBoxSize":"contentBoxSize",r=C(e,t,"inlineSize"),i=C(e,t,"blockSize");if(a.current.width!==r||a.current.height!==i){let e={width:r,height:i};a.current.width=r,a.current.height=i,s.current?s.current(e):c()&&u(e)}});return e.observe(t.current,{box:n}),()=>{e.disconnect()}},[n,t,c]),{width:i,height:o}}function C(e,t,n){return e[t]?Array.isArray(e[t])?e[t][0][n]:e[t][n]:"contentBoxSize"===t?e.contentRect["inlineSize"===n?"width":"height"]:void 0}var z="undefined"==typeof window;function O(e,t,n={}){let{initializeWithValue:i=!0}=n,o=(0,r.useCallback)(e=>n.serializer?n.serializer(e):JSON.stringify(e),[n]),u=(0,r.useCallback)(e=>{let r;if(n.deserializer)return n.deserializer(e);if("undefined"===e)return;let i=t instanceof Function?t():t;try{r=JSON.parse(e)}catch(e){return console.error("Error parsing JSON:",e),i}return r},[n,t]),a=(0,r.useCallback)(()=>{let n=t instanceof Function?t():t;if(z)return n;try{let t=window.sessionStorage.getItem(e);return t?u(t):n}catch(t){return console.warn(`Error reading sessionStorage key \u201C${e}\u201D:`,t),n}},[t,e,u]),[s,l]=(0,r.useState)(()=>i?a():t instanceof Function?t():t),d=f(t=>{z&&console.warn(`Tried setting sessionStorage key \u201C${e}\u201D even though environment is not a client`);try{let n=t instanceof Function?t(a()):t;window.sessionStorage.setItem(e,o(n)),l(n),window.dispatchEvent(new StorageEvent("session-storage",{key:e}))}catch(t){console.warn(`Error setting sessionStorage key \u201C${e}\u201D:`,t)}}),v=f(()=>{z&&console.warn(`Tried removing sessionStorage key \u201C${e}\u201D even though environment is not a client`);let n=t instanceof Function?t():t;window.sessionStorage.removeItem(e),l(n),window.dispatchEvent(new StorageEvent("session-storage",{key:e}))});(0,r.useEffect)(()=>{l(a())},[e]);let g=(0,r.useCallback)(t=>{t.key&&t.key!==e||l(a())},[e,a]);return c("storage",g),c("session-storage",g),[s,d,v]}function R(e){let[t,n]=(0,r.useState)(1),i=t+1<=e,o=t-1>0,u=(0,r.useCallback)(r=>{let i=r instanceof Function?r(t):r;if(i>=1&&i<=e){n(i);return}throw Error("Step not valid")},[e,t]);return[t,{goToNextStep:(0,r.useCallback)(()=>{i&&n(e=>e+1)},[i]),goToPrevStep:(0,r.useCallback)(()=>{o&&n(e=>e-1)},[o]),canGoToNextStep:i,canGoToPrevStep:o,setStep:u,reset:(0,r.useCallback)(()=>{n(1)},[])}]}function x(e,t){let n=(0,r.useRef)(e);u(()=>{n.current=e},[e]),(0,r.useEffect)(()=>{if(!t&&0!==t)return;let e=setTimeout(()=>{n.current()},t);return()=>{clearTimeout(e)}},[t])}var I="undefined"==typeof window;function T(e={}){let{initializeWithValue:t=!0}=e;I&&(t=!1);let[n,i]=(0,r.useState)(()=>t?{width:window.innerWidth,height:window.innerHeight}:{width:void 0,height:void 0}),o=h(i,e.debounceDelay);function a(){(e.debounceDelay?o:i)({width:window.innerWidth,height:window.innerHeight})}return c("resize",a),u(()=>{a()},[]),n}}}]);