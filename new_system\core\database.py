#!/usr/bin/env python3
"""
Database Manager - Handles all MySQL database operations
"""

import os
import logging
import mysql.connector
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database manager for MySQL operations"""
    
    def __init__(self):
        # Database configuration
        self.config = {
            'host': 'localhost',
            'user': 'root',
            'password': '8915841@@',  # From original system
            'database': 'instaadmin',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        # Test connection on initialization
        try:
            self._test_connection()
            logger.info("Database Manager initialized")
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            raise
    
    def _test_connection(self) -> bool:
        """Test database connection"""
        try:
            conn = self.get_connection()
            if not conn:
                raise Exception("Failed to get database connection")
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    def get_connection(self):
        """Get a database connection"""
        try:
            conn = mysql.connector.connect(**self.config)
            return conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return None
    
    def execute_query(self, query: str, params: Tuple = None, fetch: bool = False) -> Optional[List[Any]]:
        """Execute SQL query with optional parameters"""
        try:
            conn = self.get_connection()
            if not conn:
                raise Exception("Failed to get database connection")
            
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params or ())
            
            if fetch:
                result = cursor.fetchall()
                cursor.close()
                conn.close()
                return result
            else:
                conn.commit()
                cursor.close()
                conn.close()
                return None
                
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
    
    # Account Management
    def get_all_accounts(self) -> List[Dict[str, Any]]:
        """Get all Instagram accounts from database"""
        query = """
        SELECT id, username, password, secretkey, executive, 
               timestamp, isDelete
        FROM users 
        WHERE isDelete = 0
        ORDER BY id
        """
        
        try:
            results = self.execute_query(query, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching accounts: {e}")
            return []
    
    def get_account_by_id(self, account_id: int) -> Optional[Dict[str, Any]]:
        """Get account by ID"""
        query = "SELECT * FROM users WHERE id = %s"
        
        try:
            results = self.execute_query(query, (account_id,), fetch=True)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error fetching account {account_id}: {e}")
            return None
    
    def update_account_status(self, account_id: int, status: str) -> bool:
        """Update account status"""
        query = "UPDATE users SET timestamp = %s WHERE id = %s"
        
        try:
            self.execute_query(query, (datetime.now(), account_id))
            return True
        except Exception as e:
            logger.error(f"Error updating account status: {e}")
            return False
    
    # Message Management
    def get_all_messages(self) -> List[Dict[str, Any]]:
        """Get all message templates"""
        query = "SELECT * FROM messages ORDER BY id"
        
        try:
            results = self.execute_query(query, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching message templates: {e}")
            return []
    
    def get_message_by_id(self, message_id: int) -> Optional[Dict[str, Any]]:
        """Get message template by ID"""
        query = "SELECT * FROM messages WHERE id = %s"
        
        try:
            results = self.execute_query(query, (message_id,), fetch=True)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error fetching message template {message_id}: {e}")
            return None
    
    def get_message_images(self) -> List[Dict[str, Any]]:
        """Get all message images"""
        query = "SELECT * FROM message_images ORDER BY id"
        
        try:
            results = self.execute_query(query, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching message images: {e}")
            return []
    
    def get_message_image_by_id(self, image_id: int) -> Optional[Dict[str, Any]]:
        """Get message image by ID"""
        query = "SELECT * FROM message_images WHERE id = %s"
        
        try:
            results = self.execute_query(query, (image_id,), fetch=True)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error fetching message image {image_id}: {e}")
            return None
    
    def log_message(self, account_id: int, recipient_username: str, 
                   message_content: str, message_type: str, 
                   status: str, sent_at: datetime) -> bool:
        """Log message sending attempt"""
        query = """
        INSERT INTO message_logs 
        (account_id, recipient_username, message_content, message_type, status, sent_at)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        try:
            self.execute_query(query, (
                account_id, recipient_username, message_content, 
                message_type, status, sent_at
            ))
            return True
        except Exception as e:
            logger.error(f"Error logging message: {e}")
            return False
    
    def get_message_logs(self, account_id: int = None) -> List[Dict[str, Any]]:
        """Get message logs"""
        if account_id:
            query = "SELECT * FROM message_logs WHERE account_id = %s ORDER BY sent_at DESC"
            params = (account_id,)
        else:
            query = "SELECT * FROM message_logs ORDER BY sent_at DESC"
            params = None
        
        try:
            results = self.execute_query(query, params, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching message logs: {e}")
            return []
    
    # Scraped Users Management
    def save_user_data(self, username: str, user_id: str = None, full_name: str = "",
                      biography: str = "", followers_count: int = 0, following_count: int = 0,
                      profile_pic_url: str = "", is_verified: bool = False, is_private: bool = False,
                      external_url: str = "", account_type: str = "", business_category_name: str = "",
                      category_name: str = "", category: str = "", public_phone_number: str = "",
                      public_email: str = "", city_name: str = "", address_street: str = "",
                      zip: str = "", latitude: float = None, longitude: float = None,
                      contact_phone_number: str = "", contact_email: str = "", website: str = "",
                      scan_date: datetime = None, account_id: int = None) -> bool:
        """Save scraped user data to database"""
        query = """
        INSERT INTO scraped_users (
            username, user_id, full_name, biography, followers_count, following_count,
            profile_pic_url, is_verified, is_private, external_url, account_type,
            business_category_name, category_name, category, public_phone_number,
            public_email, city_name, address_street, zip, latitude, longitude,
            contact_phone_number, contact_email, website, scan_date, account_id
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        """
        
        try:
            self.execute_query(query, (
                username, user_id, full_name, biography, followers_count, following_count,
                profile_pic_url, is_verified, is_private, external_url, account_type,
                business_category_name, category_name, category, public_phone_number,
                public_email, city_name, address_street, zip, latitude, longitude,
                contact_phone_number, contact_email, website, scan_date or datetime.now(), account_id
            ))
            return True
        except Exception as e:
            logger.error(f"Error saving user data: {e}")
            return False
    
    def get_scraped_users(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get scraped users from database"""
        query = "SELECT * FROM scraped_users ORDER BY scan_date DESC LIMIT %s"
        
        try:
            results = self.execute_query(query, (limit,), fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching scraped users: {e}")
            return []
    
    def get_scraped_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get scraped user by username"""
        query = "SELECT * FROM scraped_users WHERE username = %s"
        
        try:
            results = self.execute_query(query, (username,), fetch=True)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error fetching scraped user {username}: {e}")
            return None
    
    def clear_scraped_users(self) -> bool:
        """Clear all scraped users data"""
        query = "DELETE FROM scraped_users"
        
        try:
            self.execute_query(query)
            return True
        except Exception as e:
            logger.error(f"Error clearing scraped users: {e}")
            return False
    
    # Table Management
    def create_tables_if_not_exist(self) -> bool:
        """Create database tables if they don't exist"""
        tables = {
            'users': """
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(255) NOT NULL UNIQUE,
                password VARCHAR(255) NOT NULL,
                secretkey VARCHAR(255),
                executive TINYINT DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                isDelete TINYINT DEFAULT 0
            )
            """,
            'messages': """
            CREATE TABLE IF NOT EXISTS messages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                message_type VARCHAR(50) DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            'message_logs': """
            CREATE TABLE IF NOT EXISTS message_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT NOT NULL,
                recipient_username VARCHAR(255) NOT NULL,
                message_content TEXT NOT NULL,
                message_type VARCHAR(50) DEFAULT 'direct',
                status VARCHAR(50) NOT NULL,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                response_data TEXT,
                FOREIGN KEY (account_id) REFERENCES users(id)
            )
            """,
            'scraped_users': """
            CREATE TABLE IF NOT EXISTS scraped_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(255) NOT NULL,
                user_id VARCHAR(255),
                full_name VARCHAR(255),
                biography TEXT,
                followers_count INT DEFAULT 0,
                following_count INT DEFAULT 0,
                profile_pic_url TEXT,
                is_verified TINYINT DEFAULT 0,
                is_private TINYINT DEFAULT 0,
                external_url TEXT,
                account_type VARCHAR(100),
                business_category_name VARCHAR(255),
                category_name VARCHAR(255),
                category VARCHAR(100),
                public_phone_number VARCHAR(50),
                public_email VARCHAR(255),
                city_name VARCHAR(255),
                address_street TEXT,
                zip VARCHAR(20),
                latitude DECIMAL(10, 8),
                longitude DECIMAL(11, 8),
                contact_phone_number VARCHAR(50),
                contact_email VARCHAR(255),
                website TEXT,
                scan_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_scan_date (scan_date)
            )
            """
        }
        
        try:
            for table_name, create_sql in tables.items():
                self.execute_query(create_sql)
                logger.info(f"Table {table_name} created/verified")
            return True
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
