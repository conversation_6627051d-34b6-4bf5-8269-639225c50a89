"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9505],{88146:function(e,t,n){n.d(t,{default:function(){return o.a}});var r=n(16340),o=n.n(r)},3336:function(e,t,n){n.d(t,{T:function(){return l},f:function(){return a}});var r=n(17610),o=n(7653),i=n(78378);let l=(0,o.forwardRef)((e,t)=>(0,o.createElement)(i.WV.span,(0,r.Z)({},e,{ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}))),a=l},85638:function(e,t,n){let r;n.d(t,{VY:function(){return eh},h_:function(){return ev},zt:function(){return ec},fC:function(){return ep},xz:function(){return ef}});var o=n(17610),i=n(7653),l=n(46196),a=n(94492),s=n(99933),u=n(78378),d=n(523),c=n(22139);let p="dismissableLayer.update",f=(0,i.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),v=(0,i.forwardRef)((e,t)=>{var n;let{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:v,onPointerDownOutside:m,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...E}=e,x=(0,i.useContext)(f),[C,T]=(0,i.useState)(null),P=null!==(n=null==C?void 0:C.ownerDocument)&&void 0!==n?n:null==globalThis?void 0:globalThis.document,[,D]=(0,i.useState)({}),O=(0,a.e)(t,e=>T(e)),_=Array.from(x.layers),[L]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),R=_.indexOf(L),k=C?_.indexOf(C):-1,M=x.layersWithOutsidePointerEventsDisabled.size>0,W=k>=R,A=function(e,t=null==globalThis?void 0:globalThis.document){let n=(0,d.W)(e),r=(0,i.useRef)(!1),o=(0,i.useRef)(()=>{});return(0,i.useEffect)(()=>{let e=e=>{if(e.target&&!r.current){let r={originalEvent:e};function i(){g("dismissableLayer.pointerDownOutside",n,r,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=i,t.addEventListener("click",o.current,{once:!0})):i()}r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));!W||n||(null==m||m(e),null==w||w(e),e.defaultPrevented||null==b||b())},P),S=function(e,t=null==globalThis?void 0:globalThis.document){let n=(0,d.W)(e),r=(0,i.useRef)(!1);return(0,i.useEffect)(()=>{let e=e=>{e.target&&!r.current&&g("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},P);return(0,c.e)(e=>{k!==x.layers.size-1||(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},P),(0,i.useEffect)(()=>{if(C)return s&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(C)),x.layers.add(C),h(),()=>{s&&1===x.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[C,P,s,x]),(0,i.useEffect)(()=>()=>{C&&(x.layers.delete(C),x.layersWithOutsidePointerEventsDisabled.delete(C),h())},[C,x]),(0,i.useEffect)(()=>{let e=()=>D({});return document.addEventListener(p,e),()=>document.removeEventListener(p,e)},[]),(0,i.createElement)(u.WV.div,(0,o.Z)({},E,{ref:O,style:{pointerEvents:M?W?"auto":"none":void 0,...e.style},onFocusCapture:(0,l.M)(e.onFocusCapture,S.onFocusCapture),onBlurCapture:(0,l.M)(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:(0,l.M)(e.onPointerDownCapture,A.onPointerDownCapture)}))});function h(){let e=new CustomEvent(p);document.dispatchEvent(e)}function g(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,u.jH)(o,i):o.dispatchEvent(i)}var m=n(17321),y=n(32623),w=n(95152),b=n(37256),E=n(55918),x=n(81523),C=n(35032);let T="Popper",[P,D]=(0,s.b)(T),[O,_]=P(T),L=(0,i.forwardRef)((e,t)=>{let{__scopePopper:n,virtualRef:r,...l}=e,s=_("PopperAnchor",n),d=(0,i.useRef)(null),c=(0,a.e)(t,d);return(0,i.useEffect)(()=>{s.onAnchorChange((null==r?void 0:r.current)||d.current)}),r?null:(0,i.createElement)(u.WV.div,(0,o.Z)({},l,{ref:c}))}),R="PopperContent",[k,M]=P(R),W=(0,i.forwardRef)((e,t)=>{var n,r,l,s,c,p,f,v;let{__scopePopper:h,side:g="bottom",sideOffset:m=0,align:E="center",alignOffset:T=0,arrowPadding:P=0,collisionBoundary:D=[],collisionPadding:O=0,sticky:L="partial",hideWhenDetached:M=!1,avoidCollisions:W=!0,onPlaced:A,...$}=e,Z=_(R,h),[z,B]=(0,i.useState)(null),F=(0,a.e)(t,e=>B(e)),[V,Y]=(0,i.useState)(null),K=(0,C.t)(V),X=null!==(n=null==K?void 0:K.width)&&void 0!==n?n:0,j=null!==(r=null==K?void 0:K.height)&&void 0!==r?r:0,N="number"==typeof O?O:{top:0,right:0,bottom:0,left:0,...O},q=Array.isArray(D)?D:[D],G=q.length>0,J={padding:N,boundary:q.filter(S),altBoundary:G},{refs:Q,floatingStyles:U,placement:ee,isPositioned:et,middlewareData:en}=(0,y.YF)({strategy:"fixed",placement:g+("center"!==E?"-"+E:""),whileElementsMounted:w.Me,elements:{reference:Z.anchor},middleware:[(0,b.cv)({mainAxis:m+j,alignmentAxis:T}),W&&(0,b.uY)({mainAxis:!0,crossAxis:!1,limiter:"partial"===L?(0,b.dr)():void 0,...J}),W&&(0,b.RR)({...J}),(0,b.dp)({...J,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),V&&(0,y.x7)({element:V,padding:P}),H({arrowWidth:X,arrowHeight:j}),M&&(0,b.Cp)({strategy:"referenceHidden"})]}),[er,eo]=I(ee),ei=(0,d.W)(A);(0,x.b)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null===(l=en.arrow)||void 0===l?void 0:l.x,ea=null===(s=en.arrow)||void 0===s?void 0:s.y,es=(null===(c=en.arrow)||void 0===c?void 0:c.centerOffset)!==0,[eu,ed]=(0,i.useState)();return(0,x.b)(()=>{z&&ed(window.getComputedStyle(z).zIndex)},[z]),(0,i.createElement)("div",{ref:Q.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:et?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null===(p=en.transformOrigin)||void 0===p?void 0:p.x,null===(f=en.transformOrigin)||void 0===f?void 0:f.y].join(" ")},dir:e.dir},(0,i.createElement)(k,{scope:h,placedSide:er,onArrowChange:Y,arrowX:el,arrowY:ea,shouldHideArrow:es},(0,i.createElement)(u.WV.div,(0,o.Z)({"data-side":er,"data-align":eo},$,{ref:F,style:{...$.style,animation:et?void 0:"none",opacity:null!==(v=en.hide)&&void 0!==v&&v.referenceHidden?0:void 0}}))))}),A={top:"bottom",right:"left",bottom:"top",left:"right"};function S(e){return null!==e}let H=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,d=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,p=d?0:e.arrowHeight,[f,v]=I(a),h={start:"0%",center:"50%",end:"100%"}[v],g=(null!==(r=null===(o=u.arrow)||void 0===o?void 0:o.x)&&void 0!==r?r:0)+c/2,m=(null!==(i=null===(l=u.arrow)||void 0===l?void 0:l.y)&&void 0!==i?i:0)+p/2,y="",w="";return"bottom"===f?(y=d?h:`${g}px`,w=`${-p}px`):"top"===f?(y=d?h:`${g}px`,w=`${s.floating.height+p}px`):"right"===f?(y=`${-p}px`,w=d?h:`${m}px`):"left"===f&&(y=`${s.floating.width+p}px`,w=d?h:`${m}px`),{data:{x:y,y:w}}}});function I(e){let[t,n="center"]=e.split("-");return[t,n]}let $=e=>{let{__scopePopper:t,children:n}=e,[r,o]=(0,i.useState)(null);return(0,i.createElement)(O,{scope:t,anchor:r,onAnchorChange:o},n)};var Z=n(3458);let z=(0,i.forwardRef)((e,t)=>{var n;let{container:r=null==globalThis?void 0:null===(n=globalThis.document)||void 0===n?void 0:n.body,...l}=e;return r?Z.createPortal((0,i.createElement)(u.WV.div,(0,o.Z)({},l,{ref:t})),r):null});var B=n(65622),F=n(8828),V=n(47178),Y=n(3336);let[K,X]=(0,s.b)("Tooltip",[D]),j=D(),N="tooltip.open",[q,G]=K("TooltipProvider"),J="Tooltip",[Q,U]=K(J),ee="TooltipTrigger",et=(0,i.forwardRef)((e,t)=>{let{__scopeTooltip:n,...r}=e,s=U(ee,n),d=G(ee,n),c=j(n),p=(0,i.useRef)(null),f=(0,a.e)(t,p,s.onTriggerChange),v=(0,i.useRef)(!1),h=(0,i.useRef)(!1),g=(0,i.useCallback)(()=>v.current=!1,[]);return(0,i.useEffect)(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,i.createElement)(L,(0,o.Z)({asChild:!0},c),(0,i.createElement)(u.WV.button,(0,o.Z)({"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute},r,{ref:f,onPointerMove:(0,l.M)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||d.isPointerInTransitRef.current||(s.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,l.M)(e.onPointerLeave,()=>{s.onTriggerLeave(),h.current=!1}),onPointerDown:(0,l.M)(e.onPointerDown,()=>{v.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,l.M)(e.onFocus,()=>{v.current||s.onOpen()}),onBlur:(0,l.M)(e.onBlur,s.onClose),onClick:(0,l.M)(e.onClick,s.onClose)})))}),en="TooltipPortal",[er,eo]=K(en,{forceMount:void 0}),ei="TooltipContent",el=(0,i.forwardRef)((e,t)=>{let n=eo(ei,e.__scopeTooltip),{forceMount:r=n.forceMount,side:l="top",...a}=e,s=U(ei,e.__scopeTooltip);return(0,i.createElement)(B.z,{present:r||s.open},s.disableHoverableContent?(0,i.createElement)(ed,(0,o.Z)({side:l},a,{ref:t})):(0,i.createElement)(ea,(0,o.Z)({side:l},a,{ref:t})))}),ea=(0,i.forwardRef)((e,t)=>{let n=U(ei,e.__scopeTooltip),r=G(ei,e.__scopeTooltip),l=(0,i.useRef)(null),s=(0,a.e)(t,l),[u,d]=(0,i.useState)(null),{trigger:c,onClose:p}=n,f=l.current,{onPointerInTransitChange:v}=r,h=(0,i.useCallback)(()=>{d(null),v(!1)},[v]),g=(0,i.useCallback)((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),v(!0)},[v]);return(0,i.useEffect)(()=>()=>h(),[h]),(0,i.useEffect)(()=>{if(c&&f){let e=e=>g(e,f),t=e=>g(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,g,h]),(0,i.useEffect)(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,s=t[i].x,u=t[i].y;a>r!=u>r&&n<(s-l)*(r-a)/(u-a)+l&&(o=!o)}return o}(n,u);r?h():o&&(h(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,p,h]),(0,i.createElement)(ed,(0,o.Z)({},e,{ref:s}))}),[es,eu]=K(J,{isInside:!1}),ed=(0,i.forwardRef)((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":l,onEscapeKeyDown:a,onPointerDownOutside:s,...u}=e,d=U(ei,n),c=j(n),{onClose:p}=d;return(0,i.useEffect)(()=>(document.addEventListener(N,p),()=>document.removeEventListener(N,p)),[p]),(0,i.useEffect)(()=>{if(d.trigger){let e=e=>{let t=e.target;null!=t&&t.contains(d.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,i.createElement)(v,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:p},(0,i.createElement)(W,(0,o.Z)({"data-state":d.stateAttribute},c,u,{ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}}),(0,i.createElement)(F.A4,null,r),(0,i.createElement)(es,{scope:n,isInside:!0},(0,i.createElement)(Y.f,{id:d.contentId,role:"tooltip"},l||r))))}),ec=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:l}=e,[a,s]=(0,i.useState)(!0),u=(0,i.useRef)(!1),d=(0,i.useRef)(0);return(0,i.useEffect)(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,i.createElement)(q,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:(0,i.useCallback)(()=>{window.clearTimeout(d.current),s(!1)},[]),onClose:(0,i.useCallback)(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>s(!0),r)},[r]),isPointerInTransitRef:u,onPointerInTransitChange:(0,i.useCallback)(e=>{u.current=e},[]),disableHoverableContent:o},l)},ep=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:s}=e,u=G(J,e.__scopeTooltip),d=j(t),[c,p]=(0,i.useState)(null),f=(0,m.M)(),v=(0,i.useRef)(0),h=null!=a?a:u.disableHoverableContent,g=null!=s?s:u.delayDuration,y=(0,i.useRef)(!1),[w=!1,b]=(0,V.T)({prop:r,defaultProp:o,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(N))):u.onClose(),null==l||l(e)}}),E=(0,i.useMemo)(()=>w?y.current?"delayed-open":"instant-open":"closed",[w]),x=(0,i.useCallback)(()=>{window.clearTimeout(v.current),y.current=!1,b(!0)},[b]),C=(0,i.useCallback)(()=>{window.clearTimeout(v.current),b(!1)},[b]),T=(0,i.useCallback)(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{y.current=!0,b(!0)},g)},[g,b]);return(0,i.useEffect)(()=>()=>window.clearTimeout(v.current),[]),(0,i.createElement)($,d,(0,i.createElement)(Q,{scope:t,contentId:f,open:w,stateAttribute:E,trigger:c,onTriggerChange:p,onTriggerEnter:(0,i.useCallback)(()=>{u.isOpenDelayed?T():x()},[u.isOpenDelayed,T,x]),onTriggerLeave:(0,i.useCallback)(()=>{h?C():window.clearTimeout(v.current)},[C,h]),onOpen:x,onClose:C,disableHoverableContent:h},n))},ef=et,ev=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,l=U(en,t);return(0,i.createElement)(er,{scope:t,forceMount:n},(0,i.createElement)(B.z,{present:n||l.open},(0,i.createElement)(z,{asChild:!0,container:o},r)))},eh=el}}]);