#!/usr/bin/env python3
"""
Delete Manager Bot - Instagram Thread Deletion Manager
Replaces the original delete*.py functionality using instagrapi
"""

import os
import sys
import time
import random
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List, Tuple, Dict, Any
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/delete_manager_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeleteManagerBot:
    """Instagram Thread Delete Manager using instagrapi"""
    
    def __init__(self, account_id: int):
        self.account_id = account_id
        self.instagram_manager = InstagramManager()
        self.db_manager = DatabaseManager()
        self.config = Config()
        
        # File paths
        self.input_csv = f"C:/files/account_data_{account_id}.csv"
        self.deleted_log_csv = f"C:/files/deleted{account_id}.csv"
        
        # Session settings
        self.timeout_duration = 254  # 4+ minutes like original (but adjustable)
        self.session_start = datetime.now()
        self.session_end = self.session_start + timedelta(seconds=self.timeout_duration)
        
        # Statistics
        self.threads_processed = 0
        self.threads_deleted = 0
        self.threads_failed = 0
        self.threads_skipped = 0
        
        logger.info(f"Delete Manager Bot initialized for account {account_id}")
        logger.info(f"Session will timeout at: {self.session_end}")
    
    def get_account_credentials(self) -> Tuple[str, str, str]:
        """Get account credentials from database"""
        try:
            account = self.db_manager.get_account_by_id(self.account_id)
            if not account:
                raise Exception(f"Account {self.account_id} not found in database")
            
            username = account['username']
            password = account['password']
            secret_key = account.get('secretkey', '').replace(" ", "")
            
            logger.info(f"Retrieved credentials for account: {username}")
            return username, password, secret_key
            
        except Exception as e:
            logger.error(f"Failed to get credentials: {e}")
            raise
    
    def setup_instagram_client(self) -> bool:
        """Setup and login Instagram client"""
        try:
            # Get credentials
            username, password, secret_key = self.get_account_credentials()
            
            # Add account to manager
            self.instagram_manager.add_account(
                account_id=self.account_id,
                username=username,
                password=password,
                secret_key=secret_key
            )
            
            # Login
            success = self.instagram_manager.login_account(self.account_id)
            if success:
                logger.info(f"Successfully logged in account {username}")
                return True
            else:
                logger.error(f"Failed to login account {username}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to setup Instagram client: {e}")
            return False
    
    def load_processed_threads(self) -> set:
        """Load already processed thread IDs from log file"""
        processed_threads = set()
        
        if os.path.exists(self.deleted_log_csv):
            try:
                existing_log = pd.read_csv(self.deleted_log_csv)
                if 'thread_id' in existing_log.columns:
                    processed_threads = set(existing_log['thread_id'].astype(str))
                    logger.info(f"Found {len(processed_threads)} already processed threads")
            except Exception as e:
                logger.warning(f"Could not load existing log file: {e}")
        
        return processed_threads
    
    def load_threads_to_delete(self) -> List[Dict[str, Any]]:
        """Load threads that need to be deleted"""
        threads_to_delete = []
        
        try:
            # Method 1: Load from CSV file (like original)
            if os.path.exists(self.input_csv):
                df = pd.read_csv(self.input_csv)
                
                # Filter for Instagram User entries (case-insensitive)
                instagram_users = df[df['title'].str.lower() == "instagram user"]
                
                for _, row in instagram_users.iterrows():
                    thread_data = {
                        'thread_id': str(row['id1']),
                        'user_id': str(row.get('id3', '')),
                        'username': str(row.get('username', '')),
                        'title': str(row.get('title', '')),
                        'full_name': str(row.get('full_name', '')),
                        'source': 'csv_file'
                    }
                    threads_to_delete.append(thread_data)
                
                logger.info(f"Found {len(instagram_users)} Instagram User threads in CSV")
            
            # Method 2: Load from database
            db_threads = self.db_manager.get_instagram_user_threads(self.account_id)
            for thread in db_threads:
                thread_data = {
                    'thread_id': str(thread['thread_id']),
                    'user_id': str(thread.get('user_id', '')),
                    'username': str(thread.get('username', '')),
                    'title': str(thread.get('title', '')),
                    'full_name': str(thread.get('full_name', '')),
                    'source': 'database'
                }
                threads_to_delete.append(thread_data)
            
            # Remove duplicates (keep CSV entries over database entries)
            seen_thread_ids = set()
            unique_threads = []
            
            for thread in threads_to_delete:
                if thread['thread_id'] not in seen_thread_ids:
                    unique_threads.append(thread)
                    seen_thread_ids.add(thread['thread_id'])
            
            logger.info(f"Total unique threads to delete: {len(unique_threads)}")
            return unique_threads
            
        except Exception as e:
            logger.error(f"Error loading threads to delete: {e}")
            return []
    
    def save_deletion_log(self, thread_id: str, status: str, username: str = "", title: str = ""):
        """Save deletion log to CSV file"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # Check if file exists
            file_exists = os.path.exists(self.deleted_log_csv)
            
            # Prepare log data
            log_data = {
                'thread_id': thread_id,
                'status': status,
                'timestamp': timestamp,
                'username': username,
                'title': title
            }
            
            # Create DataFrame and save
            df = pd.DataFrame([log_data])
            
            if file_exists:
                # Append to existing file
                df.to_csv(self.deleted_log_csv, mode='a', header=False, index=False)
            else:
                # Create new file with headers
                df.to_csv(self.deleted_log_csv, index=False)
            
            # Also log to database
            self.db_manager.log_thread_deletion(
                account_id=self.account_id,
                thread_id=thread_id,
                status=status,
                thread_title=title,
                username=username
            )
            
        except Exception as e:
            logger.error(f"Error saving deletion log: {e}")
    
    def delete_thread(self, thread_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Delete a single thread"""
        try:
            thread_id = thread_data['thread_id']
            username = thread_data.get('username', '')
            title = thread_data.get('title', '')
            
            logger.info(f"Attempting to delete thread {thread_id} ({username})")
            
            # Delete thread using Instagram manager
            success = self.instagram_manager.delete_direct_thread(self.account_id, thread_id)
            
            if success:
                logger.info(f"Successfully deleted thread {thread_id}")
                return True, "Success"
            else:
                logger.warning(f"Failed to delete thread {thread_id}")
                return False, "Failed"
                
        except Exception as e:
            error_msg = f"Error: {str(e)}"
            logger.error(f"Error deleting thread {thread_data['thread_id']}: {e}")
            return False, error_msg
    
    def should_continue(self) -> bool:
        """Check if should continue processing"""
        # Check timeout
        if datetime.now() >= self.session_end:
            logger.info(f"Session timeout reached after {self.timeout_duration} seconds")
            return False
        
        return True
    
    def calculate_delay(self) -> float:
        """Calculate delay between deletions"""
        # Random delay like original (2.5-4.5 seconds)
        return random.uniform(2.5, 4.5)
    
    def run(self, method: str = 'both') -> Dict[str, Any]:
        """
        Run the delete manager bot
        
        Args:
            method: 'csv' (CSV file only), 'database' (database only), 'both' (both sources)
        """
        try:
            logger.info(f"Starting Delete Manager Bot for account {self.account_id}")
            logger.info(f"Session timeout: {self.timeout_duration} seconds")
            
            # Setup Instagram client
            if not self.setup_instagram_client():
                raise Exception("Failed to setup Instagram client")
            
            # Load processed threads
            processed_threads = self.load_processed_threads()
            
            # Load threads to delete
            all_threads = self.load_threads_to_delete()
            
            if not all_threads:
                logger.info("No Instagram User threads found to delete")
                return {
                    'account_id': self.account_id,
                    'status': 'completed',
                    'message': 'No threads to delete',
                    'threads_processed': 0,
                    'threads_deleted': 0,
                    'threads_failed': 0,
                    'threads_skipped': 0
                }
            
            # Filter by method if specified
            if method == 'csv':
                all_threads = [t for t in all_threads if t['source'] == 'csv_file']
            elif method == 'database':
                all_threads = [t for t in all_threads if t['source'] == 'database']
            # 'both' uses all threads
            
            logger.info(f"Processing {len(all_threads)} threads using method: {method}")
            
            # Process each thread
            for i, thread_data in enumerate(all_threads):
                if not self.should_continue():
                    break
                
                thread_id = thread_data['thread_id']
                username = thread_data.get('username', '')
                title = thread_data.get('title', '')
                
                # Skip if already processed
                if thread_id in processed_threads:
                    logger.info(f"Skipping thread {thread_id} - already processed")
                    self.threads_skipped += 1
                    continue
                
                logger.info(f"Processing thread {thread_id} ({i+1}/{len(all_threads)})")
                
                # Attempt deletion
                success, status_message = self.delete_thread(thread_data)
                
                # Update statistics
                self.threads_processed += 1
                if success:
                    self.threads_deleted += 1
                else:
                    self.threads_failed += 1
                
                # Log the result
                self.save_deletion_log(thread_id, status_message, username, title)
                
                # Add to processed set
                processed_threads.add(thread_id)
                
                # Delay before next deletion
                if i < len(all_threads) - 1:  # Don't delay after last thread
                    delay = self.calculate_delay()
                    logger.info(f"Waiting {delay:.2f} seconds before next deletion...")
                    time.sleep(delay)
            
            # Final results
            results = {
                'account_id': self.account_id,
                'status': 'completed',
                'method_used': method,
                'session_duration': str(datetime.now() - self.session_start),
                'threads_found': len(all_threads),
                'threads_processed': self.threads_processed,
                'threads_deleted': self.threads_deleted,
                'threads_failed': self.threads_failed,
                'threads_skipped': self.threads_skipped,
                'success_rate': f"{(self.threads_deleted / max(self.threads_processed, 1) * 100):.1f}%" if self.threads_processed > 0 else "0%",
                'log_file': self.deleted_log_csv
            }
            
            logger.info("=== Deletion Session Complete ===")
            logger.info(f"Threads found: {len(all_threads)}")
            logger.info(f"Threads processed: {self.threads_processed}")
            logger.info(f"Threads deleted: {self.threads_deleted}")
            logger.info(f"Threads failed: {self.threads_failed}")
            logger.info(f"Threads skipped: {self.threads_skipped}")
            
            return results
            
        except Exception as e:
            logger.error(f"Critical error in delete manager bot: {e}")
            return {
                'account_id': self.account_id,
                'status': 'error',
                'error': str(e),
                'threads_processed': self.threads_processed,
                'threads_deleted': self.threads_deleted,
                'threads_failed': self.threads_failed,
                'threads_skipped': self.threads_skipped
            }

def main():
    """Main function for standalone execution"""
    if len(sys.argv) < 2:
        print("Usage: python delete_manager_bot.py <account_id> [method]")
        print("Methods: csv, database, both (default)")
        print("Example: python delete_manager_bot.py 1")
        print("Example: python delete_manager_bot.py 1 csv")
        sys.exit(1)
    
    try:
        account_id = int(sys.argv[1])
        method = sys.argv[2] if len(sys.argv) > 2 else 'both'
        
        if method not in ['csv', 'database', 'both']:
            print("Method must be 'csv', 'database', or 'both'")
            sys.exit(1)
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Run delete manager bot
        bot = DeleteManagerBot(account_id)
        results = bot.run(method)
        
        print("\n=== Final Results ===")
        for key, value in results.items():
            print(f"{key}: {value}")
        
    except ValueError:
        print("Account ID must be a number")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()





