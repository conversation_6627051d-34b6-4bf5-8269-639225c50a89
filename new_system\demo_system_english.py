#!/usr/bin/env python3
"""
Instagram Management System Demo Script
Demonstrates existing system functionality
"""

import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, List

# Add path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import DatabaseManager
from core.instagram_manager import InstagramManager

class SystemDemo:
    """System demonstration class"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.instagram_manager = InstagramManager()
        
    def print_header(self, title: str):
        """Print header"""
        print("\n" + "="*80)
        print(f" {title}")
        print("="*80)
    
    def print_section(self, title: str):
        """Print section title"""
        print(f"\n {title}")
        print("-" * 60)
    
    def print_success(self, message: str):
        """Print success message"""
        print(f"[SUCCESS] {message}")
    
    def print_info(self, message: str):
        """Print info message"""
        print(f"[INFO] {message}")
    
    def print_warning(self, message: str):
        """Print warning message"""
        print(f"[WARNING] {message}")
    
    def print_error(self, message: str):
        """Print error message"""
        print(f"[ERROR] {message}")
    
    def demo_database_status(self):
        """Demonstrate database status"""
        self.print_header("Database Connection Status")
        
        try:
            # Test database connection
            self.print_section("Database Connection Test")
            connection = self.db.get_connection()
            if connection:
                self.print_success("Database connection successful")
                connection.close()
            else:
                self.print_error("Database connection failed")
                return False
            
            # Show database statistics
            self.print_section("Database Statistics")
            
            # Account count
            accounts = self.db.get_all_accounts()
            self.print_info(f"Total Instagram accounts: {len(accounts)}")
            
            # Message templates count
            messages = self.db.get_all_messages()
            self.print_info(f"Total message templates: {len(messages)}")
            
            # Scraped users count
            scraped_users = self.db.get_scraped_users(1000)
            self.print_info(f"Total scraped users: {len(scraped_users)}")
            
            # Show account details
            self.print_section("Account Details")
            for i, account in enumerate(accounts[:3], 1):
                status = "Logged in" if account.get('is_logged_in') else "Pending login"
                self.print_info(f"Account {i}: {account['username']} - {status}")
            
            return True
            
        except Exception as e:
            self.print_error(f"Database demo failed: {e}")
            return False
    
    def demo_instagram_manager(self):
        """Demonstrate Instagram manager"""
        self.print_header("Instagram Manager Status")
        
        try:
            self.print_section("Manager Initialization")
            self.print_success("Instagram manager initialized successfully")
            
            # Show account status
            self.print_section("Account Status Check")
            accounts = self.instagram_manager.get_all_accounts()
            
            for i, account in enumerate(accounts[:3], 1):
                account_id = account['id']
                username = account['username']
                
                # Try to get account instance
                instagram_account = self.instagram_manager.get_account(account_id)
                if instagram_account:
                    login_status = "Logged in" if instagram_account.is_logged_in() else "Pending login"
                    self.print_info(f"Account {i} (ID: {account_id}): {username} - {login_status}")
                    
                    # Show session info
                    if instagram_account.is_logged_in():
                        self.print_success(f"  Session valid, last activity: {instagram_account.last_activity}")
                    else:
                        self.print_warning(f"  Login required or session expired")
                else:
                    self.print_warning(f"Account {i} (ID: {account_id}): {username} - Cannot get instance")
            
            return True
            
        except Exception as e:
            self.print_error(f"Instagram manager demo failed: {e}")
            return False
    
    def demo_bot_capabilities(self):
        """Demonstrate bot capabilities"""
        self.print_header("Bot Functionality")
        
        try:
            # Bio Scanner Bot
            self.print_section("Bio Scanner Bot")
            self.print_info("Function: Scan Instagram user profiles for detailed information")
            self.print_info("Data collected: Username, full name, bio, followers, following, profile picture")
            self.print_info("Storage: Automatic database storage with batch processing support")
            self.print_success("Status: Fully functional and integrated")
            
            # Data Retriever Bot
            self.print_section("Data Retriever Bot")
            self.print_info("Function: Retrieve user information, media content, search users")
            self.print_info("Operations: User search, profile retrieval, media download")
            self.print_info("Smart handling: Automatic Instagram API limit and error handling")
            self.print_success("Status: Fully functional and integrated")
            
            # Message Bot
            self.print_section("Message Bot")
            self.print_info("Function: Send direct messages, bulk messaging, template management")
            self.print_info("Features: Personalized messages, scheduled sending, status tracking")
            self.print_info("Security: Send frequency control, error retry, logging")
            self.print_success("Status: Fully functional and integrated")
            
            # Account Warmup Bot
            self.print_section("Account Warmup Bot")
            self.print_info("Function: Simulate human behavior to maintain account activity")
            self.print_info("Activities: Browse feed, like posts, follow users, comment, explore")
            self.print_info("Algorithm: Randomized behavior patterns, time interval control, risk avoidance")
            self.print_success("Status: Fully functional and integrated")
            
            return True
            
        except Exception as e:
            self.print_error(f"Bot capabilities demo failed: {e}")
            return False
    
    def demo_data_samples(self):
        """Demonstrate data samples"""
        self.print_header("Data Samples")
        
        try:
            # Show message template samples
            self.print_section("Message Template Samples")
            messages = self.db.get_all_messages()
            if messages:
                for i, msg in enumerate(messages[:5], 1):
                    title = msg.get('title', 'No title')
                    content = msg.get('content', 'No content')[:50] + "..." if len(msg.get('content', '')) > 50 else msg.get('content', 'No content')
                    self.print_info(f"Template {i}: {title}")
                    self.print_info(f"  Content: {content}")
            else:
                self.print_warning("No message templates available")
            
            # Show scraped user samples
            self.print_section("Scraped User Samples")
            scraped_users = self.db.get_scraped_users(5)
            if scraped_users:
                for i, user in enumerate(scraped_users[:5], 1):
                    username = user.get('username', 'Unknown')
                    full_name = user.get('full_name', 'Unknown')
                    followers = user.get('follower_count', 0)
                    bio = user.get('biography', 'No bio')[:30] + "..." if len(user.get('biography', '')) > 30 else user.get('biography', 'No bio')
                    
                    self.print_info(f"User {i}: {username} ({full_name})")
                    self.print_info(f"  Followers: {followers:,} | Bio: {bio}")
            else:
                self.print_warning("No scraped user data available")
            
            return True
            
        except Exception as e:
            self.print_error(f"Data samples demo failed: {e}")
            return False
    
    def demo_system_features(self):
        """Demonstrate system features"""
        self.print_header("System Features")
        
        try:
            # Core features
            self.print_section("Core System Features")
            features = [
                "Automatic 2FA verification - TOTP two-factor authentication support",
                "Session persistence - Automatic login state save and restore",
                "Proxy support - Integrated Evomi residential proxies with country switching",
                "Data management - Complete user data storage and retrieval",
                "Bot system - Modular bot architecture for easy extension",
                "Logging system - Detailed operation logs and error tracking",
                "Performance optimization - Smart retry mechanism and error handling",
                "Security mechanism - Instagram challenge automatic handling and avoidance"
            ]
            
            for feature in features:
                self.print_info(f"  * {feature}")
            
            # Technical architecture
            self.print_section("Technical Architecture")
            tech_stack = [
                "Backend: Flask (Python 3.8+)",
                "Instagram API: instagrapi (latest version)",
                "Database: MySQL 8.0+",
                "Proxy service: Evomi Residential Proxies",
                "Authentication: TOTP (Google Authenticator)",
                "Frontend: HTML5 + CSS3 + JavaScript",
                "UI framework: Bootstrap 5 + FontAwesome",
                "Deployment: Docker and traditional deployment support"
            ]
            
            for tech in tech_stack:
                self.print_info(f"  * {tech}")
            
            return True
            
        except Exception as e:
            self.print_error(f"System features demo failed: {e}")
            return False
    
    def demo_usage_scenarios(self):
        """Demonstrate usage scenarios"""
        self.print_header("Usage Scenarios")
        
        try:
            scenarios = [
                {
                    "title": "Account Management",
                    "description": "Manage multiple Instagram accounts with automatic login and session maintenance",
                    "features": ["Batch account import", "Automatic 2FA verification", "Session status monitoring", "Login state management"]
                },
                {
                    "title": "User Data Collection",
                    "description": "Batch scan and collect Instagram user profile information",
                    "features": ["Keyword search", "Batch profile retrieval", "Data deduplication", "Automatic storage"]
                },
                {
                    "title": "Marketing Message Sending",
                    "description": "Send personalized marketing messages with delivery status tracking",
                    "features": ["Message template management", "Personalization variables", "Send frequency control", "Status tracking"]
                },
                {
                    "title": "Account Activity Maintenance",
                    "description": "Maintain account activity through natural behavior to avoid restrictions",
                    "features": ["Smart behavior simulation", "Risk avoidance", "Activity recording", "Effect statistics"]
                }
            ]
            
            for scenario in scenarios:
                self.print_section(scenario["title"])
                self.print_info(f"Description: {scenario['description']}")
                self.print_info("Main features:")
                for feature in scenario["features"]:
                    self.print_info(f"  * {feature}")
            
            return True
            
        except Exception as e:
            self.print_error(f"Usage scenarios demo failed: {e}")
            return False
    
    def run_demo(self):
        """Run the demo"""
        print("Instagram Management System - Functionality Demo")
        print("=" * 80)
        print(f"Demo time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # Run demo modules
        demos = [
            ("Database Status", self.demo_database_status),
            ("Instagram Manager", self.demo_instagram_manager),
            ("Bot Capabilities", self.demo_bot_capabilities),
            ("Data Samples", self.demo_data_samples),
            ("System Features", self.demo_system_features),
            ("Usage Scenarios", self.demo_usage_scenarios)
        ]
        
        success_count = 0
        total_count = len(demos)
        
        for demo_name, demo_func in demos:
            try:
                if demo_func():
                    success_count += 1
                time.sleep(0.5)  # Brief pause
            except Exception as e:
                self.print_error(f"{demo_name} demo failed: {e}")
        
        # Demo summary
        self.print_header("Demo Summary")
        print(f"Demo completion: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            self.print_success("All demo modules completed successfully!")
        elif success_count >= total_count * 0.8:
            self.print_success("Most demo modules completed successfully!")
        else:
            self.print_warning("Some demo modules need attention")
        
        print("\nSystem is ready for use!")
        print("Contact technical support if you have any questions")

def main():
    """Main function"""
    try:
        demo = SystemDemo()
        demo.run_demo()
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user")
    except Exception as e:
        print(f"\n[ERROR] Demo failed: {e}")

if __name__ == "__main__":
    main()
