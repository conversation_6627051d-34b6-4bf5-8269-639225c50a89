#!/usr/bin/env python3
"""
Message Bot - Handles Instagram direct messaging functionality
Uses instagrapi to send messages to users
"""

import os
import time
import random
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager

# Configure logging
logger = logging.getLogger(__name__)

class MessageBot:
    """Instagram Message Bot using instagrapi"""
    
    def __init__(self, account_id: int, instagram_manager: InstagramManager, db_manager: DatabaseManager):
        self.account_id = account_id
        self.instagram_manager = instagram_manager
        self.db_manager = db_manager
        
        # Message settings
        self.max_messages_per_session = 20
        self.min_delay = 30  # seconds
        self.max_delay = 120  # seconds
        
        # Message templates
        self.default_messages = [
            "Hi there! 👋",
            "Hello! How are you? 😊",
            "Thanks for the follow! 🙏",
            "Great to connect with you! ✨",
            "Hope you're having a great day! 🌟"
        ]
    
    def setup_instagram_client(self) -> bool:
        """Setup Instagram client for messaging"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account or not account.is_logged_in():
                logger.error(f"Account {self.account_id} not logged in")
                return False
            
            logger.info(f"Instagram client ready for messaging with account {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Instagram client: {e}")
            return False
    
    def get_message_templates(self) -> List[Dict[str, Any]]:
        """Get message templates from database"""
        try:
            messages = self.db_manager.get_all_messages()
            if messages:
                logger.info(f"Loaded {len(messages)} message templates from database")
                return messages
            else:
                logger.warning("No message templates found in database, using defaults")
                return []
                
        except Exception as e:
            logger.error(f"Error loading message templates: {e}")
            return []
    
    def format_message(self, template: str, variables: Dict[str, str]) -> str:
        """Format message template with variables"""
        try:
            formatted_message = template
            for key, value in variables.items():
                placeholder = f"{{{key}}}"
                if placeholder in formatted_message:
                    formatted_message = formatted_message.replace(placeholder, value)
            
            logger.debug(f"Formatted message: {formatted_message}")
            return formatted_message
            
        except Exception as e:
            logger.error(f"Error formatting message: {e}")
            return template
    
    def send_direct_message(self, username: str, message: str) -> bool:
        """Send direct message to user"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account:
                logger.error(f"Account {self.account_id} not found")
                return False
            
            # Get user ID from username
            user_info = account.get_user_info(username)
            if not user_info:
                logger.error(f"Could not get user info for {username}")
                return False
            
            user_id = user_info.get('user_id')
            if not user_id:
                logger.error(f"No user ID found for {username}")
                return False
            
            # Send message
            result = account.client.direct_message(user_id, message)
            
            if result:
                logger.info(f"Message sent successfully to {username}")
                
                # Log message in database
                self.db_manager.log_message(
                    account_id=self.account_id,
                    recipient_username=username,
                    message_content=message,
                    message_type='direct',
                    status='sent',
                    sent_at=datetime.now()
                )
                
                return True
            else:
                logger.error(f"Failed to send message to {username}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending message to {username}: {e}")
            return False
    
    def send_messages_to_users(self, usernames: List[str], message_template: str = None, 
                              variables: Dict[str, str] = None) -> Dict[str, Any]:
        """Send messages to multiple users"""
        try:
            if not self.setup_instagram_client():
                return {'success': False, 'error': 'Failed to setup Instagram client'}
            
            # Get message template
            if message_template:
                message = message_template
            else:
                # Get random template from database or use default
                templates = self.get_message_templates()
                if templates:
                    template = random.choice(templates)
                    message = template.get('content', '')
                else:
                    message = random.choice(self.default_messages)
            
            # Format message with variables
            if variables:
                message = self.format_message(message, variables)
            
            # Send messages
            sent_count = 0
            failed_count = 0
            results = []
            
            for username in usernames[:self.max_messages_per_session]:
                try:
                    success = self.send_direct_message(username, message)
                    if success:
                        sent_count += 1
                        results.append({'username': username, 'status': 'sent'})
                    else:
                        failed_count += 1
                        results.append({'username': username, 'status': 'failed'})
                    
                    # Random delay between messages
                    delay = random.uniform(self.min_delay, self.max_delay)
                    time.sleep(delay)
                    
                except Exception as e:
                    logger.error(f"Error processing user {username}: {e}")
                    failed_count += 1
                    results.append({'username': username, 'status': 'error', 'error': str(e)})
            
            return {
                'success': True,
                'total_users': len(usernames),
                'sent_count': sent_count,
                'failed_count': failed_count,
                'results': results,
                'message_template': message
            }
            
        except Exception as e:
            logger.error(f"Error in send_messages_to_users: {e}")
            return {'success': False, 'error': str(e)}
    
    def send_follow_up_messages(self, username: str, follow_up_type: str = 'general') -> bool:
        """Send follow-up message based on type"""
        try:
            follow_up_messages = {
                'general': [
                    "Hope you're enjoying the content! 📱",
                    "Feel free to reach out if you have any questions! 💬",
                    "Looking forward to connecting more! 🤝"
                ],
                'business': [
                    "Interested in collaborating? Let's discuss! 💼",
                    "Check out our latest products! 🛍️",
                    "Great to have you as part of our community! 🎯"
                ],
                'personal': [
                    "Thanks for the support! 🙏",
                    "Really appreciate you following along! ❤️",
                    "You're awesome! Keep being you! ✨"
                ]
            }
            
            messages = follow_up_messages.get(follow_up_type, follow_up_messages['general'])
            message = random.choice(messages)
            
            return self.send_direct_message(username, message)
            
        except Exception as e:
            logger.error(f"Error sending follow-up message: {e}")
            return False
    
    def get_message_stats(self) -> Dict[str, Any]:
        """Get messaging statistics for the account"""
        try:
            # Get message logs from database
            message_logs = self.db_manager.get_message_logs(account_id=self.account_id)
            
            total_messages = len(message_logs)
            sent_messages = len([log for log in message_logs if log.get('status') == 'sent'])
            failed_messages = len([log for log in message_logs if log.get('status') == 'failed'])
            
            return {
                'account_id': self.account_id,
                'total_messages': total_messages,
                'sent_messages': sent_messages,
                'failed_messages': failed_messages,
                'success_rate': (sent_messages / total_messages * 100) if total_messages > 0 else 0,
                'last_message_sent': max([log.get('sent_at') for log in message_logs]) if message_logs else None
            }
            
        except Exception as e:
            logger.error(f"Error getting message stats: {e}")
            return {}

def create_message_bot(account_id: int) -> MessageBot:
    """Factory function to create message bot"""
    from core.instagram_manager import InstagramManager
    from core.database import DatabaseManager
    
    instagram_manager = InstagramManager()
    db_manager = DatabaseManager()
    
    return MessageBot(account_id, instagram_manager, db_manager)

