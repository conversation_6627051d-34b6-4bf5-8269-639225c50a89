#!/usr/bin/env python3
"""
Data Retriever Bot - Instagram Data Management and Cleanup System
Replaces the original retrieve.py functionality for data management
"""

import os
import sys
import time
import json
import pandas as pd
import threading
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
import logging
from pathlib import Path
from queue import Queue
import csv

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/data_retriever_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataRetrieverBot:
    """Instagram Data Retrieval and Management Bot"""
    
    def __init__(self, account_id: int = None):
        self.account_id = account_id
        self.instagram_manager = InstagramManager() if account_id else None
        self.db_manager = DatabaseManager()
        self.config = Config()
        
        # File paths for different message states
        self.output_dir = "C:/files"
        self.main_csv = os.path.join(self.output_dir, "failed_messages.csv")
        self.success_csv = os.path.join(self.output_dir, "success_messages.csv")
        self.error_csv = os.path.join(self.output_dir, "error_messages.csv")
        self.deleted_csv = os.path.join(self.output_dir, "deleted_messages.csv")
        
        # Status constants
        self.STATUS_PENDING = "pending"
        self.STATUS_SENT = "sent"
        self.STATUS_SEND_FAILED = "send_failed"
        self.STATUS_ERROR = "error"
        self.STATUS_DELETED = "deleted"
        self.STATUS_TOO_OLD = "too_old_not_sent"
        
        # Status categories
        self.ERROR_STATUSES = [
            self.STATUS_SEND_FAILED, self.STATUS_ERROR, self.STATUS_TOO_OLD
        ]
        self.DELETABLE_STATUSES = [self.STATUS_SENT]
        
        # Configuration
        self.interval_seconds = 10  # Check interval
        self.old_message_threshold_hours = 8.5  # 510 minutes
        
        # Thread safety
        self.csv_lock = threading.Lock()
        self.delete_queue = Queue()
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info(f"Data Retriever Bot initialized")
        if account_id:
            logger.info(f"Account ID: {account_id}")
    
    def get_account_credentials(self) -> Tuple[str, str, str]:
        """Get account credentials from database if account_id provided"""
        if not self.account_id:
            return "", "", ""
        
        try:
            account = self.db_manager.get_account_by_id(self.account_id)
            if not account:
                raise Exception(f"Account {self.account_id} not found in database")
            
            username = account['username']
            password = account['password']
            secret_key = account.get('secretkey', '').replace(" ", "")
            
            logger.info(f"Retrieved credentials for account: {username}")
            return username, password, secret_key
            
        except Exception as e:
            logger.error(f"Failed to get credentials: {e}")
            return "", "", ""
    
    def initialize_csv_files(self):
        """Initialize all CSV files with proper headers"""
        required_columns = [
            "message_id", "username", "business_client_username", "message",
            "created_at", "updated_at", "status", "deletion_reason", 
            "previous_status", "deleted_at"
        ]
        
        csv_files = [self.main_csv, self.success_csv, self.error_csv, self.deleted_csv]
        
        for csv_file in csv_files:
            try:
                if not os.path.exists(csv_file) or os.path.getsize(csv_file) == 0:
                    df = pd.DataFrame(columns=required_columns)
                    df.to_csv(csv_file, index=False, encoding='utf-8')
                    logger.info(f"Initialized CSV file: {os.path.basename(csv_file)}")
                else:
                    # Verify existing file has required columns
                    df = pd.read_csv(csv_file)
                    missing_cols = [col for col in required_columns if col not in df.columns]
                    if missing_cols:
                        logger.warning(f"Adding missing columns to {os.path.basename(csv_file)}: {missing_cols}")
                        for col in missing_cols:
                            df[col] = ""
                        df.to_csv(csv_file, index=False, encoding='utf-8')
                        
            except Exception as e:
                logger.error(f"Error initializing {csv_file}: {e}")
    
    def read_csv_to_dataframe(self, csv_file: str) -> pd.DataFrame:
        """Read CSV file into DataFrame with error handling"""
        required_columns = [
            "message_id", "username", "business_client_username", "message",
            "created_at", "updated_at", "status", "deletion_reason", 
            "previous_status", "deleted_at"
        ]
        
        try:
            if os.path.exists(csv_file) and os.path.getsize(csv_file) > 0:
                df = pd.read_csv(csv_file, encoding='utf-8')
                if not df.empty:
                    return df
            
            # Create empty DataFrame with required columns
            df = pd.DataFrame(columns=required_columns)
            df.to_csv(csv_file, index=False, encoding='utf-8')
            return df
            
        except Exception as e:
            logger.error(f"Error reading {csv_file}: {e}")
            df = pd.DataFrame(columns=required_columns)
            return df
    
    def extract_data_from_database(self) -> List[Dict[str, Any]]:
        """Extract message data from database tables"""
        try:
            # Extract from message_logs table
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                
                # Get recent message logs
                query = """
                SELECT 
                    CONCAT('msg_', id) as message_id,
                    recipient_username as username,
                    CONCAT('account_', account_id) as business_client_username,
                    message_content as message,
                    created_at,
                    created_at as updated_at,
                    status,
                    '' as deletion_reason,
                    '' as previous_status,
                    NULL as deleted_at
                FROM message_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY created_at DESC
                """
                
                cursor.execute(query)
                records = cursor.fetchall()
                
                # Convert datetime objects to strings
                for record in records:
                    if record['created_at']:
                        record['created_at'] = record['created_at'].isoformat()
                    if record['updated_at']:
                        record['updated_at'] = record['updated_at'].isoformat()
                
                logger.info(f"Extracted {len(records)} records from database")
                return records
                
        except Exception as e:
            logger.error(f"Error extracting data from database: {e}")
            return []
    
    def extract_data_from_csv_files(self) -> List[Dict[str, Any]]:
        """Extract data from existing CSV files in the system"""
        try:
            all_records = []
            
            # CSV files to check for message data
            csv_files_to_check = [
                "messages1.csv", "messages2.csv", "messages3.csv", "messages4.csv", "messages5.csv",
                "images1.csv", "images2.csv", "images3.csv", "images4.csv", "images5.csv",
                "first_message1.csv", "first_message2.csv", "first_message3.csv", "first_message4.csv", "first_message5.csv",
                "deleted1.csv", "deleted2.csv", "deleted3.csv", "deleted4.csv", "deleted5.csv"
            ]
            
            for csv_file in csv_files_to_check:
                file_path = os.path.join(self.output_dir, csv_file)
                
                if os.path.exists(file_path):
                    try:
                        df = pd.read_csv(file_path)
                        
                        for _, row in df.iterrows():
                            # Determine status based on file type and row data
                            if 'deleted' in csv_file:
                                status = self.STATUS_DELETED
                            elif row.get('status') == 'success':
                                status = self.STATUS_SENT
                            elif row.get('status') == 'failed':
                                status = self.STATUS_SEND_FAILED
                            else:
                                status = self.STATUS_PENDING
                            
                            # Check if message is too old
                            created_at = row.get('timestamp', datetime.now().isoformat())
                            try:
                                msg_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                                if datetime.now() - msg_time > timedelta(hours=self.old_message_threshold_hours):
                                    if status == self.STATUS_PENDING:
                                        status = self.STATUS_TOO_OLD
                            except:
                                pass
                            
                            record = {
                                'message_id': f"{csv_file}_{row.get('id', len(all_records))}",
                                'username': str(row.get('username', '')),
                                'business_client_username': csv_file.split('.')[0],  # e.g., "messages1"
                                'message': str(row.get('message', row.get('text', ''))),
                                'created_at': created_at,
                                'updated_at': created_at,
                                'status': status,
                                'deletion_reason': '',
                                'previous_status': '',
                                'deleted_at': created_at if status == self.STATUS_DELETED else None
                            }
                            
                            all_records.append(record)
                            
                    except Exception as e:
                        logger.warning(f"Error processing {csv_file}: {e}")
                        continue
            
            logger.info(f"Extracted {len(all_records)} records from CSV files")
            return all_records
            
        except Exception as e:
            logger.error(f"Error extracting data from CSV files: {e}")
            return []
    
    def filter_duplicates(self, new_records: List[Dict[str, Any]], existing_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Filter out duplicate records"""
        if existing_df.empty:
            return new_records
        
        # Create set of existing record keys
        existing_keys = set()
        for _, row in existing_df.iterrows():
            key = (
                str(row.get('username', '')),
                str(row.get('business_client_username', '')),
                str(row.get('message', ''))
            )
            existing_keys.add(key)
        
        # Filter new records
        filtered_records = []
        for record in new_records:
            key = (
                str(record.get('username', '')),
                str(record.get('business_client_username', '')),
                str(record.get('message', ''))
            )
            
            if key not in existing_keys:
                filtered_records.append(record)
                existing_keys.add(key)  # Prevent duplicates within new records too
        
        logger.info(f"Filtered {len(new_records)} records to {len(filtered_records)} unique records")
        return filtered_records
    
    def save_records_to_csv(self, records: List[Dict[str, Any]], csv_file: str):
        """Save records to CSV file with thread safety"""
        if not records:
            return
        
        with self.csv_lock:
            try:
                df = pd.DataFrame(records)
                
                if os.path.exists(csv_file) and os.path.getsize(csv_file) > 0:
                    # Append to existing file
                    df.to_csv(csv_file, mode='a', header=False, index=False, encoding='utf-8')
                else:
                    # Create new file with headers
                    df.to_csv(csv_file, index=False, encoding='utf-8')
                
                logger.info(f"Saved {len(records)} records to {os.path.basename(csv_file)}")
                
            except Exception as e:
                logger.error(f"Error saving records to {csv_file}: {e}")
    
    def check_for_status_changes(self):
        """Check for status changes and move records to appropriate files"""
        try:
            with self.csv_lock:
                main_df = self.read_csv_to_dataframe(self.main_csv)
                
                if main_df.empty:
                    return
                
                # Separate records by status
                success_records = main_df[main_df['status'] == self.STATUS_SENT]
                error_records = main_df[main_df['status'].isin(self.ERROR_STATUSES)]
                deleted_records = main_df[main_df['status'] == self.STATUS_DELETED]
                
                # Move records to appropriate files
                if not success_records.empty:
                    self.save_records_to_csv(success_records.to_dict('records'), self.success_csv)
                
                if not error_records.empty:
                    self.save_records_to_csv(error_records.to_dict('records'), self.error_csv)
                
                if not deleted_records.empty:
                    self.save_records_to_csv(deleted_records.to_dict('records'), self.deleted_csv)
                
                # Keep only pending records in main CSV
                remaining_df = main_df[main_df['status'] == self.STATUS_PENDING]
                remaining_df.to_csv(self.main_csv, index=False, encoding='utf-8')
                
                moved_count = len(success_records) + len(error_records) + len(deleted_records)
                if moved_count > 0:
                    logger.info(f"Moved {moved_count} records: {len(success_records)} success, {len(error_records)} error, {len(deleted_records)} deleted")
                
        except Exception as e:
            logger.error(f"Error checking status changes: {e}")
    
    def mark_old_messages_for_deletion(self):
        """Mark old sent messages for deletion"""
        try:
            success_df = self.read_csv_to_dataframe(self.success_csv)
            
            if success_df.empty:
                return
            
            current_time = datetime.now()
            messages_to_delete = []
            
            for idx, row in success_df.iterrows():
                try:
                    created_at = datetime.fromisoformat(row['created_at'].replace('Z', '+00:00'))
                    age = current_time - created_at
                    
                    # All sent messages should be marked for deletion (as per original logic)
                    if row['status'] == self.STATUS_SENT:
                        updated_row = row.copy()
                        updated_row['status'] = self.STATUS_DELETED
                        updated_row['deletion_reason'] = 'sent_message_cleanup'
                        updated_row['previous_status'] = self.STATUS_SENT
                        updated_row['deleted_at'] = current_time.isoformat()
                        
                        messages_to_delete.append(updated_row)
                        
                except Exception as e:
                    logger.warning(f"Error processing message for deletion: {e}")
                    continue
            
            if messages_to_delete:
                # Save to deleted CSV
                self.save_records_to_csv(messages_to_delete, self.deleted_csv)
                
                # Remove from success CSV
                remaining_df = success_df[~success_df.index.isin([msg['message_id'] for msg in messages_to_delete])]
                remaining_df.to_csv(self.success_csv, index=False, encoding='utf-8')
                
                logger.info(f"Marked {len(messages_to_delete)} sent messages for deletion")
            
        except Exception as e:
            logger.error(f"Error marking old messages for deletion: {e}")
    
    def run_extraction_cycle(self):
        """Run a single extraction cycle"""
        try:
            logger.info("Starting data extraction cycle")
            
            # Extract data from database
            db_records = self.extract_data_from_database()
            
            # Extract data from CSV files
            csv_records = self.extract_data_from_csv_files()
            
            # Combine all records
            all_new_records = db_records + csv_records
            
            if not all_new_records:
                logger.info("No new records found")
                return
            
            # Read existing main CSV
            existing_df = self.read_csv_to_dataframe(self.main_csv)
            
            # Filter out duplicates
            unique_records = self.filter_duplicates(all_new_records, existing_df)
            
            if unique_records:
                # Save new unique records to main CSV
                self.save_records_to_csv(unique_records, self.main_csv)
                logger.info(f"Added {len(unique_records)} new unique records")
            
            # Check for status changes
            self.check_for_status_changes()
            
            # Mark old messages for deletion
            self.mark_old_messages_for_deletion()
            
            logger.info("Extraction cycle completed")
            
        except Exception as e:
            logger.error(f"Error in extraction cycle: {e}")
    
    def cleanup_old_files(self, days_old: int = 30):
        """Clean up old log files and temporary data"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(days=days_old)
            
            cleanup_patterns = [
                "*.log", "*.tmp", "*_backup.csv", "*_temp.csv"
            ]
            
            files_cleaned = 0
            
            for pattern in cleanup_patterns:
                for file_path in Path(self.output_dir).glob(pattern):
                    try:
                        if file_path.stat().st_mtime < cutoff_time.timestamp():
                            file_path.unlink()
                            files_cleaned += 1
                            logger.info(f"Cleaned up old file: {file_path.name}")
                    except Exception as e:
                        logger.warning(f"Could not clean up {file_path}: {e}")
            
            if files_cleaned > 0:
                logger.info(f"Cleaned up {files_cleaned} old files")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get statistics about the data retrieval system"""
        try:
            stats = {
                'timestamp': datetime.now().isoformat(),
                'csv_files': {}
            }
            
            csv_files = {
                'main': self.main_csv,
                'success': self.success_csv,
                'error': self.error_csv,
                'deleted': self.deleted_csv
            }
            
            for name, file_path in csv_files.items():
                try:
                    if os.path.exists(file_path):
                        df = pd.read_csv(file_path)
                        stats['csv_files'][name] = {
                            'record_count': len(df),
                            'file_size_mb': round(os.path.getsize(file_path) / (1024 * 1024), 2),
                            'last_modified': datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                        }
                    else:
                        stats['csv_files'][name] = {
                            'record_count': 0,
                            'file_size_mb': 0,
                            'last_modified': None
                        }
                except Exception as e:
                    stats['csv_files'][name] = {'error': str(e)}
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
            return {'error': str(e)}
    
    def run(self, cycles: int = None, interval: int = None, force_proxy: bool = False, country_code: Optional[str] = None) -> Dict[str, Any]:
        """Run the data retriever bot"""
        try:
            logger.info("Starting Data Retriever Bot")
            # Assign proxy if requested and account_id provided
            try:
                if self.account_id and force_proxy and self.instagram_manager:
                    assigned = self.instagram_manager.assign_proxy_to_account(self.account_id, country_code=country_code, force_proxy=True)
                    logger.info(f"Proxy assignment for account {self.account_id}: {'OK' if assigned else 'FAILED'}")
            except Exception as e:
                logger.warning(f"Proxy assignment attempt failed: {e}")
            
            # Use custom interval if provided
            if interval:
                self.interval_seconds = interval
            
            # Initialize CSV files
            self.initialize_csv_files()
            
            # Run extraction cycles
            cycles_run = 0
            start_time = datetime.now()
            
            while True:
                try:
                    self.run_extraction_cycle()
                    cycles_run += 1
                    
                    # Check if we should stop
                    if cycles and cycles_run >= cycles:
                        logger.info(f"Completed {cycles} cycles as requested")
                        break
                    
                    # Periodic cleanup
                    if cycles_run % 100 == 0:  # Every 100 cycles
                        self.cleanup_old_files()
                    
                    # Wait for next cycle
                    logger.info(f"Waiting {self.interval_seconds} seconds for next cycle...")
                    time.sleep(self.interval_seconds)
                    
                except KeyboardInterrupt:
                    logger.info("Received interrupt signal, stopping...")
                    break
                except Exception as e:
                    logger.error(f"Error in extraction cycle {cycles_run}: {e}")
                    time.sleep(self.interval_seconds)
                    continue
            
            # Final results
            end_time = datetime.now()
            duration = end_time - start_time
            
            results = {
                'status': 'completed',
                'cycles_completed': cycles_run,
                'duration': str(duration),
                'statistics': self.get_system_statistics()
            }
            
            logger.info(f"Data Retriever Bot completed: {cycles_run} cycles in {duration}")
            return results
            
        except Exception as e:
            logger.error(f"Critical error in data retriever bot: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'cycles_completed': cycles_run if 'cycles_run' in locals() else 0
            }

def main():
    """Main function for standalone execution"""
    if len(sys.argv) < 2:
        print("Usage: python data_retriever_bot.py <cycles> [interval_seconds] [account_id]")
        print("Examples:")
        print("  python data_retriever_bot.py 10           # Run 10 cycles with default 10s interval")
        print("  python data_retriever_bot.py 0            # Run indefinitely")
        print("  python data_retriever_bot.py 5 30         # Run 5 cycles with 30s interval")
        print("  python data_retriever_bot.py 10 15 1      # Run 10 cycles, 15s interval, account 1")
        sys.exit(1)
    
    try:
        cycles = int(sys.argv[1]) if sys.argv[1] != '0' else None
        interval = int(sys.argv[2]) if len(sys.argv) > 2 else 10
        account_id = int(sys.argv[3]) if len(sys.argv) > 3 else None
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Run data retriever bot
        bot = DataRetrieverBot(account_id)
        results = bot.run(cycles, interval)
        
        print("\n=== Final Results ===")
        for key, value in results.items():
            if key == 'statistics':
                print(f"{key}:")
                for stat_key, stat_value in value.items():
                    print(f"  {stat_key}: {stat_value}")
            else:
                print(f"{key}: {value}")
        
    except ValueError:
        print("Cycles and interval must be numbers")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()




