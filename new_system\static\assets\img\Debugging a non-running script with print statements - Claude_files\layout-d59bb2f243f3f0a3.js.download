(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2004],{91192:function(e,t,n){Promise.resolve().then(n.bind(n,54710))},54710:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return o}});var r=n(27573),i=n(99205);function o(e){let{children:t}=e;return(0,r.jsx)(i.hH,{children:t})}},25449:function(e){e.exports=function(e,t,n){return e==e&&(void 0!==n&&(e=e<=n?e:n),void 0!==t&&(e=e>=t?e:t)),e}},79884:function(e,t,n){var r=n(15428),i=/^\s+/;e.exports=function(e){return e?e.slice(0,r(e)+1).replace(i,""):e}},15428:function(e){var t=/\s/;e.exports=function(e){for(var n=e.length;n--&&t.test(e.charAt(n)););return n}},52221:function(e,t,n){var r=n(25449),i=n(97172);e.exports=function(e,t,n){return void 0===n&&(n=t,t=void 0),void 0!==n&&(n=(n=i(n))==n?n:0),void 0!==t&&(t=(t=i(t))==t?t:0),r(i(e),t,n)}},87590:function(e,t,n){var r=n(62579);e.exports=function(e,t){return r(e,t)}},97172:function(e,t,n){var r=n(79884),i=n(88873),o=n(58510),u=0/0,a=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,l=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(o(e))return u;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=r(e);var n=s.test(e);return n||l.test(e)?c(e.slice(2),n?2:8):a.test(e)?u:+e}},88146:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var r=n(16340),i=n.n(r)},27895:function(e,t,n){"use strict";n.d(t,{ErrorsProvider:function(){return b},e:function(){return h},v:function(){return m}});var r=n(27573),i=n(35228),o=n(97975),u=n.n(o),a=n(55854),s=n.n(a),l=n(78646),c=n.n(l),f=n(81695),d=n(7653),v=n(15992),g=n(27218),p=n(18013);let y=(0,d.createContext)(void 0);function b(e){let{children:t}=e,n=(0,d.useRef)(1),[i,o]=(0,d.useState)([]),u=(0,d.useCallback)(e=>{o(t=>t.filter(t=>t.id!==e))},[]),a=(0,d.useCallback)((e,t)=>{let r=n.current++;return o(t=>[...t,{id:r,message:e instanceof Error?e.message:e,toastType:"error"}]),t&&setTimeout(()=>u(r),t),r},[u]),s=(0,d.useCallback)((e,t)=>{let r=n.current++;return o(t=>[...t,{id:r,message:e,toastType:"success"}]),t&&setTimeout(()=>u(r),t),r},[u]),l=(0,d.useMemo)(()=>({toasts:i,addError:a,addSuccess:s,clearToast:u}),[i,a,s,u]);return(0,r.jsx)(y.Provider,{value:l,children:t})}function h(){let e=(0,d.useContext)(y);if(!e)throw Error("Must be called within ErrorsProvider");return e}function m(){var e;let{config:t}=(0,v.useConfig)("claude_system_message"),n=t.get("id",null),r=t.get("title",null),o=t.get("message",null),a=t.get("displayFrequencyHours",null),{account:l}=(0,g.t)(),y=null!==(e=null==l?void 0:l.uuid)&&void 0!==e?e:"logged-out",b=(0,f.usePathname)(),[h,m]=(0,p.R)("dismissed-system-messages",{}),w=(0,d.useMemo)(()=>()=>m(e=>{let t=u()(e);return n&&c()(t,[y,n],Date.now()),t}),[m,y,n]);return(0,d.useMemo)(()=>{if(!l||(0,i.cG)(l,!0)||"/download"===b||!n)return!1;let e=s()(h,[y,n]);return!e||"boolean"==typeof e||!!a&&Date.now()-e>=36e5*a},[l,n,y,h,a,b])?{currentSystemMessageId:n,currentSystemMessageTitle:r,currentSystemMessageContent:o,dismissCurrentSystemMessage:w}:{currentSystemMessageId:null,currentSystemMessageTitle:null,currentSystemMessageContent:null,dismissCurrentSystemMessage:()=>null}}},18013:function(e,t,n){"use strict";n.d(t,{A:function(){return i},R:function(){return o}});var r=n(77879);function i(e,t){return(0,r.Xs)("SSS-".concat(e),t,{serializer:JSON.stringify,deserializer:JSON.parse})}function o(e,t){return(0,r._)("LSS-".concat(e),t,{serializer:JSON.stringify,deserializer:JSON.parse})}},35228:function(e,t,n){"use strict";n.d(t,{D_:function(){return u},c6:function(){return i},cG:function(){return a},kK:function(){return o},wJ:function(){return r}});let r=e=>{let{account:t,isClaudeDot:n}=e;return n?!1===t.settings.has_finished_claudeai_onboarding:!t.full_name||!t.display_name},i=(e,t)=>t&&!e.is_verified,o=(e,t)=>!!function(e,t){for(let n of e.invites)if(n.organization.capabilities&&n.organization.capabilities.includes(t))return n}(e,t?"raven":"api"),u=(e,t)=>!t&&0===e.invites.length&&0===e.memberships.filter(e=>e.organization.capabilities.includes("api")).length,a=(e,t)=>r({account:e,isClaudeDot:t})||i(e,t)||o(e,t)||u(e,t)},58884:function(e,t,n){"use strict";async function r(e,t){let n;let r=e.getReader();for(;!(n=await r.read()).done;)t(n.value)}function i(){return{data:"",event:"",id:"",retry:void 0}}n.d(t,{a:function(){return u},L:function(){return s}});var o=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};let u="text/event-stream",a="last-event-id";function s(e,t){var{signal:n,headers:s,onopen:c,onmessage:f,onclose:d,onerror:v,openWhenHidden:g,fetch:p}=t,y=o(t,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise((t,o)=>{let b;let h=Object.assign({},s);function m(){b.abort(),document.hidden||k()}h.accept||(h.accept=u),"undefined"==typeof document||g||document.addEventListener("visibilitychange",m);let w=1e3,O=0;function x(){"undefined"==typeof document||g||document.removeEventListener("visibilitychange",m),clearTimeout(O),b.abort()}null==n||n.addEventListener("abort",()=>{x(),t()});let S=null!=p?p:fetch,_=null!=c?c:l;async function k(){var n,u;b=new AbortController;try{let n,o,s,l;let c=await S(e,Object.assign(Object.assign({},y),{headers:h,signal:b.signal}));await _(c),await r(c.body,(u=function(e,t,n){let r=i(),o=new TextDecoder;return function(u,a){if(0===u.length)null==e||e(r),r=i();else if(a>0){let e=o.decode(u.subarray(0,a)),i=a+(32===u[a+1]?2:1),s=o.decode(u.subarray(i));switch(e){case"data":r.data=r.data?r.data+"\n"+s:s;break;case"event":r.event=s;break;case"id":null==t||t(r.id=s);break;case"retry":let l=parseInt(s,10);isNaN(l)||null==n||n(r.retry=l)}}}}(f,e=>{e?h[a]=e:delete h[a]},e=>{w=e}),l=!1,function(e){void 0===n?(n=e,o=0,s=-1):n=function(e,t){let n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n}(n,e);let t=n.length,r=0;for(;o<t;){l&&(10===n[o]&&(r=++o),l=!1);let e=-1;for(;o<t&&-1===e;++o)switch(n[o]){case 58:-1===s&&(s=o-r);break;case 13:l=!0;case 10:e=o}if(-1===e)break;u(n.subarray(r,e),s),r=o,s=-1}r===t?n=void 0:0!==r&&(n=n.subarray(r),o-=r)})),null==d||d(),x(),t()}catch(e){if(!b.signal.aborted)try{let t=null!==(n=null==v?void 0:v(e))&&void 0!==n?n:w;clearTimeout(O),O=setTimeout(k,t)}catch(e){x(),o(e)}}}k()})}function l(e){let t=e.headers.get("content-type");if(!(null==t?void 0:t.startsWith(u)))throw Error(`Expected content-type to be ${u}, Actual: ${t}`)}}},function(e){e.O(0,[5790,3279,6947,6340,5992,7423,1270,7879,6971,7265,3290,8877,3053,6933,9205,1293,1362,4856,1744],function(){return e(e.s=91192)}),_N_E=e.O()}]);