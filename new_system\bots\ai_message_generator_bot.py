#!/usr/bin/env python3
"""
AI Message Generator Bot - Intelligent Instagram Message Generation
Replaces the original message_generator*.py functionality using instagrapi and OpenAI
"""

import os
import sys
import time
import json
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime
from typing import Optional, List, Dict, Any, Tuple
import logging

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/ai_message_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AIMessageGeneratorBot:
    """AI-powered Instagram Message Generator using OpenAI and instagrapi"""
    
    def __init__(self, account_id: int):
        self.account_id = account_id
        self.instagram_manager = InstagramManager()
        self.db_manager = DatabaseManager()
        self.config = Config()
        
        # OpenAI Configuration - try multiple sources
        self.openai_api_key = (
            os.environ.get('OPENAI_API_KEY') or  # Environment variable first
            self.config.OPENAI_CONFIG.get('api_key', '') or  # Config file
            ''  # Empty fallback
        )
        
        if not self.openai_api_key:
            logger.warning("OpenAI API key not found. Message generation will not work.")
            logger.info("Please set OPENAI_API_KEY environment variable or update config.py")
            # Don't raise error here, allow testing of other functionality
        
        # File paths
        self.input_csv = f"C:/files/account_data_{account_id}.csv"
        self.output_csv = f"C:/files/bio_messages_{account_id}.csv"
        self.tracking_file = f"C:/files/processed_usernames_{account_id}.txt"
        self.custom_prompts_dir = "custom_prompts"
        
        # Processing limits
        self.max_rows_per_run = 2000
        self.batch_size = 10  # Process in smaller batches
        self.max_retries = 3
        
        # Statistics
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # Ensure directories exist
        os.makedirs(self.custom_prompts_dir, exist_ok=True)
        os.makedirs(os.path.dirname(self.output_csv), exist_ok=True)
        
        logger.info(f"AI Message Generator Bot initialized for account {account_id}")
    
    def get_account_credentials(self) -> Tuple[str, str, str]:
        """Get account credentials from database"""
        try:
            account = self.db_manager.get_account_by_id(self.account_id)
            if not account:
                raise Exception(f"Account {self.account_id} not found in database")
            
            username = account['username']
            password = account['password']
            secret_key = account.get('secretkey', '').replace(" ", "")
            
            logger.info(f"Retrieved credentials for account: {username}")
            return username, password, secret_key
            
        except Exception as e:
            logger.error(f"Failed to get credentials: {e}")
            raise
    
    def get_custom_prompt(self) -> str:
        """Get custom prompt for this account, or return default"""
        prompt_file = os.path.join(self.custom_prompts_dir, f"account_{self.account_id}.txt")
        
        if os.path.exists(prompt_file):
            try:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    custom_prompt = f.read().strip()
                logger.info(f"Using custom prompt for account {self.account_id}")
                return custom_prompt
            except Exception as e:
                logger.error(f"Error reading custom prompt: {e}")
        
        # Default prompt
        logger.info("Using default prompt")
        return """You are a growth consultant for Instagram creators and businesses, reaching out to potential clients with personalized messages. Your goal is to start a conversation that will lead to a sales opportunity for your Instagram growth services.

PERSONALITY:
- Friendly, warm, and human while maintaining professionalism
- Emotionally connecting but subtly strategic
- Consultant-like rather than marketing-like
- Someone who genuinely wants to help the client unlock their potential

KEY APPROACH:
1. Always reference specific details from their bio (followers, content type, interests)
2. Position yourself as someone who noticed their profile's potential
3. Make the client feel seen and valued
4. SUBTLY guide the conversation towards a sales opportunity
5. Create curiosity about how you could help them grow their account/business
6. Include a hint of FOMO (fear of missing out) without being pushy
7. Always include a clear next step or question to continue the conversation

LANGUAGE GUIDELINES:
- Use words like: potential, opportunity, unlock, grow, expand, reach, impact, achieve, goals
- Phrases to include (modify naturally):
  * "We've noticed your profile has significant potential for growth"
  * "Your content deserves to reach a wider audience"
  * "We help creators like you unlock new opportunities"
  * "With the right approach, your account could achieve much more impact"
  * "We'd love to share how we could help you reach your goals"

MESSAGE STRUCTURE:
1. Personalized greeting using their username
2. Acknowledge their specific content/interests from bio
3. Compliment something genuine about their profile
4. Introduce the growth opportunity concept
5. Add subtle FOMO element
6. Include clear next step or question

MESSAGE RULES:
- Keep under 100 words total
- Feel like a real person, not automated
- Never make assumptions about details not in their bio
- Include specific follower numbers if mentioned in bio
- Maximum 2-3 emojis (use sparingly and naturally)
- Always end with an invitation to respond

The message should feel like it's from a real consultant who believes in the client's potential, not just a standard marketing message."""
    
    def load_processed_usernames(self) -> set:
        """Load already processed usernames from tracking files"""
        processed = set()
        
        # Load from tracking file
        if os.path.exists(self.tracking_file):
            try:
                with open(self.tracking_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        username = line.strip().lower()
                        if username:
                            processed.add(username)
                logger.info(f"Loaded {len(processed)} usernames from tracking file")
            except Exception as e:
                logger.error(f"Error loading tracking file: {e}")
        
        # Also check output file if it exists
        if os.path.exists(self.output_csv):
            try:
                output_df = pd.read_csv(self.output_csv)
                if 'username' in output_df.columns:
                    for username in output_df['username']:
                        if not pd.isna(username):
                            processed.add(str(username).strip().lower())
                logger.info(f"Total {len(processed)} usernames already processed")
            except Exception as e:
                logger.error(f"Error loading output file: {e}")
        
        return processed
    
    def save_username_to_tracking(self, username: str):
        """Save a processed username to the tracking file"""
        try:
            with open(self.tracking_file, 'a', encoding='utf-8') as f:
                f.write(f"{username.strip().lower()}\n")
        except Exception as e:
            logger.error(f"Error updating tracking file: {e}")
    
    def enhance_user_bio_with_instagram_data(self, username: str, original_bio: str) -> str:
        """Enhance user bio with live Instagram data using instagrapi"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account or not account.is_logged_in():
                logger.warning("Account not logged in, using original bio")
                return original_bio
            
            # Get user info from Instagram
            user_info = self.instagram_manager.get_user_info(self.account_id, username)
            if not user_info:
                logger.warning(f"Could not get Instagram info for {username}")
                return original_bio
            
            # Enhance bio with real-time data
            enhanced_bio = original_bio or ""
            
            # Add follower count if available
            if user_info.get('follower_count', 0) > 0:
                enhanced_bio += f"\nFollowers: {user_info['follower_count']:,}"
            
            # Add following count
            if user_info.get('following_count', 0) > 0:
                enhanced_bio += f"\nFollowing: {user_info['following_count']:,}"
            
            # Add media count
            if user_info.get('media_count', 0) > 0:
                enhanced_bio += f"\nPosts: {user_info['media_count']:,}"
            
            # Add verification status
            if user_info.get('is_verified', False):
                enhanced_bio += "\nVerified account"
            
            # Add privacy status
            if user_info.get('is_private', False):
                enhanced_bio += "\nPrivate account"
            
            logger.debug(f"Enhanced bio for {username}")
            return enhanced_bio
            
        except Exception as e:
            logger.warning(f"Error enhancing bio for {username}: {e}")
            return original_bio
    
    def load_input_data(self) -> List[Dict[str, Any]]:
        """Load and filter input data from CSV"""
        try:
            if not os.path.exists(self.input_csv):
                logger.error(f"Input CSV file not found: {self.input_csv}")
                return []
            
            # Load CSV
            df = pd.read_csv(self.input_csv)
            logger.info(f"Loaded {len(df)} rows from input CSV")
            
            # Filter out Instagram Users and empty usernames
            df = df[df['title'] != "Instagram User"]
            df = df[df['title'] != "Instagram user"]
            df = df[df['username'].notna()]
            df = df[df['username'] != '']
            
            logger.info(f"Filtered to {len(df)} valid users")
            
            # Load already processed usernames
            processed_usernames = self.load_processed_usernames()
            
            # Filter out already processed users
            df['username_lower'] = df['username'].str.lower()
            df = df[~df['username_lower'].isin(processed_usernames)]
            
            logger.info(f"After removing processed users: {len(df)} remaining")
            
            # Limit to max rows per run
            if len(df) > self.max_rows_per_run:
                df = df.head(self.max_rows_per_run)
                logger.info(f"Limited to {self.max_rows_per_run} rows for this run")
            
            # Convert to list of dictionaries
            users_data = []
            for _, row in df.iterrows():
                user_data = {
                    'username': str(row['username']).strip(),
                    'id1': str(row.get('id1', '')),
                    'id3': str(row.get('id3', '')),
                    'title': str(row.get('title', '')),
                    'bio': str(row.get('bio', '') if not pd.isna(row.get('bio', '')) else ''),
                    'full_name': str(row.get('full_name', '') if not pd.isna(row.get('full_name', '')) else '')
                }
                users_data.append(user_data)
            
            return users_data
            
        except Exception as e:
            logger.error(f"Error loading input data: {e}")
            return []
    
    async def generate_message_for_user(self, session: aiohttp.ClientSession, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a single message using OpenAI API"""
        try:
            username = user_data['username']
            original_bio = user_data['bio']
            
            # Enhance bio with real Instagram data
            enhanced_bio = self.enhance_user_bio_with_instagram_data(username, original_bio)
            
            # Prepare OpenAI request
            url = "https://api.openai.com/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {self.openai_api_key}",
                "Content-Type": "application/json"
            }
            
            system_prompt = self.get_custom_prompt()
            
            data = {
                "model": self.config.OPENAI_CONFIG.get('model', 'gpt-4o-mini'),
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Create a personalized Instagram growth consultant message for '{username}' with this bio: '{enhanced_bio}'"}
                ],
                "max_tokens": self.config.OPENAI_CONFIG.get('max_tokens', 200),
                "temperature": self.config.OPENAI_CONFIG.get('temperature', 0.7)
            }
            
            async with session.post(url, headers=headers, json=data) as response:
                response_json = await response.json()
                
                if response.status == 200:
                    message = response_json["choices"][0]["message"]["content"].strip()
                    self.success_count += 1
                    
                    return {
                        'username': username,
                        'id1': user_data['id1'],
                        'id3': user_data['id3'],
                        'title': user_data['title'],
                        'full_name': user_data['full_name'],
                        'bio': enhanced_bio,
                        'original_bio': original_bio,
                        'message': message,
                        'status': 'success'
                    }
                else:
                    error_msg = response_json.get('error', {}).get('message', 'Unknown error')
                    self.error_count += 1
                    logger.error(f"OpenAI API error for {username}: {error_msg}")
                    
                    return {
                        'username': username,
                        'id1': user_data['id1'],
                        'id3': user_data['id3'],
                        'title': user_data['title'],
                        'full_name': user_data['full_name'],
                        'bio': enhanced_bio,
                        'original_bio': original_bio,
                        'message': f"Error: {error_msg}",
                        'status': 'error'
                    }
                    
        except Exception as e:
            self.error_count += 1
            logger.error(f"Exception generating message for {user_data['username']}: {e}")
            
            return {
                'username': user_data['username'],
                'id1': user_data['id1'],
                'id3': user_data['id3'],
                'title': user_data['title'],
                'full_name': user_data.get('full_name', ''),
                'bio': user_data['bio'],
                'original_bio': user_data['bio'],
                'message': f"Error: {str(e)}",
                'status': 'error'
            }
    
    async def generate_messages_batch(self, users_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate messages in parallel batches"""
        all_results = []
        
        async with aiohttp.ClientSession() as session:
            # Process in batches to avoid overwhelming the API
            for i in range(0, len(users_data), self.batch_size):
                batch = users_data[i:i + self.batch_size]
                logger.info(f"Processing batch {i//self.batch_size + 1}/{(len(users_data)-1)//self.batch_size + 1} ({len(batch)} users)")
                
                # Create tasks for batch
                tasks = [self.generate_message_for_user(session, user_data) for user_data in batch]
                
                # Execute batch
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"Batch processing exception: {result}")
                        self.error_count += 1
                    else:
                        all_results.append(result)
                        self.processed_count += 1
                
                # Add delay between batches to respect rate limits
                if i + self.batch_size < len(users_data):
                    await asyncio.sleep(1)
        
        return all_results
    
    def save_results(self, results: List[Dict[str, Any]]):
        """Save results to CSV file"""
        try:
            if not results:
                logger.warning("No results to save")
                return
            
            # Convert to DataFrame
            df = pd.DataFrame(results)
            
            # Append to existing file or create new
            if os.path.exists(self.output_csv):
                df.to_csv(self.output_csv, mode='a', header=False, index=False, encoding='utf-8')
            else:
                df.to_csv(self.output_csv, index=False, encoding='utf-8')
            
            # Update tracking file
            for result in results:
                if result['status'] == 'success':
                    self.save_username_to_tracking(result['username'])
            
            logger.info(f"Saved {len(results)} results to {self.output_csv}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    async def retry_failed_messages(self):
        """Retry failed messages from previous runs"""
        try:
            if not os.path.exists(self.output_csv):
                logger.info("No output file to check for errors")
                return
            
            logger.info("Checking for failed messages to retry...")
            
            df = pd.read_csv(self.output_csv)
            error_mask = df['message'].str.contains('Error:', na=False, case=False)
            error_rows = df[error_mask]
            
            if len(error_rows) == 0:
                logger.info("No errors found to retry")
                return
            
            logger.info(f"Found {len(error_rows)} failed messages to retry")
            
            # Prepare retry data
            retry_users = []
            for _, row in error_rows.iterrows():
                retry_users.append({
                    'username': str(row['username']),
                    'id1': str(row.get('id1', '')),
                    'id3': str(row.get('id3', '')),
                    'title': str(row.get('title', '')),
                    'bio': str(row.get('original_bio', row.get('bio', ''))),
                    'full_name': str(row.get('full_name', ''))
                })
            
            # Retry generation
            retry_results = await self.generate_messages_batch(retry_users)
            
            # Update the original file with successful retries
            for result in retry_results:
                if result['status'] == 'success':
                    # Update the row in the dataframe
                    mask = df['username'] == result['username']
                    df.loc[mask, 'message'] = result['message']
                    df.loc[mask, 'status'] = 'success'
            
            # Save updated dataframe
            df.to_csv(self.output_csv, index=False, encoding='utf-8')
            logger.info("Retry completed and results updated")
            
        except Exception as e:
            logger.error(f"Error during retry: {e}")
    
    def setup_instagram_client(self) -> bool:
        """Setup and login Instagram client for enhanced bio data"""
        try:
            # Get credentials
            username, password, secret_key = self.get_account_credentials()
            
            # Add account to manager
            self.instagram_manager.add_account(
                account_id=self.account_id,
                username=username,
                password=password,
                secret_key=secret_key
            )
            
            # Login (optional for message generation, but helpful for bio enhancement)
            success = self.instagram_manager.login_account(self.account_id)
            if success:
                logger.info(f"Instagram client logged in for enhanced bio data")
            else:
                logger.warning("Instagram login failed, will use basic bio data only")
            
            return True
            
        except Exception as e:
            logger.warning(f"Instagram setup failed, continuing with basic functionality: {e}")
            return False
    
    async def run(self, retry_errors: bool = True) -> Dict[str, Any]:
        """Run the AI message generator bot"""
        try:
            start_time = time.time()
            logger.info(f"Starting AI Message Generator Bot for account {self.account_id}")
            
            # Setup Instagram client (optional)
            self.setup_instagram_client()
            
            # Load input data
            logger.info("Loading input data...")
            users_data = self.load_input_data()
            
            if not users_data:
                logger.warning("No users to process")
                return {
                    'account_id': self.account_id,
                    'status': 'completed',
                    'processed_count': 0,
                    'success_count': 0,
                    'error_count': 0,
                    'message': 'No users to process'
                }
            
            logger.info(f"Processing {len(users_data)} users...")
            
            # Generate messages
            results = await self.generate_messages_batch(users_data)
            
            # Save results
            self.save_results(results)
            
            # Retry failed messages if requested
            if retry_errors:
                await self.retry_failed_messages()
            
            # Calculate final statistics
            total_time = time.time() - start_time
            
            final_results = {
                'account_id': self.account_id,
                'status': 'completed',
                'processing_time': f"{total_time:.2f} seconds",
                'total_processed': self.processed_count,
                'successful_messages': self.success_count,
                'failed_messages': self.error_count,
                'success_rate': f"{(self.success_count / max(self.processed_count, 1) * 100):.1f}%",
                'output_file': self.output_csv,
                'input_file': self.input_csv
            }
            
            logger.info("=== AI Message Generation Complete ===")
            logger.info(f"Total time: {total_time:.2f} seconds")
            logger.info(f"Processed: {self.processed_count}")
            logger.info(f"Successful: {self.success_count}")
            logger.info(f"Failed: {self.error_count}")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Critical error in AI message generator: {e}")
            return {
                'account_id': self.account_id,
                'status': 'error',
                'error': str(e),
                'processed_count': self.processed_count,
                'success_count': self.success_count,
                'error_count': self.error_count
            }

def main():
    """Main function for standalone execution"""
    if len(sys.argv) < 2:
        print("Usage: python ai_message_generator_bot.py <account_id> [retry_errors]")
        print("Example: python ai_message_generator_bot.py 1")
        print("Example: python ai_message_generator_bot.py 1 true")
        sys.exit(1)
    
    try:
        account_id = int(sys.argv[1])
        retry_errors = len(sys.argv) > 2 and sys.argv[2].lower() == 'true'
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)
        
        # Run AI message generator bot
        async def run_bot():
            bot = AIMessageGeneratorBot(account_id)
            results = await bot.run(retry_errors)
            
            print("\n=== Final Results ===")
            for key, value in results.items():
                print(f"{key}: {value}")
        
        # Run the async function
        asyncio.run(run_bot())
        
    except ValueError:
        print("Account ID must be a number")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

