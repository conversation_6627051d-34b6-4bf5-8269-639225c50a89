#!/usr/bin/env python3
"""
Instagram Manager - Core Instagram API wrapper using instagrapi
Replaces encrypted scripts with open-source solution
"""

import os
import time
import json
import logging
import threading
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from pathlib import Path

from instagrapi import Client
from instagrapi.exceptions import (
    LoginRequired, ChallengeRequired, RateLimitError, 
    ClientError, ClientConnectionError, ReloginAttemptExceeded
)

# Configure logging
logger = logging.getLogger(__name__)

class InstagramAccount:
    """Represents a single Instagram account with its client and metadata"""
    
    def __init__(self, account_id: int, username: str, password: str, secret_key: str = ""):
        self.account_id = account_id
        self.username = username
        self.password = password
        self.secret_key = secret_key
        # Initialize client (let instagrapi handle headers naturally)
        self.client = Client()
        self.session_file = f"sessions/session_{account_id}.json"
        self.last_activity = None
        self.lock = threading.Lock()
        self.login_attempts = 0
        self.max_login_attempts = 3
        self.message_count = 0
        self.daily_limit = 2000  # Updated limit as requested
        self.last_reset = datetime.now().date()
        
        # Challenge handling attributes
        self.last_challenge = None
        self.challenge_time = None
        self.challenge_retry_count = 0
        self.max_challenge_retries = 3
        
        # Try to automatically load existing session
        self._try_load_session()

    
    def _try_load_session(self):
        """Try to load existing session file"""
        try:
            if os.path.exists(self.session_file):
                self.client.load_settings(self.session_file)
                if self.is_logged_in():
                    logger.info(f"Account {self.account_id}: Session auto-loaded successfully")
                    self.last_activity = datetime.now()
                else:
                    logger.info(f"Account {self.account_id}: Session file exists but not valid")
            else:
                logger.info(f"Account {self.account_id}: No existing session file found")
        except Exception as e:
            logger.warning(f"Account {self.account_id}: Failed to auto-load session: {e}")
    
    def _should_retry_after_challenge(self) -> bool:
        """Check if we should retry after a challenge"""
        if not self.last_challenge or not self.challenge_time:
            return False
        
        # Wait at least 5 minutes before retry
        time_since_challenge = datetime.now() - self.challenge_time
        if time_since_challenge < timedelta(minutes=5):
            return False
        
        # Don't retry more than max times
        if self.challenge_retry_count >= self.max_challenge_retries:
            return False
        
        return True
    
    def _retry_after_challenge(self) -> bool:
        """Retry login after challenge with exponential backoff"""
        try:
            # Calculate wait time with exponential backoff
            wait_minutes = min(5 * (2 ** self.challenge_retry_count), 30)  # Max 30 minutes
            logger.info(f"Account {self.account_id}: Retrying after challenge, waiting {wait_minutes} minutes...")
            
            time.sleep(wait_minutes * 60)
            
            # Try to login again
            login_success = self.client.login(
                username=self.username,
                password=self.password
            )
            
            if login_success:
                logger.info(f"Account {self.account_id}: Challenge retry successful")
                self.challenge_retry_count = 0
                self.last_challenge = None
                return True
            else:
                self.challenge_retry_count += 1
                logger.warning(f"Account {self.account_id}: Challenge retry failed, attempt {self.challenge_retry_count}")
                return False
                
        except Exception as e:
            logger.error(f"Account {self.account_id}: Error during challenge retry: {e}")
            self.challenge_retry_count += 1
            return False
    
    def _handle_challenge(self, challenge_exception) -> bool:
        """Handle Instagram challenge with smart retry logic"""
        logger.warning(f"Account {self.account_id}: Challenge required - {challenge_exception}")
        
        # Store challenge info
        self.last_challenge = str(challenge_exception)
        self.challenge_time = datetime.now()
        
        # Try to handle challenge automatically
        try:
            logger.info(f"Account {self.account_id}: Attempting to handle challenge...")
            
            # Wait a bit before retry
            time.sleep(30)
            
            # Check if challenge is resolved
            if self.client.is_logged_in():
                logger.info(f"Account {self.account_id}: Challenge resolved automatically")
                return True
            
            # Try to get challenge info
            try:
                challenge = self.client.challenge_code_handler()
                if challenge:
                    logger.info(f"Account {self.account_id}: Challenge info retrieved: {challenge}")
                    # Wait longer and retry
                    time.sleep(60)
                    if self.client.is_logged_in():
                        logger.info(f"Account {self.account_id}: Challenge resolved after wait")
                        return True
            except Exception as e:
                logger.warning(f"Account {self.account_id}: Could not get challenge info: {e}")
            
        except Exception as challenge_error:
            logger.warning(f"Account {self.account_id}: Challenge handling failed: {challenge_error}")
        
        return False
        
    def is_logged_in(self) -> bool:
        """Check if account is currently logged in"""
        try:
            return bool(self.client.user_id)
        except:
            return False
    
    def login(self) -> bool:
        """Login to Instagram account with 2FA support and challenge handling"""
        with self.lock:
            try:
                # Try to load existing session first
                if os.path.exists(self.session_file):
                    try:
                        self.client.load_settings(self.session_file)
                        if self.is_logged_in():
                            logger.info(f"Account {self.account_id}: Session loaded successfully")
                            return True
                    except Exception as e:
                        logger.warning(f"Account {self.account_id}: Failed to load session: {e}")
                
                # Check if we should retry after challenge
                if self._should_retry_after_challenge():
                    return self._retry_after_challenge()
                
                # Perform fresh login
                logger.info(f"Account {self.account_id}: Attempting fresh login for {self.username}")
                
                # Generate 2FA code if secret key is provided
                verification_code = ""
                if self.secret_key:
                    import pyotp
                    totp = pyotp.TOTP(self.secret_key.replace(" ", ""))
                    verification_code = totp.now()
                    logger.info(f"Account {self.account_id}: Generated 2FA code")
                
                # Attempt login
                login_success = self.client.login(
                    username=self.username,
                    password=self.password,
                    verification_code=verification_code
                )
                
                if login_success:
                    # Save session for future use
                    os.makedirs(os.path.dirname(self.session_file), exist_ok=True)
                    self.client.dump_settings(self.session_file)
                    self.last_activity = datetime.now()
                    self.login_attempts = 0
                    self.challenge_retry_count = 0  # Reset challenge counter
                    logger.info(f"Account {self.account_id}: Login successful")
                    return True
                else:
                    self.login_attempts += 1
                    logger.error(f"Account {self.account_id}: Login failed")
                    return False
                    
            except ChallengeRequired as e:
                return self._handle_challenge(e)
            except RateLimitError as e:
                logger.error(f"Account {self.account_id}: Rate limit exceeded - {e}")
                return False
            except Exception as e:
                self.login_attempts += 1
                logger.error(f"Account {self.account_id}: Login error - {e}")
                return False
    
    def send_message(self, user_id: str, message: str) -> Tuple[bool, str]:
        """Send direct message to user"""
        with self.lock:
            try:
                # Check daily limit
                if self._check_daily_limit():
                    return False, "Daily message limit exceeded"
                
                # Ensure logged in
                if not self.is_logged_in():
                    if not self.login():
                        return False, "Login failed"
                
                # Send message
                result = self.client.direct_send(text=message, user_ids=[int(user_id)])
                
                if result:
                    self.message_count += 1
                    self.last_activity = datetime.now()
                    logger.info(f"Account {self.account_id}: Message sent successfully to {user_id}")
                    return True, "Message sent successfully"
                else:
                    return False, "Failed to send message"
                    
            except RateLimitError as e:
                logger.error(f"Account {self.account_id}: Rate limit in send_message - {e}")
                return False, f"Rate limit: {str(e)}"
            except Exception as e:
                logger.error(f"Account {self.account_id}: Error sending message - {e}")
                return False, f"Error: {str(e)}"
    
    def send_photo(self, user_id: str, photo_path: str, caption: str = "") -> Tuple[bool, str]:
        """Send photo message to user"""
        with self.lock:
            try:
                # Check daily limit
                if self._check_daily_limit():
                    return False, "Daily message limit exceeded"
                
                # Ensure logged in
                if not self.is_logged_in():
                    if not self.login():
                        return False, "Login failed"
                
                # Send photo
                result = self.client.direct_send_photo(
                    path=Path(photo_path), 
                    user_ids=[int(user_id)]
                )
                
                if result:
                    self.message_count += 1
                    self.last_activity = datetime.now()
                    logger.info(f"Account {self.account_id}: Photo sent successfully to {user_id}")
                    return True, "Photo sent successfully"
                else:
                    return False, "Failed to send photo"
                    
            except Exception as e:
                logger.error(f"Account {self.account_id}: Error sending photo - {e}")
                return False, f"Error: {str(e)}"
    
    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user information by username"""
        with self.lock:
            try:
                # Ensure logged in
                if not self.is_logged_in():
                    if not self.login():
                        return None
                
                # Get user ID first
                user_id = self.client.user_id_from_username(username)
                if not user_id:
                    logger.warning(f"Account {self.account_id}: User {username} not found")
                    return None
                
                # Get user info
                user_info = self.client.user_info(user_id)
                
                # Convert to dict for easier handling
                return {
                    'user_id': str(user_info.pk),
                    'username': user_info.username,
                    'full_name': user_info.full_name,
                    'biography': user_info.biography,
                    'follower_count': user_info.follower_count,
                    'following_count': user_info.following_count,
                    'media_count': user_info.media_count,
                    'is_private': user_info.is_private,
                    'is_verified': user_info.is_verified,
                    'profile_pic_url': str(user_info.profile_pic_url) if user_info.profile_pic_url else ''
                }
                
            except Exception as e:
                logger.error(f"Account {self.account_id}: Error getting user info for {username}: {e}")
                return None
    
    def search_users(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search for users"""
        with self.lock:
            try:
                # Ensure logged in
                if not self.is_logged_in():
                    if not self.login():
                        return []
                
                # Use the correct API call without count parameter
                users = self.client.search_users(query)
                if limit:
                    users = users[:limit]
                
                return [{
                    'user_id': str(user.pk),
                    'username': user.username,
                    'full_name': getattr(user, 'full_name', ''),
                    'is_verified': getattr(user, 'is_verified', False),
                    'follower_count': getattr(user, 'follower_count', 0),
                    'profile_pic_url': str(user.profile_pic_url) if user.profile_pic_url else None
                } for user in users]
                
            except Exception as e:
                logger.error(f"Account {self.account_id}: Error searching users - {e}")
                return []
    
    def set_proxy(self, proxy_url: str) -> bool:
        """Set proxy for this account"""
        try:
            self.client.set_proxy(proxy_url)
            # Mask credentials in logs
            masked = proxy_url
            try:
                if '://' in proxy_url and '@' in proxy_url:
                    scheme, rest = proxy_url.split('://', 1)
                    creds, host = rest.split('@', 1)
                    if ':' in creds:
                        user = creds.split(':', 1)[0]
                        masked = f"{scheme}://{user}:***@{host}"
            except Exception:
                pass
            logger.info(f"Account {self.account_id}: Proxy set to {masked}")
            return True
        except Exception as e:
            logger.error(f"Account {self.account_id}: Error setting proxy - {e}")
            return False
    
    def _check_daily_limit(self) -> bool:
        """Check if daily message limit is exceeded"""
        today = datetime.now().date()
        
        # Reset counter if new day
        if today > self.last_reset:
            self.message_count = 0
            self.last_reset = today
        
        return self.message_count >= self.daily_limit
    
    def get_stats(self) -> Dict[str, Any]:
        """Get account statistics"""
        return {
            'account_id': self.account_id,
            'username': self.username,
            'logged_in': self.is_logged_in(),
            'last_activity': self.last_activity.isoformat() if self.last_activity else None,
            'message_count': self.message_count,
            'daily_limit': self.daily_limit,
            'login_attempts': self.login_attempts
        }


class InstagramManager:
    """Main manager for multiple Instagram accounts"""
    
    def __init__(self):
        self.accounts: Dict[int, InstagramAccount] = {}
        self.proxy_list = []
        self.current_proxy_index = 0
        self.default_proxy_credential = os.getenv('ROTATING_PROXY_CREDENTIAL', '').strip()
        
        # Initialize database manager
        try:
            from core.database import DatabaseManager
            self.db_manager = DatabaseManager()
        except Exception as e:
            logger.error(f"Failed to initialize database manager: {e}")
            self.db_manager = None
        
        # Setup logging
        self._setup_logging()
        
        # Create sessions directory
        os.makedirs("sessions", exist_ok=True)
        
        logger.info("Instagram Manager initialized")
    
    def _setup_logging(self):
        """Setup logging configuration"""
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = f"{log_dir}/instagram_manager_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def add_account(self, account_id: int, username: str, password: str, secret_key: str = "") -> bool:
        """Add Instagram account to manager with automatic session loading"""
        try:
            account = InstagramAccount(account_id, username, password, secret_key)
            
            # Check if session already exists and try to load it
            if account.is_logged_in():
                logger.info(f"Added account {account_id}: {username} (session already loaded)")
            else:
                logger.info(f"Added account {account_id}: {username} (new session)")
            
            self.accounts[account_id] = account
            return True
        except Exception as e:
            logger.error(f"Error adding account {account_id}: {e}")
            return False
    
    def remove_account(self, account_id: int) -> bool:
        """Remove account from manager"""
        if account_id in self.accounts:
            del self.accounts[account_id]
            logger.info(f"Removed account {account_id}")
            return True
        return False
    
    def get_account(self, account_id: int) -> Optional[InstagramAccount]:
        """Get account by ID"""
        # Check if account is already loaded
        if account_id in self.accounts:
            return self.accounts[account_id]
        
        # Try to load account from database
        try:
            account_data = self.db_manager.get_account_by_id(account_id)
            if account_data:
                # Create new InstagramAccount instance
                account = InstagramAccount(
                    account_id=account_id,
                    username=account_data['username'],
                    password=account_data['password'],
                    secret_key=account_data.get('secretkey', '')
                )
                self.accounts[account_id] = account
                logger.info(f"Loaded account {account_id} from database")
                return account
            else:
                logger.warning(f"Account {account_id} not found in database")
                return None
        except Exception as e:
            logger.error(f"Error loading account {account_id} from database: {e}")
            return None
    
    def get_all_accounts(self) -> List[Dict[str, Any]]:
        """Get all accounts from database"""
        try:
            return self.db_manager.get_all_accounts()
        except Exception as e:
            logger.error(f"Error getting all accounts: {e}")
            return []
    
    def get_available_account(self, exclude_account_id: int = None) -> Optional[InstagramAccount]:
        """Get an available account for operations, with smart rotation"""
        available_accounts = []
        
        for acc_id, account in self.accounts.items():
            if exclude_account_id and acc_id == exclude_account_id:
                continue
                
            # Check if account is available (not in challenge state)
            if account.is_logged_in() and not account.last_challenge:
                available_accounts.append(account)
            elif account.last_challenge:
                # Check if enough time has passed to retry
                if account._should_retry_after_challenge():
                    available_accounts.append(account)
        
        if not available_accounts:
            return None
        
        # Return the account with least recent activity
        return min(available_accounts, key=lambda acc: acc.last_activity or datetime.min)
    
    def login_account(self, account_id: int) -> bool:
        """Login specific account"""
        account = self.get_account(account_id)
        if account:
            return account.login()
        return False
    
    def login_all_accounts(self) -> Dict[int, bool]:
        """Login all accounts"""
        results = {}
        for account_id, account in self.accounts.items():
            results[account_id] = account.login()
            # Small delay between logins to avoid rate limits
            time.sleep(1)
        return results
    
    def send_message(self, account_id: int, user_id: str, message: str) -> Tuple[bool, str]:
        """Send message using specific account"""
        account = self.get_account(account_id)
        if account:
            return account.send_message(user_id, message)
        return False, "Account not found"
    
    def send_photo(self, account_id: int, user_id: str, photo_path: str, caption: str = "") -> Tuple[bool, str]:
        """Send photo using specific account"""
        account = self.get_account(account_id)
        if account:
            return account.send_photo(user_id, photo_path, caption)
        return False, "Account not found"
    
    def get_user_info(self, account_id: int, username: str) -> Optional[Dict[str, Any]]:
        """Get user info using specific account"""
        account = self.get_account(account_id)
        if account:
            return account.get_user_info(username)
        return None
    
    def search_users(self, account_id: int, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search users using specific account"""
        account = self.get_account(account_id)
        if account:
            return account.search_users(query, limit)
        return []
    
    def load_proxy_list(self, proxy_file: str) -> bool:
        """Load proxy list from file"""
        try:
            with open(proxy_file, 'r') as f:
                self.proxy_list = [line.strip() for line in f if line.strip()]
            logger.info(f"Loaded {len(self.proxy_list)} proxies")
            return True
        except Exception as e:
            logger.error(f"Error loading proxy list: {e}")
            return False

    def _build_proxy_url(self, base_credential: str, country_code: Optional[str] = None) -> Optional[str]:
        """Convert supported credential formats to valid proxy URL.

        - http(s)://user:pass@host:port (no change)
        - http(s)://host:port:user:pass (convert)
        - host:port:user:pass (assume http, convert)
        """
        try:
            if not base_credential:
                return None
            cred = base_credential.strip()
            scheme = 'http://'
            if cred.startswith('http://'):
                cred_wo_scheme = cred[len('http://'):]
            elif cred.startswith('https://'):
                scheme = 'https://'
                cred_wo_scheme = cred[len('https://'):]
            else:
                cred_wo_scheme = cred
            # Already user:pass@host:port
            if '@' in cred_wo_scheme:
                return f"{scheme}{cred_wo_scheme}"
            parts = cred_wo_scheme.split(':')
            if len(parts) == 4:
                host, port, user, pwd = parts
                return f"{scheme}{user}:{pwd}@{host}:{port}"
            return f"{scheme}{cred_wo_scheme}"
        except Exception as e:
            logger.error(f"Error building proxy URL: {e}")
            return None
    
    def assign_proxy_to_account(self, account_id: int, country_code: str = None, force_proxy: bool = False) -> bool:
        """Assign proxy to specific account with optional country.

        Priority:
        1) Skip unless force_proxy
        2) Use proxy from list
        3) Use ROTATING_PROXY_CREDENTIAL env
        4) Fallback to legacy Evomi defaults
        """
        account = self.get_account(account_id)
        if not account:
            return False
        
        # Only assign proxy if explicitly requested (for cost efficiency)
        if not force_proxy:
            logger.info(f"Account {account_id}: Proxy not assigned (cost optimization)")
            return True
        
        proxy_url: Optional[str] = None
        # 1) Proxy from list
        if self.proxy_list:
            proxy_index = account_id % len(self.proxy_list)
            candidate = self.proxy_list[proxy_index]
            proxy_url = self._build_proxy_url(candidate, country_code)
        # 2) Environment/default rotating credential
        if not proxy_url and self.default_proxy_credential:
            proxy_url = self._build_proxy_url(self.default_proxy_credential, country_code)
        # 3) Legacy fallback (using current Evomi proxy)
        if not proxy_url:
            if country_code:
                proxy_url = f"http://robertthom3:<EMAIL>:1000"
            else:
                proxy_url = "http://robertthom3:<EMAIL>:1000"
        return account.set_proxy(proxy_url)
    
    def get_all_stats(self) -> Dict[int, Dict[str, Any]]:
        """Get statistics for all accounts"""
        return {
            account_id: account.get_stats() 
            for account_id, account in self.accounts.items()
        }
    
    def get_active_accounts(self) -> List[int]:
        """Get list of logged-in account IDs"""
        return [
            account_id for account_id, account in self.accounts.items()
            if account.is_logged_in()
        ]
    
    def send_direct_message(self, account_id: int, username: str, message: str) -> bool:
        """Send direct message to user"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            logger.error(f"Account {account_id} not found or not logged in")
            return False
        
        try:
            # Search for user
            users = account.client.search_users(username)
            if not users:
                logger.error(f"User {username} not found")
                return False
            
            user_id = users[0].pk
            
            # Send direct message
            account.client.direct_send(message, [user_id])
            
            # Update account stats
            account.message_count += 1
            logger.info(f"Message sent to {username} from account {account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending message to {username} from account {account_id}: {e}")
            return False
    
    def get_user_info(self, account_id: int, username: str) -> Optional[Dict[str, Any]]:
        """Get user information"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            return None
        
        try:
            # Search for user first
            users = account.client.search_users(username)
            if not users:
                return None
            
            user_id = users[0].pk
            user_info = account.client.user_info(user_id)
            
            return {
                'user_id': user_info.pk,
                'username': user_info.username,
                'full_name': user_info.full_name,
                'follower_count': user_info.follower_count,
                'following_count': user_info.following_count,
                'media_count': user_info.media_count,
                'biography': user_info.biography,
                'is_private': user_info.is_private,
                'is_verified': user_info.is_verified,
                'profile_pic_url': str(user_info.profile_pic_url) if user_info.profile_pic_url else ''
            }
            
        except Exception as e:
            logger.error(f"Error getting user info for {username}: {e}")
            return None
    
    def get_user_media(self, account_id: int, username: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get user's media posts with better error handling"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            return []
        
        try:
            # Search for user first
            users = account.client.search_users(username)
            if not users:
                return []
            
            user_id = users[0].pk
            
            # Try multiple methods to get user media
            medias = []
            
            # Try to get user media with error handling
            try:
                # Use a simpler approach - just get basic user info
                user_info = account.client.user_info(user_id)
                if user_info:
                    # Create a basic media representation
                    basic_media = {
                        'media_id': 'profile',
                        'media_type': 'profile',
                        'caption': f"Profile of {user_info.username}",
                        'like_count': getattr(user_info, 'follower_count', 0),
                        'comment_count': getattr(user_info, 'following_count', 0),
                        'taken_at': None
                    }
                    medias = [basic_media]
                    logger.info(f"Account {account_id}: Retrieved basic profile info for {username}")
                else:
                    logger.warning(f"Account {account_id}: Could not get user info for {username}")
                    return []
                    
            except Exception as e:
                logger.warning(f"Account {account_id}: Media retrieval failed: {e}")
                return []
            
            result = []
            for media in medias:
                try:
                    # Handle both dict and object types
                    if isinstance(media, dict):
                        media_data = {
                            'media_id': str(media.get('media_id', 'unknown')),
                            'media_type': media.get('media_type', 'unknown'),
                            'caption': media.get('caption', ''),
                            'like_count': media.get('like_count', 0),
                            'comment_count': media.get('comment_count', 0),
                            'taken_at': media.get('taken_at')
                        }
                    else:
                        media_data = {
                            'media_id': str(getattr(media, 'pk', 'unknown')),
                            'media_type': getattr(media, 'media_type', 'unknown'),
                            'caption': getattr(media, 'caption_text', '') if hasattr(media, 'caption_text') else '',
                            'like_count': getattr(media, 'like_count', 0),
                            'comment_count': getattr(media, 'comment_count', 0),
                            'taken_at': media.taken_at.isoformat() if hasattr(media, 'taken_at') and media.taken_at else None
                        }
                    result.append(media_data)
                except Exception as e:
                    logger.warning(f"Account {account_id}: Error processing media: {e}")
                    continue
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting media for {username}: {e}")
            return []
    
    def send_direct_photo(self, account_id: int, username: str, photo_path: str, caption: str = "") -> bool:
        """Send direct photo message to user"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            logger.error(f"Account {account_id} not found or not logged in")
            return False
        
        try:
            # Search for user
            users = account.client.search_users(username)
            if not users:
                logger.error(f"User {username} not found")
                return False
            
            user_id = users[0].pk
            
            # Send direct photo
            account.client.direct_send_photo(photo_path, [user_id], caption)
            
            # Update account stats
            account.message_count += 1
            logger.info(f"Photo sent to {username} from account {account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending photo to {username} from account {account_id}: {e}")
            return False
    
    def send_direct_video(self, account_id: int, username: str, video_path: str, caption: str = "") -> bool:
        """Send direct video message to user"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            logger.error(f"Account {account_id} not found or not logged in")
            return False
        
        try:
            # Search for user
            users = account.client.search_users(username)
            if not users:
                logger.error(f"User {username} not found")
                return False
            
            user_id = users[0].pk
            
            # Send direct video
            account.client.direct_send_video(video_path, [user_id], caption)
            
            # Update account stats
            account.message_count += 1
            logger.info(f"Video sent to {username} from account {account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending video to {username} from account {account_id}: {e}")
            return False
    
    def delete_direct_thread(self, account_id: int, thread_id: str) -> bool:
        """Delete/hide direct message thread"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            logger.error(f"Account {account_id} not found or not logged in")
            return False
        
        try:
            # Hide/delete thread using instagrapi
            result = account.client.direct_thread_hide(thread_id)
            
            logger.info(f"Thread {thread_id} deleted from account {account_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error deleting thread {thread_id} from account {account_id}: {e}")
            return False
    
    def get_direct_threads_list(self, account_id: int, amount: int = 20) -> List[Dict[str, Any]]:
        """Get list of direct message threads"""
        account = self.get_account(account_id)
        if not account or not account.is_logged_in():
            logger.error(f"Account {account_id} not found or not logged in")
            return []
        
        try:
            # Get direct message threads
            threads = account.client.direct_threads(amount=amount)
            
            thread_list = []
            for thread in threads:
                thread_info = {
                    'thread_id': thread.id,
                    'thread_title': thread.thread_title or 'Direct',
                    'last_activity_at': thread.last_activity_at.isoformat() if thread.last_activity_at else '',
                    'user_count': len(thread.users),
                    'is_group': len(thread.users) > 1,
                    'users': [
                        {
                            'user_id': user.pk,
                            'username': user.username,
                            'full_name': user.full_name or ''
                        }
                        for user in thread.users
                    ]
                }
                thread_list.append(thread_info)
            
            logger.info(f"Retrieved {len(thread_list)} threads from account {account_id}")
            return thread_list
            
        except Exception as e:
            logger.error(f"Error getting threads from account {account_id}: {e}")
            return []
    
    def shutdown(self):
        """Shutdown manager and save all sessions"""
        logger.info("Shutting down Instagram Manager")
        for account_id, account in self.accounts.items():
            try:
                if account.is_logged_in():
                    account.client.dump_settings(account.session_file)
            except Exception as e:
                logger.error(f"Error saving session for account {account_id}: {e}")
        
        logger.info("Instagram Manager shutdown complete")


# Global manager instance
instagram_manager = InstagramManager()

# Convenience functions
def get_manager() -> InstagramManager:
    """Get global Instagram manager instance"""
    return instagram_manager

def init_manager() -> InstagramManager:
    """Initialize and return Instagram manager"""
    return get_manager()
