(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5445],{701:function(e,n,t){Promise.resolve().then(t.bind(t,46749))},46749:function(e,n,t){"use strict";t.r(n);var o=t(27573),r=t(11607),i=t(39761),s=t(22396);function u(e){let{children:n}=e;return(0,i.l)(),n}n.default=(0,s.M)(u,{onLoading:()=>(0,o.jsx)(u,{children:(0,o.jsx)(r.<PERSON>,{})})})},39761:function(e,n,t){"use strict";t.d(n,{E:function(){return u},l:function(){return a}});var o=t(27573),r=t(18013),i=t(7653),s=t(45790);let u={default:{title:(0,o.jsx)(s.Z,{id:"lKv8exH0MR",defaultMessage:"Default"}),user:"--font-sans-serif",claude:"--font-serif"},system:{title:(0,o.jsx)(s.Z,{id:"KNFWQ+T1/T",defaultMessage:"Match system"}),user:"--font-system",claude:"--font-system"},dyslexia:{title:(0,o.jsx)(s.Z,{id:"twBGxrOFSV",defaultMessage:"Dyslexic friendly"}),user:"--font-dyslexia",claude:"--font-dyslexia"}},a=()=>{let[e,n]=(0,r.R)("customStyles:chatFont","default");return(0,i.useEffect)(()=>{let n=u[e];document.documentElement.style.setProperty("--font-user-message","var(".concat(n.user,")")),document.documentElement.style.setProperty("--font-claude-message","var(".concat(n.claude,")"))},[e]),{chatFontSetting:e,setChatFontSetting:n}}},22396:function(e,n,t){"use strict";t.d(n,{M:function(){return a}});var o=t(27573),r=t(48321),i=t(8571),s=t(45790);function u(){let e=(0,i.m)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("p",{children:(0,o.jsx)(s.Z,{defaultMessage:"If you intended to log into <link>Anthropic Console</link>, please navigate to that page and try again.",id:"/an+9Ro/tl",values:{link:n=>(0,o.jsx)("a",{className:"underline",href:e.consoleAbsoluteUrl,children:n})}})}),(0,o.jsx)("p",{children:(0,o.jsx)(s.Z,{defaultMessage:"If you intended to log into Claude Chat, please log out and try again.",id:"j/CYnDh9yk"})})]})}let a=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(0,r.withCurrentAccountRequired)(e,{productSurface:"claude-ai",errorMessage:(0,o.jsx)(u,{}),...n})}},19170:function(e,n,t){"use strict";t.d(n,{Ck:function(){return k},IR:function(){return _},MZ:function(){return E},Pd:function(){return m},Uc:function(){return v},W1:function(){return l},_y:function(){return y},fb:function(){return x},gq:function(){return S},k7:function(){return p},ml:function(){return g},wO:function(){return d},yU:function(){return f},yq:function(){return h}});var o=t(3053),r=t(5362),i=t(8571),s=t(29305),u=t(13262),a=t(77930),c=t(81695);function l(e,n){return(0,r.WE)("/api/auth/login_methods?email=".concat(e,"&source=").concat(n),{enabled:!!e&&(0,s.vV)(e),meta:{noToast:!0}})}let d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:n}=(0,o.z$)(),t=(0,i.Z)();return(0,r.uC)("/api/auth/send_magic_link","POST",{...e,onSuccess:(t,o,r)=>{t.sso_url?n({event_key:"login.email.sso_initiated"}):n({event_key:"login.email.magic_link_sent"}),e.onSuccess&&e.onSuccess(t,o,r)},onError:(t,o,r)=>{n({event_key:"login.email.magic_link_send_error",error:t.message}),e.onError&&e.onError(t,o,r)},onMutate:e=>{n({event_key:"login.email.sending_magic_link"})},transformVariables:e=>({...e,source:t?"claude":"console"})})},f=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=(0,i.Z)();return(0,r.uC)("/api/auth/exchange_nonce_for_code","POST",{...e,transformVariables:e=>({...e,source:n?"claude":"console"})})},g=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:n}=(0,o.z$)(),t=(0,i.Z)();return(0,r.uC)("/api/auth/verify_magic_link","POST",{...e,onSuccess:(t,o,r)=>{"code"===o.credentials.method?n({event_key:"login.email.finished"}):n({event_key:"login.email.magic_link_success"}),t.created&&C(n,t.account,"email"),e.onSuccess&&e.onSuccess(t,o,r)},onError:(t,o,r)=>{"code"===o.credentials.method?n({event_key:"login.email.code_verification_error",error:t.message}):n({event_key:"login.email.magic_link_verification_error",error:t.message}),e.onError&&e.onError(t,o,r)},onMutate:e=>{"code"===e.credentials.method?n({event_key:"login.email.verifying_code"}):n({event_key:"login.email.verifying_magic_link"})},transformVariables:e=>({...e,source:t?"claude":"console"})})},_=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:n}=(0,o.z$)(),t=(0,i.Z)(),s=(0,c.useRouter)();return(0,r.uC)("/api/auth/verify_google","POST",{...e,onSuccess:(t,o,r)=>{!t.success&&t.sso_url?(n({event_key:"login.email.sso_initiated"}),s.push(t.sso_url)):(n({event_key:"login.google.finished"}),t.created&&C(n,t.account,"google"),e.onSuccess&&e.onSuccess(t,o,r))},onError:(t,o,r)=>{n({event_key:"login.google.verification_error",error:t.message}),e.onError&&e.onError(t,o,r)},onMutate:e=>{n({event_key:"login.google.verifying"})},transformVariables:e=>({...e,source:t?"claude":"console"})})},m=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:n}=(0,o.z$)(),t=(0,i.Z)(),s=(0,c.useRouter)();return(0,r.uC)("/api/auth/accept_invite","POST",{...e,onSuccess:(t,o,r)=>{!t.success&&t.sso_url?(n({event_key:"login.email.sso_initiated"}),s.push(t.sso_url)):e.onSuccess&&e.onSuccess(t,o,r)},transformVariables:e=>({...e,source:t?"claude":"console"})})},y=()=>(0,r.uC)("/api/auth/logout","POST"),v=()=>(0,r.uC)("/api/auth/logout/all-sessions","POST"),h=()=>{let e=(0,a.useQueryClient)();return(0,r.uC)("/api/account","PUT",{async onSuccess(){await e.invalidateQueries({queryKey:[u.aY]})}})},k=()=>(0,r.Ne)("/api/account","PUT",(e,n)=>(null==n?void 0:n.account)?{...n,account:{...n.account,settings:e}}:n,{queryKey:[u.aY],transformVariables:e=>({settings:e})}),p=()=>{let e=(0,a.useQueryClient)();return(0,r.uC)("/api/account/accept_legal_docs","PUT",{async onSuccess(){await e.invalidateQueries({queryKey:[u.aY]})}})};function S(e){return(0,r.WE)("/api/signups/".concat(e),{enabled:!!e,staleTime:0})}function E(e){return(0,r.WE)("/api/enterprise_auth/idp_redirect_url?organization_id=".concat(e),{meta:{noToast:!0}})}function x(e,n,t){return(0,r.WE)("/api/enterprise_auth/sso_callback?code=".concat(e,"&state=").concat(n,"&source=").concat(t),{meta:{noToast:!0}})}function C(e,n,t){e({event_key:"login.account.created",authMethod:t},n)}},29305:function(e,n,t){"use strict";t.d(n,{KS:function(){return i},vV:function(){return r}});let o=/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/,r=e=>o.test(e),i=(e,n)=>{if(!n)return!1;let t=e.split("@")[1];return n.includes(t)}}},function(e){e.O(0,[5790,3279,6947,6340,5992,7423,1270,7879,6971,1211,7265,1121,3097,2821,1006,9505,9833,294,436,8877,3053,3780,8321,1293,1362,4856,1744],function(){return e(e.s=701)}),_N_E=e.O()}]);