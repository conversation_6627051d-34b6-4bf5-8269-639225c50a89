#!/usr/bin/env python3
"""
Check table structure
"""

from core.database import DatabaseManager

def main():
    db = DatabaseManager()
    
    try:
        # Check scraped_users table structure
        result = db.execute_query('DESCRIBE scraped_users', fetch=True)
        print('Current scraped_users table structure:')
        for row in result:
            print(f"  {row['Field']}: {row['Type']} {row['Null']} {row['Key']} {row['Default']}")
        
        # Check if we need to add missing columns
        existing_columns = [row['Field'] for row in result]
        required_columns = [
            'followers_count', 'following_count', 'profile_pic_url', 'is_verified', 
            'is_private', 'external_url', 'account_type', 'business_category_name',
            'category_name', 'category', 'public_phone_number', 'public_email',
            'city_name', 'address_street', 'zip', 'latitude', 'longitude',
            'contact_phone_number', 'contact_email', 'website'
        ]
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        
        if missing_columns:
            print(f"\nMissing columns: {missing_columns}")
            print("Adding missing columns...")
            
            for col in missing_columns:
                if col in ['latitude', 'longitude']:
                    sql = f"ALTER TABLE scraped_users ADD COLUMN {col} DECIMAL(10,8)"
                elif col in ['followers_count', 'following_count']:
                    sql = f"ALTER TABLE scraped_users ADD COLUMN {col} INT DEFAULT 0"
                elif col in ['is_verified', 'is_private']:
                    sql = f"ALTER TABLE scraped_users ADD COLUMN {col} TINYINT DEFAULT 0"
                else:
                    sql = f"ALTER TABLE scraped_users ADD COLUMN {col} VARCHAR(255) DEFAULT ''"
                
                try:
                    db.execute_query(sql)
                    print(f"  Added column: {col}")
                except Exception as e:
                    print(f"  Error adding {col}: {e}")
        else:
            print("\nAll required columns exist!")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()

