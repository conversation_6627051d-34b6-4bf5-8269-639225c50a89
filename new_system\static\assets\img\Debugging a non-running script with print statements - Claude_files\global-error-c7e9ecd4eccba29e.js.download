(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6470],{77687:function(e,t,a){Promise.resolve().then(a.bind(a,74566))},74566:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return v}});var l=a(27573),c=a(64483),s=a(22769),n=a(14581),r=a.n(n),i=a(94957),o=a.n(i),h=a(42312),_=a.n(h),f=a(95601),x=a.n(f),u=a(19839),m=a.n(u),b=a(45144),p=a(7653);function v(e){let{error:t}=e;return(0,p.useEffect)(()=>{b.Tb(t,{tags:{page:"global-error"}})},[t]),(0,l.jsx)("html",{className:"h-screen antialiased [font-feature-settings:'ss01'] ".concat(_().variable," ").concat(x().variable," ").concat(m().variable," ").concat(r().variable," ").concat(o().variable," bg-bg-200"),lang:"en",children:(0,l.jsx)("body",{className:"from-bg-200 to-bg-100 text-text-100 font-styrene min-h-screen bg-gradient-to-b bg-fixed tracking-tight",children:(0,l.jsxs)("div",{className:"flex h-screen flex-col items-center justify-center px-4 py-10 text-center",children:[(0,l.jsxs)("div",{className:"flex flex-1 flex-col items-center justify-center gap-2 pb-14",children:[(0,l.jsx)(s.s,{className:"mb-11 mr-px h-7"}),(0,l.jsx)("h1",{className:"font-copernicus text-2xl font-medium tracking-tight sm:text-[2rem]",children:"Claude will return soon"}),(0,l.jsxs)("p",{className:"text-text-200 max-w-xl sm:text-lg",children:["Claude.ai is currently experiencing a temporary service disruption. We’re"," ",(0,l.jsx)("a",{className:"hover:text-text-000 underline underline-offset-2 transition-colors",href:"https://status.anthropic.com/",children:"working on it"}),", please check back soon."]})]}),(0,l.jsx)("a",{href:"https://anthropic.com",title:"Learn more about Anthropic",className:"text-text-300 hover:text-text-100",children:(0,l.jsx)(c.I,{height:12})})]})})})}},64483:function(e,t,a){"use strict";a.d(t,{I:function(){return c}});var l=a(27573);function c(e){let{height:t=14,className:a}=e;return(0,l.jsxs)("svg",{height:t,className:a,viewBox:"0 0 110 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","aria-label":"Anthropic",children:[(0,l.jsx)("path",{d:"M26.92 2.43646H30.929V11.8011H33.4879V2.43646H37.4969V0.198895H26.92V2.43646Z"}),(0,l.jsx)("path",{d:"M22.3992 8.32044L17.0254 0.198895H14.1253V11.8011H16.5989V3.67956L21.9727 11.8011H24.8728V0.198895H22.3992V8.32044Z"}),(0,l.jsx)("path",{d:"M47.7326 4.8232H42.103V0.198895H39.544V11.8011H42.103V7.06077H47.7326V11.8011H50.2916V0.198895H47.7326V4.8232Z"}),(0,l.jsx)("path",{d:"M4.75962 0.198895L0 11.8011H2.66129L3.63471 9.36464H8.61422L9.58747 11.8011H12.2488L7.48914 0.198895H4.75962ZM4.49553 7.20994L6.12438 3.1326L7.75323 7.20994H4.49553Z"}),(0,l.jsx)("path",{d:"M71.4966 0C68.0506 0 65.611 2.48619 65.611 6.01657C65.611 9.51381 68.0506 12 71.4966 12C74.9256 12 77.348 9.51381 77.348 6.01657C77.348 2.48619 74.9256 0 71.4966 0ZM71.4966 9.67956C69.4836 9.67956 68.2553 8.28729 68.2553 6.01657C68.2553 3.71271 69.4836 2.32044 71.4966 2.32044C73.4926 2.32044 74.7038 3.71271 74.7038 6.01657C74.7038 8.28729 73.4926 9.67956 71.4966 9.67956Z"}),(0,l.jsx)("path",{d:"M107.27 7.90608C106.827 9.03315 105.94 9.67956 104.729 9.67956C102.716 9.67956 101.487 8.28729 101.487 6.01657C101.487 3.71271 102.716 2.32044 104.729 2.32044C105.94 2.32044 106.827 2.96685 107.27 4.09392H109.983C109.318 1.60773 107.322 0 104.729 0C101.283 0 98.843 2.48619 98.843 6.01657C98.843 9.51381 101.283 12 104.729 12C107.339 12 109.335 10.3757 110 7.90608H107.27Z"}),(0,l.jsx)("path",{d:"M90.9615 0.198895L95.7212 11.8011H98.3313L93.5717 0.198895H90.9615Z"}),(0,l.jsx)("path",{d:"M85.5707 0.198895H79.7364V11.8011H82.2953V7.59116H85.5707C88.2832 7.59116 89.938 6.19889 89.938 3.89503C89.938 1.59116 88.2832 0.198895 85.5707 0.198895ZM85.4513 5.35359H82.2953V2.43646H85.4513C86.7137 2.43646 87.379 2.9337 87.379 3.89503C87.379 4.85635 86.7137 5.35359 85.4513 5.35359Z"}),(0,l.jsx)("path",{d:"M63.6492 3.72928C63.6492 1.54144 61.9944 0.198895 59.2819 0.198895H53.4476V11.8011H56.0065V7.25967H58.8553L61.4144 11.8011H64.2463L61.4127 6.91376C62.8349 6.38254 63.6492 5.26392 63.6492 3.72928ZM56.0065 2.43646H59.1625C60.4249 2.43646 61.0903 2.88398 61.0903 3.72928C61.0903 4.57459 60.4249 5.0221 59.1625 5.0221H56.0065V2.43646Z"})]})}},22769:function(e,t,a){"use strict";a.d(t,{s:function(){return s}});var l=a(27573),c=a(10607);function s(e){let{className:t}=e;return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 184 40",className:(0,c.Z)("text-text-000",t),fill:"currentColor",children:[(0,l.jsx)("path",{shapeRendering:"optimizeQuality",fill:"#D97757",d:"m7.75 26.27 7.77-4.36.13-.38-.13-.21h-.38l-1.3-.08-4.44-.12-3.85-.16-3.73-.2-.94-.2L0 19.4l.09-.58.79-.53 1.13.1 2.5.17 3.75.26 2.72.16 4.03.42h.64l.09-.26-.22-.16-.17-.16-3.88-2.63-4.2-2.78-2.2-1.6L3.88 11l-.6-.76-.26-1.66L4.1 7.39l1.45.1.37.1 1.47 1.13 3.14 2.43 4.1 3.02.6.5.24-.17.03-.12-.27-.45L13 9.9l-2.38-4.1-1.06-1.7-.28-1.02c-.1-.42-.17-.77-.17-1.2L10.34.21l.68-.22 1.64.22.69.6 1.02 2.33 1.65 3.67 2.56 4.99.75 1.48.4 1.37.15.42h.26v-.24l.21-2.81.39-3.45.38-4.44.13-1.25.62-1.5L23.1.57l.96.46.79 1.13-.11.73-.47 3.05-.92 4.78-.6 3.2h.35l.4-.4 1.62-2.15 2.72-3.4 1.2-1.35 1.4-1.49.9-.71h1.7l1.25 1.86-.56 1.92-1.75 2.22-1.45 1.88-2.08 2.8-1.3 2.24.12.18.31-.03 4.7-1 2.54-.46 3.03-.52 1.37.64.15.65-.54 1.33-3.24.8-3.8.76-5.66 1.34-.07.05.08.1 2.55.24 1.09.06h2.67l4.97.37 1.3.86.78 1.05-.13.8-2 1.02-2.7-.64-6.3-1.5-2.16-.54h-.3v.18l1.8 1.76 3.3 2.98 4.13 3.84.21.95-.53.75-.56-.08-3.63-2.73-1.4-1.23-3.17-2.67h-.21v.28l.73 1.07 3.86 5.8.2 1.78-.28.58-1 .35-1.1-.2L26 33.14l-2.33-3.57-1.88-3.2-.23.13-1.11 11.95-.52.61-1.2.46-1-.76-.53-1.23.53-2.43.64-3.17.52-2.52.47-3.13.28-1.04-.02-.07-.23.03-2.36 3.24-3.59 4.85-2.84 3.04-.68.27-1.18-.61.11-1.09.66-.97 3.93-5 2.37-3.1 1.53-1.79-.01-.26h-.09L6.8 30.56l-1.86.24-.8-.75.1-1.23.38-.4 3.14-2.16Z"}),(0,l.jsx)("path",{shapeRendering:"optimizeQuality",d:"M64.48 33.54c-5.02 0-8.45-2.8-10.07-7.11a19.19 19.19 0 0 1-1.23-7.03c0-7.23 3.24-12.25 10.4-12.25 4.81 0 7.78 2.1 9.47 7.11h2.06l-.28-6.91c-2.88-1.86-6.48-2.8-10.86-2.8-6.17 0-11.42 2.76-14.34 7.74a16.77 16.77 0 0 0-2.22 8.65c0 5.53 2.61 10.43 7.51 13.15a17.51 17.51 0 0 0 8.73 2.06c4.78 0 8.57-.91 11.93-2.5l.87-7.62h-2.1c-1.26 3.48-2.76 5.57-5.25 6.68-1.22.55-2.76.83-4.62.83ZM86.13 7.15l.2-3.4h-1.42l-6.32 1.9v1.03l2.8 1.3v23.78c0 1.62-.83 1.98-3 2.25v1.74h10.75v-1.74c-2.18-.27-3-.63-3-2.25V7.16Zm42.75 29h.83l7.27-1.38v-1.78l-1.02-.08c-1.7-.16-2.14-.51-2.14-1.9V18.33l.2-4.07h-1.15l-6.87.99v1.74l.67.12c1.86.27 2.41.79 2.41 2.09v11.3c-1.78 1.38-3.48 2.25-5.5 2.25-2.24 0-3.63-1.14-3.63-3.8V18.34l.2-4.07h-1.18l-6.88.99v1.74l.71.12c1.86.27 2.41.79 2.41 2.09v10.43c0 4.42 2.5 6.52 6.48 6.52 3.04 0 5.53-1.62 7.4-3.87l-.2 3.87ZM108.9 22.08c0-5.65-3-7.82-8.42-7.82-4.78 0-8.25 1.98-8.25 5.26 0 .98.35 1.73 1.06 2.25l3.64-.48c-.16-1.1-.24-1.77-.24-2.05 0-1.86.99-2.8 3-2.8 2.97 0 4.47 2.09 4.47 5.45v1.1l-7.5 2.25c-2.5.68-3.92 1.27-4.87 2.65a5 5 0 0 0-.7 2.8c0 3.2 2.2 5.46 5.96 5.46 2.72 0 5.13-1.23 7.23-3.56.75 2.33 1.9 3.56 3.95 3.56 1.66 0 3.16-.67 4.5-1.98l-.4-1.38c-.58.16-1.14.24-1.73.24-1.15 0-1.7-.91-1.7-2.69v-8.26Zm-9.6 10.87c-2.05 0-3.32-1.19-3.32-3.28 0-1.42.67-2.25 2.1-2.73l6.08-1.93v5.84c-1.94 1.47-3.08 2.1-4.86 2.1Zm63.3 1.82v-1.78l-1.03-.08c-1.7-.16-2.13-.51-2.13-1.9V7.15l.2-3.4h-1.43l-6.32 1.9v1.03l2.8 1.3v7.82a8.83 8.83 0 0 0-5.37-1.54c-6.28 0-11.18 4.78-11.18 11.93 0 5.89 3.52 9.96 9.32 9.96 3 0 5.61-1.46 7.23-3.72l-.2 3.72h.84l7.27-1.38Zm-13.16-18.14c3 0 5.25 1.74 5.25 4.94v9a7.2 7.2 0 0 1-5.21 2.1c-4.3 0-6.48-3.4-6.48-7.94 0-5.1 2.49-8.1 6.44-8.1Zm28.53 4.5c-.56-2.64-2.18-4.14-4.43-4.14-3.36 0-5.69 2.53-5.69 6.16 0 5.37 2.84 8.85 7.43 8.85a8.6 8.6 0 0 0 7.39-4.35l1.34.36c-.6 4.66-4.82 8.14-10 8.14-6.08 0-10.27-4.5-10.27-10.9 0-6.45 4.55-10.99 10.63-10.99 4.54 0 7.74 2.73 8.77 7.47l-15.84 4.86v-2.14l10.67-3.31Z"})]})}},14581:function(e){e.exports={style:{fontFamily:"'__copernicus_669e4a', '__copernicus_Fallback_669e4a'"},className:"__className_669e4a",variable:"__variable_669e4a"}},94957:function(e){e.exports={style:{fontFamily:"'__openDyslexic_e4ce97', '__openDyslexic_Fallback_e4ce97'"},className:"__className_e4ce97",variable:"__variable_e4ce97"}},42312:function(e){e.exports={style:{fontFamily:"'__styreneA_dcab32', '__styreneA_Fallback_dcab32'"},className:"__className_dcab32",variable:"__variable_dcab32"}},95601:function(e){e.exports={style:{fontFamily:"'__styreneB_820c23', '__styreneB_Fallback_820c23'"},className:"__className_820c23",variable:"__variable_820c23"}},19839:function(e){e.exports={style:{fontFamily:"'__tiempos_b4db0f', '__tiempos_Fallback_b4db0f'"},className:"__className_b4db0f",variable:"__variable_b4db0f"}},10607:function(e,t,a){"use strict";function l(){for(var e,t,a=0,l="";a<arguments.length;)(e=arguments[a++])&&(t=function e(t){var a,l,c="";if("string"==typeof t||"number"==typeof t)c+=t;else if("object"==typeof t){if(Array.isArray(t))for(a=0;a<t.length;a++)t[a]&&(l=e(t[a]))&&(c&&(c+=" "),c+=l);else for(a in t)t[a]&&(c&&(c+=" "),c+=a)}return c}(e))&&(l&&(l+=" "),l+=t);return l}a.d(t,{W:function(){return l}}),t.Z=l}},function(e){e.O(0,[5284,1293,1362,4856,1744],function(){return e(e.s=77687)}),_N_E=e.O()}]);