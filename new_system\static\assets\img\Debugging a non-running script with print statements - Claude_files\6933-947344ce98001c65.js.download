"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6933],{19170:function(e,t,n){n.d(t,{Ck:function(){return p},IR:function(){return v},MZ:function(){return k},Pd:function(){return _},Uc:function(){return m},W1:function(){return l},_y:function(){return y},fb:function(){return Q},gq:function(){return S},k7:function(){return C},ml:function(){return f},wO:function(){return d},yU:function(){return g},yq:function(){return h}});var r=n(3053),u=n(5362),i=n(8571),a=n(29305),o=n(13262),s=n(77930),c=n(81695);function l(e,t){return(0,u.WE)("/api/auth/login_methods?email=".concat(e,"&source=").concat(t),{enabled:!!e&&(0,a.vV)(e),meta:{noToast:!0}})}let d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:t}=(0,r.z$)(),n=(0,i.Z)();return(0,u.uC)("/api/auth/send_magic_link","POST",{...e,onSuccess:(n,r,u)=>{n.sso_url?t({event_key:"login.email.sso_initiated"}):t({event_key:"login.email.magic_link_sent"}),e.onSuccess&&e.onSuccess(n,r,u)},onError:(n,r,u)=>{t({event_key:"login.email.magic_link_send_error",error:n.message}),e.onError&&e.onError(n,r,u)},onMutate:e=>{t({event_key:"login.email.sending_magic_link"})},transformVariables:e=>({...e,source:n?"claude":"console"})})},g=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,i.Z)();return(0,u.uC)("/api/auth/exchange_nonce_for_code","POST",{...e,transformVariables:e=>({...e,source:t?"claude":"console"})})},f=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:t}=(0,r.z$)(),n=(0,i.Z)();return(0,u.uC)("/api/auth/verify_magic_link","POST",{...e,onSuccess:(n,r,u)=>{"code"===r.credentials.method?t({event_key:"login.email.finished"}):t({event_key:"login.email.magic_link_success"}),n.created&&T(t,n.account,"email"),e.onSuccess&&e.onSuccess(n,r,u)},onError:(n,r,u)=>{"code"===r.credentials.method?t({event_key:"login.email.code_verification_error",error:n.message}):t({event_key:"login.email.magic_link_verification_error",error:n.message}),e.onError&&e.onError(n,r,u)},onMutate:e=>{"code"===e.credentials.method?t({event_key:"login.email.verifying_code"}):t({event_key:"login.email.verifying_magic_link"})},transformVariables:e=>({...e,source:n?"claude":"console"})})},v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:t}=(0,r.z$)(),n=(0,i.Z)(),a=(0,c.useRouter)();return(0,u.uC)("/api/auth/verify_google","POST",{...e,onSuccess:(n,r,u)=>{!n.success&&n.sso_url?(t({event_key:"login.email.sso_initiated"}),a.push(n.sso_url)):(t({event_key:"login.google.finished"}),n.created&&T(t,n.account,"google"),e.onSuccess&&e.onSuccess(n,r,u))},onError:(n,r,u)=>{t({event_key:"login.google.verification_error",error:n.message}),e.onError&&e.onError(n,r,u)},onMutate:e=>{t({event_key:"login.google.verifying"})},transformVariables:e=>({...e,source:n?"claude":"console"})})},_=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{track:t}=(0,r.z$)(),n=(0,i.Z)(),a=(0,c.useRouter)();return(0,u.uC)("/api/auth/accept_invite","POST",{...e,onSuccess:(n,r,u)=>{!n.success&&n.sso_url?(t({event_key:"login.email.sso_initiated"}),a.push(n.sso_url)):e.onSuccess&&e.onSuccess(n,r,u)},transformVariables:e=>({...e,source:n?"claude":"console"})})},y=()=>(0,u.uC)("/api/auth/logout","POST"),m=()=>(0,u.uC)("/api/auth/logout/all-sessions","POST"),h=()=>{let e=(0,s.useQueryClient)();return(0,u.uC)("/api/account","PUT",{async onSuccess(){await e.invalidateQueries({queryKey:[o.aY]})}})},p=()=>(0,u.Ne)("/api/account","PUT",(e,t)=>(null==t?void 0:t.account)?{...t,account:{...t.account,settings:e}}:t,{queryKey:[o.aY],transformVariables:e=>({settings:e})}),C=()=>{let e=(0,s.useQueryClient)();return(0,u.uC)("/api/account/accept_legal_docs","PUT",{async onSuccess(){await e.invalidateQueries({queryKey:[o.aY]})}})};function S(e){return(0,u.WE)("/api/signups/".concat(e),{enabled:!!e,staleTime:0})}function k(e){return(0,u.WE)("/api/enterprise_auth/idp_redirect_url?organization_id=".concat(e),{meta:{noToast:!0}})}function Q(e,t,n){return(0,u.WE)("/api/enterprise_auth/sso_callback?code=".concat(e,"&state=").concat(t,"&source=").concat(n),{meta:{noToast:!0}})}function T(e,t,n){e({event_key:"login.account.created",authMethod:n},t)}},96933:function(e,t,n){n.d(t,{$H:function(){return U},$Y:function(){return P},Bk:function(){return $},CJ:function(){return K},Cy:function(){return q},Gk:function(){return A},Ke:function(){return D},Ls:function(){return B},OD:function(){return j},PD:function(){return z},Pe:function(){return k},QR:function(){return S},Rq:function(){return p},W:function(){return M},_C:function(){return Q},cE:function(){return C},cc:function(){return T},cj:function(){return G},f7:function(){return w},fc:function(){return V},gK:function(){return W},gm:function(){return E},j1:function(){return b},j7:function(){return x},jl:function(){return R},y6:function(){return O}});var r=n(19170),u=n(3053),i=n(5362),a=n(27218),o=n(1812),s=n(98731),c=n(13262),l=n(25551),d=n(51432),g=n(77930),f=n(50803),v=n(87590),_=n.n(v),y=n(81695),m=n(7653),h=n(1146);let p=e=>{let t=(0,g.useQueryClient)(),{activeOrganization:n}=(0,a.t)(),r=null==n?void 0:n.uuid,[u,i]=(0,m.useReducer)(e=>e+1,0),o=(0,l.B)([c.I8,{orgUUID:r},{uuid:e}]),s=(0,m.useMemo)(()=>t.getQueryData(o),[t,o,e,u]);return(0,m.useEffect)(()=>t.getQueryCache().subscribe(e=>{"updated"===e.type&&"success"===e.action.type&&(0,f._x)({queryKey:o},e.query)&&i()}),[t,i,o]),{data:s}},C=e=>{let t=(0,g.useQueryClient)(),{activeOrganization:n}=(0,a.t)(),r=null==n?void 0:n.uuid,u=(0,l.B)([c.I8,{orgUUID:r},{uuid:e}]);return t.getQueryData(u)},S=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid,r=new URLSearchParams,{limit:u,offset:o,defer:s}=e;void 0!==u&&r.append("limit",u.toString()),void 0!==o&&r.append("offset",o.toString());let l=r.toString();return l=l?"?".concat(l):"",(0,i.WE)("/api/organizations/".concat(null!=n?n:"","/chat_conversations").concat(l),{queryKey:[c.tv,{orgUUID:n},{limit:u,offset:o}],staleTime:3e5,enabled:!s&&!!n})},k=()=>{let{activeOrganization:e}=(0,a.t)(),t=(0,i.OJ)();return(0,m.useCallback)(async n=>{let{limit:r,offset:u}=n,i=new URLSearchParams;void 0!==r&&i.append("limit",r.toString()),void 0!==u&&i.append("offset",u.toString());let a=i.toString();return a=a?"?".concat(a):"",(await t("/api/organizations/".concat(null==e?void 0:e.uuid,"/chat_conversations").concat(a))).json()},[null==e?void 0:e.uuid,t])},Q=function(e){let{suppressError:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{activeOrganization:n}=(0,a.t)(),r=null==n?void 0:n.uuid,u=(0,m.useRef)([]),o=(0,g.useQueryClient)(),s="/api/organizations/".concat(null!=r?r:"","/chat_conversations/").concat(e,"?tree=True&rendering_mode=messages&render_all_tools=true"),l=(0,i.WE)(s,{queryKey:[c.I8,{orgUUID:r},{uuid:e}],staleTime:3e5,enabled:!!r&&!!e,meta:{noToast:t}},t=>(o.invalidateQueries({queryKey:[c.Gj,{orgUUID:r,conversationUUID:e}]}),(0,h.fX)(t))),d=(0,m.useMemo)(()=>{if(!l.data)return u.current=[],u.current;let e=(0,h.kX)(l.data);if(e.length!==u.current.length||e.some((e,t)=>{var n;return e.uuid!==(null===(n=u.current[t])||void 0===n?void 0:n.uuid)}))return u.current=e,u.current;for(let t=0;t<e.length;t++)_()(e[t],u.current[t])||(u.current[t]=e[t]);return u.current},[l.data]);return{...l,currentPath:d}},T=e=>{let t=(0,g.useQueryClient)(),{activeOrganization:n}=(0,a.t)(),r=null==n?void 0:n.uuid;return(0,m.useCallback)(()=>{t.invalidateQueries({queryKey:[c.I8,{orgUUID:r},{uuid:e}]})},[t,r,e])},U=(e,t)=>{let{mutate:n}=P(),r=(0,y.useRouter)(),u=t||(0,d.H)();return(0,m.useCallback)(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{model:void 0,controlsOpen:!1},i={uuid:u,name:"",model:t.model};t.projectUuid&&(i.project_uuid=t.projectUuid),n(i);let a="/chat/".concat(u).concat(t.controlsOpen?"?controls=1":"");r[e](a)},[n,r,e,u])},P=()=>{let{activeOrganization:e}=(0,a.t)(),t=null==e?void 0:e.uuid,n=(0,g.useQueryClient)(),r=[c.tv,{orgUUID:t}],u=e=>[c.ph,{orgUUID:t},{uuid:e}],o=e=>[c.I8,{orgUUID:t},{uuid:e}];return(0,i.uC)("/api/organizations/".concat(null!=t?t:"","/chat_conversations"),"POST",{enabled:!!t,async onMutate(e){let t=u(e.uuid),i={summary:"",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),...e};await Promise.all([n.cancelQueries({queryKey:r}),n.cancelQueries({queryKey:t})]),n.setQueryData(r,e=>e&&[i,...e]),n.setQueryData(t,e=>({...e||{},...i,chat_messages:(null==e?void 0:e.chat_messages)||[]}))},onSuccess:e=>{n.setQueryData(r,t=>{if(!t)return;let n=t.findIndex(t=>t.uuid===e.uuid);if(-1===n)return t;let r=[...t];return r.splice(n,1,e),r}),n.setQueryData(o(e.uuid),t=>({...t||{},...e,chat_messages:(null==t?void 0:t.chat_messages)||[],messageByUuid:new Map,parentByChildUuid:new Map,childrenByParentUuid:new Map,selectedChildByUuid:new Map})),N(n,t)},onError(e,t){n.setQueryData(r,e=>null==e?void 0:e.filter(e=>e.uuid!==t.uuid)),n.setQueryData(u(t.uuid),void 0)}})},b=()=>{let{activeOrganization:e}=(0,a.t)(),t=null==e?void 0:e.uuid,n=(0,g.useQueryClient)();return(0,i.Ne)(e=>{let{uuid:n}=e;return"/api/organizations/".concat(null!=t?t:"","/chat_conversations/").concat(n)},"DELETE",(e,t)=>{let{uuid:n}=e;if(!t)return[];let r=t.findIndex(e=>e.uuid===n);if(!(r>-1))return t;{let e=[...t];return e.splice(r,1),e}},{enabled:!!t,queryKey:[c.tv,{orgUUID:t}],onSuccess:async(e,r)=>{let{uuid:u,projectUuid:i}=r;await n.invalidateQueries({queryKey:[c.I8,{orgUUID:t},{uuid:u}]}),i&&await n.invalidateQueries({queryKey:[c.lx,{orgUuid:t,projectUuid:i}]}),await N(n,t)}})},w=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid,r=(0,g.useQueryClient)();return(0,i.uC)("/api/organizations/".concat(n,"/chat_conversations/delete_many"),"POST",{onSuccess:async t=>{await N(r,n),e&&e(t)}})},E=()=>{let{activeOrganization:e}=(0,a.t)(),t=null==e?void 0:e.uuid,n=null==e?void 0:e.capabilities.includes("raven");return(0,i.WE)("/api/organizations/".concat(t,"/shares"),{queryKey:[c.PB,{orgUUID:t}],enabled:!!t&&n,staleTime:3e5})},q=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid;return(0,i.WE)("/api/organizations/".concat(n,"/chat_conversations/").concat(e,"/latest"),{enabled:!!n&&!!e,queryKey:[c.Gj,{orgUUID:n,conversationUUID:e}],staleTime:0,meta:{noToast:!0}},e=>{let{snapshot:t,shareable:n,disabled_reason:r}=e;return{shareable:n,disabled_reason:r,snapshot:t?{...t,chat_messages:t.chat_messages.map(e=>(0,h.Dk)(e))}:null}})},K=e=>{let{activeOrganization:t,account:n}=(0,a.t)(),r=null==t?void 0:t.uuid,u=n&&r?"/api/organizations/".concat(r,"/chat_snapshots/").concat(e,"?rendering_mode=messages&render_all_tools=true"):"/api/chat_snapshots/".concat(e,"?rendering_mode=messages&render_all_tools=true");return(0,i.WE)(u,{retry:!1,refetchInterval:!1,enabled:!!e,staleTime:0,queryKey:[c.ld,{orgUUID:r,snapshotUUID:e}]},e=>({...e,chat_messages:e.chat_messages.map(e=>(0,h.Dk)(e))}))},z=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid,r=(0,g.useQueryClient)();return(0,i.uC)("/api/organizations/".concat(n,"/chat_conversations/").concat(e,"/share"),"POST",{onSuccess:()=>{r.invalidateQueries({queryKey:[c.Gj,{orgUUID:n,conversationUUID:e}]}),r.invalidateQueries({queryKey:[c.PB,{orgUUID:n}]}),r.invalidateQueries({queryKey:[c.VH,{orgUUID:n}]})}})},B=(e,t)=>{let{activeOrganization:n}=(0,a.t)(),r=null==n?void 0:n.uuid,u=(0,g.useQueryClient)();return(0,i.uC)("/api/organizations/".concat(r,"/share/").concat(t),"DELETE",{enabled:!!r&&!!t,meta:{errorMessage:"There was an error while trying to unshare the conversation. Please try again."},onSuccess:()=>{u.invalidateQueries({queryKey:[c.Gj,{orgUUID:r,conversationUUID:e}]}),u.invalidateQueries({queryKey:[c.PB,{orgUUID:r}]}),u.invalidateQueries({queryKey:[c.VH,{orgUUID:r}]})}})},O=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid;return(0,i.WE)("/api/organizations/".concat(n,"/artifact-versions/").concat(e),{retry:!1,refetchInterval:!1,enabled:!!e,staleTime:0,queryKey:[c.qB,{artifactVersionUUID:e}]})},D=function(e,t,n){var r,s,l;let d=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{track:f}=(0,u.z$)(),v=null!==(s=null===(r=(0,o.T)())||void 0===r?void 0:r.preview_feature_uses_artifacts)&&void 0!==s&&s,{activeOrganization:_}=(0,a.t)(),y=null==_?void 0:_.uuid,m=(0,g.useQueryClient)(),h=[c.I8,{orgUUID:y},{uuid:e}];return(0,i.uC)("/api/organizations/".concat(null!=y?y:"","/chat_conversations/").concat(e,"/chat_messages/").concat(null!==(l=t.uuid)&&void 0!==l?l:"","/chat_feedback"),t.chat_feedback?"PUT":"POST",{enabled:!!y,meta:{errorMessage:"We could not save your feedback. Please try again later."},...d,onSuccess(){for(var r,u,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];f({event_key:"claudeai.conversation.feedback.sent",message_uuid:t.uuid,conversation_uuid:e,artifacts_enabled:v,conversationHasArtifacts:n,type:null===(r=t.chat_feedback)||void 0===r?void 0:r.type}),m.invalidateQueries({queryKey:h}),null===(u=d.onSuccess)||void 0===u||u.call(d,...a)}})},I=()=>{let e=(0,g.useQueryClient)(),{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid;return(0,m.useCallback)((t,r)=>{t&&(e.setQueryData([c.I8,{orgUUID:n},{uuid:t}],e=>e&&{...e,...r}),e.setQueriesData({queryKey:[c.tv,{orgUUID:n}]},e=>null==e?void 0:e.map(e=>e.uuid!==t?e:{...e,...r})))},[n,e])},M=e=>{let t=(0,g.useQueryClient)(),{activeOrganization:n}=(0,a.t)(),r=null==n?void 0:n.uuid,u=I();return(0,i.uC)("/api/organizations/".concat(r,"/chat_conversations/").concat(e),"PUT",{onMutate:n=>{var i;let{name:a}=n,o=t.getQueryData([c.I8,{orgUUID:r},{uuid:e}]);return u(e,{name:a}),{oldTitle:null!==(i=null==o?void 0:o.name)&&void 0!==i?i:""}},onError(t,n,r){r&&u(e,{name:r.oldTitle})}})},x=e=>{let t=I(),{activeOrganization:n}=(0,a.t)();return(0,i.uC)("/api/organizations/".concat(null==n?void 0:n.uuid,"/chat_conversations/").concat(e,"/title"),"POST",{onSuccess(n){let{title:r}=n;t(e,{name:null!=r?r:""})},meta:{noToast:!0}})};function j(e){var t,n;let r=(0,s.K)(e);return r+" "+e.attachments.map(e=>e.extracted_content).join(" ")+" "+((null===(t=e.content)||void 0===t?void 0:t.filter(e=>"tool_use"===e.type).map(e=>e.input?JSON.stringify(e.input):e.partial_json||"").join(" "))||"")+" "+((null===(n=e.content)||void 0===n?void 0:n.filter(e=>"tool_result"===e.type).flatMap(e=>e.content.filter(e=>"text"===e.type).map(e=>e.text)).join(" "))||"")}let W=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid;return(0,i.uC)("/api/organizations/".concat(n,"/chat_conversations/").concat(e,"/stop_response"),"POST")},V=()=>{let{activeOrganization:e}=(0,a.t)(),t=null==e?void 0:e.uuid;return(0,i.uC)(e=>{let{conversation_uuid:n}=e;return"/api/organizations/".concat(t,"/chat_conversations/").concat(n,"/tool_result")},"POST",{enabled:!!t,meta:{errorMessage:"We could not record the tool result. Please try again later."},transformVariables:e=>{let{conversation_uuid:t,...n}=e;return n}})},R=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid;return(0,i.uC)("/api/organizations/".concat(n,"/chat_conversations/").concat(e,"/current_leaf_message_uuid"),"PUT")},Z=e=>{let{activeOrganization:t}=(0,a.t)(),n=(0,g.useQueryClient)(),r=null==t?void 0:t.uuid;return(0,i.Ne)("/api/organizations/".concat(r,"/chat_conversations/").concat(e,"?rendering_mode=raw"),"PUT",(e,t)=>t?{...t,settings:{...t.settings,...e.settings}}:void 0,{enabled:!!e,onSuccess:()=>{N(n,r)},queryKey:[c.I8,{orgUUID:r},{uuid:e}]})},$=e=>{let{account:t}=(0,a.t)(),{track:n}=(0,u.z$)(),{mutate:i}=(0,r.Ck)(),{mutate:o}=Z(e);return{toggleConvoAndAccountSetting:r=>{if(!t)return;let u={};for(let[t,i]of Object.entries(r))void 0!==r[t]&&(u[t]=i,n({event_key:"claudeai.conversation.setting.toggled",conversation_uuid:e||null,setting:t,enabled:i}));e&&o({settings:u}),i({...t.settings,...u})}}},N=async(e,t)=>{await e.invalidateQueries({queryKey:[c.tv,{orgUUID:t}]})},A=e=>{let{activeOrganization:t}=(0,a.t)(),n=(0,g.useQueryClient)(),r=null==t?void 0:t.uuid;return(0,i.Ne)("/api/organizations/".concat(r,"/chat_conversations/").concat(e,"?rendering_mode=raw"),"PUT",(e,t)=>t?{...t,...e}:void 0,{onSuccess:()=>{N(n,r)},queryKey:[c.I8,{orgUUID:r},{uuid:e}]})},G=()=>{let{data:e,isLoading:t}=S({limit:void 0});return{data:null==e?void 0:e.filter(e=>e.is_starred),isLoading:t}}},1146:function(e,t,n){n.d(t,{Dk:function(){return g},Ef:function(){return c},FC:function(){return a},Gg:function(){return l},HX:function(){return _},QC:function(){return s},fX:function(){return v},kX:function(){return m},mj:function(){return h},vv:function(){return y},w$:function(){return d},wZ:function(){return o}});var r=n(45144),u=n(52221),i=n.n(u);let a="new-assistant-message-uuid",o="new-human-message-uuid",s="00000000-0000-4000-8000-000000000000",c=e=>e!==a;function l(e,t){let n=e.get(t);if(void 0===n){let e=Error("Key not found: ".concat(t));throw(0,r.Tb)(e),e}return n}let d=(e,t)=>{let n=null!=t?t:e.current_leaf_message_uuid;if(!n)return e;for(;n!==s;){var r;let t=l(e.parentByChildUuid,n),u=null===(r=l(e.childrenByParentUuid,t))||void 0===r?void 0:r.indexOf(n);e.selectedChildByUuid.set(t,u),n=t}return{...e,current_leaf_message_uuid:t}},g=e=>e.content?e:{...e,content:[{type:"text",text:e.text||"",citations:[]}]},f=e=>{var t,n,r;let u=new Map,i=new Map,a=new Map,o=new Map,c=new Map;if(0===e.chat_messages.length)return{messageByUuid:i,parentByChildUuid:a,childrenByParentUuid:o,selectedChildByUuid:c};let l=e.chat_messages.map(g);for(let e of l)u.set(e.index,e),i.set(e.uuid,e);for(let e of l){let t=e.parent_message_uuid;if(!t){let r=e.index-1;for(;!t&&r>=0;)t=null===(n=u.get(r))||void 0===n?void 0:n.uuid,r--}t=null!=t?t:s,a.set(e.uuid,t),o.set(t,(null!==(r=o.get(t))&&void 0!==r?r:[]).concat(e.uuid)),c.set(t,0)}if(!(null===(t=o.get(s))||void 0===t?void 0:t[0]))throw Error("No root message found");return{chat_messages:l,messageByUuid:i,parentByChildUuid:a,childrenByParentUuid:o,selectedChildByUuid:c}},v=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=new Set;for(let t of e.chat_messages)"assistant"===t.sender&&t.parent_message_uuid&&n.add(t.parent_message_uuid);let r=e.chat_messages.filter(e=>"assistant"===e.sender||n.has(e.uuid)||void 0===e.parent_message_uuid||t),u={...e,chat_messages:r},i={...u,...f(u)};return i.current_leaf_message_uuid||d(i),d(i,u.current_leaf_message_uuid)},_=e=>{let t=Array.from(e.messageByUuid.values()).map(e=>e.index);return 0===t.length?-1:Math.max(...t)},y=(e,t)=>{for(let r of(e.chat_messages=e.chat_messages.concat(t),t)){var n;if(!r.parent_message_uuid)throw Error("Parent UUID required");let t=r.parent_message_uuid;e.messageByUuid.set(r.uuid,r),e.parentByChildUuid.set(r.uuid,t);let u=null!==(n=e.childrenByParentUuid.get(t))&&void 0!==n?n:[];u.push(r.uuid),e.childrenByParentUuid.set(t,u),e.selectedChildByUuid.set(t,u.length-1)}return d(e,t[t.length-1].uuid)},m=(e,t)=>{let n=[],r=null!=t?t:s;for(;e.childrenByParentUuid.has(r);){let t=l(e.childrenByParentUuid,r);if(0===t.length)break;let u=l(e.selectedChildByUuid,r),i=l(e.messageByUuid,t[u]);n.push({...i,parent_message_uuid:r,nOptions:t.length,selectedOption:u}),r=i.uuid}return n};function h(e,t,n){var r;let u=l(e.parentByChildUuid,t.uuid),a=l(e.childrenByParentUuid,u),o=i()((null!==(r=t.selectedOption)&&void 0!==r?r:0)+n,0,a.length-1);e.selectedChildByUuid.set(u,o);let s=m(e,u);return d(e,s[s.length-1].uuid)}},1812:function(e,t,n){n.d(t,{I:function(){return o},T:function(){return a}});var r=n(27573),u=n(7653);let i=(0,u.createContext)(null),a=()=>{let e=(0,u.useContext)(i);return void 0===e?null:e},o=e=>{let{children:t,settings:n}=e;return(0,r.jsx)(i.Provider,{value:n,children:t})}},29305:function(e,t,n){n.d(t,{KS:function(){return i},vV:function(){return u}});let r=/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}$/,u=e=>r.test(e),i=(e,t)=>{if(!t)return!1;let n=e.split("@")[1];return t.includes(n)}},98731:function(e,t,n){n.d(t,{K:function(){return r}});let r=e=>{let t="";if(e.content)for(let n of e.content)"text"===n.type&&(t+=n.text);return t}},25551:function(e,t,n){n.d(t,{B:function(){return u}});var r=n(7653);function u(e){let t=JSON.stringify(e);return(0,r.useMemo)(()=>e,[t])}},51432:function(e,t,n){n.d(t,{H:function(){return r}});let r=()=>void 0===crypto.randomUUID?"".concat(1e7,"-",1e3,"-",4e3,"-",8e3,"-",1e11).replace(/[018]/g,e=>{let t=parseInt(e);return(t^crypto.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16)}):crypto.randomUUID()}}]);