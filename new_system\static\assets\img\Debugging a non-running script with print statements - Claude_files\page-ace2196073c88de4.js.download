(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9537],{76905:function(e,t,n){Promise.resolve().then(n.bind(n,57329))},57329:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return K}});var s=n(27573),l=n(96933),a=n(1133),i=n(81695),o=n(48894),r=n(47082),c=n(76986),u=n(27218),d=n(98731);function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500;if(e.length<=t)return e;let n=Math.ceil(t/2);return"".concat(e.substring(0,n),"\n\n  [...]\n\n  ").concat(e.substring(e.length-n))}var v=n(7653),f=n(40950),h=n(99205),x=n(57271),p=n(1812),g=n(11607),_=n(14575),j=n(4820),y=n(88755),C=n(7680),w=n(18701),N=n(40030),b=n(24438),k=n(3053),S=n(18013),M=n(14448),A=n(67995);let Z=e=>"".concat(e.serverName,"-").concat(e.name);var z=n(70354),D=n(52939),U=n(96346),I=n(30070),T=n(10607),E=n(45790),O=n(41668),R=n(35756);function P(e){let{isModalOpen:t,currentTool:n,currentToolInput:l,handleDeny:a,handleAllow:i,handleAlwaysAllow:o}=e,[r,c]=(0,v.useState)(!1);return(0,s.jsxs)(D.u_,{isOpen:t,onClose:()=>a(),title:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(E.Z,{defaultMessage:"Allow tool from “{serverName}” (local)?",id:"Lss1c5+TXX",values:{serverName:null==n?void 0:n.serverName}})}),children:[(0,s.jsxs)("div",{className:"text-xs my-2 bg-bg-300 rounded-md px-2 py-1",children:[(0,s.jsxs)("button",{onClick:()=>c(!r),className:"flex justify-between items-center w-full text-[0.625rem] text-text-000 font-medium transition-colors duration-200 hover:text-text-300",children:[(0,s.jsx)("div",{children:(0,s.jsx)(E.Z,{defaultMessage:"Run {toolName} from {serverName}",id:"6/XY9VpX3Z",values:{toolName:null==n?void 0:n.name,serverName:null==n?void 0:n.serverName}})}),(0,s.jsx)(U.p,{className:(0,T.Z)("transform transition-transform duration-300 ease-in-out",r?"rotate-0":"-rotate-90"),size:12})]}),(0,s.jsxs)("div",{className:(0,T.Z)("overflow-hidden transition-all duration-500 ease-in-out",r?"max-h-screen opacity-100":"max-h-0 opacity-0"),children:[(null==n?void 0:n.description)&&(0,s.jsx)("div",{className:"mt-2 mb-1",children:null==n?void 0:n.description}),l&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("div",{className:"text-[0.625rem] text-text-400 mb-1",children:(0,s.jsx)(E.Z,{defaultMessage:"Tool Input:",id:"zye//zQc25"})}),(0,s.jsx)(O.Z,{language:"json",style:R.Z,className:"rounded !text-xs !p-2",children:JSON.stringify(JSON.parse(l),null,2)})]})]})]}),(0,s.jsxs)("p",{className:"text-[0.625rem] text-text-300 flex gap-1.5 items-center",children:[(0,s.jsx)(I.v,{size:"18px"}),(0,s.jsx)("span",{children:(0,s.jsx)(E.Z,{defaultMessage:"Malicious MCP servers or conversation content could potentially trick Claude into attempting harmful actions through your installed tools. <bold>Review each action carefully before approving.</bold>",id:"GCRi+qB/iA",values:{bold:e=>(0,s.jsx)("span",{className:"font-medium",children:e})}})})]}),(0,s.jsxs)("div",{className:"flex justify-between mt-3 text-sm",children:[(0,s.jsxs)("div",{children:[o&&(0,s.jsx)(z.z,{onClick:o,size:"sm",variant:"outline",className:"mr-2",children:(0,s.jsx)(E.Z,{defaultMessage:"Allow for This Chat",id:"E3y1M9HTd6"})}),(0,s.jsx)(z.z,{onClick:i,size:"sm",variant:"outline",children:(0,s.jsx)(E.Z,{defaultMessage:"Allow Once",id:"d+Xd7eN3T+"})})]}),(0,s.jsx)("div",{children:(0,s.jsx)(z.z,{size:"sm",onClick:a,children:(0,s.jsx)(E.Z,{defaultMessage:"Deny",id:"htvX+Z11l+"})})})]})]})}function X(e){var t,n;let{conversation:a,conversationUUID:o,isLoadingHarmony:r,chatInputDefaultMessage:C}=e,{currentPath:w,data:N,isLoading:z,isPlaceholderData:D}=(0,l._C)(o,{suppressError:!0}),{changeDisplayedConversationPath:U}=(0,x.p)(),{mcpTools:I}=(0,c.c)(),{onInvokeTool:T,isModalOpen:E,currentTool:O,currentToolInput:R,handleAllow:X,handleDeny:L,handleAlwaysAllow:K}=function(e){let{conversationUUID:t,mcpTools:n}=e,s=(0,j.Q)(),{track:l}=(0,k.z$)(),[a,i]=(0,v.useState)(!1),[o,r]=(0,v.useState)(null),[c,u]=(0,v.useState)(null),[d,m]=(0,v.useState)(null),[f,h]=(0,S.A)("alwaysAllowTools-".concat(t),{}),x=(0,v.useRef)(f),p=(0,v.useCallback)(async(e,t,l,a,o)=>{let c=n.find(e=>e.name===t);if(!c)return s(e,t,l,a,o);if("dolphin"===t){var d;let n=(d=(0,A.I)(a))&&"object"==typeof d?"actions"in d&&Array.isArray(d.actions)?d.actions.map(e=>({type:e.action,state:"pending",coordinate:"coordinate"in e?e.coordinate:void 0,start_coordinate:"start_coordinate"in e?e.start_coordinate:void 0,text:"text"in e?e.text:void 0,scroll_direction:"scroll_direction"in e?e.scroll_direction:void 0,scroll_amount:"scroll_amount"in e?e.scroll_amount:void 0,duration:"duration"in e?e.duration:void 0})):"action"in d?[{type:d.action,state:"pending",coordinate:"coordinate"in d?d.coordinate:void 0,start_coordinate:"start_coordinate"in d?d.start_coordinate:void 0,text:"text"in d?d.text:void 0,scroll_direction:"scroll_direction"in d?d.scroll_direction:void 0,scroll_amount:"scroll_amount"in d?d.scroll_amount:void 0,duration:"duration"in d?d.duration:void 0}]:[]:[];if(1===n.length&&"screenshot"===n[0].type)return s(e,t,l,a,o);if(n.length>0)return new Promise(e=>{r(c),u(l),m(a),e()})}let v=x.current[Z(c)];return(null==v?void 0:v.allowed)?s(e,t,l,a,o):new Promise(e=>{r(c),u(l),m(a),i(!0),e()})},[s,n]),g=(0,v.useCallback)(()=>{i(!1),o&&null!==c&&null!==d&&s(t,o.name,c,d)},[t,o,c,d,s]),_=(0,v.useCallback)(()=>{if(i(!1),o&&null!==c&&null!==d){var e;s(t,o.name,c,d,{overriddenResult:{content:[{type:"text",text:"The user has chosen to disallow the tool call."}],is_error:!0}}),l({event_key:"mcp.tools.called",result:"disallowed",argument_count:Object.keys(null!==(e=(0,A.I)(d))&&void 0!==e?e:{}).length})}},[t,o,c,d,s,l]),{value:y}=(0,M.F)("claude_ai_allow_tool_always_allow"),C=(0,v.useCallback)(()=>{y&&(o&&h(e=>{let t={...e,[Z(o)]:{allowed:!0,lastUpdated:Date.now()}};return x.current=t,t}),i(!1),o&&null!==c&&null!==d&&s(t,o.name,c,d))},[t,o,c,d,s,h,y]);return{onInvokeTool:p,isModalOpen:a,currentTool:o,currentToolInput:d,handleAllow:g,handleDeny:_,handleAlwaysAllow:y?C:void 0}}({conversationUUID:o,mcpTools:I}),Q=(0,j.z)(null==N?void 0:N.settings),[J,q]=(0,v.useState)(),F=(0,v.useCallback)(e=>{q(e)},[q]),H=(0,h.pV)(o,T,Q),V=(0,h.lE)(o,T,Q,J),{mutate:Y}=(0,l.jl)(o),B=H.isStreaming||V.isStreaming,G=H.runStream;!function(e,t){let[n,s]=(0,v.useState)(!1),{activeOrganization:a}=(0,u.t)(),i=(0,f.Z)(),o=e?e.uuid:"",{mutate:r}=(0,l.W)(o),{mutate:c,error:h}=(0,l.j7)(o),x=(0,v.useRef)(!1);(0,v.useEffect)(()=>{var t;if(!h||(null==e?void 0:e.name))return;let n=(null==e?void 0:null===(t=e.chat_messages)||void 0===t?void 0:t[0])?(0,d.K)(e.chat_messages[0]):"";if(n){if(x.current)return;let e=n.slice(0,30),t=n.length>30?"...":"";x.current=!0,r({name:"\uD83D\uDCAC ".concat(e).concat(t)})}},[h,e,r]),(0,v.useEffect)(()=>{if(t||n||(null==e?void 0:e.name))return;let l=null==e?void 0:e.chat_messages;if(!l||l.length<2||0===(0,d.K)(l[1]).length)return;let i=["Message 1:",m((0,d.K)(l[0])),"Message 2:",m((0,d.K)(l[1]))].join("\n\n");a&&(s(!0),c({message_content:i,recent_titles:[]}))},[t,n,e,a,c,i])}(N,B),function(e){let{conversationUUID:t,isStreaming:n}=e,[s,l]=(0,v.useState)(n),{mcpClients:a}=(0,c.c)(),i=a.dolphin,o=(0,y.useIsClaudeApp)(),r=!!i&&o;(0,v.useEffect)(()=>{r&&(!n&&s?(l(!1),i.notification({method:"notifications/progress",params:{progress:1,total:1,progressToken:"streaming"}})):!s&&n&&(i.notification({method:"notifications/progress",params:{progress:0,total:1,progressToken:"streaming"}}),l(!0)))},[t,r,n,s,i])}({conversationUUID:o,isStreaming:B});let W=(0,v.useCallback)(async(e,t)=>{var n;await G({prompt:t,attachments:e.attachments,files:null!==(n=e.files_v2)&&void 0!==n?n:e.files,syncSources:e.sync_sources,parent_message_uuid:e.parent_message_uuid,personalized_style:J})},[G,J]),$=(0,v.useCallback)((e,t)=>{U(o,e,t,e=>Y({current_leaf_message_uuid:e}))},[U,o,Y]),ee=(0,_.d)();return(N||z||D||(0,i.notFound)(),!ee&&(z||r))?(0,s.jsx)("div",{className:"fixed inset-0 grid place-items-center",children:(0,s.jsx)(g.Loading,{delay:1e3})}):(0,s.jsxs)(p.I,{settings:(null==N?void 0:N.settings)||{},children:[(0,s.jsx)(b.J,{isStreaming:B,chatInputDefaultMessage:C,conversationTitle:a?"".concat(a.name," - Claude"):"Claude",conversationUUID:o,isLoading:z||!!r,messages:w,name:null!==(n=null!==(t=null==N?void 0:N.name)&&void 0!==t?t:null==a?void 0:a.name)&&void 0!==n?n:"Untitled Chat",onSend:H.runStream,onRetry:V.runStream,projectUuid:null==N?void 0:N.project_uuid,changeDisplayedConversationPath:$,editMessage:W,setPersonalizedStyleCallback:F,onToolAllow:X}),(0,s.jsx)(P,{isModalOpen:E,currentTool:O,currentToolInput:R,handleDeny:L,handleAllow:X,handleAlwaysAllow:K})]})}function L(e){let{data:t}=(0,l._C)(e.conversationUUID,{suppressError:!0}),n=(null==t?void 0:t.project_uuid)||"",{userHasHarmony:a,projectHasHarmony:i}=(0,w.Y)(n),[o,r]=(0,v.useState)(!!(a&&i)),c=(0,v.useCallback)(()=>r(!1),[]);return(0,s.jsx)(N.C,{projectUuid:n,onLoad:c,children:(0,s.jsx)(C.r,{children:(0,s.jsx)(X,{...e,isLoadingHarmony:o})})})}function K(e){let{params:{uuid:t}}=e,n=(0,i.useSearchParams)(),c=null==n?void 0:n.get("q"),{data:u}=(0,l.QR)({limit:void 0}),d=null==u?void 0:u.find(e=>e.uuid===t),m=null==d?void 0:d.name;return(0,a.ZQ)(null!=m?m:""),(0,s.jsx)(r.M,{conversationUUID:t,children:(0,s.jsx)(o.d,{conversationUUID:t,children:(0,s.jsx)(L,{conversation:d,conversationUUID:t,chatInputDefaultMessage:null!=c?c:void 0})})})}}},function(e){e.O(0,[1276,7277,9906,7611,4408,5790,3279,6947,6340,5992,7423,1270,7879,6971,1211,7265,1121,3097,2821,1006,9505,9833,294,5859,3290,7376,6520,3420,2970,3589,6959,223,3216,885,5575,7744,8877,6933,8321,7699,7633,9205,3545,879,7680,4639,2300,2507,5022,9544,7106,4438,1293,1362,4856,1744],function(){return e(e.s=76905)}),_N_E=e.O()}]);