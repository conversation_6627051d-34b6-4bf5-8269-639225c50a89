{% extends "layouts/base.html" %}
{% block content %}
<style>
    .container { padding: 10px; }
    .centered-text { text-align: center; }
    .glossy-button {
        background-image: linear-gradient(to top, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1)),
                        linear-gradient(to bottom, rgba(10, 10, 10, 0.258), rgba(92, 89, 89, 0.23));
        background-size: 100% 200%;
        background-position: 0 100%;
        color: white;
        border-radius: 10px;
        padding: 10px 20px;
        border: none;
        cursor: pointer;
        transition: background-position 0.3s ease;
    }
    .card img {
        margin-bottom: 10px;
        width: 80px;
        height: 80px;
    }
    .card .btn { margin-bottom: 5px; }
    .centered-texts {
        text-align: center;
        font-weight: bold;
        color: WHITE;
        margin-bottom: 4px;
        text-shadow: 2px 2px 4px rgba(4, 4, 4, 0.827);
        font-size: 15px;
    }
    .equal-height {
        min-height: 400px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .time-slot-row {
        margin-bottom: 10px;
        padding: 5px;
        border-radius: 5px;
        background-color: rgba(255, 255, 255, 0.1);
    }
    .slot-result {
        max-height: 200px;
        overflow-y: auto;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
    }
    textarea.form-control {
        background-color: rgba(255, 255, 255, 0.9);
        color: #333;
    }
    
    /* Improved Schedule Slots Styles */
    .schedule-slot-item {
        background-color: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .schedule-slot-item:hover {
        background-color: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.2);
    }
    
    .slot-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        cursor: pointer;
    }
    
    .slot-title {
        font-weight: bold;
        color: #fff;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .slot-actions {
        display: flex;
        gap: 5px;
    }
    
    .account-type-selector {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .account-type-dropdown {
        width: 100%;
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
    }
    
    .account-input-group {
        display: none;
        margin-top: 10px;
        padding: 10px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 5px;
    }
    
    .account-input-group.active {
        display: block;
    }
    
    .days-week-compact {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .day-toggle {
        width: 35px;
        height: 35px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        background-color: transparent;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 11px;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .day-toggle.active {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .day-toggle:hover {
        transform: scale(1.1);
    }
    
    .schedule-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    
    .slot-collapsed .slot-content {
        display: none;
    }
    
    .expand-icon {
        transition: transform 0.3s ease;
        font-size: 12px;
    }
    
    .slot-collapsed .expand-icon {
        transform: rotate(-90deg);
    }
    
    .selected-accounts-preview {
        font-size: 11px;
        color: #aaa;
    }
    
    .quick-fill-buttons {
        display: flex;
        gap: 5px;
        margin-bottom: 10px;
        flex-wrap: wrap;
    }
    
    .quick-fill-btn {
        padding: 5px 10px;
        font-size: 11px;
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 4px;
        cursor: pointer;
    }
    
    .quick-fill-btn:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }
    
    .account-badge {
        display: inline-block;
        padding: 2px 8px;
        margin: 2px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        font-size: 11px;
    }
    
    .schedule-builder-container {
        max-height: 350px;
        overflow-y: auto;
        padding-right: 5px;
    }
    
    .schedule-builder-container::-webkit-scrollbar {
        width: 5px;
    }
    
    .schedule-builder-container::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 5px;
    }
    
    .schedule-builder-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 5px;
    }
/* Add these styles to your existing CSS section or update the existing ones */

/* Change all white text to thick black in schedule slots */
    .schedule-slot-item .text-white {
        color: #000 !important;
        font-weight: 600; /* Makes the text thicker/bolder */
    }

    .schedule-slot-item label {
        color: #000 !important;
        font-weight: 600;
    }

    .slot-title {
        color: #000 !important; /* Changed from #fff to #000 */
        font-weight: bold;
    }

    .selected-accounts-preview {
        color: #333 !important; /* Changed from #aaa to dark gray */
        font-weight: 500;
    }

    /* Update form controls to have better contrast with black text */
    .schedule-slot-item .form-control {
        background-color: rgba(255, 255, 255, 0.9);
        color: #000;
        font-weight: 500;
        border: 1px solid rgba(0, 0, 0, 0.3);
    }

    .schedule-slot-item select.form-control {
        background-color: rgba(255, 255, 255, 0.9);
        color: #000;
        font-weight: 500;
    }

    /* Update account type dropdown */
    .account-type-dropdown {
        background-color: rgba(255, 255, 255, 0.9) !important;
        color: #000 !important;
        font-weight: 500;
        border: 1px solid rgba(0, 0, 0, 0.3) !important;
    }

    /* Update day toggle buttons */
    .day-toggle {
        color: #000 !important;
        border: 2px solid rgba(0, 0, 0, 0.5) !important;
        font-weight: bold;
    }

    .day-toggle.active {
        color: #fff !important; /* Keep white text when active for contrast */
        background-color: #007bff;
        border-color: #007bff;
    }

    /* Update quick fill buttons */
    .quick-fill-btn {
        color: #000 !important;
        font-weight: 600;
        border: 1px solid rgba(0, 0, 0, 0.3) !important;
        background-color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Update account badges */
    .account-badge {
        color: #000 !important;
        font-weight: 600;
        background-color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Update the JSON preview if needed */
    #jsonContent {
        color: #000 !important;
        font-weight: 500;
    }

    /* Fix the expand icon color */
    .expand-icon {
        color: #000 !important;
        font-weight: bold;
    }

    /* Update placeholder text color for better visibility */
    .schedule-slot-item input::placeholder {
        color: #666 !important;
        font-weight: 400;
    }

    /* Make sure the "Configured Accounts:" text is black */
    .account-summary small {
        color: #000 !important;
        font-weight: 600;
    }
    .auto-split-toggle {
        background-color: rgba(255, 255, 255, 0.2);
        color: #000;
        border: 2px solid #007bff;
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-block;
        text-align: center;
    }
    
    .auto-split-toggle:hover {
        background-color: rgba(0, 123, 255, 0.2);
        transform: scale(1.05);
    }
    
    .auto-split-toggle.active {
        background-color: #007bff;
        color: #fff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.5);
    }
    
    .auto-split-toggle.active:hover {
        background-color: #0056b3;
    }
    
    .auto-split-settings {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
        display: none;
    }
    
    .auto-split-settings.active {
        display: block;
    }
    
    .time-gap-input {
        width: 80px;
        display: inline-block;
        margin: 0 5px;
    }
    
</style>

<body>
    <div class="card">
        <div class="card-header card-header-primary" style="display: flex; justify-content: center; align-items: center; height: 70px;">
            <img src="{{ url_for('static', filename='assets/img/inst.png') }}" alt="Loader" style="width: 40px; height: 40px; margin-right: 10px;">
            <h3 style="margin: 0; font-size: 18px; line-height: 1; position: relative; top: -5px;">Instagram Bot</h3>
        </div>
        <div class="card-header card-header-primary" style="display: flex; justify-content: space-between; align-items: center; height: 70px;">
            <div style="display: flex; align-items: center;">
                <a href="#api_run2" class="btn btn-info" style="height: 40px; margin-right: 10px; display: flex; align-items: center;">Phase 2 Find New Users</a>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
                <a href="/set_scheduler/" class="btn btn-success" style="height: 40px; display: flex; align-items: center;">
                    <i class="fa fa-calendar-check-o" style="margin-right: 5px;"></i> Schedule Manager
                </a>
                <a href="#delete_slot" class="btn btn-warning" style="height: 40px; display: flex; align-items: center;">Delete Slot Page</a>
            </div>
        </div>
        
        <br>
        <div class="row mb-4 d-flex justify-content-center">
            <div class="col-md-11">
                <!-- First Row: Data Collection (Scanning) -->
                <div class="row justify-content-center">
                    <!-- Primary Scanning Section -->
                    <div class="col-md-4 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Primary Scanning</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="captureInput" class="form-control" placeholder="Enter account numbers (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2,3 or 5,10,15)</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunCapture()">Run Capture</button>
                                <div id="captureStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Scanning...</span>
                                    </div>
                                    <small class="text-white">Processing Scans...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <!-- General Scanning Section -->
                    <div class="col-md-4 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">General Scanning</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="generalCaptureInput" class="form-control" placeholder="Enter account numbers (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2,3 or 5,10,15)</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunGeneralCapture()">Run General Capture</button>
                                <div id="generalCaptureStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Scanning...</span>
                                    </div>
                                    <small class="text-white">Processing General Scans...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <!-- Bio Scanning Section -->
                    <div class="col-md-4 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Bio Scanning</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="bioInput" class="form-control" placeholder="Enter account numbers (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2,3 or 5,10,15)</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunBio()">Run Bio Scan</button>
                                <div id="bioStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Scanning...</span>
                                    </div>
                                    <small class="text-white">Processing Bio Scans...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>
                </div>

                <!-- Second Row: Account Management & Warmup -->
                <div class="row justify-content-center">
                    <!-- Account Warmup Section -->
                    <div class="col-md-6 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Account Warmup</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="warmupInput" class="form-control" placeholder="Enter account numbers (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2,3 or 5,10,15)</small>
                                <br><br>
                                <button class="btn btn-success" style="height: 40px;" onclick="confirmRunWarmup()">Run Warmup</button>
                                <div id="warmupStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Warming up...</span>
                                    </div>
                                    <small class="text-white">Performing human-like activities...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <!-- Message Generator Section -->
                    <div class="col-md-6 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Message Generator</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="messageGeneratorInput" class="form-control" placeholder="Enter account numbers (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2,3 or 5,10,15)</small>
                                <br><br>
                                <div class="d-flex justify-content-between">
                                    <button class="btn btn-primary" style="height: 40px; width: 48%;" onclick="confirmRunMessageGenerator()">Run Generator</button>
                                    <button class="btn btn-info" style="height: 40px; width: 48%;" id="customPromptBtn">Custom Prompts</button>
                                </div>
                                <div id="messageGeneratorStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Generating...</span>
                                    </div>
                                    <small class="text-white">Processing Message Generator...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>
                </div>

                <!-- Third Row: Messaging & Automation -->
                <div class="row justify-content-center">
                    <!-- Primary Message Bot Section -->
                    <div class="col-md-4 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Primary Message Bot</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="messageIdInput1" class="form-control" placeholder="Enter Account ID (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2)</small>
                                <br><br>
                                <select id="messageAction" class="form-control">
                                    <option value="start">Start</option>
                                    <option value="resume">Resume</option>
                                </select>
                                <small class="text-white">Select action type</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunAutomation()">RUN</button>
                                <div id="messageStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Running...</span>
                                    </div>
                                    <small class="text-white">Processing Messages...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <!-- General Message Bot Section -->
                    <div class="col-md-4 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">General Message Bot</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="generalMessageIdInput" class="form-control" placeholder="Enter Account ID (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2)</small>
                                <br><br>
                                <select id="generalMessageAction" class="form-control">
                                    <option value="start">Start</option>
                                    <option value="resume">Resume</option>
                                </select>
                                <small class="text-white">Select action type</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunGeneralMessage()">RUN</button>
                                <div id="generalMessageStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Running...</span>
                                    </div>
                                    <small class="text-white">Processing General Messages...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <!-- Image Bot Section -->
                    <div class="col-md-4 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Image Bot</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="imageIdInput" class="form-control" placeholder="Enter Account ID (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2)</small>
                                <br><br>
                                <select id="imageAction" class="form-control">
                                    <option value="start">Start</option>
                                    <option value="resume">Resume</option>
                                </select>
                                <small class="text-white">Select action type</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunImage()">RUN</button>
                                <div id="imageStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Running...</span>
                                    </div>
                                    <small class="text-white">Processing Images...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>
                </div>

                <!-- Third Row: Advanced Messaging & Cleanup -->
                <div class="row justify-content-center">
                    <!-- LLM Message Bot Section -->
                    <div class="col-md-6 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">LLM Message Bot</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="firstMessageIdInput" class="form-control" placeholder="Enter Account ID (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2)</small>
                                <br><br>
                                <select id="firstMessageAction" class="form-control">
                                    <option value="start">Start</option>
                                    <option value="resume">Resume</option>
                                </select>
                                <small class="text-white">Select action type</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunFirstMessage()">RUN</button>
                                <div id="firstMessageStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Running...</span>
                                    </div>
                                    <small class="text-white">Processing First Messages...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>

                    <!-- Delete Blocked Users Section -->
                    <div class="col-md-6 username-box">
                        <div class="card mb-3 glossy-button equal-height">
                            <br>
                            <h6 class="centered-texts">Delete Blocked Users</h6>
                            <br>
                            <div style="text-align: center;">
                                <img src="{{ url_for('static', filename='assets/img/transformation.png') }}" alt="Loader">
                                <br>
                                <input type="text" id="cleanInput" class="form-control" placeholder="Enter account numbers (1-125)">
                                <small class="text-white">Enter account numbers (1-125) separated by commas (e.g., 1,2,3 or 5,10,15)</small>
                                <br><br>
                                <button class="btn btn-primary" style="height: 40px;" onclick="confirmRunClean()">Run Clean</button>
                                <div id="cleanStatus" class="mt-2" style="display: none;">
                                    <div class="spinner-border text-light" role="status">
                                        <span class="sr-only">Cleaning...</span>
                                    </div>
                                    <small class="text-white">Processing Clean...</small>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Custom Prompt Editor Modal -->
    <div class="modal" id="promptEditorModal" tabindex="-1" role="dialog" style="display: none; background-color: rgba(0,0,0,0.5); position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 1050; overflow: auto;">
        <div class="modal-dialog modal-lg" role="document" style="margin: 30px auto; pointer-events: auto; max-width: 80%;">
            <div class="modal-content" style="background-color: #2c2c2c; color: white; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.2);">
                <div class="modal-header" style="border-bottom: 1px solid #444; padding: 15px; display: flex; justify-content: space-between; align-items: center;">
                    <h5 class="modal-title" id="promptEditorModalLabel" style="margin: 0; font-size: 18px;">Custom Prompt Editor</h5>
                    <button type="button" class="close" id="closePromptModal" style="color: white; background: none; border: none; font-size: 24px; cursor: pointer; padding: 0;">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" style="padding: 15px;">
                    <!-- CSV Upload Section -->
                    <div style="background-color: #1a1a1a; padding: 15px; border-radius: 8px; margin-bottom: 20px; border: 1px solid #444;">
                        <h6 style="color: #fff; margin-bottom: 10px;">
                            <i class="fa fa-file-csv"></i> Upload CSV File
                        </h6>
                        <p style="color: #ccc; font-size: 14px; margin-bottom: 10px;">
                            Upload a CSV file with prompts to create multiple account files at once. 
                            The CSV should have a "prompt" column header.
                        </p>
                        <div class="form-group" style="margin-bottom: 10px;">
                            <input type="file" id="promptCsvFile" accept=".csv" style="display: none;">
                            <label for="promptCsvFile" class="btn btn-info" style="cursor: pointer; margin-bottom: 10px;">
                                <i class="fa fa-upload"></i> Choose CSV File
                            </label>
                            <span id="csvFileName" style="color: #ccc; margin-left: 10px;">No file selected</span>
                        </div>
                        <button type="button" class="btn btn-warning" id="uploadCsvBtn" style="width: 100%;" disabled>
                            <i class="fa fa-cloud-upload"></i> Upload and Create Prompt Files
                        </button>
                        <div id="csvUploadStatus" class="alert" style="display: none; margin-top: 10px; padding: 10px; border-radius: 4px;"></div>
                    </div>
                    
                    <!-- Separator -->
                    <div style="text-align: center; margin: 20px 0;">
                        <span style="color: #666;">— OR —</span>
                    </div>
                    
                    <!-- Individual Account Editor -->
                    <div style="background-color: #1a1a1a; padding: 15px; border-radius: 8px; border: 1px solid #444;">
                        <h6 style="color: #fff; margin-bottom: 10px;">
                            <i class="fa fa-edit"></i> Edit Individual Account Prompt
                        </h6>
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="promptAccountSelect" style="display: block; margin-bottom: 5px; font-weight: bold;">Select Account</label>
                            <select class="form-control" id="promptAccountSelect" style="width: 100%; padding: 8px; border-radius: 4px; background-color: #444; color: white; border: 1px solid #555;">
                                <option value="">Select an account</option>
                                <!-- Generate options 1-125 -->
                                <script>
                                    for (let i = 1; i <= 125; i++) {
                                        document.write(`<option value="${i}">Account ${i}</option>`);
                                    }
                                </script>
                            </select>
                        </div>
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="promptTextArea" style="display: block; margin-bottom: 5px; font-weight: bold;">Prompt Text</label>
                            <textarea class="form-control" id="promptTextArea" rows="15" style="width: 100%; padding: 10px; border-radius: 4px; background-color: #333; color: white; border: 1px solid #555; font-family: monospace; font-size: 14px; line-height: 1.5;"></textarea>
                        </div>
                        <div id="promptEditorStatus" class="alert alert-info" style="display: none; padding: 10px; border-radius: 4px; margin-top: 10px;"></div>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid #444; padding: 15px; display: flex; justify-content: flex-end;">
                    <button type="button" class="btn btn-secondary" id="closePromptModalBtn" style="margin-right: 10px; padding: 8px 16px; border-radius: 4px; background-color: #6c757d; border: none; color: white; cursor: pointer;">Close</button>
                    <button type="button" class="btn btn-primary" id="resetPromptBtn" style="margin-right: 10px; padding: 8px 16px; border-radius: 4px; background-color: #007bff; border: none; color: white; cursor: pointer;">Reset Prompt</button>
                    <button type="button" class="btn btn-success" id="savePromptBtn" style="padding: 8px 16px; border-radius: 4px; background-color: #28a745; border: none; color: white; cursor: pointer;">Save Prompt</button>
                </div>
            </div>
        </div>
    </div>

    <template id="slotTemplate">
        <div class="schedule-slot-item slot-item">
            <div class="slot-header" onclick="toggleSlot(this)">
                <div class="slot-title">
                    <span class="expand-icon">▼</span>
                    <input type="text" class="form-control slot-id" placeholder="Slot Name" style="width: 120px; height: 28px; font-size: 13px;" onclick="event.stopPropagation()">
                    <span class="selected-accounts-preview"></span>
                </div>
                <div class="slot-actions">
                    <button type="button" class="btn btn-sm btn-danger remove-slot" onclick="event.stopPropagation(); removeSlot(this)">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="slot-content">
                <!-- Time and Basic Settings -->
                <div class="row mb-3">
                    <div class="col-4">
                        <label class="text-white small">Time</label>
                        <input type="time" class="form-control slot-time" value="09:00" style="font-size: 13px;">
                    </div>
                    <div class="col-4">
                        <label class="text-white small">Action</label>
                        <select class="form-control slot-action" style="font-size: 13px;">
                            <option value="start">Start</option>
                            <option value="resume">Resume</option>
                        </select>
                    </div>
                    <div class="col-4">
                        <label class="text-white small">Auto-Split</label>
                        <button type="button" class="auto-split-toggle" onclick="toggleAutoSplitButton(this)">
                            Auto-Split OFF
                        </button>
                    </div>
                </div>
                
                <!-- Auto-split settings -->
                <div class="auto-split-settings">
                    <div class="row mb-2">
                        <div class="col-12">
                            <label class="text-white small">Auto-Split Settings</label>
                            <p class="text-white small mb-2">Will create multiple slots with 10 accounts each</p>
                            <div class="d-flex align-items-center">
                                <span class="text-white small">Time gap between slots:</span>
                                <input type="number" class="form-control time-gap-input" value="60" min="15" max="120">
                                <span class="text-white small">minutes</span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button type="button" class="btn btn-warning btn-sm" onclick="applyAutoSplit(this)">
                                Apply Auto-Split (10 accounts per slot)
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Days Selection -->
                <div class="mb-3">
                    <label class="text-white small">Days of Week</label>
                    <div class="days-week-compact">
                        <button type="button" class="day-toggle active" data-day="0">M</button>
                        <button type="button" class="day-toggle active" data-day="1">T</button>
                        <button type="button" class="day-toggle active" data-day="2">W</button>
                        <button type="button" class="day-toggle active" data-day="3">T</button>
                        <button type="button" class="day-toggle active" data-day="4">F</button>
                        <button type="button" class="day-toggle active" data-day="5">S</button>
                        <button type="button" class="day-toggle active" data-day="6">S</button>
                    </div>
                </div>
                
                <!-- Account Type Selector Dropdown -->
                <div class="account-type-selector">
                    <select class="account-type-dropdown" onchange="toggleAccountInput(this)">
                        <option value="">Select Account Type to Configure</option>
                        <option value="message">Message Accounts</option>
                        <option value="image">Image Accounts</option>
                        <option value="delete">Delete Accounts</option>
                        <option value="bio">Bio Scanning Accounts</option>
                        <option value="scanning">Scanning Accounts</option>
                        <option value="general-scanning">General Scanning Accounts</option>
                        <option value="message-generator">Message Generator Accounts</option>
                        <option value="general-message">General Message Accounts</option>
                        <option value="first-message">First Message Accounts</option>
                    </select>
                    
                    <!-- Account Input Groups -->
                    <div class="account-input-group" data-type="message">
                        <label class="text-white small">Message Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-5')">1-5</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '6-10')">6-10</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-10')">1-10</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control message-accounts" placeholder="e.g. 1,2,3,4,5">
                    </div>
                    
                    <div class="account-input-group" data-type="image">
                        <label class="text-white small">Image Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '11-15')">11-15</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '16-20')">16-20</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '11-20')">11-20</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control image-accounts" placeholder="e.g. 6,7,8,9,10">
                    </div>
                    
                    <div class="account-input-group" data-type="delete">
                        <label class="text-white small">Delete Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '21-25')">21-25</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '26-30')">26-30</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control delete-accounts" placeholder="e.g. 11,12,13,14,15">
                    </div>
                    
                    <div class="account-input-group" data-type="bio">
                        <label class="text-white small">Bio Scanning Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control bio-accounts" placeholder="e.g. 16,17,18">
                    </div>
                    
                    <div class="account-input-group" data-type="scanning">
                        <label class="text-white small">Scanning Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control scanning-accounts" placeholder="e.g. 19,20,21">
                    </div>
                    
                    <div class="account-input-group" data-type="general-scanning">
                        <label class="text-white small">General Scanning Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control general-scanning-accounts" placeholder="e.g. 22,23,24">
                    </div>
                    
                    <div class="account-input-group" data-type="message-generator">
                        <label class="text-white small">Message Generator Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control message-generator-accounts" placeholder="e.g. 25,26,27">
                    </div>
                    
                    <div class="account-input-group" data-type="general-message">
                        <label class="text-white small">General Message Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control general-message-accounts" placeholder="e.g. 28,29,30">
                    </div>
                    
                    <div class="account-input-group" data-type="first-message">
                        <label class="text-white small">First Message Accounts</label>
                        <div class="quick-fill-buttons">
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, '1-40')">All 40</button>
                            <button type="button" class="quick-fill-btn" onclick="quickFillAccounts(this, 'clear')">Clear</button>
                        </div>
                        <input type="text" class="form-control first-message-accounts" placeholder="e.g. 31,32,33">
                    </div>
                </div>
                
                <!-- Account Summary -->
                <div class="mt-2 account-summary" style="display: none;">
                    <small class="text-white">Configured Accounts:</small>
                    <div class="account-badges"></div>
                </div>
            </div>
        </div>
    </template>
    
    <script>
        let slotCounter = 0;
        
        // Notification function - using SweetAlert2
        function showNotification(message, isError = false) {
            Swal.fire({
                title: isError ? 'Error' : 'Success',
                text: message,
                icon: isError ? 'error' : 'success',
                confirmButtonColor: '#3085d6',
                confirmButtonText: 'OK'
            });
        }
        
        // Helper function for debugging responses
        function logResponse(response, stage) {
            console.log(`[${stage}] Status: ${response.status}, ${response.statusText}`);
            return response;
        }
        
        // ===== CUSTOM PROMPT FUNCTIONS =====
        function openPromptEditor() {
            console.log("Opening prompt editor modal");
            try {
                const modal = document.getElementById('promptEditorModal');
                if (!modal) {
                    console.error("Modal element not found");
                    return;
                }
                
                modal.style.display = 'block';
                
                const accountSelect = document.getElementById('promptAccountSelect');
                const promptTextArea = document.getElementById('promptTextArea');
                const statusDiv = document.getElementById('promptEditorStatus');
                
                if (accountSelect) accountSelect.value = '';
                if (promptTextArea) promptTextArea.value = '';
                if (statusDiv) statusDiv.style.display = 'none';
                
                console.log("Modal should be visible now");
            } catch (e) {
                console.error("Error opening prompt editor:", e);
                alert("Error opening prompt editor: " + e.message);
            }
        }
        
        function closePromptEditor() {
            console.log("Closing prompt editor modal");
            try {
                const modal = document.getElementById('promptEditorModal');
                if (modal) {
                    modal.style.display = 'none';
                }
            } catch (e) {
                console.error("Error closing prompt editor:", e);
            }
        }
        
        function loadPromptForAccount() {
            const accountId = document.getElementById('promptAccountSelect').value;
            if (!accountId) return;
        
            const statusDiv = document.getElementById('promptEditorStatus');
            const promptTextArea = document.getElementById('promptTextArea');
            
            statusDiv.textContent = 'Loading prompt...';
            statusDiv.className = 'alert alert-info';
            statusDiv.style.display = 'block';
            promptTextArea.value = '';
            
            fetch(`http://localhost:8080/custom-prompt/${accountId}`)
                .then(response => {
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        promptTextArea.value = data.prompt_text;
                        statusDiv.textContent = 'Custom prompt loaded.';
                        statusDiv.className = 'alert alert-success';
                    } else {
                        promptTextArea.value = '';
                        statusDiv.textContent = 'No custom prompt found. Create one below.';
                        statusDiv.className = 'alert alert-info';
                    }
                })
                .catch(error => {
                    console.error('Error loading prompt:', error);
                    statusDiv.textContent = `Error loading prompt: ${error.message}`;
                    statusDiv.className = 'alert alert-danger';
                });
        }
        
        function saveCustomPrompt() {
            const accountId = document.getElementById('promptAccountSelect').value;
            const promptText = document.getElementById('promptTextArea').value;
            const statusDiv = document.getElementById('promptEditorStatus');
            
            if (!accountId) {
                statusDiv.textContent = 'Please select an account first.';
                statusDiv.className = 'alert alert-danger';
                statusDiv.style.display = 'block';
                return;
            }
            
            if (!promptText || promptText.trim() === '') {
                statusDiv.textContent = 'Prompt text cannot be empty.';
                statusDiv.className = 'alert alert-danger';
                statusDiv.style.display = 'block';
                return;
            }
            
            statusDiv.textContent = 'Saving prompt...';
            statusDiv.className = 'alert alert-info';
            statusDiv.style.display = 'block';
            
            fetch('http://localhost:8080/custom-prompt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    account_id: parseInt(accountId),
                    prompt_text: promptText
                })
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(data => {
                statusDiv.textContent = `${data.message}`;
                statusDiv.className = 'alert alert-success';
                showNotification(`Custom prompt for Account ${accountId} saved successfully!`);
            })
            .catch(error => {
                console.error('Error saving prompt:', error);
                statusDiv.textContent = `Error saving prompt: ${error.message}`;
                statusDiv.className = 'alert alert-danger';
            });
        }
        
        function resetToDefaultPrompt() {
            const accountId = document.getElementById('promptAccountSelect').value;
            const statusDiv = document.getElementById('promptEditorStatus');
            const promptTextArea = document.getElementById('promptTextArea');
            
            if (!accountId) {
                statusDiv.textContent = 'Please select an account first.';
                statusDiv.className = 'alert alert-danger';
                statusDiv.style.display = 'block';
                return;
            }
            
            Swal.fire({
                title: 'Confirm Reset',
                text: 'Are you sure you want to reset the prompt? This will delete any custom prompt for this account.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, reset it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    fetch(`http://localhost:8080/custom-prompt/${accountId}`, {
                        method: 'DELETE',
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Network response was not ok');
                        return response.json();
                    })
                    .then(data => {
                        promptTextArea.value = '';
                        statusDiv.textContent = 'Custom prompt has been deleted.';
                        statusDiv.className = 'alert alert-success';
                        statusDiv.style.display = 'block';
                        showNotification(`Account ${accountId} prompt has been reset`);
                    })
                    .catch(error => {
                        console.error('Error resetting prompt:', error);
                        statusDiv.textContent = `Error resetting prompt: ${error.message}`;
                        statusDiv.className = 'alert alert-danger';
                        statusDiv.style.display = 'block';
                    });
                }
            });
        }
        
        // ===== CSV UPLOAD FUNCTIONS =====
        function setupCsvUpload() {
            const fileInput = document.getElementById('promptCsvFile');
            const uploadBtn = document.getElementById('uploadCsvBtn');
            const fileNameSpan = document.getElementById('csvFileName');
            
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        fileNameSpan.textContent = file.name;
                        uploadBtn.disabled = false;
                    } else {
                        fileNameSpan.textContent = 'No file selected';
                        uploadBtn.disabled = true;
                    }
                });
            }
            
            if (uploadBtn) {
                uploadBtn.addEventListener('click', uploadCsvFile);
            }
        }
        
        function uploadCsvFile() {
            const fileInput = document.getElementById('promptCsvFile');
            const statusDiv = document.getElementById('csvUploadStatus');
            const uploadBtn = document.getElementById('uploadCsvBtn');
            
            if (!fileInput.files || !fileInput.files[0]) {
                statusDiv.textContent = 'Please select a CSV file first.';
                statusDiv.className = 'alert alert-danger';
                statusDiv.style.display = 'block';
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            
            // Disable upload button and show loading
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Uploading...';
            
            statusDiv.textContent = 'Uploading CSV file...';
            statusDiv.className = 'alert alert-info';
            statusDiv.style.display = 'block';
            
            fetch('http://localhost:8080/custom-prompt/upload-csv', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    statusDiv.innerHTML = `
                        <strong>Success!</strong><br>
                        ${data.message}<br>
                        <small>Files created: ${data.files_created.join(', ')}</small>
                    `;
                    statusDiv.className = 'alert alert-success';
                    
                    // Reset file input
                    fileInput.value = '';
                    document.getElementById('csvFileName').textContent = 'No file selected';
                    
                    // Show success notification
                    showNotification(`Successfully created ${data.files_created.length} custom prompt files!`, false);
                } else {
                    statusDiv.textContent = data.error || 'Failed to upload CSV file.';
                    statusDiv.className = 'alert alert-danger';
                }
            })
            .catch(error => {
                console.error('Error uploading CSV:', error);
                statusDiv.textContent = `Error uploading CSV: ${error.message}`;
                statusDiv.className = 'alert alert-danger';
            })
            .finally(() => {
                // Re-enable upload button
                uploadBtn.disabled = false;
                uploadBtn.innerHTML = '<i class="fa fa-cloud-upload"></i> Upload and Create Prompt Files';
            });
        }
        
        // ===== CAPTURE BOT FUNCTIONS =====
        function updateCaptureStatus(show) {
            const button = document.querySelector("#captureInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('captureStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunCapture() {
            var captureValue = document.getElementById("captureInput").value;
            if (!captureValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var captures = captureValue.split(',').map(s => s.trim());
            
            for (let capture of captures) {
                let num = parseInt(capture);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = captures.length === 1 
                ? `Are you sure you want to scan Account ${captures[0]}?`
                : `Are you sure you want to scan Accounts ${captures.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, scan it!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runCapture();
                        resolve();
                    });
                }
            });
        }
        
        function runCapture() {
            var captureValue = document.getElementById("captureInput").value;
            
            if (!captureValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accounts = captureValue.split(',').map(s => s.trim());
            updateCaptureStatus(true);
        
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter numbers between 1 and 125.", true);
                    updateCaptureStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-capture/${captureValue}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id}: ${result.status === 'Success' ? 'Completed scanning' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed scanning ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`Task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                updateCaptureStatus(false);
            });
        }
        
        // ===== GENERAL CAPTURE BOT FUNCTIONS =====
        function updateGeneralCaptureStatus(show) {
            const button = document.querySelector("#generalCaptureInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('generalCaptureStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunGeneralCapture() {
            var generalCaptureValue = document.getElementById("generalCaptureInput").value;
            if (!generalCaptureValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var captures = generalCaptureValue.split(',').map(s => s.trim());
            
            for (let capture of captures) {
                let num = parseInt(capture);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = captures.length === 1 
                ? `Are you sure you want to run general capture for Account ${captures[0]}?`
                : `Are you sure you want to run general capture for Accounts ${captures.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, run it!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runGeneralCapture();
                        resolve();
                    });
                }
            });
        }
        
        function runGeneralCapture() {
            var generalCaptureValue = document.getElementById("generalCaptureInput").value;
            
            if (!generalCaptureValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accounts = generalCaptureValue.split(',').map(s => s.trim());
            updateGeneralCaptureStatus(true);
        
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter numbers between 1 and 125.", true);
                    updateGeneralCaptureStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-general-capture/${generalCaptureValue}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id}: ${result.status === 'Success' ? 'Completed general capture' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed general capture for ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`Task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                updateGeneralCaptureStatus(false);
            });
        }
        
        // ===== CLEAN BOT FUNCTIONS =====
        function updateCleanStatus(show) {
            const button = document.querySelector("#cleanInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('cleanStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunClean() {
            var cleanValue = document.getElementById("cleanInput").value;
            if (!cleanValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var accounts = cleanValue.split(',').map(s => s.trim());
            
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = accounts.length === 1 
                ? `Are you sure you want to clean Account ${accounts[0]}?`
                : `Are you sure you want to clean Accounts ${accounts.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, clean it!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runClean();
                        resolve();
                    });
                }
            });
        }
        
        function runClean() {
            var cleanValue = document.getElementById("cleanInput").value;
            
            if (!cleanValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accounts = cleanValue.split(',').map(s => s.trim());
            updateCleanStatus(true);
        
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter numbers between 1 and 125.", true);
                    updateCleanStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-delete/${cleanValue}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id}: ${result.status === 'Success' ? 'Completed cleaning' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed cleaning ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`Task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                updateCleanStatus(false);
            });
        }
        
        // ===== BIO BOT FUNCTIONS =====
        function updateBioStatus(show) {
            const button = document.querySelector("#bioInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('bioStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function updateWarmupStatus(show) {
            const button = document.querySelector("#warmupInput").closest('.card').querySelector(".btn-success");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('warmupStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunBio() {
            var bioValue = document.getElementById("bioInput").value;
            if (!bioValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var bios = bioValue.split(',').map(s => s.trim());
            
            for (let bio of bios) {
                let num = parseInt(bio);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = bios.length === 1 
                ? `Are you sure you want to run bio scan for Account ${bios[0]}?`
                : `Are you sure you want to run bio scan for Accounts ${bios.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, scan it!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runBio();
                        resolve();
                    });
                }
            });
        }
        
        function confirmRunWarmup() {
            var warmupValue = document.getElementById("warmupInput").value;
            if (!warmupValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var warmups = warmupValue.split(',').map(s => s.trim());
            
            for (let warmup of warmups) {
                let num = parseInt(warmup);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = warmups.length === 1 
                ? `Are you sure you want to run warmup for Account ${warmups[0]}?`
                : `Are you sure you want to run warmup for Accounts ${warmups.join(', ')}?`;
        
            Swal.fire({
                title: 'Account Warmup Confirmation',
                text: message + '\n\nThis will perform human-like activities (browsing, liking, commenting, following) to warm up the account safely.',
                icon: 'info',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, warm up!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runWarmup();
                        resolve();
                    });
                }
            });
        }
        
        function runBio() {
            var bioValue = document.getElementById("bioInput").value;
            
            if (!bioValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accounts = bioValue.split(',').map(s => s.trim());
            updateBioStatus(true);
        
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter numbers between 1 and 125.", true);
                    updateBioStatus(false);
                    return;
                }
            }
        
            // Call local Flask API with relative path, individual account requests to avoid port/CORS issues
            const requests = accounts.map(acc => {
                const accountId = parseInt(acc);
                return fetch(`/api/v1/bots/bio_scanner`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                    },
                    body: JSON.stringify({ account_id: accountId, use_proxy_fallback: true })
                })
                .then(res => {
                    if (!res.ok) throw new Error(`HTTP ${res.status}`);
                    return res.json();
                })
                .then(data => ({ account_id: accountId, success: data && data.success !== false, data }))
                .catch(err => ({ account_id: accountId, success: false, error: err && err.message ? err.message : String(err) }));
            });

            Promise.all(requests)
                .then(results => {
                    let successCount = 0;
                    results.forEach(r => {
                        if (r.success) {
                            successCount += 1;
                            showNotification(`Account ${r.account_id}: Completed bio scanning`);
                        } else {
                            const errMsg = r.error || (r.data && r.data.error) || 'Unknown error';
                            showNotification(`Account ${r.account_id}: Failed - ${errMsg}`, true);
                        }
                    });
                    showNotification(`Completed bio scanning ${successCount} out of ${results.length} accounts`);
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                updateBioStatus(false);
            });
        }
        
        function runWarmup() {
            var warmupValue = document.getElementById("warmupInput").value;
            
            if (!warmupValue) {
                showNotification("Please enter account numbers (1-125)", true);
                return;
            }
        
            var accounts = warmupValue.split(',').map(s => s.trim());
            updateWarmupStatus(true);
        
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter numbers between 1 and 125.", true);
                    updateWarmupStatus(false);
                    return;
                }
            }
        
            // Call local Flask API with relative path, individual account requests to avoid port/CORS issues
            const requests = accounts.map(acc => {
                const accountId = parseInt(acc);
                return fetch(`/api/v1/bots/warmup`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ account_id: accountId })
                })
                .then(res => {
                    if (!res.ok) throw new Error(`HTTP ${res.status}`);
                    return res.json();
                })
                .then(data => ({ account_id: accountId, success: data && data.success !== false, data }))
                .catch(err => ({ account_id: accountId, success: false, error: err && err.message ? err.message : String(err) }));
            });

            Promise.all(requests)
                .then(results => {
                    let successCount = 0;
                    results.forEach(r => {
                        if (r.success) {
                            successCount += 1;
                            const stats = r.data && r.data.stats ? r.data.stats : {};
                            const actions = Object.values(stats).reduce((sum, val) => sum + val, 0);
                            showNotification(`Account ${r.account_id}: Completed warmup (${actions} actions performed)`);
                        } else {
                            const errMsg = r.error || (r.data && r.data.error) || 'Unknown error';
                            showNotification(`Account ${r.account_id}: Failed - ${errMsg}`, true);
                        }
                    });
                    showNotification(`Completed warmup for ${successCount} out of ${results.length} accounts`);
                })
                .catch(error => {
                    showNotification(`Error: ${error.message}`, true);
                })
                .finally(() => {
                    updateWarmupStatus(false);
            });
        }
        
        // ===== MESSAGE GENERATOR FUNCTIONS =====
        function updateMessageGeneratorStatus(show) {
            const button = document.querySelector("#messageGeneratorInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('messageGeneratorStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunMessageGenerator() {
            var messageGeneratorValue = document.getElementById("messageGeneratorInput").value;
            if (!messageGeneratorValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var accounts = messageGeneratorValue.split(',').map(s => s.trim());
            
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = accounts.length === 1 
                ? `Are you sure you want to run message generator for Account ${accounts[0]}?`
                : `Are you sure you want to run message generator for Accounts ${accounts.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, generate messages!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runMessageGenerator();
                        resolve();
                    });
                }
            });
        }
        
        function runMessageGenerator() {
            var messageGeneratorValue = document.getElementById("messageGeneratorInput").value;
            
            if (!messageGeneratorValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accounts = messageGeneratorValue.split(',').map(s => s.trim());
            updateMessageGeneratorStatus(true);
        
            for (let account of accounts) {
                let num = parseInt(account);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter numbers between 1 and 125.", true);
                    updateMessageGeneratorStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-message-generator/${messageGeneratorValue}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id}: ${result.status === 'Success' ? 'Completed message generation' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed message generation for ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`Task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                updateMessageGeneratorStatus(false);
            });
        }
        
        // ===== FIRST MESSAGE BOT FUNCTIONS =====
        function updateFirstMessageStatus(show) {
            const statusDiv = document.getElementById('firstMessageStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunFirstMessage() {
            var accountIdValue = document.getElementById("firstMessageIdInput").value;
            var action = document.getElementById("firstMessageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = accountIds.length === 1 
                ? `Are you sure you want to ${action} first message for Account ${accountIds[0]}?`
                : `Are you sure you want to ${action} first message for Accounts ${accountIds.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: `Yes, ${action} it!`,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runFirstMessage();
                        resolve();
                    });
                }
            });
        }
        
        function runFirstMessage() {
            var accountIdValue = document.getElementById("firstMessageIdInput").value;
            var action = document.getElementById("firstMessageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            var button = document.querySelector("#firstMessageIdInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = true;
            updateFirstMessageStatus(true);
        
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter account numbers between 1 and 125.", true);
                    if (button) button.disabled = false;
                    updateFirstMessageStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-first-message/${accountIdValue}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (response.results && Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id} (${action}): ${result.status === 'Success' ? 'Completed first message successfully' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed first message ${action} for ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`First message task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                if (button) button.disabled = false;
                updateFirstMessageStatus(false);
            });
        }
        
        // ===== MESSAGE BOT FUNCTIONS =====
        function updateMessageStatus(show) {
            const statusDiv = document.getElementById('messageStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunAutomation() {
            var accountIdValue = document.getElementById("messageIdInput1").value;
            var action = document.getElementById("messageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = accountIds.length === 1 
                ? `Are you sure you want to ${action} messaging for Account ${accountIds[0]}?`
                : `Are you sure you want to ${action} messaging for Accounts ${accountIds.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: `Yes, ${action} it!`,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runMessage();
                        resolve();
                    });
                }
            });
        }
        
        function runMessage() {
            var accountIdValue = document.getElementById("messageIdInput1").value;
            var action = document.getElementById("messageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            var button = document.querySelector("#messageIdInput1").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = true;
            updateMessageStatus(true);
        
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter account numbers between 1 and 125.", true);
                    if (button) button.disabled = false;
                    updateMessageStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-message/${accountIdValue}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (response.results && Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id} (${action}): ${result.status === 'Success' ? 'Completed successfully' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed ${action} for ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`Task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                if (button) button.disabled = false;
                updateMessageStatus(false);
            });
        }
        
        // ===== GENERAL MESSAGE BOT FUNCTIONS =====
        function updateGeneralMessageStatus(show) {
            const statusDiv = document.getElementById('generalMessageStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunGeneralMessage() {
            var accountIdValue = document.getElementById("generalMessageIdInput").value;
            var action = document.getElementById("generalMessageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = accountIds.length === 1 
                ? `Are you sure you want to ${action} general message for Account ${accountIds[0]}?`
                : `Are you sure you want to ${action} general message for Accounts ${accountIds.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: `Yes, ${action} it!`,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runGeneralMessage();
                        resolve();
                    });
                }
            });
        }
        
        function runGeneralMessage() {
            var accountIdValue = document.getElementById("generalMessageIdInput").value;
            var action = document.getElementById("generalMessageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            var button = document.querySelector("#generalMessageIdInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = true;
            updateGeneralMessageStatus(true);
        
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter account numbers between 1 and 125.", true);
                    if (button) button.disabled = false;
                    updateGeneralMessageStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-general-message/${accountIdValue}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (response.results && Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id} (${action}): ${result.status === 'Success' ? 'Completed general message successfully' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed general message ${action} for ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`General message task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                if (button) button.disabled = false;
                updateGeneralMessageStatus(false);
            });
        }
        
        // ===== IMAGE BOT FUNCTIONS =====
        function updateImageStatus(show) {
            const statusDiv = document.getElementById('imageStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function confirmRunImage() {
            var accountIdValue = document.getElementById("imageIdInput").value;
            var action = document.getElementById("imageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Please enter valid account numbers between 1 and 125", true);
                    return;
                }
            }
        
            let message = accountIds.length === 1 
                ? `Are you sure you want to ${action} image processing for Account ${accountIds[0]}?`
                : `Are you sure you want to ${action} image processing for Accounts ${accountIds.join(', ')}?`;
        
            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: `Yes, ${action} it!`,
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        runImage();
                        resolve();
                    });
                }
            });
        }
        
        function runImage() {
            var accountIdValue = document.getElementById("imageIdInput").value;
            var action = document.getElementById("imageAction").value;
            
            if (!accountIdValue) {
                showNotification("Please enter account numbers (1-40)", true);
                return;
            }
        
            var accountIds = accountIdValue.split(',').map(s => s.trim());
            var button = document.querySelector("#imageIdInput").closest('.card').querySelector(".btn-primary");
            if (button) button.disabled = true;
            updateImageStatus(true);
        
            for (let id of accountIds) {
                let num = parseInt(id);
                if (isNaN(num) || num < 1 || num > 125) {
                    showNotification("Invalid input. Please enter account numbers between 1 and 125.", true);
                    if (button) button.disabled = false;
                    updateImageStatus(false);
                    return;
                }
            }
        
            fetch(`http://localhost:8080/run-image/${accountIdValue}/${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                if (response.results && Array.isArray(response.results)) {
                    response.results.forEach(result => {
                        const message = `Account ${result.account_id} (${action}): ${result.status === 'Success' ? 'Completed image processing' : 'Failed - ' + result.error}`;
                        showNotification(message, result.status !== 'Success');
                    });
                    
                    const successCount = response.results.filter(r => r.status === 'Success').length;
                    showNotification(`Completed image processing (${action}) for ${successCount} out of ${response.results.length} accounts`);
                } else if (response.results) {
                    showNotification(`Account ${response.results.account_id}: ${response.results.message}`);
                } else if (response.task_id) {
                    showNotification(`Task started with ID: ${response.task_id}`);
                }
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                if (button) button.disabled = false;
                updateImageStatus(false);
            });
        }
        
        // ===== SCHEDULE BUILDER FUNCTIONS =====
        function toggleSlot(element) {
            const slotItem = element.closest('.schedule-slot-item');
            if (!slotItem) return;
            
            slotItem.classList.toggle('slot-collapsed');
            updateSlotPreview(slotItem);
        }
        
        function removeSlot(button) {
            const slotItem = button.closest('.schedule-slot-item');
            if (slotItem) {
                slotItem.remove();
                updateJsonPreview();
            }
        }
        
        function toggleAccountInput(select) {
            const slotItem = select.closest('.schedule-slot-item');
            const selectedValue = select.value;
            
            const allGroups = slotItem.querySelectorAll('.account-input-group');
            allGroups.forEach(group => {
                group.classList.remove('active');
            });
            
            if (selectedValue) {
                const selectedGroup = slotItem.querySelector(`.account-input-group[data-type="${selectedValue}"]`);
                if (selectedGroup) {
                    selectedGroup.classList.add('active');
                }
            }
            
            updateSlotPreview(slotItem);
        }
        
        function quickFillAccounts(button, range) {
    const inputGroup = button.closest('.account-input-group');
    const input = inputGroup.querySelector('input[type="text"]');
    
    if (range === 'clear') {
        input.value = '';
    } else if (range === '1-40') {
        // Fill all 40 accounts
        const accounts = [];
        for (let i = 1; i <= 40; i++) {
            accounts.push(i);
        }
        input.value = accounts.join(',');
    } else {
        const [start, end] = range.split('-').map(Number);
        const accounts = [];
        for (let i = start; i <= end; i++) {
            accounts.push(i);
        }
        input.value = accounts.join(',');
    }
    
    input.dispatchEvent(new Event('input'));
    updateSlotPreview(inputGroup.closest('.schedule-slot-item'));
}
        
        function updateSlotPreview(slotItem) {
            const preview = slotItem.querySelector('.selected-accounts-preview');
            if (!preview) return;
            
            const accounts = [];
            const inputs = slotItem.querySelectorAll('.account-input-group input[type="text"]');
            
            inputs.forEach(input => {
                if (input.value.trim()) {
                    const type = input.classList[1].replace('-accounts', '');
                    const count = input.value.split(',').filter(id => id.trim()).length;
                    if (count > 0) {
                        accounts.push(`${count} ${type}`);
                    }
                }
            });
            
            preview.textContent = accounts.length > 0 ? `(${accounts.join(', ')})` : '';
            
            updateAccountSummary(slotItem);
            updateJsonPreview();
        }
        
        function updateAccountSummary(slotItem) {
            const summaryDiv = slotItem.querySelector('.account-summary');
            const badgesDiv = slotItem.querySelector('.account-badges');
            
            if (!summaryDiv || !badgesDiv) return;
            
            badgesDiv.innerHTML = '';
            let hasAccounts = false;
            
            const accountTypes = {
                'message-accounts': 'Message',
                'image-accounts': 'Image',
                'delete-accounts': 'Delete',
                'bio-accounts': 'Bio',
                'scanning-accounts': 'Scan',
                'general-scanning-accounts': 'Gen Scan',
                'message-generator-accounts': 'Msg Gen',
                'general-message-accounts': 'Gen Msg',
                'first-message-accounts': 'First Msg'
            };
            
            Object.entries(accountTypes).forEach(([className, label]) => {
                const input = slotItem.querySelector(`.${className}`);
                if (input && input.value.trim()) {
                    const ids = input.value.split(',').filter(id => id.trim());
                    if (ids.length > 0) {
                        hasAccounts = true;
                        const badge = document.createElement('span');
                        badge.className = 'account-badge';
                        badge.textContent = `${label}: ${ids.join(', ')}`;
                        badgesDiv.appendChild(badge);
                    }
                }
            });
            
            summaryDiv.style.display = hasAccounts ? 'block' : 'none';
        }
        
        function addNewSlot() {
            console.log("Adding new slot");
            slotCounter++;
            const template = document.getElementById('slotTemplate');
            const slotContainer = document.getElementById('slotContainer');
            
            if (!template || !slotContainer) {
                console.error("Could not find template or container element");
                return;
            }
            
            const clone = document.importNode(template.content, true);
            
            const slotIdInput = clone.querySelector('.slot-id');
            if (slotIdInput) slotIdInput.value = 'slot' + slotCounter;
            
            const dayToggles = clone.querySelectorAll('.day-toggle');
            dayToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    this.classList.toggle('active');
                    updateJsonPreview();
                });
            });
            
            const inputs = clone.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    updateSlotPreview(this.closest('.schedule-slot-item'));
                });
                input.addEventListener('input', function() {
                    updateSlotPreview(this.closest('.schedule-slot-item'));
                });
            });
            
            slotContainer.appendChild(clone);
            updateJsonPreview();
        }
        
        function generateSlotJson() {
            const slots = [];
            const slotItems = document.querySelectorAll('.slot-item');
            
            slotItems.forEach(item => {
                const slotId = item.querySelector('.slot-id').value.trim();
                const startTime = item.querySelector('.slot-time').value;
                const action = item.querySelector('.slot-action').value;
                
                const runOnceCheckbox = item.querySelector('.run-once-checkbox');
                const runOnce = runOnceCheckbox ? runOnceCheckbox.checked : true;
                
                const selectedDays = [];
                const dayToggles = item.querySelectorAll('.day-toggle.active');
                dayToggles.forEach(toggle => {
                    selectedDays.push(parseInt(toggle.dataset.day));
                });
                
                const messageInput = item.querySelector('.message-accounts').value.trim();
                const imageInput = item.querySelector('.image-accounts').value.trim();
                const deleteInput = item.querySelector('.delete-accounts').value.trim();
                const bioInput = item.querySelector('.bio-accounts').value.trim();
                const scanningInput = item.querySelector('.scanning-accounts').value.trim();
                const generalScanningInput = item.querySelector('.general-scanning-accounts').value.trim();
                const messageGeneratorInput = item.querySelector('.message-generator-accounts').value.trim();
                const generalMessageInput = item.querySelector('.general-message-accounts').value.trim();
                const firstMessageInput = item.querySelector('.first-message-accounts').value.trim();
                
                const messageAccounts = messageInput ? messageInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const imageAccounts = imageInput ? imageInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const deleteAccounts = deleteInput ? deleteInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const bioAccounts = bioInput ? bioInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const scanningAccounts = scanningInput ? scanningInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const generalScanningAccounts = generalScanningInput ? generalScanningInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const messageGeneratorAccounts = messageGeneratorInput ? messageGeneratorInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const generalMessageAccounts = generalMessageInput ? generalMessageInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                const firstMessageAccounts = firstMessageInput ? firstMessageInput.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : [];
                
                if (slotId && (messageAccounts.length > 0 || imageAccounts.length > 0 || deleteAccounts.length > 0 || 
                              bioAccounts.length > 0 || scanningAccounts.length > 0 || generalScanningAccounts.length > 0 ||
                              messageGeneratorAccounts.length > 0 || generalMessageAccounts.length > 0 || firstMessageAccounts.length > 0)) {
                    slots.push({
                        slot_id: slotId,
                        message_accounts: messageAccounts,
                        image_accounts: imageAccounts,
                        delete_accounts: deleteAccounts,
                        bio_accounts: bioAccounts,
                        scanning_accounts: scanningAccounts,
                        general_scanning_accounts: generalScanningAccounts,
                        message_generator_accounts: messageGeneratorAccounts,
                        general_message_accounts: generalMessageAccounts,
                        first_message_accounts: firstMessageAccounts,
                        start_time: startTime,
                        days_of_week: selectedDays.length > 0 ? selectedDays : [0,1,2,3,4,5,6],
                        action: action,
                        run_once: runOnce
                    });
                }
            });
            
            return {
                slots: slots
            };
        }
        
        function updateJsonPreview() {
            const jsonObj = generateSlotJson();
            const jsonContent = document.getElementById('jsonContent');
            if (jsonContent) {
                jsonContent.textContent = JSON.stringify(jsonObj, null, 2);
            }
        }
        
        function toggleJsonView() {
            const preview = document.getElementById('jsonPreview');
            if (!preview) return;
            
            if (preview.style.display === 'none') {
                preview.style.display = 'block';
                updateJsonPreview();
            } else {
                preview.style.display = 'none';
            }
        }
        
        function toggleAllSlots() {
            const slots = document.querySelectorAll('.schedule-slot-item');
            const anyExpanded = Array.from(slots).some(slot => !slot.classList.contains('slot-collapsed'));
            
            slots.forEach(slot => {
                if (anyExpanded) {
                    slot.classList.add('slot-collapsed');
                } else {
                    slot.classList.remove('slot-collapsed');
                }
            });
        }
        
        function updateScheduleStatus(show) {
            const button = document.querySelector("#scheduleConfig")?.closest('.card')?.querySelector(".btn-primary");
            if (button) button.disabled = show;
            const statusDiv = document.getElementById('scheduleStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
        }
        
        function generateAndSetSchedule() {
            const jsonObj = generateSlotJson();
            
            if (jsonObj.slots.length === 0) {
                showNotification("Please add at least one valid slot", true);
                return;
            }
            
            Swal.fire({
                title: 'Confirmation',
                text: `Are you sure you want to set ${jsonObj.slots.length} schedule slots?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, set schedule!',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return new Promise(resolve => {
                        updateScheduleStatus(true);
                        
                        fetch('http://localhost:8080/schedule/slots', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify(jsonObj)
                        })
                        .then(response => {
                            if (!response.ok) throw new Error('Network response was not ok');
                            return response.json();
                        })
                        .then(response => {
                            showNotification(`Schedule set successfully! ${response.slots} slots configured.`);
                            refreshSlotDropdown();
                            viewSchedule();
                            resolve();
                        })
                        .catch(error => {
                            showNotification(`Error: ${error.message}`, true);
                            resolve();
                        })
                        .finally(() => {
                            updateScheduleStatus(false);
                        });
                    });
                }
            });
        }
        
        // ===== VIEW SCHEDULE FUNCTIONS =====
        function viewSchedule() {
            console.log("Refreshing slots view");
            
            const viewStatus = document.getElementById('viewStatus');
            if (viewStatus) viewStatus.style.display = 'block';
            
            const resultDiv = document.getElementById('scheduleResult');
            if (resultDiv) resultDiv.style.display = 'none';
            
            fetch('http://localhost:8080/schedule/slots', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                console.log(`[GET schedule] Status: ${response.status}, ${response.statusText}`);
                if (!response.ok) {
                    throw new Error(`Server returned ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(response => {
                console.log("Schedule data:", response);
                
                try {
                    updateSchedulerStatus();
                } catch (e) {
                    console.error("Error updating scheduler status:", e);
                }
                
                try {
                    refreshSlotDropdown();
                } catch (e) {
                    console.error("Error refreshing dropdown:", e);
                }
                
                const resultDiv = document.getElementById('scheduleResult');
                if (!resultDiv) {
                    console.error("Schedule result div not found");
                    return;
                }
                
                if (!response.slots || response.slots.length === 0) {
                    resultDiv.innerHTML = '<p class="text-white">No schedule slots configured</p>';
                    resultDiv.style.display = 'block';
                    return;
                }
                
                let html = `<h6 class="text-white">Scheduler Running: ${response.scheduler_running ? 'Yes' : 'No'}</h6>`;
                html += `<p class="text-white small">Next Run: ${response.next_scheduled_run || 'Not scheduled'}</p>`;
                html += `<hr class="bg-white">`;
                
                response.slots.forEach((slot, index) => {
                    html += `<div class="time-slot-row">`;
                    html += `<h6 class="text-white">${slot.slot_id} (${slot.start_time})</h6>`;
                    
                    const daysMap = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
                    const days = slot.days_of_week.map(d => daysMap[d]).join(", ");
                    html += `<p class="text-white small mb-1">Days: ${days}</p>`;
                    html += `<p class="text-white small mb-1">Action: ${slot.action || 'start'}</p>`;
                    html += `<p class="text-white small mb-1">Run Once: ${slot.run_once ? 'Yes' : 'No'}</p>`;
                    
                    if (slot.has_run) {
                        html += `<p class="text-white small mb-1 text-warning">Status: Has Run</p>`;
                    }
                    
                    if (slot.message_accounts && slot.message_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">Message: ${slot.message_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.image_accounts && slot.image_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">Image: ${slot.image_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.delete_accounts && slot.delete_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">Delete: ${slot.delete_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.bio_accounts && slot.bio_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">Bio: ${slot.bio_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.scanning_accounts && slot.scanning_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">Scanning: ${slot.scanning_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.general_scanning_accounts && slot.general_scanning_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">General Scanning: ${slot.general_scanning_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.message_generator_accounts && slot.message_generator_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">Msg Gen: ${slot.message_generator_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.general_message_accounts && slot.general_message_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">General Msg: ${slot.general_message_accounts.join(', ')}</p>`;
                    }
                    
                    if (slot.first_message_accounts && slot.first_message_accounts.length > 0) {
                        html += `<p class="text-white small mb-1">First Msg: ${slot.first_message_accounts.join(', ')}</p>`;
                    }
                    
                    html += `</div>`;
                });
                
                resultDiv.innerHTML = html;
                resultDiv.style.display = 'block';
            })
            .catch(error => {
                console.error("Error viewing schedule:", error);
                
                const resultDiv = document.getElementById('scheduleResult');
                if (resultDiv) {
                    resultDiv.innerHTML = `<p class="text-white">Error: ${error.message}</p>`;
                    resultDiv.style.display = 'block';
                }
                
                showNotification(`Error fetching schedule: ${error.message}`, true);
            })
            .finally(() => {
                const viewStatus = document.getElementById('viewStatus');
                if (viewStatus) viewStatus.style.display = 'none';
            });
        }
        
        // ===== SCHEDULER CONTROL FUNCTIONS =====
        function updateStartStatus(show, text = "Processing...") {
            const startButton = document.querySelector("#startStatus")?.closest('.card')?.querySelector(".btn-success");
            const stopButton = document.querySelector("#startStatus")?.closest('.card')?.querySelector(".btn-danger");
            if (startButton) startButton.disabled = show;
            if (stopButton) stopButton.disabled = show;
            
            const statusDiv = document.getElementById('startStatus');
            if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
            
            const statusText = document.getElementById('startStatusText');
            if (statusText) statusText.textContent = text;
        }
        
        function startScheduler() {
            console.log("Starting scheduler");
            updateStartStatus(true, "Starting scheduler...");
            
            fetch('http://localhost:8080/schedule/slots/start', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) throw new Error('Network response was not ok');
                return response.json();
            })
            .then(response => {
                showNotification(`Scheduler started successfully! ${response.message}`);
                updateSchedulerStatus();
                refreshSlotDropdown();
            })
            .catch(error => {
                showNotification(`Error: ${error.message}`, true);
            })
            .finally(() => {
                updateStartStatus(false);
            });
        }
        
        function stopScheduler() {
    console.log("Stopping scheduler");
    updateStartStatus(true, "Stopping scheduler...");
    
    fetch('http://localhost:8080/schedule/slots/stop', {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
    })
    .then(response => {
        showNotification(`Scheduler stopped successfully!`);
        updateSchedulerStatus();
    })
    .catch(error => {
        showNotification(`Error: ${error.message}`, true);
    })
    .finally(() => {
        updateStartStatus(false);
    });
}

function updateSchedulerStatus() {
    fetch('http://localhost:8080/schedule/slots', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
    })
    .then(response => {
        const statusDiv = document.getElementById('schedulerStatus');
        if (!statusDiv) return;
        
        let html = `<p class="text-white mb-0">Current Status: <strong>${response.scheduler_running ? 'RUNNING' : 'STOPPED'}</strong></p>`;
        
        if (response.next_scheduled_run) {
            html += `<p class="text-white mb-0 small">Next Run: ${response.next_scheduled_run}</p>`;
        }
        
        statusDiv.innerHTML = html;
    })
    .catch(error => {
        const statusDiv = document.getElementById('schedulerStatus');
        if (statusDiv) {
            statusDiv.innerHTML = '<p class="text-white">Error checking status</p>';
        }
    });
}

// ===== RUN SLOT NOW FUNCTIONS =====
function updateRunSlotStatus(show) {
    const button = document.querySelector("#slotToRun")?.closest('.card')?.querySelector(".btn-primary");
    if (button) button.disabled = show;
    
    const statusDiv = document.getElementById('runSlotStatus');
    if (statusDiv) statusDiv.style.display = show ? 'block' : 'none';
}

function runSlotNow() {
    const slotSelect = document.getElementById('slotToRun');
    if (!slotSelect) return;
    
    const slotId = slotSelect.value;
    
    if (!slotId) {
        showNotification("Please select a slot to run", true);
        return;
    }
    
    updateRunSlotStatus(true);
    
    fetch(`http://localhost:8080/schedule/slots/run-now/${slotId}`, {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
    })
    .then(response => {
        if (response.tasks && response.tasks.length > 0) {
            response.tasks.forEach(task => {
                showNotification(`Started ${task.type} task for slot ${slotId} with ${task.accounts.length} accounts`);
            });
        } else {
            showNotification(`Started slot ${slotId} successfully`);
        }
    })
    .catch(error => {
        showNotification(`Error: ${error.message}`, true);
    })
    .finally(() => {
        updateRunSlotStatus(false);
    });
}

function refreshSlotDropdown() {
    console.log("Refreshing slot dropdown");
    
    fetch('http://localhost:8080/schedule/slots', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log(`[GET slots for dropdown] Status: ${response.status}, ${response.statusText}`);
        if (!response.ok) {
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(response => {
        console.log("Slot data for dropdown:", response);
        
        const slotSelect = document.getElementById('slotToRun');
        if (!slotSelect) {
            console.error("Slot select element not found");
            return;
        }
        
        while (slotSelect.options.length > 1) {
            slotSelect.remove(1);
        }
        
        if (response.slots && response.slots.length > 0) {
            response.slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.slot_id;
                
                const runOnceText = slot.run_once ? " [Run Once]" : "";
                option.text = `${slot.slot_id} (${slot.start_time}) - ${slot.action || 'start'}${runOnceText}`;
                
                slotSelect.add(option);
            });
            console.log(`Added ${response.slots.length} options to dropdown`);
        } else {
            console.log("No slots available for dropdown");
        }
    })
    .catch(error => {
        console.error("Error refreshing slot dropdown:", error);
    });
}

function cleanupExecutedSlots() {
    console.log("Cleaning up executed one-time slots");
    
    fetch('http://localhost:8080/schedule/cleanup', {
        method: 'POST',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => {
        if (!response.ok) throw new Error('Network response was not ok');
        return response.json();
    })
    .then(response => {
        showNotification(`Cleaned up ${response.removed_slots || 0} executed slots`);
        viewSchedule();
    })
    .catch(error => {
        showNotification(`Error: ${error.message}`, true);
    });
}


// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM content loaded");
    
    try {
        // Set up the custom prompt button click handler
        const customPromptBtn = document.getElementById('customPromptBtn');
        if (customPromptBtn) {
            customPromptBtn.addEventListener('click', function() {
                console.log("Custom prompt button clicked");
                openPromptEditor();
            });
        } else {
            console.error("Custom prompt button not found");
        }
        
        // Set up modal event handlers
        const promptAccountSelect = document.getElementById('promptAccountSelect');
        if (promptAccountSelect) {
            promptAccountSelect.addEventListener('change', loadPromptForAccount);
        }
        
        const closePromptModal = document.getElementById('closePromptModal');
        if (closePromptModal) {
            closePromptModal.addEventListener('click', closePromptEditor);
        }
        
        const closePromptModalBtn = document.getElementById('closePromptModalBtn');
        if (closePromptModalBtn) {
            closePromptModalBtn.addEventListener('click', closePromptEditor);
        }
        
        const resetPromptBtn = document.getElementById('resetPromptBtn');
        if (resetPromptBtn) {
            resetPromptBtn.addEventListener('click', resetToDefaultPrompt);
        }
        
        const savePromptBtn = document.getElementById('savePromptBtn');
        if (savePromptBtn) {
            savePromptBtn.addEventListener('click', saveCustomPrompt);
        }
        
        // Set up CSV upload functionality
        setupCsvUpload();
        
        // Refresh slot dropdown
        refreshSlotDropdown();
        
        // Check scheduler status
        updateSchedulerStatus();
        
        // Add initial slot for schedule builder
        setTimeout(addNewSlot, 500);
    } catch (e) {
        console.error("Error during initialization:", e);
    }
});

function toggleAutoSplitButton(button) {
    const slotItem = button.closest('.schedule-slot-item');
    const autoSplitSettings = slotItem.querySelector('.auto-split-settings');
    
    button.classList.toggle('active');
    
    if (button.classList.contains('active')) {
        autoSplitSettings.classList.add('active');
        button.textContent = 'Auto-Split ON';
    } else {
        autoSplitSettings.classList.remove('active');
        button.textContent = 'Auto-Split OFF';
    }
}

// Function to apply auto-split
function applyAutoSplit(button) {
    const slotItem = button.closest('.schedule-slot-item');
    const baseSlotId = slotItem.querySelector('.slot-id').value || 'slot';
    const baseTime = slotItem.querySelector('.slot-time').value;
    const action = slotItem.querySelector('.slot-action').value;
    const timeGap = parseInt(slotItem.querySelector('.time-gap-input').value) || 60;
    
    // Get selected days
    const selectedDays = [];
    slotItem.querySelectorAll('.day-toggle.active').forEach(toggle => {
        selectedDays.push(parseInt(toggle.dataset.day));
    });
    
    // Get all account inputs
    const accountInputs = [
        { type: 'message', input: slotItem.querySelector('.message-accounts') },
        { type: 'image', input: slotItem.querySelector('.image-accounts') },
        { type: 'delete', input: slotItem.querySelector('.delete-accounts') },
        { type: 'bio', input: slotItem.querySelector('.bio-accounts') },
        { type: 'scanning', input: slotItem.querySelector('.scanning-accounts') },
        { type: 'general-scanning', input: slotItem.querySelector('.general-scanning-accounts') },
        { type: 'message-generator', input: slotItem.querySelector('.message-generator-accounts') },
        { type: 'general-message', input: slotItem.querySelector('.general-message-accounts') },
        { type: 'first-message', input: slotItem.querySelector('.first-message-accounts') }
    ];
    
    // Find which account type has values
    let accountsToSplit = [];
    let accountType = '';
    
    for (let accountInput of accountInputs) {
        if (accountInput.input && accountInput.input.value.trim()) {
            const accounts = accountInput.input.value.split(',').map(id => id.trim()).filter(id => id);
            if (accounts.length > 0) {
                accountsToSplit = accounts;
                accountType = accountInput.type;
                break;
            }
        }
    }
    
    if (accountsToSplit.length === 0) {
        showNotification("Please enter accounts before applying auto-split", true);
        return;
    }
    
    // Calculate how many slots we need
    const accountsPerSlot = 10;
    const numberOfSlots = Math.ceil(accountsToSplit.length / accountsPerSlot);
    
    if (numberOfSlots === 1) {
        showNotification("Less than 10 accounts - no need to split", true);
        return;
    }
    
    // Remove the current slot
    slotItem.remove();
    
    // Create new slots
    for (let i = 0; i < numberOfSlots; i++) {
        // Calculate accounts for this slot
        const startIdx = i * accountsPerSlot;
        const endIdx = Math.min(startIdx + accountsPerSlot, accountsToSplit.length);
        const slotAccounts = accountsToSplit.slice(startIdx, endIdx);
        
        // Calculate time for this slot
        const [hours, minutes] = baseTime.split(':').map(Number);
        const totalMinutes = hours * 60 + minutes + (i * timeGap);
        const newHours = Math.floor(totalMinutes / 60) % 24;
        const newMinutes = totalMinutes % 60;
        const newTime = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
        
        // Add new slot
        addNewSlot();
        
        // Get the newly added slot (it's the last one)
        const newSlot = document.querySelector('#slotContainer .schedule-slot-item:last-child');
        
        // Set slot properties
        newSlot.querySelector('.slot-id').value = `${baseSlotId}_${i + 1}`;
        newSlot.querySelector('.slot-time').value = newTime;
        newSlot.querySelector('.slot-action').value = action;
        
        // Set days
        newSlot.querySelectorAll('.day-toggle').forEach((toggle, idx) => {
            if (selectedDays.includes(idx)) {
                toggle.classList.add('active');
            } else {
                toggle.classList.remove('active');
            }
        });
        
        // Set accounts based on type
        const accountTypeInput = newSlot.querySelector(`.${accountType}-accounts`);
        if (accountTypeInput) {
            accountTypeInput.value = slotAccounts.join(',');
            
            // Show the correct account input group
            const dropdown = newSlot.querySelector('.account-type-dropdown');
            dropdown.value = accountType;
            toggleAccountInput(dropdown);
        }
        
        // Update preview
        updateSlotPreview(newSlot);
    }
    
    showNotification(`Created ${numberOfSlots} slots with ${accountsPerSlot} accounts each (1 hour gaps)`, false);
    updateJsonPreview();
}

// Modified addNewSlot function to include auto-split feature
function addNewSlotWithAutoSplit() {
    console.log("Adding new slot with auto-split feature");
    slotCounter++;
    const template = document.getElementById('slotTemplate');
    const slotContainer = document.getElementById('slotContainer');
    
    if (!template || !slotContainer) {
        console.error("Could not find template or container element");
        return;
    }
    
    const clone = document.importNode(template.content, true);
    
    const slotIdInput = clone.querySelector('.slot-id');
    if (slotIdInput) slotIdInput.value = 'slot' + slotCounter;
    
    const dayToggles = clone.querySelectorAll('.day-toggle');
    dayToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            this.classList.toggle('active');
            updateJsonPreview();
        });
    });
    
    const inputs = clone.querySelectorAll('input, select');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            updateSlotPreview(this.closest('.schedule-slot-item'));
        });
        input.addEventListener('input', function() {
            updateSlotPreview(this.closest('.schedule-slot-item'));
        });
    });
    
    slotContainer.appendChild(clone);
    updateJsonPreview();
}

// Replace the existing addNewSlot function with this one
addNewSlot = addNewSlotWithAutoSplit;


</script>

</body>
</html>
{% endblock content %}