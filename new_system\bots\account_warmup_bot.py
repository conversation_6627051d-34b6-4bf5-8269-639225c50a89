#!/usr/bin/env python3
"""
Account Warmup <PERSON>t - Implements human-like activities for Instagram accounts
Uses instagrapi to perform natural browsing, liking, commenting, and following
"""

import os
import time
import random
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager

# Configure logging
logger = logging.getLogger(__name__)

class AccountWarmupBot:
    """Instagram Account Warmup Bot using instagrapi"""
    
    def __init__(self, account_id: int, instagram_manager: InstagramManager, db_manager: DatabaseManager):
        self.account_id = account_id
        self.instagram_manager = instagram_manager
        self.db_manager = db_manager
        
        # Warmup settings
        self.max_actions_per_session = 50
        self.min_delay = 2
        self.max_delay = 8
        self.session_duration = 30  # minutes
        
        # Action probabilities (realistic human behavior)
        self.action_weights = {
            'browse_feed': 0.4,      # 40% - browsing
            'like_post': 0.3,        # 30% - liking
            'follow_user': 0.15,     # 15% - following
            'comment_post': 0.1,     # 10% - commenting
            'browse_explore': 0.05   # 5% - explore
        }
        
        # Safe hashtags for discovery
        self.safe_hashtags = [
            'photography', 'travel', 'food', 'fitness', 'fashion',
            'art', 'nature', 'music', 'technology', 'business'
        ]
        
        # Comment templates
        self.comment_templates = [
            "Great post! 👍",
            "Love this! ❤️",
            "Amazing content! 🔥",
            "Beautiful! ✨",
            "Thanks for sharing! 🙏",
            "Inspiring! 💫",
            "Awesome! 😍",
            "Keep it up! 💪"
        ]
    
    def run_warmup_session(self) -> Dict[str, Any]:
        """Run a complete warmup session"""
        try:
            logger.info(f"Starting warmup session for account {self.account_id}")
            
            # Setup Instagram client
            if not self.setup_instagram_client():
                return {'success': False, 'error': 'Failed to setup Instagram client'}
            
            # Initialize session
            session_start = datetime.now()
            actions_performed = 0
            session_stats = {
                'browse_feed': 0,
                'like_post': 0,
                'follow_user': 0,
                'comment_post': 0,
                'browse_explore': 0
            }
            
            # Run warmup loop
            while actions_performed < self.max_actions_per_session:
                # Check session duration
                if (datetime.now() - session_start).total_seconds() / 60 >= self.session_duration:
                    logger.info("Session duration reached, ending warmup")
                    break
                
                # Choose action based on weights
                action = self.choose_action()
                
                # Perform action
                success = self.perform_action(action)
                if success:
                    session_stats[action] += 1
                    actions_performed += 1
                    
                    # Log progress
                    logger.info(f"Action {actions_performed}/{self.max_actions_per_session}: {action}")
                
                # Random delay between actions
                delay = random.uniform(self.min_delay, self.max_delay)
                time.sleep(delay)
            
            # Session complete
            logger.info(f"Warmup session completed. Actions: {session_stats}")
            return {
                'success': True,
                'actions_performed': actions_performed,
                'session_stats': session_stats,
                'session_duration': (datetime.now() - session_start).total_seconds() / 60
            }
            
        except Exception as e:
            logger.error(f"Error during warmup session: {e}")
            return {'success': False, 'error': str(e)}
    
    def setup_instagram_client(self) -> bool:
        """Setup Instagram client for warmup"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account or not account.is_logged_in():
                logger.error(f"Account {self.account_id} not logged in")
                return False
            
            logger.info(f"Instagram client ready for account {self.account_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup Instagram client: {e}")
            return False
    
    def choose_action(self) -> str:
        """Choose next action based on weights"""
        actions = list(self.action_weights.keys())
        weights = list(self.action_weights.values())
        return random.choices(actions, weights=weights)[0]
    
    def perform_action(self, action: str) -> bool:
        """Perform a specific warmup action"""
        try:
            if action == 'browse_feed':
                return self.browse_feed()
            elif action == 'like_post':
                return self.like_random_post()
            elif action == 'follow_user':
                return self.follow_random_user()
            elif action == 'comment_post':
                return self.comment_random_post()
            elif action == 'browse_explore':
                return self.browse_explore()
            else:
                logger.warning(f"Unknown action: {action}")
                return False
                
        except Exception as e:
            logger.error(f"Error performing action {action}: {e}")
            return False
    
    def browse_feed(self) -> bool:
        """Browse timeline feed"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account:
                return False
            
            # Get timeline feed
            feed = account.client.feed_timeline()
            
            # Browse through some posts (don't interact, just view)
            posts_to_browse = min(5, len(feed))
            for i in range(posts_to_browse):
                post = feed[i]
                logger.debug(f"Browsing post: {post.pk}")
                time.sleep(random.uniform(1, 3))  # Simulate reading time
            
            logger.info(f"Browsed {posts_to_browse} posts from feed")
            return True
            
        except Exception as e:
            logger.error(f"Error browsing feed: {e}")
            return False
    
    def like_random_post(self) -> bool:
        """Like a random post from feed"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account:
                return False
            
            # Get timeline feed
            feed = account.client.feed_timeline()
            if not feed:
                return False
            
            # Choose random post
            post = random.choice(feed[:10])  # From first 10 posts
            
            # Check if already liked
            if not post.has_liked:
                # Like the post
                account.client.media_like(post.pk)
                logger.info(f"Liked post: {post.pk}")
                return True
            else:
                logger.debug(f"Post {post.pk} already liked")
                return False
                
        except Exception as e:
            logger.error(f"Error liking post: {e}")
            return False
    
    def follow_random_user(self) -> bool:
        """Follow a random user from suggestions"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account:
                return False
            
            # Get suggested users
            suggestions = account.client.suggested_users()
            if not suggestions:
                return False
            
            # Choose random user
            user = random.choice(suggestions[:5])  # From first 5 suggestions
            
            # Check if already following
            if not user.following:
                # Follow the user
                account.client.user_follow(user.pk)
                logger.info(f"Followed user: {user.username}")
                return True
            else:
                logger.debug(f"Already following {user.username}")
                return False
                
        except Exception as e:
            logger.error(f"Error following user: {e}")
            return False
    
    def comment_random_post(self) -> bool:
        """Comment on a random post"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account:
                return False
            
            # Get timeline feed
            feed = account.client.feed_timeline()
            if not feed:
                return False
            
            # Choose random post
            post = random.choice(feed[:5])  # From first 5 posts
            
            # Choose random comment
            comment = random.choice(self.comment_templates)
            
            # Post comment
            account.client.media_comment(post.pk, comment)
            logger.info(f"Commented on post {post.pk}: {comment}")
            return True
            
        except Exception as e:
            logger.error(f"Error commenting on post: {e}")
            return False
    
    def browse_explore(self) -> bool:
        """Browse explore page"""
        try:
            account = self.instagram_manager.get_account(self.account_id)
            if not account:
                return False
            
            # Get explore feed
            explore_feed = account.client.explore_feed()
            
            # Browse through some posts
            posts_to_browse = min(3, len(explore_feed))
            for i in range(posts_to_browse):
                post = explore_feed[i]
                logger.debug(f"Browsing explore post: {post.pk}")
                time.sleep(random.uniform(1, 2))
            
            logger.info(f"Browsed {posts_to_browse} posts from explore")
            return True
            
        except Exception as e:
            logger.error(f"Error browsing explore: {e}")
            return False
    
    def get_warmup_stats(self) -> Dict[str, Any]:
        """Get warmup statistics for the account"""
        try:
            # This would typically query a database table for warmup stats
            # For now, return basic info
            return {
                'account_id': self.account_id,
                'last_warmup': datetime.now().isoformat(),
                'total_sessions': 1,
                'total_actions': self.max_actions_per_session
            }
            
        except Exception as e:
            logger.error(f"Error getting warmup stats: {e}")
            return {}

def create_warmup_bot(account_id: int) -> AccountWarmupBot:
    """Factory function to create warmup bot"""
    from core.instagram_manager import InstagramManager
    from core.database import DatabaseManager
    
    instagram_manager = InstagramManager()
    db_manager = DatabaseManager()
    
    return AccountWarmupBot(account_id, instagram_manager, db_manager)
