"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7106],{56065:function(e,t,n){n.d(t,{e:function(){return f}});var s=n(27573),o=n(8571),a=n(70354),r=n(14448),i=n(45144),c=n(10607),l=n(8927),u=n(68425),d=n(7653),p=n(40950),h=n(45790),m=n(93558),g=n(37584),b=n(17796);let f=(0,d.forwardRef)((e,t)=>{let{content:n,className:f,type:y,onSandboxConfirmation:z,capabilities:x,onReportError:C,onSetContent:R,onRefresh:S,lazy:q=!0}=e,w=(0,p.Z)(),{userContentRendererUrl:E}=(0,o.m)(),[v,j]=(0,d.useState)(!1),M=(0,d.useRef)(null),{value:P}=(0,r.F)("apps_use_turmeric"),[T,k]=(0,d.useState)(0),I=e=>e>=100,{sendRequest:O,updatePermission:F}=function(e){let{iframeRef:t,allowedOrigin:n,onRateLimited:s=null,initialPermissions:o,onPermissionRequested:a,onCapabilityAction:r}=e,[i,c]=(0,d.useState)(null),l=(0,d.useRef)(o),[u,p]=(0,d.useState)(0);(0,d.useEffect)(()=>{if(!t.current&&u<20){let e=setTimeout(()=>{p(e=>e+1)},500);return()=>clearTimeout(e)}if(!t.current){console.warn("Max retries reached while waiting for iframe");return}let e=new b.i({iframe:t.current,allowedOrigin:n,onRateLimited:s,onPermissionRequested:async e=>{if(m.TD.includes(e)||"accepted"===l.current[e])return"accepted";let t=await a(e);return l.current={...l.current,[e]:t},t},onCapabilityAction:r});return c(e),()=>{e.cleanup()}},[t,n,l,s,a,r,u]);let h=(0,d.useCallback)(async(e,t)=>{if(i)return i.sendRequest(e,t);throw Error("Communicator not initialized")},[i]),g=(0,d.useCallback)(()=>{null==i||i.restartListening()},[i]),f=(0,d.useCallback)((e,t)=>{l.current={...l.current,[e]:t}},[]);return{sendRequest:null===i?void 0:h,restartListening:g,permissions:l.current,updatePermission:f}}({iframeRef:M,allowedOrigin:E,onCapabilityAction:(0,d.useCallback)(async e=>e.method===m.to.ReadyForContent?(j(!0),null==z||z(),Promise.resolve()):e.method===m.to.GetFile?x&&x.getFile?Promise.resolve({"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.GetFileResponse",value:await x.getFile(e.payload.key)}):Promise.reject():e.method===m.to.SendConversationMessage?x&&x.sendMessage?(await x.sendMessage({prompt:e.payload.message,attachments:[],files:[],syncSources:[]}),Promise.resolve()):Promise.reject():e.method===m.to.ClaudeCompletion?x&&x.claudeComplete?(k(e=>e+1),Promise.resolve({"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionResponse",completion:await x.claudeComplete(e.payload.prompt)})):Promise.reject():e.method===m.to.BroadcastContentSize?Promise.resolve():e.method===m.to.ReportError?(null==C||C(e.payload.error),Promise.resolve()):Promise.reject(),[z,x,j,C,k]),onPermissionRequested:(0,d.useCallback)(async e=>e===m.to.ClaudeCompletion&&I(T)?"denied":Promise.resolve("accepted"),[T]),initialPermissions:{}});(0,d.useEffect)(()=>{I(T)&&(F(m.to.ClaudeCompletion,"denied"),(0,i.uT)("Claude completion request limit hit."))},[T,F]);let L=(0,d.useCallback)(()=>{(0,i.uT)("Claude completion modal confirmed"),k(0),F(m.to.ClaudeCompletion,"accepted")},[F]),A=(0,d.useCallback)(async e=>{let t=await (null==O?void 0:O(m.to.GetScreenshot,g.D7));if(!(null==t?void 0:t.screenshot))return;if(t.screenshot.length>10485760)throw Error(w.formatMessage({defaultMessage:"Screenshot too large",id:"ifBuzEbIz2"}));if(!/^data:image\/(png|jpeg|gif);base64,/.test(t.screenshot))throw Error(w.formatMessage({defaultMessage:"Invalid screenshot format",id:"KCmGxpLNuq"}));let n=e.title.replace(/[^a-zA-Z0-9-_]/g,"_").substring(0,50)+".png";Object.assign(document.createElement("a"),{href:t.screenshot,download:n}).click()},[w,O]);return(0,d.useEffect)(()=>{v&&n&&y&&(null==R||R(),null==O||O(m.to.SetContent,{content:n,type:y,"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.SandboxContent"}))},[n,O,y,v,R]),(0,d.useImperativeHandle)(t,()=>({refresh:()=>{null==S||S(),j(!1);let e=M.current;e&&(e.src="".concat(E).concat(C?"?errorReportingMode=parent":""))},downloadScreenshot:A})),(0,s.jsxs)("div",{className:(0,c.Z)("relative",f),children:[(0,s.jsx)("iframe",{ref:M,className:"h-full w-full",sandbox:"allow-scripts allow-same-origin",title:w.formatMessage({defaultMessage:"Claude content",id:"/Ma3teJf9/"}),loading:q?"lazy":void 0,src:"".concat(E).concat(C?"?errorReportingMode=parent":""),allow:"fullscreen"}),(0,s.jsx)(l.M,{children:!v&&(0,s.jsx)(u.E.div,{className:"bg-bg-100 absolute inset-0 z-10",initial:{opacity:1},animate:{opacity:1},exit:{opacity:0},transition:{duration:.15,ease:"easeInOut"}})}),P&&I(T)&&(0,s.jsx)("div",{className:"fixed inset-0 bg-always-black bg-opacity-50 flex items-center justify-center z-modal",children:(0,s.jsxs)("div",{className:"bg-bg-100 p-4 rounded shadow-lg",children:[(0,s.jsx)("p",{className:"text-fg-100 mb-4",children:(0,s.jsx)(h.Z,{defaultMessage:"Many requests made. Acknowledge that you’re still here to continue.",id:"kmh/1AmVcl"})}),(0,s.jsx)(a.z,{onClick:L,variant:"primary",children:(0,s.jsx)(h.Z,{defaultMessage:"I’m still here",id:"NwhjMfjigc"})})]})})]})});f.displayName="RichSandbox"},93558:function(e,t,n){n.d(t,{KX:function(){return h},O3:function(){return y},P2:function(){return b},TD:function(){return z},q2:function(){return f},to:function(){return o},vr:function(){return m}});var s,o,a=n(82083),r=n(41270),i=n(37584);(s=o||(o={})).ReadyForContent="anthropic.claude.usercontent.sandbox.ReadyForContent",s.SetContent="anthropic.claude.usercontent.sandbox.SetContent",s.GetFile="anthropic.claude.usercontent.sandbox.GetFile",s.SendConversationMessage="anthropic.claude.usercontent.sandbox.SendConversationMessage",s.RunCode="anthropic.claude.usercontent.sandbox.RunCode",s.ClaudeCompletion="anthropic.claude.usercontent.sandbox.ClaudeCompletion",s.ReportError="anthropic.claude.usercontent.sandbox.ReportError",s.GetScreenshot="anthropic.claude.usercontent.sandbox.GetScreenshot",s.BroadcastContentSize="anthropic.claude.usercontent.sandbox.BroadcastContentSize";let c=r.z.object({type:r.z.literal("UnsupportedImports"),unsupportedModules:r.z.array(r.z.string()),nonExistentIcons:r.z.array(r.z.string())}),l=r.z.object({type:r.z.literal("RuntimeError"),message:r.z.string()}),u=r.z.object({type:r.z.literal("FileNotFound"),fileName:r.z.string()}),d=r.z.object({type:r.z.literal("ClaudeCompletionError"),message:r.z.string()}),p=r.z.discriminatedUnion("type",[c,l,u,d]),h=r.z.object({code:r.z.string()}),m=r.z.object({status:r.z.enum(["success","error"]),result:r.z.string().optional(),logs:r.z.array(r.z.string()),error:r.z.string().optional()}),g={"anthropic.claude.usercontent.sandbox.SetContent":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.SetContent"),payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.SandboxContent"),content:r.z.string(),type:r.z.nativeEnum(a.JP),watchContentSize:r.z.boolean().optional()})}),responseSchema:i.AY,alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.ReadyForContent":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.ReadyForContent"),payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/google.protobuf.Empty")})}),responseSchema:i.AY,alwaysPermitted:!0},"anthropic.claude.usercontent.sandbox.BroadcastContentSize":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.BroadcastContentSize"),payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.BroadcastContentSizePayload"),height:r.z.number(),width:r.z.number()})}),responseSchema:i.AY,alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.GetFile":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.GetFile"),payload:r.z.strictObject({key:r.z.string(),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.GetFileRequest")})}),responseSchema:i.Tk.extend({payload:r.z.strictObject({value:r.z.instanceof(Uint8Array).nullable(),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.GetFileResponse")})}),alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.SendConversationMessage":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.SendConversationMessage"),payload:r.z.strictObject({message:r.z.string(),messageType:r.z.enum(["text","error"]),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.SendConversationMessageRequest")})}),responseSchema:i.AY,alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.RunCode":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.RunCode"),payload:r.z.strictObject({code:r.z.string(),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.RunCodeRequest")})}),responseSchema:i.Tk.extend({payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.RunCodeResponse")}).merge(m)}),alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.ClaudeCompletion":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.ClaudeCompletion"),payload:r.z.strictObject({prompt:r.z.string(),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionRequest")})}),responseSchema:i.Tk.extend({payload:r.z.strictObject({completion:r.z.string().nullable(),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionResponse")})}),alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.ReportError":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.ReportError"),payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.ReportErrorRequest"),error:p})}),responseSchema:i.AY,alwaysPermitted:!0},"anthropic.claude.usercontent.sandbox.GetScreenshot":{requestSchema:i.Zq.extend({method:r.z.literal("anthropic.claude.usercontent.sandbox.GetScreenshot"),payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/google.protobuf.Empty")})}),responseSchema:i.Tk.extend({payload:r.z.strictObject({screenshot:r.z.string().nullable(),"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.GetScreenshotResponse")})}),alwaysPermitted:!1}},b=r.z.enum(["anthropic.claude.usercontent.sandbox.ReadyForContent","anthropic.claude.usercontent.sandbox.GetFile","anthropic.claude.usercontent.sandbox.SendConversationMessage","anthropic.claude.usercontent.sandbox.ClaudeCompletion","anthropic.claude.usercontent.sandbox.ReportError","anthropic.claude.usercontent.sandbox.BroadcastContentSize"]),f=r.z.discriminatedUnion("method",[g["anthropic.claude.usercontent.sandbox.ReadyForContent"].requestSchema,g["anthropic.claude.usercontent.sandbox.GetFile"].requestSchema,g["anthropic.claude.usercontent.sandbox.SendConversationMessage"].requestSchema,g["anthropic.claude.usercontent.sandbox.ClaudeCompletion"].requestSchema,g["anthropic.claude.usercontent.sandbox.ReportError"].requestSchema,g["anthropic.claude.usercontent.sandbox.BroadcastContentSize"].requestSchema]);r.z.union([i.AY,g["anthropic.claude.usercontent.sandbox.GetFile"].responseSchema,g["anthropic.claude.usercontent.sandbox.SendConversationMessage"].responseSchema,g["anthropic.claude.usercontent.sandbox.ClaudeCompletion"].responseSchema]),r.z.enum(["anthropic.claude.usercontent.sandbox.SetContent","anthropic.claude.usercontent.sandbox.RunCode","anthropic.claude.usercontent.sandbox.GetScreenshot"]),r.z.discriminatedUnion("method",[g["anthropic.claude.usercontent.sandbox.SetContent"].requestSchema,g["anthropic.claude.usercontent.sandbox.RunCode"].requestSchema,g["anthropic.claude.usercontent.sandbox.GetScreenshot"].requestSchema]);let y=r.z.union([g["anthropic.claude.usercontent.sandbox.SetContent"].responseSchema,g["anthropic.claude.usercontent.sandbox.RunCode"].responseSchema,g["anthropic.claude.usercontent.sandbox.GetScreenshot"].responseSchema]),z=Object.entries(g).filter(e=>{let[t,n]=e;return!0===n.alwaysPermitted}).map(e=>{let[t,n]=e;return t})},37584:function(e,t,n){n.d(t,{AY:function(){return c},D7:function(){return l},Kj:function(){return i},Tk:function(){return r},Zq:function(){return a},qV:function(){return o},v8:function(){return u}});var s=n(41270);let o=s.z.object({channel:s.z.enum(["request","response"]),requestId:s.z.string()}).passthrough(),a=o.strict().extend({channel:s.z.literal("request"),method:s.z.string(),payload:s.z.strictObject({"@type":s.z.string()})}),r=o.extend({channel:s.z.literal("response"),status:s.z.number().int().min(100).max(599),payload:s.z.strictObject({"@type":s.z.string()}).passthrough()}).passthrough(),i=r.extend({status:s.z.number().int().min(400).max(599),payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.ErrorResponse"),error:s.z.string()})}),c=r.extend({payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/google.protobuf.Empty")})}),l={"@type":"type.googleapis.com/google.protobuf.Empty"},u=e=>({"@type":"type.googleapis.com/anthropic.claude.usercontent.ErrorResponse",error:e})},17796:function(e,t,n){n.d(t,{i:function(){return a}});var s=n(93558),o=n(37584);class a{setupMessageListener(){window.addEventListener("message",this.boundHandleMessage,!1)}handleMessage(e){if(e.origin!==this.allowedOrigin||e.source!==this.iframe.contentWindow)return;let t=Date.now();if(t-this.lastResetTime>this.RESET_INTERVAL&&(this.messageCount=0,this.lastResetTime=t),this.messageCount++,this.messageCount>this.MAX_MESSAGES_PER_INTERVAL){window.removeEventListener("message",this.boundHandleMessage),this.onRateLimited&&this.onRateLimited();return}let n=o.qV.safeParse(e.data);if(!n.success){console.warn("Received message does not conform to basic message shape, ignoring");return}let s=n.data;if("response"===s.channel){this.handleResponse(s);return}this.messageQueue.push(s),this.isConsumerRunning||this.consumeMessages()}handleResponse(e){let t=this.inFlightRequests.get(e.requestId);if(void 0!==t){t(e);return}}async consumeMessages(){for(this.isConsumerRunning=!0;this.messageQueue.length>0;){let e=this.messageQueue.shift();if(!e)continue;let t=s.P2.safeParse(e.method);if(!t.success){this.sendErrorResponse(e.requestId,400,"Unknown method");continue}let n=t.data,a=s.q2.safeParse(e);if(!a.success){this.sendErrorResponse(e.requestId,400,"Invalid payload content");continue}let r=a.data;if(this.requestLog.has(r.requestId)){this.sendErrorResponse(r.requestId,400,"Request ID already processed");continue}this.requestLog.set(r.requestId,{message:r,requestTimestamp:new Date().getTime()}),await this.onPermissionRequested(n).then(e=>"denied"!==e||(this.sendErrorResponse(r.requestId,403,"Permission denied"),!1)).catch(e=>(console.error("Error checking permission for method ".concat(n,":"),e),this.sendErrorResponse(r.requestId,500,"Error checking permission"),!1))&&(this.onCapabilityAction(r,this.boundSendRequest).then(e=>{var t;let n=o.Tk.parse({channel:"response",status:200,requestId:r.requestId,payload:null!=e?e:o.D7}),s=this.requestLog.get(r.requestId);s&&this.requestLog.set(r.requestId,{...s,response:n,responseTimestamp:new Date().getTime()}),null===(t=this.iframe.contentWindow)||void 0===t||t.postMessage(n,this.allowedOrigin)}).catch(e=>{console.error("Error processing action for method ".concat(n,":"),e),this.sendErrorResponse(r.requestId,500,"Internal server error while processing action")}),await new Promise(e=>requestAnimationFrame(e)))}this.isConsumerRunning=!1}sendErrorResponse(e,t,n){var s;let a=o.Kj.parse({channel:"response",status:t,requestId:e,payload:(0,o.v8)(n)}),r=this.requestLog.get(e);r&&this.requestLog.set(e,{...r,response:a,responseTimestamp:new Date().getTime()}),null===(s=this.iframe.contentWindow)||void 0===s||s.postMessage(a,this.allowedOrigin)}restartListening(){window.removeEventListener("message",this.boundHandleMessage),this.messageCount=0,this.lastResetTime=Date.now(),this.setupMessageListener()}cleanup(){window.removeEventListener("message",this.boundHandleMessage),this.messageQueue=[],this.isConsumerRunning=!1,this.inFlightRequests.clear()}async sendRequest(e,t){return new Promise((n,a)=>{var r;let i=Date.now().toString();this.inFlightRequests.set(i,e=>{let t=o.Tk.safeParse(e);if(!t.success){a(Error("Invalid response format"));return}let r=t.data;if(r.status>=400){let e=o.Kj.safeParse(r);e.success?a(Error(e.data.payload.error)):a(Error("Error response (".concat(r.status,")")));return}let i=s.O3.safeParse(r);i.success?n(i.data.payload):a(Error("Invalid response payload for the method"))}),null===(r=this.iframe.contentWindow)||void 0===r||r.postMessage({channel:"request",method:e,requestId:i,payload:t},this.allowedOrigin)})}getRequestLog(){return Array.from(this.requestLog.entries()).map(e=>{let[t,n]=e;return{...n,requestId:t}}).sort((e,t)=>t.requestTimestamp-e.requestTimestamp)}constructor({iframe:e,allowedOrigin:t,onRateLimited:n=null,onPermissionRequested:s,onCapabilityAction:o}){this.messageCount=0,this.lastResetTime=Date.now(),this.MAX_MESSAGES_PER_INTERVAL=30,this.RESET_INTERVAL=5e3,this.onRateLimited=null,this.messageQueue=[],this.isConsumerRunning=!1,this.requestLog=new Map,this.inFlightRequests=new Map,this.iframe=e,this.allowedOrigin=t,this.onRateLimited=n,this.onPermissionRequested=s,this.onCapabilityAction=o,this.boundHandleMessage=this.handleMessage.bind(this),this.boundSendRequest=this.sendRequest.bind(this),this.setupMessageListener()}}},37199:function(e,t,n){n.d(t,{i:function(){return l},s:function(){return c}});var s=n(5362),o=n(53949),a=n(7653);function r(e,t){let n=new Map;return t.forEach(e=>{var t;let s=null!==(t=n.get(e.file_name))&&void 0!==t?t:[];s.push(e),n.set(e.file_name,s)}),n.get(e)}function i(e,t){let n=r(e,t);if(!n||0===n.length)return null;let s=n.filter(e=>e.file_kind===o.Y.Blob);return 0===s.length?null:s[s.length-1]}function c(){let e=function(){let e=(0,s.OJ)();return(0,a.useCallback)(async(t,n,s,o,a)=>{let r=i(t,n);if(!r&&o){let n=await e("/api/organizations/".concat(s,"/projects/").concat(o,"/docs")),a=(await n.json()).find(e=>e.file_name===t);if(a)return new TextEncoder().encode(a.content);let c=await e("/api/organizations/".concat(s,"/projects/").concat(o,"/files"));r=i(t,await c.json())}if(!r)return null;try{let e=a?"/api/organizations/".concat(s,"/shared_artifact/").concat(a,"/files/").concat(r.file_uuid,"/contents"):"/api/organizations/".concat(s,"/files/").concat(r.file_uuid,"/contents"),t=await fetch(e);if(!t.ok)return null;let n=await t.arrayBuffer();if(n.byteLength>2147483648-1)return null;return new Uint8Array(n)}catch(e){return null}},[e])}();return(0,a.useCallback)(async(t,n,s,o,a,i)=>o?function(e,t){let n=r(e,t);if(!n||0===n.length)return null;let s=n[n.length-1];if(s){let e=s.extracted_content;return new TextEncoder().encode(e)}return null}(t,n)||await e(t,s,o,a,i):null,[e])}function l(e,t,n,s,o){let r=c();return(0,a.useCallback)(async a=>r(a,e,t,n,s,o),[e,t,n,s,r,o])}}}]);