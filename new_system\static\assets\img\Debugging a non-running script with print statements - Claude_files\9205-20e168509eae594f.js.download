"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9205],{1804:function(e,t,n){n.d(t,{o:function(){return u}});var r=n(27573),a=n(8571),i=n(27218),s=n(88146),o=n(45790);let l=()=>{let e=(0,a.Z)(),{websiteBaseUrl:t}=(0,a.m)();return e?(0,r.jsx)(o.Z,{defaultMessage:"Or consider <link>upgrading to Claude Pro</link>.",id:"j0jOXOpUyS",values:{link:e=>(0,r.jsx)("a",{href:"/upgrade/pro",className:"font-bold underline",rel:"noreferrer",children:e})}}):(0,r.jsx)(o.Z,{defaultMessage:"Or <link>contact sales</link> if you would be interested in upgrading to a paid version of <PERSON>.",id:"3hk4yMCf5v",values:{link:e=>(0,r.jsx)(s.default,{href:"".concat(t,"/contact-sales"),className:"font-bold underline",children:e})}})};function u(){let e=(0,i.w7)(),t=(0,i.uh)();return e&&!t?(0,r.jsx)(l,{}):null}},99205:function(e,t,n){n.d(t,{hH:function(){return Y},wN:function(){return X},Qk:function(){return K},pV:function(){return F},lE:function(){return G}});var r,a,i,s,o=n(27573),l=n(96933),u=n(1804),c=n(3053),d=n(8571),h=n(27218),p=n(27895),m=n(66092),_=n(30947),f=n(6385),g=n(66894),k=n(14448),v=n(13262),x=n(51432),y=n(45144),w=n(77930),b=n(5068),C=n(13784),j=n(88146),S=n(81695),E=n(7653),M=n(45790),O=n(15992),L=n(8073),I=n(58884);let D=(e,t)=>t.reduce((t,n)=>t+e.blockSize(n),0),N=(e,t,n)=>{let{blockSize:r,sliceBlock:a}=e,i=[];if(n<=0)return[];let s=0;for(let e of t){if(s+r(e)>=n){i.push(a(e,n-s));break}i.push(e),s+=r(e)}return i},P=1e3/60;class T{_get_smoothed_completion(){var e,t,n;if(0===this.start)return[];let r=(Date.now()-this.start)/1e3,a=this.arrivals[this.arrivals.length-1][1]+(this.model_done?100:0),i=.9*r-(null!==(n=null===(e=(t=this.blockOperations).getBlockDeadlineOffset)||void 0===e?void 0:e.call(t))&&void 0!==n?n:.3),s=this.arrivals.filter(e=>e[0]<i).map(e=>e[1]),o=s[s.length-1],l=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.01;if(t===n)return t;let a=e(t),i=e(n);if(t>=n)throw Error("Lower x is greater than upper x");if(a>r)throw Error("Lower f is greater than zero");if(i<-r)throw Error("Upper f is less than zero");for(;a<-r;){let r=(t+n)/2,s=e(r);s<=0?(t=r,a=s):(n=r,i=s)}return t}(e=>{let t=(e-this.x)/(r-this.t),n=1/(r-this.t);return 2*this.gamma*n*(t-this.v)-1/(e-o)+1/(a-e)},o,a),u=(l-this.x)/(r-this.t);return this.v=this.alpha*this.v+(1-this.alpha)*u,this.smoothed_completion_is_unchanged=this.x>=this.totalCompletionLength,this.x=Math.max(l,this.x),this.t=r,this.smoother_done=this.model_done&&this.x>=this.totalCompletionLength,this.stats.push({t:r,x:l,v:u,min_chars:o,max_chars:a}),N(this.blockOperations,this.blocksList,this.x)}addBlock(e,t){this.blocksList.push(t)}updateBlock(e,t){this.blocksList[e]=t}getBlocks(){return this.blocksList}onMessage(e){switch(e.type){case"content_block_start":{let t=this.blockOperations.createBlockFromStartEvent(e);null!==t&&this.addBlock(e.index,t);break}case"content_block_delta":{let t=this.blocksList[e.index];if(!t)break;let n=this.blockOperations.applyDeltaEvent(e,t);this.updateBlock(e.index,n);break}case"content_block_stop":{let t=this.blocksList[e.index];if(!t)break;let n=this.blockOperations.stopBlockEvent(e,t);this.updateBlock(e.index,n);break}case"message_stop":this.model_done=!0}0===this.start&&(this.start=Date.now());let t=D(this.blockOperations,this.blocksList);if(this.totalCompletionLength=t,this.arrivals.push([(Date.now()-this.start)/1e3,t]),this.dont_smooth&&this.on_completion){let e=structuredClone(this.blocksList);this.on_completion(e)}}async task(e,t){for(;!this.smoother_done&&!t.aborted;){if(this.blocksList.length>0){let t=this._get_smoothed_completion();this.smoothed_completion_is_unchanged||e(t)}await new Promise(e=>setTimeout(e,P))}}constructor(e){this.alpha=.99,this.gamma=1e-5,this.v=100,this.x=0,this.t=0,this.arrivals=[[-9999,0]],this.start=0,this.totalCompletionLength=0,this.smoothed_completion_is_unchanged=!1,this.dont_smooth=!1,this.model_done=!1,this.smoother_done=!1,this.stats=[],this.blocksList=[],this.blockOperations=e}}(r=i||(i={}))[r.APPEND_MESSAGE_WITH_COMPLETION=1]="APPEND_MESSAGE_WITH_COMPLETION",r[r.RETRY_MESSAGE_WITH_COMPLETION=2]="RETRY_MESSAGE_WITH_COMPLETION",(a=s||(s={})).Ping="ping",a.Completion="completion",a.Error="error",a.ContentBlockDelta="content_block_delta",a.ContentBlockStart="content_block_start",a.ContentBlockStop="content_block_stop",a.MessageDelta="message_delta",a.MessageStart="message_start",a.MessageStop="message_stop",a.MessageLimit="message_limit";let B=["close_file","create_file","delete_file","draw_svg","file_search","open_file","repl","str_replace","update_file"];async function H(e){let t,n,{applicationType:r,endpoint:a,orgUuid:i,conversationUuid:s,body:o,onCompletion:l,onInvokeTool:u,abortController:c,completion:d,getTools:h,smoothingEnabled:p=!0}=e,m=(t={},n=!1,new T({blockSize:e=>{if("text"===e.type)return e.text.length;if("thinking"===e.type)return e.thinking.length;if("tool_use"===e.type){var t;return(null===(t=e.partial_json)||void 0===t?void 0:t.length)||0}return e.type,0},sliceBlock:(e,t)=>{if("tool_result"===e.type)return e;if("text"===e.type)return{...e,text:e.text.slice(0,t)};if("tool_use"===e.type){var n;return{...e,partial_json:null===(n=e.partial_json)||void 0===n?void 0:n.slice(0,t)}}return"thinking"===e.type?{...e,thinking:e.thinking.slice(0,t)}:e},createBlockFromStartEvent:e=>("thinking"===e.content_block.type&&(e.content_block.start_timestamp=new Date().toISOString()),e.content_block),applyDeltaEvent:(e,r)=>{var a,i;switch(r.type){case"text":switch(n=!1,e.delta.type){case"text_delta":return{...r,text:r.text+e.delta.text};case"citation_start_delta":{let n=e.delta.citation;return t={...t,[n.uuid]:{...n,start_index:r.text.length}},r}case"citation_end_delta":{let n=e.delta.citation_uuid;return{...r,citations:(null!==(a=r.citations)&&void 0!==a?a:[]).concat({...t[n],end_index:r.text.length})}}default:return r}case"thinking":switch(n=!0,e.delta.type){case"thinking_delta":return{...r,thinking:r.thinking+e.delta.thinking};case"thinking_summary_delta":return{...r,summaries:[...r.summaries||[],e.delta.summary]};case"thinking_cut_off_delta":return{...r,cut_off:e.delta.cut_off};default:return r}case"tool_result":if(n=!1,"input_json_delta"===e.delta.type)return{type:"tool_result",tool_use_id:"",name:"",is_error:!1,content:[{type:"text",text:e.delta.partial_json}]};return r;case"tool_use":if(n=!1,"input_json_delta"===e.delta.type)return{...r,partial_json:(null!==(i=r.partial_json)&&void 0!==i?i:"")+e.delta.partial_json};return r}},stopBlockEvent:(e,t)=>"thinking"===t.type?{...t,stop_timestamp:e.stop_timestamp}:t,getBlockDeadlineOffset(){if(n)return 3}}));m.dont_smooth=!p,m.on_completion=l;let f=p?m.task(l,c.signal):Promise.resolve(),g={"Content-Type":"application/json",Accept:"text/event-stream",...(0,L.F)(r)},k=null,v=null,x=JSON.stringify({...d,...o,text:void 0,rendering_mode:"messages",organization_uuid:void 0,conversation_uuid:void 0}),w="",b="",C="",j=0,S=(0,I.L)(function(e,t,n){let r;if(void 0===t||void 0===n)throw Error("Missing org uuid or conversation uuid");switch(e){case 1:r="completion";break;case 2:r="retry_completion";break;default:throw Error("Invalid endpoint")}return"/api/organizations/".concat(t,"/chat_conversations/").concat(n,"/").concat(r)}(a,i,s),{method:"POST",credentials:"include",headers:g,body:x,openWhenHidden:!0,signal:c.signal,async onopen(e){var t;if(!e.ok||!(null===(t=e.headers.get("content-type"))||void 0===t?void 0:t.includes(I.a))){let t=await e.text(),n={};try{n=JSON.parse(t)}catch(e){}throw(0,_.fT)(e.status,n,"Failed to fetch",e.headers)}},onmessage:e=>{var t,n;switch(e.event){case"ping":default:return;case"completion":{let n=JSON.parse(e.data);if(void 0===n.completion)throw Error("Unexpected message received when streaming: ".concat(e.data));let r={type:"content_block_delta",index:0,delta:{type:"text_delta",text:n.completion}};k=n.stop_reason,v=null!==(t=n.messageLimit)&&void 0!==t?t:null,m.onMessage(r);break}case"content_block_start":{let t=JSON.parse(e.data);j=t.index,"tool_use"===t.content_block.type&&(b=t.content_block.name,C=t.content_block.id),m.onMessage(t);break}case"content_block_delta":{let t=JSON.parse(e.data);if(t.index!==j&&"thinking_summary_delta"!==t.delta.type)throw Error("Content block index did not match the expected index");(null===(n=t.delta)||void 0===n?void 0:n.type)==="input_json_delta"&&(B.includes(b)||h().some(e=>e.name===b))&&(w+=t.delta.partial_json),m.onMessage(t);break}case"content_block_stop":{let t=JSON.parse(e.data);if(t.index!==j)throw Error("Content block index did not match the expected index");if(m.onMessage(t),!b)break;u(s,b,C,w),w="",b="",C="";break}case"message_limit":v=JSON.parse(e.data).message_limit;break;case"message_delta":case"message_start":case"message_stop":{let t=JSON.parse(e.data);"message_stop"===t.type&&(k="message_stop"),m.onMessage(t)}}},onclose(){m.model_done=!0},onerror(e){throw e instanceof _.Hx&&529===e.statusCode||(0,y.Tb)(e),m.model_done=!0,m.smoother_done=!0,e}});return await Promise.all([S,f]),{stopReason:k,messageLimitResult:v}}var R=n(1146),z=n(77879),Q=n(78230),Z=n(49185),A=n(57271);let W=(0,E.createContext)([null,null]);function Y(e){let{children:t}=e,n=(0,E.useState)({});return(0,o.jsx)(W.Provider,{value:n,children:t})}function F(e,t,n){let{onIncrementalCompletion:r,onStartAppend:a,onStreamCompleted:s}=(0,A.p)(),o=V({conversationUUID:e,endpoint:i.APPEND_MESSAGE_WITH_COMPLETION,onIncrementalCompletion:r,onStreamCompleted:s,onInvokeTool:t,getTools:n}),{runStream:l}=o,u=(0,E.useCallback)(async t=>{let{prompt:n,attachments:r,files:i,syncSources:s,rethrowErrors:o=!1,modelOverride:u,parent_message_uuid:c,personalized_style:d}=t;await a(e,e=>{if(!e)throw Error("Conversation tree must be provided for tree append");let t=(0,R.HX)(e),a=[{uuid:R.wZ,content:[{type:"text",text:n,citations:[]}],created_at:new Date().toISOString(),sender:"human",attachments:r,files:i,files_v2:i,sync_sources:s,index:t+1,parent_message_uuid:null!=c?c:R.QC},{uuid:R.FC,content:[],created_at:new Date().toISOString(),sender:"assistant",attachments:[],files:[],files_v2:[],sync_sources:[],index:t+2,parent_message_uuid:R.wZ}];return(0,R.vv)(e,a)}),await l({prompt:n,attachments:r,files:i,syncSources:s,rethrowErrors:o,modelOverride:u,parent_message_uuid:c,personalized_style:d})},[l,a,e]);return{...o,runStream:u}}function G(e,t,n,r){let{onIncrementalCompletion:a,onStreamCompleted:s,onStartAppend:o}=(0,A.p)(),l=V({conversationUUID:e,endpoint:i.RETRY_MESSAGE_WITH_COMPLETION,onIncrementalCompletion:a,onInvokeTool:t,onStreamCompleted:s,getTools:n}),{runStream:u}=l,c=(0,E.useCallback)(async(t,n)=>{await o(e,e=>{let n=[{uuid:R.FC,content:[],created_at:new Date().toISOString(),sender:"assistant",attachments:[],files:[],files_v2:[],sync_sources:[],parent_message_uuid:t,selectedOption:0,index:(0,R.HX)(e)+1}];return(0,R.vv)(e,n)}),await u({prompt:"",attachments:[],files:[],syncSources:[],parent_message_uuid:t,personalized_style:r,paprika_mode:n,isRetry:!0})},[e,u,o,r]);return{...l,runStream:c}}let J="incomplete_stream",U=Error(J),X=e=>{var t,n;let r=q(e),{config:a}=(0,O.useExperiment)("tepui_default_on_claude_free"),i=null==a?void 0:null===(t=a.get("console_default_model_override",null))||void 0===t?void 0:t.model,{layer:s}=(0,O.useLayer)("frontend"),o=null===(n=s.get("console_default_model_override",null))||void 0===n?void 0:n.model,{config:l}=(0,O.useConfig)("console_default_model"),u=l.get("model",null);return r||i||o||u||"claude-2.1"},q=e=>{let t=(0,l.cE)(e||""),n=(0,S.useSearchParams)(),r=null==n?void 0:n.get("model");return(null==t?void 0:t.model)?null==t?void 0:t.model:r||void 0},$={value:null},K=()=>{let e=(0,w.useQueryClient)();return{failedStreamRetryData:(0,b.useQuery)({queryKey:[v.sf],queryFn:()=>$.value,initialData:null}).data||null,setFailedStreamRetryData:(0,E.useCallback)(t=>{$.value=t,e.setQueryData([v.sf],t)},[e])}};function V(e){var t;let{conversationUUID:n,endpoint:r,getTools:a,onIncrementalCompletion:i,onInvokeTool:s,onStreamCompleted:v}=e,{applicationType:w}=(0,d.m)(),{activeOrganization:b}=(0,h.t)(),{value:L}=(0,k.F)("apps_no_smoothing"),I=null==b?void 0:b.uuid,{getRemoteMcpTools:D}=(0,m.$)(),{context:N,clearContext:P,setContext:T}=function(e){let[t,n]=(0,E.useContext)(W),[r,a]=(0,E.useState)(),i=(0,E.useCallback)(()=>{n?n(t=>{let n={...t};return delete n[e],n}):a(void 0)},[e,n]),s=(0,E.useCallback)(t=>{n?n(n=>({...n,[e]:t})):a(t)},[e,n]);return{context:t?t[e]:r,clearContext:i,setContext:s}}(n),B=(0,S.useSearchParams)(),R=null==B?void 0:B.get("t"),A=null!==(t=q(n))&&void 0!==t?t:"",Y=null==B?void 0:B.get("max_tokens"),F=(0,Z.n)(),{addError:G,clearToast:X}=(0,p.e)(),$=(0,E.useMemo)(x.H,[]),V=(0,l.$H)("push",$),{setFailedStreamRetryData:ee}=K(),et=(0,E.useCallback)(async e=>{await (null==v?void 0:v(n,e)),P()},[v,n,P]),en=(0,E.useCallback)((e,t,r)=>{if(e===J)return G((0,o.jsx)("p",{children:(0,o.jsx)(M.Z,{defaultMessage:"Claude’s response was interrupted. Please check your network connection or <link>contact support</link> if the issue persists.",id:"C83ex9tIjX",values:{link:e=>(0,o.jsx)(j.default,{href:"https://support.anthropic.com/en/articles/9015913-how-to-get-support",className:"underline",children:e})}})}));if(!(e instanceof _.Hx))return G((0,o.jsx)(M.Z,{defaultMessage:"We couldn’t connect to Claude. Please check your network connection and try again.",id:"KzO0j4cGtg"}));if("rate_limit_error"===e.type){if(e.message.includes("{")&&I)try{let t=JSON.parse(e.message);F(t,I,n)}catch(e){}return G((0,o.jsxs)("p",{children:[(0,o.jsx)(M.Z,{defaultMessage:"You’ve reached the limit for Claude messages at this time. Please wait before trying again.",id:"CBMEJMlMzs"}),"\xa0",(0,o.jsx)(u.o,{})]}))}if("not_found_error"===e.type)return G((0,o.jsx)(M.Z,{defaultMessage:"Claude model version not found.",id:"kdrUaXpTqd"}));if("billing_error"===e.type)return G((0,o.jsx)("p",{children:(0,o.jsx)(M.Z,{defaultMessage:"We had an unexpected billing error, please <link>contact support.</link>",id:"MBYn4HHrFu",values:{link:e=>(0,o.jsx)("a",{className:"underline",href:"https://support.anthropic.com/en/articles/9015913-how-can-i-contact-support",target:"_blank",rel:"noreferrer",children:e})}})}));if("overloaded_error"===e.type)return G((0,o.jsxs)("p",{children:[(0,o.jsx)(M.Z,{defaultMessage:"Due to unexpected capacity constraints, Claude is unable to respond to your message. Please try again soon.",id:"TRpxX8Mu1L"}),(0,o.jsx)(u.o,{})]}));if("exceeded_max_uploads_per_message"===e.errorCode){G((0,o.jsx)(M.Z,{defaultMessage:"Your message will exceed the maximum number of files allowed per message. Consider removing some of your files or adding files over several messages.",id:"/jNwaAHdg9"}));return}if("exceeded_max_image_limit_per_chat"===e.errorCode){let e=G((0,o.jsx)("p",{children:r>0?(0,o.jsx)(M.Z,{defaultMessage:"Your message will exceed the <link>maximum image count</link> for this chat. Try uploading {count, plural, one {# document} other {# documents}} with fewer pages, removing images, <newChatLink>or starting a new conversation.</newChatLink>{upgradeInstructions}",id:"gltmHPfviz",values:{link:e=>(0,o.jsx)("a",{className:"underline",target:"_blank",rel:"noreferrer",href:f._R,children:e}),newChatLink:t=>(0,o.jsx)("a",{href:"#",className:"underline",onClick:t=>{t.preventDefault(),V(),X(e)},children:t}),upgradeInstructions:()=>(0,o.jsx)(u.o,{}),count:r}}):(0,o.jsx)(M.Z,{defaultMessage:"Your message will exceed the <link>maximum image count</link> for this chat. Try removing images <newChatLink>or starting a new conversation.</newChatLink>{upgradeInstructions}",id:"QmLhKBubcc",values:{link:e=>(0,o.jsx)("a",{className:"underline",target:"_blank",rel:"noreferrer",href:f._R,children:e}),newChatLink:t=>(0,o.jsx)("a",{href:"#",className:"underline",onClick:t=>{t.preventDefault(),V(),X(e)},children:t}),upgradeInstructions:()=>(0,o.jsx)(u.o,{})}})}));return}if("invalid_request_error"===e.type&&413===e.statusCode){let e=G((0,o.jsx)("p",{children:t>0?(0,o.jsx)(M.Z,{defaultMessage:"Your message will exceed the <link>length limit</link> for this chat. Try attaching fewer or smaller files <newChatLink>or starting a new conversation.</newChatLink>{upgradeInstructions}",id:"G1lejdsk5p",values:{link:e=>(0,o.jsx)("a",{className:"underline",target:"_blank",rel:"noreferrer",href:f.jC,children:e}),newChatLink:t=>(0,o.jsx)("a",{href:"#",className:"underline",onClick:t=>{t.preventDefault(),V(),X(e)},children:t}),upgradeInstructions:()=>(0,o.jsx)(u.o,{})}}):(0,o.jsx)(M.Z,{defaultMessage:"Your message will exceed the <link>length limit</link> for this chat. Try shortening your message <newChatLink>or starting a new conversation.</newChatLink>{upgradeInstructions}",id:"EWtBrIlAAR",values:{link:e=>(0,o.jsx)("a",{className:"underline",target:"_blank",rel:"noreferrer",href:f.jC,children:e}),newChatLink:t=>(0,o.jsx)("a",{href:"#",className:"underline",onClick:t=>{t.preventDefault(),V(),X(e)},children:t}),upgradeInstructions:()=>(0,o.jsx)(u.o,{})}})}));return}return"invalid_request_error"===e.type&&"read_only_mode"===e.extra.code?G((0,o.jsx)("p",{children:(0,o.jsx)(M.Z,{defaultMessage:"Due to capacity constraints, chatting with Claude is currently not available. Please try again in a little while.",id:"W2dIBW8oIg"})})):G(e)},[G,I,F,n,V,X]),er=(0,c.z$)(),ea=function(){let{height:e,width:t}=(0,z.iP)(),n=[];return n.length?n.join("\n"):null}(),{config:ei}=(0,O.useExperiment)("no_more_smoother"),es=(0,E.useCallback)(async e=>{let{prompt:t,attachments:o,files:l,syncSources:u,rethrowErrors:c=!1,modelOverride:d,parent_message_uuid:h,personalized_style:p,isRetry:m=!1,paprika_mode:_}=e;if(N)return;if(!b)throw Error("Cannot stream without an organization");let f=new AbortController;T({controller:f}),ee(null);let k={prompt:t,parent_message_uuid:h,timezone:C.ou.local().zoneName||void 0,personalized_styles:p?[p]:void 0},v=(0,Q.l)();(v||ea)&&(k.custom_system_prompt=[v,ea].filter(Boolean).join("\n")),A&&"string"==typeof A&&(k.model=A),d&&(k.model=d),"string"==typeof R&&(k.temperature=parseInt(R)),Y&&"string"==typeof Y&&(k.max_tokens_to_sample=parseInt(Y)),m&&_&&(k.paprika_mode=_);let x=a();x.length&&(k.tools=x);let j=D();j.length>0&&(k.tools=[...x,...j]);let S=[],E=null,M=null,O=null;try{let e=ei.get("no_smoothing",!1);if(E=(O=await H({applicationType:w,endpoint:r,orgUuid:b.uuid,conversationUuid:n,body:{organization_uuid:b.uuid,conversation_uuid:n,text:t,attachments:o,files:l.map(e=>e.file_uuid),sync_sources:u.map(e=>e.uuid)},onCompletion:e=>{i(n,e),S=e},onInvokeTool:s,abortController:f,completion:k,getTools:a,smoothingEnabled:!L&&!e})).stopReason,M=O.messageLimitResult,O&&!E)throw U}catch(e){if(ee({prompt:t,attachments:o,files:l}),e===U?(i(n,S),en(J,o.length+l.length,l.filter(e=>"document"===e.file_kind).length),er.track({event_key:"sse_interrupted"}),(0,y.uT)("sse_interrupted"),await new Promise(e=>setTimeout(e,3e4))):en(e,o.length+l.length,l.filter(e=>"document"===e.file_kind).length),(0,y.Tb)(e),c||(0,g.yG)())throw e}finally{await et(M)}return S},[N,b,T,ee,ea,A,R,Y,a,D,ei,w,r,n,s,L,i,en,et,er]),eo=(0,E.useCallback)(()=>{N&&(N.controller.abort(),et(null))},[N,et]);return{runStream:es,isStreaming:!!N,abortStream:eo}}},78230:function(e,t,n){n.d(t,{h:function(){return i},l:function(){return s}});var r=n(7653);let a="customSystemPrompt",i=()=>{let[e,t]=(0,r.useState)(()=>"undefined"==typeof localStorage?"":localStorage.getItem(a)||"");return(0,r.useEffect)(()=>{localStorage.setItem(a,e)},[e]),{customSystemPrompt:e,setCustomSystemPrompt:t}},s=()=>"undefined"==typeof localStorage?"":localStorage.getItem(a)},49185:function(e,t,n){n.d(t,{I:function(){return u},n:function(){return l}});var r=n(5362),a=n(27218),i=n(13262),s=n(77930),o=n(7653);let l=()=>{let e=(0,s.useQueryClient)();return(0,o.useCallback)((t,n,r)=>{"within_limit"!==t.type&&(t.conversationUUID=r),e.setQueryData([i.aY],e=>e?{...e,messageLimits:{...e.messageLimits,[n]:t}}:e)},[e])},u=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid,i=l(),s={type:"within_limit"};return(0,r.uC)(()=>"/api/organizations/".concat(n,"/reset_rate_limits"),"POST",{onSuccess:()=>{i(s,n,e||"")},meta:{noToast:!0}})}},57271:function(e,t,n){n.d(t,{p:function(){return u}});var r=n(27218),a=n(13262),i=n(77930),s=n(7653),o=n(1146),l=n(49185);let u=()=>{let e=(0,i.useQueryClient)(),{activeOrganization:t}=(0,r.t)(),n=null==t?void 0:t.uuid,u=(0,s.useRef)(),c=(0,l.n)(),d=(0,s.useCallback)(async(t,r)=>{let i=[a.I8,{orgUUID:n},{uuid:t}],s=e.getQueryData(i);if(!s)throw Error("Conversation tree must be provided for append");let o=r(s);u.current=o,await e.cancelQueries({queryKey:i}),e.setQueryData(i,u.current)},[e,n]),h=(0,s.useCallback)((t,r)=>{0!==r.length&&e.setQueryData([a.I8,{orgUUID:n},{uuid:t}],()=>{let e=u.current;if(!e)throw Error("Conversation tree must be provided for incremental completion");if(!e.current_leaf_message_uuid)throw Error("Couldn't figure out where to put completion");let t=e.messageByUuid.get(e.current_leaf_message_uuid);if(!t)throw Error("New assistant message not found");return t.content=r,{...e,created_at:new Date().toISOString()}})},[e,n]);return{onStartAppend:d,onStreamCompleted:(0,s.useCallback)(async(t,r)=>{let i=[a.I8,{orgUUID:n},{uuid:t}];u.current=void 0,r?(e.setQueryData([a.tv,{orgUUID:n}],e=>{if(!e)return;let n=e.find(e=>e.uuid===t);return n?[{...n,updated_at:new Date().toISOString()},...e.filter(e=>e.uuid!==t)]:e}),await e.invalidateQueries({queryKey:i})):e.invalidateQueries({queryKey:i}),r&&n&&c(r,n,t)},[e,c,n]),onIncrementalCompletion:h,changeDisplayedConversationPath:(0,s.useCallback)((t,r,i,s)=>{e.setQueryData([a.I8,{orgUUID:n},{uuid:t}],e=>{if(!e)throw Error("Conversation tree must be provided for changeDisplayedConversationPath");let t=(0,o.mj)(e,r,i);return t.current_leaf_message_uuid&&s&&s(t.current_leaf_message_uuid),t})},[e,n])}}},66092:function(e,t,n){n.d(t,{$:function(){return i}});var r=n(7653),a=n(18013);function i(){let[e,t]=(0,a.R)("mcp-enabled-tools",{}),n=(0,r.useCallback)((e,n)=>{t(t=>({...t,[e]:n}))},[t]),i=(0,r.useCallback)(t=>!!e[t],[e]),s=(0,r.useCallback)(()=>{let t=Object.entries(e).filter(e=>{let[t,n]=e;return n}).map(e=>{let[t]=e;return t});return 0===t.length?[]:t.map(e=>({type:"remote_mcp_v0",name:e}))},[e]);return{enabledTools:e,setEnabledTools:t,toggleTool:n,isToolEnabled:i,getRemoteMcpTools:s}}},66894:function(e,t,n){n.d(t,{Ry:function(){return i},cm:function(){return a},yG:function(){return r}}),n(68571);let r=()=>!1,a=()=>!1,i=()=>a()||r()}}]);