(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185,3053],{31783:function(){},60449:function(){},31431:function(e,t,n){Promise.resolve().then(n.bind(n,775)),Promise.resolve().then(n.bind(n,1399)),Promise.resolve().then(n.t.bind(n,36529,23)),Promise.resolve().then(n.bind(n,24173)),Promise.resolve().then(n.bind(n,5122)),Promise.resolve().then(n.bind(n,77930)),Promise.resolve().then(n.bind(n,4272)),Promise.resolve().then(n.bind(n,31123)),Promise.resolve().then(n.bind(n,43193)),Promise.resolve().then(n.bind(n,15969)),Promise.resolve().then(n.bind(n,49809)),Promise.resolve().then(n.bind(n,39155)),Promise.resolve().then(n.bind(n,5068)),Promise.resolve().then(n.bind(n,45446)),Promise.resolve().then(n.bind(n,46933)),Promise.resolve().then(n.bind(n,5713)),Promise.resolve().then(n.t.bind(n,22147,23)),Promise.resolve().then(n.t.bind(n,14581,23)),Promise.resolve().then(n.t.bind(n,94957,23)),Promise.resolve().then(n.t.bind(n,42312,23)),Promise.resolve().then(n.t.bind(n,95601,23)),Promise.resolve().then(n.t.bind(n,19839,23)),Promise.resolve().then(n.bind(n,56323)),Promise.resolve().then(n.bind(n,76986)),Promise.resolve().then(n.bind(n,8571)),Promise.resolve().then(n.bind(n,50862)),Promise.resolve().then(n.bind(n,40287)),Promise.resolve().then(n.bind(n,27218)),Promise.resolve().then(n.bind(n,27895)),Promise.resolve().then(n.bind(n,20823)),Promise.resolve().then(n.bind(n,15878)),Promise.resolve().then(n.bind(n,18850)),Promise.resolve().then(n.bind(n,79483)),Promise.resolve().then(n.bind(n,80386)),Promise.resolve().then(n.bind(n,36720)),Promise.resolve().then(n.bind(n,11019)),Promise.resolve().then(n.bind(n,58574)),Promise.resolve().then(n.bind(n,16369)),Promise.resolve().then(n.bind(n,17089)),Promise.resolve().then(n.bind(n,53746)),Promise.resolve().then(n.bind(n,69321)),Promise.resolve().then(n.bind(n,22889)),Promise.resolve().then(n.bind(n,39465)),Promise.resolve().then(n.t.bind(n,85903,23)),Promise.resolve().then(n.bind(n,67587)),Promise.resolve().then(n.bind(n,43965))},32559:function(e,t,n){"use strict";n.d(t,{GO:function(){return _},Gm:function(){return P},Kn:function(){return S},L8:function(){return k},O3:function(){return h},Qs:function(){return j},RN:function(){return v},Yc:function(){return w},bQ:function(){return b},cd:function(){return a},dY:function(){return C},iR:function(){return z},nL:function(){return i},u0:function(){return y}});var r,a,i,s=n(96933),o=n(5362),l=n(27218),c=n(30947),u=n(13262),d=n(77930),p=n(21520),m=n.n(p),f=n(7653),g=n(95586);(a||(a={})).Student="student",(r=i||(i={})).Study="study",r.Career="career",r.Research="research";let h=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{defer:t}=e,{activeOrganization:n}=(0,l.t)(),r=(0,g.U)(),a=null==n?void 0:n.uuid;return(0,o.WE)("/api/organizations/".concat(a,"/projects?include_harmony_projects=true"),{queryKey:[u.gi,{orgUuid:a}],enabled:!!(n&&r&&!t),select:e=>e.filter(e=>!e.archived_at)})},v=()=>{let{activeOrganization:e}=(0,l.t)(),t=(0,o.OJ)();return(0,f.useCallback)(async()=>(await t("/api/organizations/".concat(null==e?void 0:e.uuid,"/projects"))).json(),[null==e?void 0:e.uuid,t])},b=()=>{let{data:e,isLoading:t}=h();return{data:null==e?void 0:e.filter(e=>e.is_starred),isLoading:t}},x=(e,t)=>{let n=e.map(e=>({...e,lastConversation:null==t?void 0:t.find(t=>t.project_uuid===e.uuid)})).filter(e=>e.lastConversation).sort((e,t)=>new Date(t.lastConversation.created_at).getTime()-new Date(e.lastConversation.created_at).getTime()),r=null==e?void 0:e.filter(e=>!n.find(t=>t.uuid===e.uuid));return[...n,...r]},_=()=>{let e=v(),t=(0,s.Pe)();return(0,f.useCallback)(async()=>{let[n,r]=await Promise.all([e(),t({limit:void 0})]);return x(n,r)},[t,e])},y=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{defer:t}=e,n=(0,g.U)(),{data:r,isLoading:a}=(0,s.QR)({limit:void 0,defer:!n||t}),{data:i,isLoading:o}=h({defer:t});return(0,f.useMemo)(()=>a||o?{data:i,isLoading:!0}:i&&r?{data:x(i,r),isLoading:!1}:{data:i,isLoading:!1},[i,r,a,o])},w=e=>{let{activeOrganization:t}=(0,l.t)(),n=null==t?void 0:t.uuid;return(0,o.WE)("/api/organizations/".concat(n,"/projects/").concat(e),{queryKey:[u.$T,{orgUuid:n,projectUuid:e}],enabled:!!(t&&e),staleTime:0,meta:{noToast:e=>e instanceof c.Hx&&404===e.statusCode}})},j=()=>{let{activeOrganization:e}=(0,l.t)(),t=null==e?void 0:e.uuid,n=(0,d.useQueryClient)();return(0,o.uC)(()=>"/api/organizations/".concat(t,"/projects"),"POST",{onSuccess:()=>{n.invalidateQueries({queryKey:[u.gi,{orgUuid:t}]}),n.invalidateQueries({queryKey:[u.n$,{orgUuid:t}]})}})},C=e=>{let{activeOrganization:t}=(0,l.t)(),n=null==t?void 0:t.uuid,r=P();return(0,o.Ne)(()=>"/api/organizations/".concat(n,"/projects/").concat(e),"PUT",(e,t)=>t?{...t,...e}:void 0,{onSuccess:()=>{r(e)},queryKey:[u.$T,{orgUuid:n,projectUuid:e}]})},k=e=>{let{activeOrganization:t}=(0,l.t)(),n=null==t?void 0:t.uuid,r=P();return(0,o.uC)(()=>"/api/organizations/".concat(n,"/projects/").concat(e),"DELETE",{onSuccess:()=>{r(e)}})},z=e=>{let{activeOrganization:t}=(0,l.t)(),n=null==t?void 0:t.uuid;return(0,o.WE)("/api/organizations/".concat(n,"/projects/").concat(e,"/conversations"),{queryKey:[u.lx,{orgUuid:n,projectUuid:e}],enabled:!!t,staleTime:0,select:e=>m()(e,e=>new Date(e.updated_at)).reverse()})},S=()=>{var e,t;let n=h(),r=null===(t=n.data)||void 0===t?void 0:null===(e=t.find(e=>e.is_starter_project))||void 0===e?void 0:e.uuid;return{...n,data:r}},P=()=>{let{activeOrganization:e}=(0,l.t)(),t=null==e?void 0:e.uuid,n=(0,d.useQueryClient)();return e=>{n.invalidateQueries({queryKey:[u.$T,{orgUuid:t,projectUuid:e}]}),n.invalidateQueries({queryKey:[u.gi,{orgUuid:t}]}),n.invalidateQueries({queryKey:[u.n$,{orgUuid:t}]}),n.invalidateQueries({queryKey:[u.lx,{orgUuid:t,projectUuid:e}]})}}},95586:function(e,t,n){"use strict";n.d(t,{H:function(){return s},U:function(){return i}});var r=n(27218),a=n(15992);let i=()=>{let e=(0,r.ZJ)(),t=(0,r.Cf)();return(0,a.useConfig)("claude_ai_projects_limits").config.get("max_free_projects",0)>0||t||e},s=()=>{let e=(0,r.ZJ)(),t=(0,r.Cf)(),n=(0,a.useConfig)("claude_ai_projects_limits");return e||t?void 0:n?n.config.get("max_free_projects",0):0}},775:function(e,t,n){"use strict";n.d(t,{DesktopChecks:function(){return E}});var r,a,i=n(27573),s=n(85069),o=n(43965),l=n(88755),c=n(15992),u=n(96933),d=n(26525),p=n(15281),m=n(27895),f=n(70354),g=n(95622),h=n(31825),v=n(7653),b=n(40950),x=n(45790),_=n(48912),y=n(32559);(r=a||(a={})).QuickEntry="QuickEntry",r.Statsig="Statsig";let w=e=>{let{text:t}=e;return(0,i.jsx)(_.U,{className:"grid-cols-1 grid gap-2.5 [&_>_*]:min-w-0",components:{a:e=>{let{node:t,...n}=e;return(0,i.jsx)("a",{...n,className:"underline font-bold"})}},children:t})},j=()=>{let e=(0,b.Z)(),t=(0,y.GO)(),n=(0,y.RN)(),r=(0,u.Pe)(),a=(0,d.kR)(),{addError:s,addSuccess:o}=(0,m.e)(),l=(0,p.w)();return(0,v.useEffect)(()=>{var t,n;null===(t=(n=window).requestLocaleChange)||void 0===t||t.call(n,e.locale)},[e]),(0,g.id)("QuickEntry",{getProjects:n,getRecentProjects:t,getConversations:()=>r({limit:void 0}),uploadFile:a,newConversationWithFirstMessage:l},[n,t,r,a,l]),(0,g.id)("Statsig",{provideRequestedGates:e=>e.reduce((e,t)=>(e[t]=c.Statsig.checkGate(t),e),{})},[]),(0,g.id)(h.C.Toast,{showToast:(e,t,n)=>{let{markdown:r}=null!=n?n:{},a=r?(0,i.jsx)(w,{text:e}):e;(null==n?void 0:n.mcpServer)&&n.openMcpSettingsButton&&(a=(0,i.jsxs)("div",{className:"flex flex-col gap-2",children:[a,(0,i.jsx)(f.z,{className:"w-fit",onClick:()=>{var e,t;(null===(e=window.claudeAppBindings)||void 0===e?void 0:e.openMcpSettings)&&n.mcpServer&&(null===(t=window.claudeAppBindings)||void 0===t||t.openMcpSettings(n.mcpServer))},children:(0,i.jsx)(x.Z,{defaultMessage:"Open MCP Settings",id:"7xYNeUaQoO"})})]})),"success"===t?o(a):s(a)}},[o,s]),null};var C=n(44602),k=n(88146);function z(e){let[t,n]=(0,v.useState)("opacity-0");return(0,v.useEffect)(()=>void setTimeout(()=>n("opacity-100"),10),[]),(0,i.jsx)("div",{className:"bg-bg-200 absolute top-0 left-0 w-screen h-screen z-overlay transition-opacity duration-1000 ".concat(t),children:e.children})}function S(){return(0,i.jsx)(z,{children:(0,i.jsx)(C.C,{productSurface:"claude-ai",headline:(0,i.jsx)(x.Z,{defaultMessage:"Upgrade Your App",id:"ENkdMyU89t"}),subheading:(0,i.jsx)(x.Z,{defaultMessage:"Your version of the Claude app is out of date. Please upgrade to the latest version to continue using Claude.",id:"TGeVQwc7+S"}),button:(0,i.jsx)("div",{className:"flex flex-col items-center",children:(0,i.jsx)(k.default,{href:"/download",target:"_blank",children:(0,i.jsx)(f.z,{size:"lg",children:(0,i.jsx)(x.Z,{defaultMessage:"Open Download Page",id:"lwk5Ll/8MT"})})})})})})}let P=()=>{let e=(0,c.useConfig)("nest_version_info").config.value,t=(0,o.q)();if(!(t&&(0,s.Rx)(t)))return null;for(let n in e)if((0,s.Rx)(t,{version:n})&&e[n].disabled)return(0,i.jsx)(S,{});return null},E=()=>(0,l.useIsClaudeApp)()?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(j,{}),(0,i.jsx)(P,{})]}):null},1399:function(e,t,n){"use strict";n.d(t,{NuxProvider:function(){return s},s:function(){return i}});var r=n(27573),a=n(7653);let i=(0,a.createContext)(null);function s(e){let{children:t}=e,[n,s]=(0,a.useState)({items:{},currentlyShowing:null}),l=(0,a.useCallback)((e,t)=>{s(n=>{if(n.items[e])return n;let r={...n.items,[e]:t};return{items:r,currentlyShowing:o(r,!0)}})},[]),c=(0,a.useCallback)(e=>{s(t=>{let{[e]:n,...r}=t.items;return{items:r,currentlyShowing:t.currentlyShowing===e?o(r):t.currentlyShowing}})},[]),u={items:n.items,currentlyShowing:n.currentlyShowing,registerNux:l,unregisterNux:c};return(0,r.jsx)(i.Provider,{value:u,children:t})}let o=(e,t)=>{let n=Object.entries(e).sort((e,t)=>{let n=e[1].priority||0,r=t[1].priority||0;return n!==r?r-n:new Date(t[1].dateAdded).getTime()-new Date(e[1].dateAdded).getTime()});if(t){var r,a;return null!==(a=null===(r=n[0])||void 0===r?void 0:r[0])&&void 0!==a?a:null}for(let[e,t]of n)if(t.showAfterOthersDismiss)return e;return null}},93558:function(e,t,n){"use strict";n.d(t,{KX:function(){return m},O3:function(){return b},P2:function(){return h},TD:function(){return x},q2:function(){return v},to:function(){return a},vr:function(){return f}});var r,a,i=n(82083),s=n(41270),o=n(37584);(r=a||(a={})).ReadyForContent="anthropic.claude.usercontent.sandbox.ReadyForContent",r.SetContent="anthropic.claude.usercontent.sandbox.SetContent",r.GetFile="anthropic.claude.usercontent.sandbox.GetFile",r.SendConversationMessage="anthropic.claude.usercontent.sandbox.SendConversationMessage",r.RunCode="anthropic.claude.usercontent.sandbox.RunCode",r.ClaudeCompletion="anthropic.claude.usercontent.sandbox.ClaudeCompletion",r.ReportError="anthropic.claude.usercontent.sandbox.ReportError",r.GetScreenshot="anthropic.claude.usercontent.sandbox.GetScreenshot",r.BroadcastContentSize="anthropic.claude.usercontent.sandbox.BroadcastContentSize";let l=s.z.object({type:s.z.literal("UnsupportedImports"),unsupportedModules:s.z.array(s.z.string()),nonExistentIcons:s.z.array(s.z.string())}),c=s.z.object({type:s.z.literal("RuntimeError"),message:s.z.string()}),u=s.z.object({type:s.z.literal("FileNotFound"),fileName:s.z.string()}),d=s.z.object({type:s.z.literal("ClaudeCompletionError"),message:s.z.string()}),p=s.z.discriminatedUnion("type",[l,c,u,d]),m=s.z.object({code:s.z.string()}),f=s.z.object({status:s.z.enum(["success","error"]),result:s.z.string().optional(),logs:s.z.array(s.z.string()),error:s.z.string().optional()}),g={"anthropic.claude.usercontent.sandbox.SetContent":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.SetContent"),payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.SandboxContent"),content:s.z.string(),type:s.z.nativeEnum(i.JP),watchContentSize:s.z.boolean().optional()})}),responseSchema:o.AY,alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.ReadyForContent":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.ReadyForContent"),payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/google.protobuf.Empty")})}),responseSchema:o.AY,alwaysPermitted:!0},"anthropic.claude.usercontent.sandbox.BroadcastContentSize":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.BroadcastContentSize"),payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.BroadcastContentSizePayload"),height:s.z.number(),width:s.z.number()})}),responseSchema:o.AY,alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.GetFile":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.GetFile"),payload:s.z.strictObject({key:s.z.string(),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.GetFileRequest")})}),responseSchema:o.Tk.extend({payload:s.z.strictObject({value:s.z.instanceof(Uint8Array).nullable(),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.GetFileResponse")})}),alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.SendConversationMessage":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.SendConversationMessage"),payload:s.z.strictObject({message:s.z.string(),messageType:s.z.enum(["text","error"]),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.SendConversationMessageRequest")})}),responseSchema:o.AY,alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.RunCode":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.RunCode"),payload:s.z.strictObject({code:s.z.string(),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.RunCodeRequest")})}),responseSchema:o.Tk.extend({payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.RunCodeResponse")}).merge(f)}),alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.ClaudeCompletion":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.ClaudeCompletion"),payload:s.z.strictObject({prompt:s.z.string(),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionRequest")})}),responseSchema:o.Tk.extend({payload:s.z.strictObject({completion:s.z.string().nullable(),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionResponse")})}),alwaysPermitted:!1},"anthropic.claude.usercontent.sandbox.ReportError":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.ReportError"),payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.ReportErrorRequest"),error:p})}),responseSchema:o.AY,alwaysPermitted:!0},"anthropic.claude.usercontent.sandbox.GetScreenshot":{requestSchema:o.Zq.extend({method:s.z.literal("anthropic.claude.usercontent.sandbox.GetScreenshot"),payload:s.z.strictObject({"@type":s.z.literal("type.googleapis.com/google.protobuf.Empty")})}),responseSchema:o.Tk.extend({payload:s.z.strictObject({screenshot:s.z.string().nullable(),"@type":s.z.literal("type.googleapis.com/anthropic.claude.usercontent.sandbox.GetScreenshotResponse")})}),alwaysPermitted:!1}},h=s.z.enum(["anthropic.claude.usercontent.sandbox.ReadyForContent","anthropic.claude.usercontent.sandbox.GetFile","anthropic.claude.usercontent.sandbox.SendConversationMessage","anthropic.claude.usercontent.sandbox.ClaudeCompletion","anthropic.claude.usercontent.sandbox.ReportError","anthropic.claude.usercontent.sandbox.BroadcastContentSize"]),v=s.z.discriminatedUnion("method",[g["anthropic.claude.usercontent.sandbox.ReadyForContent"].requestSchema,g["anthropic.claude.usercontent.sandbox.GetFile"].requestSchema,g["anthropic.claude.usercontent.sandbox.SendConversationMessage"].requestSchema,g["anthropic.claude.usercontent.sandbox.ClaudeCompletion"].requestSchema,g["anthropic.claude.usercontent.sandbox.ReportError"].requestSchema,g["anthropic.claude.usercontent.sandbox.BroadcastContentSize"].requestSchema]);s.z.union([o.AY,g["anthropic.claude.usercontent.sandbox.GetFile"].responseSchema,g["anthropic.claude.usercontent.sandbox.SendConversationMessage"].responseSchema,g["anthropic.claude.usercontent.sandbox.ClaudeCompletion"].responseSchema]),s.z.enum(["anthropic.claude.usercontent.sandbox.SetContent","anthropic.claude.usercontent.sandbox.RunCode","anthropic.claude.usercontent.sandbox.GetScreenshot"]),s.z.discriminatedUnion("method",[g["anthropic.claude.usercontent.sandbox.SetContent"].requestSchema,g["anthropic.claude.usercontent.sandbox.RunCode"].requestSchema,g["anthropic.claude.usercontent.sandbox.GetScreenshot"].requestSchema]);let b=s.z.union([g["anthropic.claude.usercontent.sandbox.SetContent"].responseSchema,g["anthropic.claude.usercontent.sandbox.RunCode"].responseSchema,g["anthropic.claude.usercontent.sandbox.GetScreenshot"].responseSchema]),x=Object.entries(g).filter(e=>{let[t,n]=e;return!0===n.alwaysPermitted}).map(e=>{let[t,n]=e;return t})},37584:function(e,t,n){"use strict";n.d(t,{AY:function(){return l},D7:function(){return c},Kj:function(){return o},Tk:function(){return s},Zq:function(){return i},qV:function(){return a},v8:function(){return u}});var r=n(41270);let a=r.z.object({channel:r.z.enum(["request","response"]),requestId:r.z.string()}).passthrough(),i=a.strict().extend({channel:r.z.literal("request"),method:r.z.string(),payload:r.z.strictObject({"@type":r.z.string()})}),s=a.extend({channel:r.z.literal("response"),status:r.z.number().int().min(100).max(599),payload:r.z.strictObject({"@type":r.z.string()}).passthrough()}).passthrough(),o=s.extend({status:r.z.number().int().min(400).max(599),payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/anthropic.claude.usercontent.ErrorResponse"),error:r.z.string()})}),l=s.extend({payload:r.z.strictObject({"@type":r.z.literal("type.googleapis.com/google.protobuf.Empty")})}),c={"@type":"type.googleapis.com/google.protobuf.Empty"},u=e=>({"@type":"type.googleapis.com/anthropic.claude.usercontent.ErrorResponse",error:e})},17796:function(e,t,n){"use strict";n.d(t,{i:function(){return i}});var r=n(93558),a=n(37584);class i{setupMessageListener(){window.addEventListener("message",this.boundHandleMessage,!1)}handleMessage(e){if(e.origin!==this.allowedOrigin||e.source!==this.iframe.contentWindow)return;let t=Date.now();if(t-this.lastResetTime>this.RESET_INTERVAL&&(this.messageCount=0,this.lastResetTime=t),this.messageCount++,this.messageCount>this.MAX_MESSAGES_PER_INTERVAL){window.removeEventListener("message",this.boundHandleMessage),this.onRateLimited&&this.onRateLimited();return}let n=a.qV.safeParse(e.data);if(!n.success){console.warn("Received message does not conform to basic message shape, ignoring");return}let r=n.data;if("response"===r.channel){this.handleResponse(r);return}this.messageQueue.push(r),this.isConsumerRunning||this.consumeMessages()}handleResponse(e){let t=this.inFlightRequests.get(e.requestId);if(void 0!==t){t(e);return}}async consumeMessages(){for(this.isConsumerRunning=!0;this.messageQueue.length>0;){let e=this.messageQueue.shift();if(!e)continue;let t=r.P2.safeParse(e.method);if(!t.success){this.sendErrorResponse(e.requestId,400,"Unknown method");continue}let n=t.data,i=r.q2.safeParse(e);if(!i.success){this.sendErrorResponse(e.requestId,400,"Invalid payload content");continue}let s=i.data;if(this.requestLog.has(s.requestId)){this.sendErrorResponse(s.requestId,400,"Request ID already processed");continue}this.requestLog.set(s.requestId,{message:s,requestTimestamp:new Date().getTime()}),await this.onPermissionRequested(n).then(e=>"denied"!==e||(this.sendErrorResponse(s.requestId,403,"Permission denied"),!1)).catch(e=>(console.error("Error checking permission for method ".concat(n,":"),e),this.sendErrorResponse(s.requestId,500,"Error checking permission"),!1))&&(this.onCapabilityAction(s,this.boundSendRequest).then(e=>{var t;let n=a.Tk.parse({channel:"response",status:200,requestId:s.requestId,payload:null!=e?e:a.D7}),r=this.requestLog.get(s.requestId);r&&this.requestLog.set(s.requestId,{...r,response:n,responseTimestamp:new Date().getTime()}),null===(t=this.iframe.contentWindow)||void 0===t||t.postMessage(n,this.allowedOrigin)}).catch(e=>{console.error("Error processing action for method ".concat(n,":"),e),this.sendErrorResponse(s.requestId,500,"Internal server error while processing action")}),await new Promise(e=>requestAnimationFrame(e)))}this.isConsumerRunning=!1}sendErrorResponse(e,t,n){var r;let i=a.Kj.parse({channel:"response",status:t,requestId:e,payload:(0,a.v8)(n)}),s=this.requestLog.get(e);s&&this.requestLog.set(e,{...s,response:i,responseTimestamp:new Date().getTime()}),null===(r=this.iframe.contentWindow)||void 0===r||r.postMessage(i,this.allowedOrigin)}restartListening(){window.removeEventListener("message",this.boundHandleMessage),this.messageCount=0,this.lastResetTime=Date.now(),this.setupMessageListener()}cleanup(){window.removeEventListener("message",this.boundHandleMessage),this.messageQueue=[],this.isConsumerRunning=!1,this.inFlightRequests.clear()}async sendRequest(e,t){return new Promise((n,i)=>{var s;let o=Date.now().toString();this.inFlightRequests.set(o,e=>{let t=a.Tk.safeParse(e);if(!t.success){i(Error("Invalid response format"));return}let s=t.data;if(s.status>=400){let e=a.Kj.safeParse(s);e.success?i(Error(e.data.payload.error)):i(Error("Error response (".concat(s.status,")")));return}let o=r.O3.safeParse(s);o.success?n(o.data.payload):i(Error("Invalid response payload for the method"))}),null===(s=this.iframe.contentWindow)||void 0===s||s.postMessage({channel:"request",method:e,requestId:o,payload:t},this.allowedOrigin)})}getRequestLog(){return Array.from(this.requestLog.entries()).map(e=>{let[t,n]=e;return{...n,requestId:t}}).sort((e,t)=>t.requestTimestamp-e.requestTimestamp)}constructor({iframe:e,allowedOrigin:t,onRateLimited:n=null,onPermissionRequested:r,onCapabilityAction:a}){this.messageCount=0,this.lastResetTime=Date.now(),this.MAX_MESSAGES_PER_INTERVAL=30,this.RESET_INTERVAL=5e3,this.onRateLimited=null,this.messageQueue=[],this.isConsumerRunning=!1,this.requestLog=new Map,this.inFlightRequests=new Map,this.iframe=e,this.allowedOrigin=t,this.onRateLimited=n,this.onPermissionRequested=r,this.onCapabilityAction=a,this.boundHandleMessage=this.handleMessage.bind(this),this.boundSendRequest=this.sendRequest.bind(this),this.setupMessageListener()}}},37199:function(e,t,n){"use strict";n.d(t,{i:function(){return c},s:function(){return l}});var r=n(5362),a=n(53949),i=n(7653);function s(e,t){let n=new Map;return t.forEach(e=>{var t;let r=null!==(t=n.get(e.file_name))&&void 0!==t?t:[];r.push(e),n.set(e.file_name,r)}),n.get(e)}function o(e,t){let n=s(e,t);if(!n||0===n.length)return null;let r=n.filter(e=>e.file_kind===a.Y.Blob);return 0===r.length?null:r[r.length-1]}function l(){let e=function(){let e=(0,r.OJ)();return(0,i.useCallback)(async(t,n,r,a,i)=>{let s=o(t,n);if(!s&&a){let n=await e("/api/organizations/".concat(r,"/projects/").concat(a,"/docs")),i=(await n.json()).find(e=>e.file_name===t);if(i)return new TextEncoder().encode(i.content);let l=await e("/api/organizations/".concat(r,"/projects/").concat(a,"/files"));s=o(t,await l.json())}if(!s)return null;try{let e=i?"/api/organizations/".concat(r,"/shared_artifact/").concat(i,"/files/").concat(s.file_uuid,"/contents"):"/api/organizations/".concat(r,"/files/").concat(s.file_uuid,"/contents"),t=await fetch(e);if(!t.ok)return null;let n=await t.arrayBuffer();if(n.byteLength>2147483648-1)return null;return new Uint8Array(n)}catch(e){return null}},[e])}();return(0,i.useCallback)(async(t,n,r,a,i,o)=>a?function(e,t){let n=s(e,t);if(!n||0===n.length)return null;let r=n[n.length-1];if(r){let e=r.extracted_content;return new TextEncoder().encode(e)}return null}(t,n)||await e(t,r,a,i,o):null,[e])}function c(e,t,n,r,a){let s=l();return(0,i.useCallback)(async i=>s(i,e,t,n,r,a),[e,t,n,r,s,a])}},84165:function(e,t,n){"use strict";n.d(t,{C:function(){return r}});let r=async(e,t,n)=>{if(!t||!n)return Promise.resolve(null);try{let t=await fetch("/api/artifacts/api/".concat(n,"/completion"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e})});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let r=await t.json();return Promise.resolve(r.completion)}catch(e){return Promise.reject(e)}}},51789:function(e,t,n){"use strict";n.d(t,{r:function(){return i},y:function(){return a}});var r=n(7653);let a=(0,r.createContext)(null),i=()=>{let e=(0,r.useContext)(a);if(!e)throw Error("useReplExecutionSandbox must be used within a ReplExecutionSandboxContextProvider");return e}},56323:function(e,t,n){"use strict";n.d(t,{ReplExecutionSandboxProvider:function(){return m}});var r=n(27573),a=n(8571),i=n(27218),s=n(14448),o=n(7653),l=n(93558),c=n(17796),u=n(37199),d=n(84165),p=n(51789);let m=e=>{let{children:t}=e,{userContentRendererUrl:n}=(0,a.m)(),{activeOrganization:l}=(0,i.t)(),c=null==l?void 0:l.uuid,{value:m}=(0,s.F)("apps_use_turmeric"),g=(0,o.useRef)({conversationUuid:"",conversationData:{conversationFiles:[],allAttachments:[]}}),h=(0,o.useCallback)((e,t)=>{g.current={conversationUuid:e,conversationData:t}},[]),v=(0,u.s)(),b=(0,o.useCallback)(async e=>{let t="function"==typeof g.current.conversationData?await g.current.conversationData(g.current.conversationUuid):g.current.conversationData;return v(e,t.allAttachments,t.conversationFiles,null==l?void 0:l.uuid,t.projectUuid)},[v,g,l]),x=(0,o.useCallback)(e=>(0,d.C)(e,m,c),[m,c]);return(0,r.jsx)(p.y.Provider,{value:{executeReplCode:e=>f({code:e,iframeSrc:n,getFile:b,claudeCompleteCallback:x}),setSandboxState:h},children:t})};async function f(e){let t,{code:n,iframeSrc:r,getFile:a,claudeCompleteCallback:i}=e,s=document.createElement("iframe");s.sandbox.add("allow-scripts","allow-same-origin"),s.title="Claude content",s.src=r,s.className="absolute h-0 w-0 opacity-0 pointer-events-none",document.body.appendChild(s);let o=new Promise(e=>{t=e}),u=new c.i({iframe:s,allowedOrigin:r,onRateLimited:null,onPermissionRequested:()=>Promise.resolve("accepted"),onCapabilityAction:async e=>{if(e.method===l.to.ReadyForContent)return t(),Promise.resolve();if(e.method===l.to.GetFile)return Promise.resolve({"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.GetFileResponse",value:await a(e.payload.key)});if(e.method===l.to.ClaudeCompletion)try{let t=await i(e.payload.prompt);return Promise.resolve({"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionResponse",completion:t})}catch(e){return Promise.resolve({"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.ClaudeCompletionResponse",completion:null})}else if(e.method===l.to.BroadcastContentSize)return Promise.resolve();else if(e.method===l.to.ReportError)return Promise.resolve();return Promise.reject()}});await Promise.race([o,new Promise((e,t)=>setTimeout(()=>t(Error("Execution Sandbox timed out")),1e4))]);let d=await u.sendRequest(l.to.RunCode,{code:n,"@type":"type.googleapis.com/anthropic.claude.usercontent.sandbox.RunCodeRequest"});u.cleanup(),document.body.removeChild(s);let p=l.vr.safeParse(d);return p.success?p.data:{status:"error",logs:[],error:"Invalid execution result: "+p.error.message}}},26525:function(e,t,n){"use strict";n.d(t,{Ch:function(){return E},H0:function(){return H},Qe:function(){return z},Qt:function(){return T},Vn:function(){return L},kR:function(){return O},kS:function(){return R},sE:function(){return N}});var r=n(56683),a=n(27573),i=n(5161),s=n(99205),o=n(76220),l=n(3053),c=n(5362),u=n(27218),d=n(27895),p=n(9788),m=n(32737),f=n(30947),g=n(6385),h=n(14448),v=n(35807),b=n(42755),x=n(45144),_=n(7653),y=n(45790),w=n(40950),j=n(41496),C=n(1804);function k(){let e=(0,r._)(["\n  relative\n  grid\n  place-content-center\n  aspect-square\n  rounded-xl\n  cursor-pointer\n  transition\n  [fieldset:not(:disabled)_&]:hover:bg-bg-400\n  [fieldset:disabled_&]:opacity-50\n  [fieldset:disabled_&]:pointer-events-none\n  focus-within:ring\n  inline-flex\n  items-center\n  justify-center\n  relative\n  shrink-0\n  ring-offset-2\n  ring-offset-bg-300\n  ring-accent-main-100\n  focus-visible:outline-none\n  focus-visible:ring-1\n  disabled:pointer-events-none\n  disabled:opacity-50\n  disabled:shadow-none\n  disabled:drop-shadow-none\n  bg-[radial-gradient(ellipse,_var(--tw-gradient-stops))]\n  from-bg-500/10\n  from-50%\n  to-bg-500/30\n  border-0.5\n  border-border-400\n  font-medium\n  font-styrene\n  text-text-100/90\n  transition-colors\n  active:bg-bg-500/50\n  hover:text-text-000\n  hover:bg-bg-500/60\n"]);return k=function(){return e},e}let z=20,S=e=>{let t=e.webkitRelativePath;return t&&t.length?t:e.name},P=e=>e.replace(/\0/g,""),E=e=>{e.extracted_content=P(e.extracted_content),e.file_name=P(e.file_name),e.file_type=P(e.file_type)},M=(e,t)=>({file_name:S(e),file_type:e.type,file_size:e.size,extracted_content:t});function R(e){let{syncSourceCount:t,files:n,setFiles:r,numExistingConversationFiles:l,attachments:c,setAttachments:u,numExistingConversationAttachments:p,detectSameAttachment:f,selectedModel:g,blobFileUploadsEnabled:h}=e,{addError:x}=(0,d.e)(),j=(0,s.wN)(null);return function(e){let{syncSourceCount:t,onUploadComplete:n,existingAttachments:r,existingFiles:s,numExistingConversationAttachments:l,numExistingConversationFiles:c,selectedModel:u,blobFileUploadsEnabled:d}=e,p=(0,w.Z)(),{modelConfig:f}=(0,o.V)(u),g=f.image_in,[h,x,j,C]=function(e){let{syncSourceCount:t,onUploadComplete:n,existingAttachments:r,existingFiles:s,numExistingConversationFiles:l,selectedModel:c,blobFileUploadsEnabled:u}=e,{modelConfig:d}=(0,o.V)(c),p=d.image_in,m=H()&&d.pdf_in,{isUploading:f,setIsUploading:g,handleError:h,handleUpload:v}=L({imagesEnabled:p,blobFileUploadsEnabled:u,onUploadComplete:n,rasterizePdfUploadsEnabled:m}),b=(0,_.useCallback)(e=>{let{newUploads:n=[],numNewPotentialAttachments:o=0,showError:c=!0}=e,{attachmentUploads:d,imageUploads:f,rasterizedDocumentUploads:g,outOfContextFileUploads:v}=(0,i.Jw)(n,{imagesEnabled:p,blobFileUploadsEnabled:u,rasterizePdfUploadsEnabled:m});return o+d.length+f.length+g.length+v.length+r.length+s.length+t>z?(c&&h((0,a.jsx)(y.Z,{defaultMessage:"You can add at most {count, plural, one {# attachment} other {# attachments}} to a message. Please select fewer attachments.",id:"qxV00r12ob",values:{count:z}})),!1):!(f.length+g.length+s.length+l>100)||(c&&h((0,a.jsx)(y.Z,{defaultMessage:"You can add at most {count, plural, one {# file} other {# files}} to a chat. {suggestion}",id:"QyrCxYs5Jy",values:{count:100,suggestion:l<100?(0,a.jsx)(y.Z,{defaultMessage:"Please select fewer files.",id:"a6KVizNPLe"}):(0,a.jsx)(y.Z,{defaultMessage:"Please consider starting a new chat.",id:"oN0R8lZNm6"})}})),!1)},[p,u,r,s,t,l,h,m]);return[f,g,b,(0,_.useCallback)(async e=>{b({newUploads:e})&&await v(e)},[v,b])]}({syncSourceCount:t,onUploadComplete:n,existingFiles:s,existingAttachments:r,numExistingConversationFiles:c,numExistingConversationAttachments:l,selectedModel:u,blobFileUploadsEnabled:d}),k=function(e){let{handleUpload:t}=e,n=(0,_.useRef)({ready:0,total:0}),r=(0,_.useCallback)(()=>{n.current={ready:0,total:0}},[]),a=(0,_.useCallback)(e=>{n.current.ready+=1,n.current.total===n.current.ready&&t(e)},[t]),i=(0,_.useCallback)((e,t)=>{let r=e.length;for(let s=0;s<r;s++){let r=e[s];r.isFile?(n.current.total+=1,r.file(e=>{t.push(e),a(t)})):r.isDirectory&&r.createReader().readEntries(e=>{i(e,t)})}},[a]);return(0,_.useCallback)(e=>{var t;e.preventDefault(),r();let n=null===(t=e.dataTransfer)||void 0===t?void 0:t.items;if(!n)return;let a=[];for(let e=0;e<(null==n?void 0:n.length);e++){let t=n[e].webkitGetAsEntry();t&&a.push(t)}i(a,[])},[i,r])}({handleUpload:C}),S=(0,_.useCallback)(async e=>{await C(Array.from(e))},[C]),P=(0,_.useCallback)(async e=>{let t=e.target.files;t&&await C(Array.from(t)),e.target.value=""},[C]),E=g?(0,a.jsx)(y.Z,{defaultMessage:"Upload docs or images to Claude\\n(Max {maxUploads}, 30mb each)",id:"ialBpH0eLp",values:{maxUploads:z}}):(0,a.jsx)(y.Z,{defaultMessage:"Add content ({maxUploads} max, 30mb each)\\nAccepts pdf, txt, csv, etc.",id:"70asB5brJP",values:{maxUploads:z}}),M=(0,_.useCallback)(e=>{P(e)},[P]),R=(0,_.useMemo)(()=>({type:"file",accept:(0,i.tB)({imagesEnabled:g,outOfContextFilesEnabled:d}).join(","),onChange:M,multiple:!0,"aria-label":p.formatMessage({defaultMessage:"Upload files",id:"2jjdVzJX0c"})}),[d,g,M,p]);return[h,x,j,k,S,C,(0,a.jsx)(m.u,{className:"text-center",tooltipContent:E,children:(0,a.jsxs)(F,{children:[(0,a.jsx)("input",{"data-testid":"file-upload",className:"absolute inset-0 -z-10 overflow-hidden rounded-xl opacity-0",...R}),h?(0,a.jsx)(v.U,{className:"animate-spin",size:18}):(0,a.jsx)(b.p,{size:18,weight:"light"})]})},"upload-tooltip"),R]}({syncSourceCount:t,onUploadComplete:(0,_.useCallback)(async(e,t)=>{f&&e.find(e=>f(e.extracted_content))&&x((0,a.jsx)(y.Z,{defaultMessage:"The same attachment was already added earlier. Claude sees the full conversation when replying, so there’s no need to re-upload.",id:"Uo3qvhGn7z"})),u(t=>[...t,...e]),r(e=>[...e,...t])},[u,f,x,r]),existingAttachments:c,existingFiles:n,numExistingConversationAttachments:p,numExistingConversationFiles:l,selectedModel:null!=g?g:j,blobFileUploadsEnabled:h})}let q=async(e,t,n)=>{let r=new Image;if(r.src=URL.createObjectURL(e),await new Promise(e=>{r.onload=e,setTimeout(e,250)}),!r.width||!r.height||"image/png"!==e.type&&r.width<=t&&r.height<=n)return e;let a=r.width/r.height,i=r.width,s=r.height;r.width>t&&(s=(i=t)/a),s>n&&(i=(s=n)*a);let o=document.createElement("canvas");o.style.display="none",document.body.appendChild(o);let l=o.getContext("2d");if(!l||!l.imageSmoothingQuality)return document.body.removeChild(o),e;l.imageSmoothingQuality="high",o.width=i,o.height=s,l.drawImage(r,0,0,i,s);let c=await new Promise(e=>{o.toBlob(t=>e(t),"image/webp",.85)});return(document.body.removeChild(o),c)?new File([c],e.name,{type:"image/webp"}):e},N=()=>{let{value:e}=(0,h.F)("use_canvas_resize");return(0,_.useCallback)(async t=>e?await q(t,2e3,2e3):t,[e])},T=()=>{let e=D(),t=O();return async(n,r,a)=>{try{return await t(n,r,a)}catch(t){e(t,(0,i.Wr)(n)?"image":"file")}}},Z=e=>{let t=D(),n=O(),r=(0,c.OJ)();return async(a,i,s)=>{let o={};try{o.file=await n(a,i,s)}catch(n){n instanceof f.Hx&&"document_too_many_pages"===n.errorCode?o.attachment=await I(r,a,i,e):t(n,"document")}return o}},O=()=>{let e=(0,c.OJ)();return async(t,n,r)=>{let a=new FormData;a.append("file",t);let i=r?"/api/organizations/".concat(n,"/projects/").concat(r,"/upload"):"/api/".concat(n,"/upload"),s=await e(i,{method:"POST",body:a}),o=await s.json();if(s.ok)return o;throw(0,f.fT)(s.status,o,"File upload failed",s.headers)}},A=async(e,t,n,r)=>{let i;let s=new FormData;s.append("file",t);try{i=await e("/api/organizations/".concat(n,"/convert_document"),{method:"POST",body:s})}catch(e){return(0,x.Tb)(e),r((0,a.jsx)(y.Z,{defaultMessage:"One or more file uploads have failed. Please try again.",id:"FIwcYDeyIC"}))}if(429===i.status)return r((0,a.jsx)(y.Z,{defaultMessage:"Too many file upload attempts. Please wait and try again later.",id:"h/6cMHSn1V"}));if(413===i.status)return r((0,a.jsx)(y.Z,{defaultMessage:"Uploaded file is too large. Try uploading a smaller part of the document, or copy/pasting an excerpt from the file.",id:"/mCoauj3Ws"}));if(!i.ok)return r((0,a.jsx)(y.Z,{defaultMessage:"Text extraction failed for one of the uploaded files. Please try again.",id:"ngYPeW4aWf"}));let o=await i.json();return o&&0!==Object.keys(o).length&&o.extracted_content?(o.file_name=S(t),o):r((0,a.jsx)(y.Z,{defaultMessage:"Text extraction failed for one of the uploaded files. Please try again.",id:"ngYPeW4aWf"}))},L=e=>{var t;let{imagesEnabled:n,blobFileUploadsEnabled:r,rasterizePdfUploadsEnabled:s,onUploadComplete:o,onError:p,projectUuid:m}=e,{activeOrganization:f}=(0,u.t)(),h=null!==(t=null==f?void 0:f.uuid)&&void 0!==t?t:"",[v,b]=(0,_.useState)(!1),{addError:x}=(0,d.e)(),w=(0,_.useCallback)(e=>{b(!1),x(e),p&&p()},[x,p,b]),C=N(),k=T(),z=Z(w),S=(0,c.OJ)(),P=(0,l.z$)(),E=(0,_.useCallback)(async(e,t)=>{let{attachmentUploads:l,imageUploads:c,rasterizedDocumentUploads:u,unsupportedUploads:d,tooLargeUploads:p,outOfContextFileUploads:f}=(0,i.Jw)(e,{imagesEnabled:n,blobFileUploadsEnabled:r,rasterizePdfUploadsEnabled:s}),v=d.map(e=>(0,i.mD)(e.name));if(v.length>0)return w((0,a.jsx)(y.Z,{defaultMessage:"Files of the following {count, plural, one {format is} other {formats are}} not supported: {formats}",id:"9JcWWPAZoB",values:{count:v.length,formats:(0,a.jsx)(j.yX,{type:"conjunction",value:[...new Set(v)]})}}));if(p.length>0)return P.track({event_key:"file_upload_too_large",large_files:p.map(e=>({file_type:e.type,file_size:e.size}))}),w((0,a.jsx)(y.Z,{defaultMessage:"You may not upload files larger than 30mb.",id:"Gr/GvEZTX1"}));b(!0);let x=[],_=0,E=(e,n)=>{if(void 0===t)return!0;let r=new Blob([n]).size/g.r6;return!((_+=r)>t)||(w((0,a.jsx)(y.Z,{defaultMessage:"Failed to upload file {filename}. Project knowledge exceeds maximum. Remove files to continue.",id:"MbvF6K3PJd",values:{filename:e}})),!1)};for(let e of l){let t=await I(S,e,h,w);if(!(t&&E(e.name,t.extracted_content)))return;await o([t],[])}for(let e of c){let t=await C(e),n=await k(t,h,m);n&&x.push(n)}for(let e of u){let{file:t,attachment:n}=await z(e,h,m);if(t&&x.push(t),n){if(!E(e.name,n.extracted_content))return;await o([n],[])}}for(let e of f){let t=await k(e,h,m);t&&x.push(t)}await o([],x),b(!1)},[r,S,o,n,w,k,z,h,C,P,s,m]);return{isUploading:v,setIsUploading:b,handleError:w,handleUpload:E}},F=p.q.label(k()),I=async(e,t,n,r)=>{if(!(0,i.dF)(t))try{let e=await (0,i.CL)(t);return M(t,e)}catch(e){}let a=await A(e,t,n,r);if(a)return E(a),a},D=()=>{let{addError:e}=(0,d.e)();return(0,_.useCallback)((t,n)=>{if(t instanceof f.Hx){if("rate_limit_error"===t.type){e((0,a.jsxs)("p",{children:[(0,a.jsx)(y.Z,{defaultMessage:"You’ve reached your limit for {resourceType} uploads. Please try again later.",id:"M88GeEDB7r",values:{resourceType:n}}),(0,a.jsx)(C.o,{})]}));return}if("document_password_protected"===t.errorCode){e((0,a.jsx)("p",{children:(0,a.jsx)(y.Z,{defaultMessage:"The document you uploaded is password protected. Please remove the password and try again.",id:"DHwwKwuaCR"})}));return}}if(t instanceof f.Hx&&"Image is too large"===t.message){e((0,a.jsx)("p",{children:(0,a.jsx)(y.Z,{defaultMessage:"Image is too large. <link>Learn more</link>",id:"SFbt+MAvby",values:{link:e=>(0,a.jsx)("a",{href:"https://support.anthropic.com/en/articles/9002500-what-kinds-of-images-can-i-upload-to-claude-ai",target:"_blank",rel:"noopener noreferrer",className:"underline hover:text-accent-main-100",children:e})}})}));return}e((0,a.jsx)("p",{children:(0,a.jsx)(y.Z,{defaultMessage:"Your {resourceType} upload failed. Please try again.",id:"ymWsV10Pz1",values:{resourceType:n}})}))},[e])},H=()=>{let{value:e}=(0,h.F)("janus_claude-ai");return e}},5161:function(e,t,n){"use strict";n.d(t,{CL:function(){return E},Jw:function(){return z},MR:function(){return v},Wr:function(){return j},dF:function(){return S},eh:function(){return M},mD:function(){return y},tB:function(){return b},wI:function(){return u}});var r=n(27218),a=n(1812),i=n(14448),s=n(18916);let o=[".DS_Store"],l=["jpg","jpeg","png","gif","webp"],c=["image/jpeg","image/png","image/gif","image/webp"],u={"image/jpeg":"jpg","image/png":"png","image/gif":"gif","image/webp":"webp"},d=["bmp","ico","tiff","tif","psd","raw","cr2","nef","orf","sr2","mobi","jp2","jpx","jpm","mj2","svg","svgz","ai","eps","ps","indd","heic","mp4","mov","avi","mkv","wmv","flv","webm","mpeg","mpg","m4v","3gp","ogv","ogg","rm","rmvb","asf","amv","mpe","m1v","m2v","svi","3g2","roq","nsv","f4v","f4p","f4a","f4b","qt","hdmov","divx","div","m2ts","mts","vob","mp3","wav","wma","aac","flac","alac","aiff","ogg","opus","m4a","amr","awb","wmv","ra","rm","mid","midi","mka","ttf","otf","woff","woff2","eot","sfnt","ttc","suit","zip"],p=["xls","xlsx","xlsb","xlm","xlsm","xlt","xltm","xltx","ods"],m=["txt","py","ipynb","js","jsx","html","css","java","cs","php","c","cc","cpp","cxx","cts","h","hh","hpp","rs","R","Rmd","swift","go","rb","kt","kts","ts","tsx","m","mm","mts","scala","rs","dart","lua","pl","pm","t","sh","bash","zsh","csv","log","ini","cfg","config","json","proto","yaml","yml","toml","lua","sql","bat","md","coffee","tex","latex","gd","gdshader","tres","tscn"],f=["docx","rtf","epub","odt","odp","pdf"],g=["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/rtf","application/epub+zip","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.presentation","application/pdf"],h=["csv"],v=3e7,b=e=>{let{imagesEnabled:t=!1,outOfContextFilesEnabled:n=!1}=e;return[".pdf",".doc",".docx",".rtf",".epub",".odt",".odp",".pptx",..._(),...t?x():[],...n?p.map(e=>".".concat(e)):[]]},x=()=>l.map(e=>".".concat(e)),_=()=>m.map(e=>".".concat(e)),y=e=>e.split(".").pop().toLowerCase(),w=(e,t)=>{let{imagesEnabled:n=!1,blobFileUploadsEnabled:r=!1}=t,a=y(e.name);return!!(!n&&(l.includes(a)||c.includes(e.type)))||(r?d.includes(a):[...d,...p].includes(a))},j=e=>{let t=y(e.name);return l.includes(t)||c.includes(e.type)},C=e=>[...h,...p].includes(y(e.name)),k=e=>!!o.includes(e.name),z=(e,t)=>{let{imagesEnabled:n=!1,blobFileUploadsEnabled:r=!1,rasterizePdfUploadsEnabled:a}=t,i=[],s=[],o=[],l=[],c=[],u=[];return e.filter(e=>!k(e)).forEach(e=>{let t=n&&j(e);w(e,{imagesEnabled:n,blobFileUploadsEnabled:r})?c.push(e):e.size>v?u.push(e):r&&C(e)?o.push(e):a&&("pdf"===y(e.name)||"application/pdf"===e.type)?l.push(e):S(e)?i.push(e):t?s.push(e):i.push(e)}),{attachmentUploads:i,imageUploads:s,rasterizedDocumentUploads:l,unsupportedUploads:c,tooLargeUploads:u,outOfContextFileUploads:o}},S=e=>{let t=y(e.name);return f.includes(t)||g.includes(e.type)},P=e=>{let t=new Uint8Array(e);if(t.length>=4){if(239===t[0]&&187===t[1]&&191===t[2])return"utf-8";if(254===t[0]&&255===t[1]||255===t[0]&&254===t[1])return"utf-16";if(0===t[0]&&0===t[1]&&254===t[2]&&255===t[3]||255===t[0]&&254===t[1]&&0===t[2]&&0===t[3])return"utf-32"}let n=!0,r=Math.min(t.length-1,1e3);for(let e=0;e<r;e+=2)if(0===t[e]&&0===t[e+1]||0!==t[e]&&0!==t[e+1]){n=!1;break}if(n)return"utf-16";let a=!0,i=!1;for(let e=0;e<t.length;e++){let n=t[e];if(n>127){if(i=!0,(224&n)==192){if(e+1>=t.length||(192&t[e+1])!=128){a=!1;break}e+=1}else if((240&n)==224){if(e+2>=t.length||(192&t[e+1])!=128||(192&t[e+2])!=128){a=!1;break}e+=2}else if((248&n)==240){if(e+3>=t.length||(192&t[e+1])!=128||(192&t[e+2])!=128||(192&t[e+3])!=128){a=!1;break}e+=3}else{a=!1;break}}}if(a&&i)return"utf-8";let s=(e=>{let t={ascii:0,nonAscii:0,cyrillic:0,latin1:0,control:0,total:Math.min(e.length,4e3)};for(let n=0;n<t.total;n++){let r=e[n];r<32?t.control++:r<127?t.ascii++:r>127&&(t.nonAscii++,(r>=192&&r<=255||r>=168&&r<=183)&&t.cyrillic++,(r>=128&&r<=159||r>=160&&r<=255)&&t.latin1++)}return t})(t),o=s.cyrillic/s.total*100,l=s.latin1/s.total*100,c=s.ascii/s.total*100;if(s.nonAscii>0){if(o>.3)return"windows-1251";if(l>s.control/s.total*100)return"windows-1252"}return c>90&&s.control/s.total<.1?"ascii":"utf-8"},E=async e=>{let t=await e.arrayBuffer(),n=P(t);try{return new TextDecoder(n,{fatal:!0}).decode(t)}catch(e){if("utf-8"!==n)return new TextDecoder("utf-8",{fatal:!0}).decode(t)}throw Error("Failed to decode file as plain text")};function M(){var e;let{value:t}=(0,i.F)("ooc_attachments"),{account:n}=(0,r.t)(),o=(0,a.T)(),l=null!==(e=null!=o?o:null==n?void 0:n.settings)&&void 0!==e?e:{},{value:c}=(0,s.h)(l);return!!t&&c}},76986:function(e,t,n){"use strict";n.d(t,{McpProvider:function(){return g},c:function(){return f}});var r=n(27573);n(95622);var a=n(3053),i=n(27895),s=n(40223),o=n(65654),l=n(7653),c=n(5362),u=n(27218);o.kO.shape.params;var d=n(75140);class p{start(){return this._port.onmessage=e=>{var t;null===(t=this.onmessage)||void 0===t||t.call(this,e.data)},this._port.onmessageerror=e=>{var t;null===(t=this.onerror)||void 0===t||t.call(this,Error("MessagePort message error: ".concat(e.data)))},Promise.resolve()}close(){var e;return this._port.close(),null===(e=this.onclose)||void 0===e||e.call(this),Promise.resolve()}send(e){return this._port.postMessage(e),Promise.resolve()}constructor(e){this._port=e}}let m=(0,l.createContext)({mcpClients:{},mcpTools:[],isLoading:!1,isEnabled:!1}),f=()=>{let e=(0,l.useContext)(m);return e||console.warn("useMcp being used outside of McpProvider, this is probably a bug"),e};function g(e){let{children:t}=e,[n,s]=(0,l.useState)({}),[p,f]=(0,l.useState)(!1),{addError:g}=(0,i.e)(),{tools:v,onDolphinInterrupted:b}=function(e){let{mcpClients:t}=e,[n,r]=(0,l.useState)([]),s=(0,l.useRef)(!1),c=(0,l.useRef)(null),u=(0,l.useRef)(!1),{addError:p}=(0,i.e)(),{track:m}=(0,a.z$)(),f=(0,l.useCallback)(async(e,n,r)=>{if("auto-dolphin"===e){let e=u.current?"Claude would like to run the tool “".concat(n.name,"” but was previously interrupted. Do you want to allow this?"):"Claude would like to run the tool “".concat(n.name,"”. Do you want to allow this?");if(s.current&&!confirm(e))return m({event_key:"mcp.tools.called",result:"disallowed",argument_count:Object.keys(r).length}),u.current=!0,{content:[{type:"text",text:"The user has chosen to disallow the tool call."}],isError:!0};c.current=Date.now(),s.current=!1}try{let a=await t[e].callTool({name:n.name,arguments:r},o.M$,{timeout:24e4});if(m({event_key:"mcp.tools.called",result:"success",argument_count:Object.keys(r).length}),"content"in a)return a;return{content:[{type:"text",text:JSON.stringify(a.toolResult,null,2)}],isError:!1}}catch(e){throw p("Failed to call tool ".concat(n.name,": ").concat(e)),m({event_key:"mcp.tools.called",result:"failure",argument_count:Object.keys(r).length}),e}},[p,t,m]);return(0,l.useEffect)(()=>{let e=!1;return async function(){if(!t)return;let n=await (0,d.k)(t,{method:"tools/list",params:{}},o.Gd);e||r(Object.entries(n).map(e=>{var t;let[n,r]=e;return m({event_key:"mcp.tools.listed",tool_count_on_server:r.tools.length}),null!==(t=r.tools.map(e=>({...e,serverName:n,callback:t=>f(n,e,t)})))&&void 0!==t?t:[]}).flat())}(),()=>{e=!0}},[f,t,m]),{tools:n,onDolphinInterrupted:(0,l.useCallback)(()=>{if("number"==typeof c.current&&Date.now()<c.current+100)return},[c])}}({mcpClients:n}),{track:x}=(0,a.z$)();!function(e){let{mcpClients:t}=e,{addError:n}=(0,i.e)(),{track:r}=(0,a.z$)(),{activeOrganization:s}=(0,u.t)(),o=(0,c.uC)("/api/organizations/".concat(null==s?void 0:s.uuid,"/dust/mcp_messages"),"POST",{enabled:!1,onError:()=>n("MCP sampling failed")});(0,l.useEffect)(()=>{},[!1,t,o,r])}({mcpClients:n});let _=(0,l.useCallback)(e=>{if(e.source!==window)return;let{type:t,serverName:n}=e.data;if("mcp-server-connected"!==t||!n)return;let[r]=e.ports;h(r).then(e=>{console.log("Connected to MCP server ".concat(n)),s(t=>({...null!=t?t:{},[n]:e})),"dolphin"===n&&e.setNotificationHandler(o.b5,e=>{"USER_INTERRUPT"===e.params.data&&b()}),x({event_key:"mcp.servers.connected",result:"success"})},e=>{console.error("Could not attach to MCP server ".concat(n,":"),e),g("Could not attach to MCP server ".concat(n)),x({event_key:"mcp.servers.connected",result:"failure"})})},[g,x,b]);(0,l.useEffect)(()=>{window.addEventListener("message",_);let e=!1;async function t(){var t,n;if(!(null===(t=window.claudeAppBindings)||void 0===t?void 0:t.listMcpServers)||!(null===(n=window.claudeAppBindings)||void 0===n?void 0:n.connectToMcpServer))return;let r=await window.claudeAppBindings.listMcpServers();x({event_key:"mcp.servers.listed",server_count:r.length}),e||await Promise.all(r.map(e=>window.claudeAppBindings.connectToMcpServer(e).catch(t=>{console.error("Could not connect to MCP server ".concat(e,":"),t),g("Could not connect to MCP server ".concat(e))})))}return f(!0),t().finally(()=>f(!1)),()=>{e=!0,window.removeEventListener("message",_)}},[_,g,x]);let y=(0,l.useMemo)(()=>Object.keys(n).length>0,[n]);return(0,r.jsx)(m.Provider,{value:{mcpClients:n,mcpTools:v,isLoading:p,isEnabled:y},children:t})}async function h(e){let t=new p(e),n=new s.K({name:"claude-ai",version:"0.1.0"},{capabilities:{}});return await t.start(),await n.connect(t),n}},75140:function(e,t,n){"use strict";n.d(t,{k:function(){return a}});var r=n(65654);async function a(e,t,n,a){return e?Object.fromEntries((await Promise.all(Object.entries(e).map(async e=>{let[i,s]=e;try{let e=await s.request(t,n,{timeout:5e3,...a});return[i,e]}catch(e){if(e instanceof r.yp&&e.code===r.jK.MethodNotFound)return null;return console.error("Request ".concat(t.method," to ").concat(i," failed: ").concat(e)),null}}))).filter(e=>null!==e)):{}}},18916:function(e,t,n){"use strict";n.d(t,{h:function(){return i}});var r=n(14448),a=n(15992);function i(e){let t=function(){let{layer:e}=(0,a.useLayer)("frontend");return e.get("analysis_tool_experiment_enabled",!1)}(),{value:n}=(0,r.F)("rely_on_analysis_flag"),{value:i}=(0,r.F)("analysis_tool_launch_ga");if(n){var s;return{value:null!==(s=null==e?void 0:e.enabled_artifacts_attachments)&&void 0!==s?s:i,source:"flag"}}return{value:(null==e?void 0:e.enabled_artifacts_attachments)||t,source:"experiment"}}},76220:function(e,t,n){"use strict";n.d(t,{V:function(){return c},u:function(){return u}});var r=n(5362),a=n(27218),i=n(14448),s=n(13262),o=n(15992);let l={image_in:!1,pdf_in:!1},c=e=>{let{activeOrganization:t}=(0,a.t)(),n=null==t?void 0:t.uuid,{data:o,isLoading:c,isPlaceholderData:u}=(0,r.WE)("/api/organizations/".concat(n,"/model_configs/").concat(e),{queryKey:[s.Qn,n,e],enabled:!!n,staleTime:3e5,meta:{noToast:!0}}),d=!u&&o||l,{value:p}=(0,i.F)("claude_ai_cinnamon");return"claude-3-5-haiku-20241022"===e&&p&&(d={image_in:!0,pdf_in:!1}),{isLoading:c,modelConfig:d}},u=()=>{let{config:e}=(0,o.useConfig)("claude_ai_models"),t=e.get("models",[]),n=t.filter(e=>!e.inactive),r=n.filter(e=>!e.overflow),a=n.filter(e=>e.overflow);return{allModelOptions:t,activeModelOptions:n,mainModels:r,overflowModels:a}}},15281:function(e,t,n){"use strict";n.d(t,{b:function(){return m},w:function(){return f}});var r=n(96933),a=n(3053),i=n(27218),s=n(13262),o=n(51432),l=n(77930),c=n(23368),u=n.n(c),d=n(81695),p=n(7653);let m=()=>{let e="new-conversation-params-for-navigation",t=(0,l.useQueryClient)(),n=(0,p.useCallback)(()=>{let t=localStorage.getItem(e);if(!t)return{};try{let e=JSON.parse(t);return u()(e,e=>Date.now()-e.created_at<6e4)}catch(t){localStorage.removeItem(e)}return{}},[]);return{prepareNewConversation:(0,p.useCallback)((r,a)=>{let i={created_at:Date.now(),...a};t.setQueryData([e,r],i);let s=n();s[r]=i;try{localStorage.setItem(e,JSON.stringify(s))}catch(e){}},[t,n]),getCreateParamsForConvo:(0,p.useCallback)(r=>{let a=n(),i=t.getQueryData([e,r])||a[r];t.removeQueries({queryKey:[e,r]});let s=u()(a,(e,t)=>t!==r);return localStorage.setItem(e,JSON.stringify(s)),i},[t,n])}};function f(){var e;let t=(0,a.z$)(),{data:n=[]}=(0,r.QR)({limit:1}),{mutateAsync:c}=(0,r.$Y)(),[u,f]=(0,p.useReducer)(o.H,"",o.H),{prepareNewConversation:g}=m(),h=(0,l.useQueryClient)(),{activeOrganization:v,account:b}=(0,i.t)(),x=null==v?void 0:v.uuid,_=(0,d.useRouter)();return(0,p.useCallback)(async e=>{var r;let{model:a,project_uuid:i,include_conversation_preferences:o,paprika_mode:l,name:d}=e,p=c({uuid:u,name:null!=d?d:"",model:a,project_uuid:i,include_conversation_preferences:o,paprika_mode:l});if(g(u,e),await p,(null==b?void 0:null===(r=b.settings)||void 0===r?void 0:r.has_finished_claudeai_onboarding)===!1){let e=new URLSearchParams;e.append("returnTo","/chat/".concat(u));let t="/onboarding?".concat(e.toString());_.push(t)}else _.push("/chat/".concat(u));return f(),h.invalidateQueries({queryKey:[s.tv,{orgUUID:x}]}),0===n.length&&await t.track({event_key:"chat.conversation.first_conversation_created",surface:"claude.ai"}),{uuid:u}},[c,u,n,t,_,g,h,x,null==b?void 0:null===(e=b.settings)||void 0===e?void 0:e.has_finished_claudeai_onboarding])}},15878:function(e,t,n){"use strict";n.d(t,{IntercomTracker:function(){return d}});var r=n(66894),a=n(86530),i=n.n(a),s=n(7653),o=n(27218),l=n(20823);let c={primary_owner:4,owner:3,admin:2,user:1},u=e=>{var t;return null!==(t=c[e])&&void 0!==t?t:0};function d(){let e=(0,l.o)(),{account:t,intercomAccountHash:n,activeOrganization:a}=(0,o.t)();return(0,s.useEffect)(()=>{var a,s,o,l;if((0,r.yG)()||!t||!e)return;let c=t.memberships.map(e=>e.organization),d=(null==t?void 0:t.memberships.filter(e=>e.organization.capabilities.includes("raven")&&"team"===e.organization.raven_type))||[],p=(null==t?void 0:t.memberships.filter(e=>e.organization.capabilities.includes("raven")&&"enterprise"===e.organization.raven_type))||[],m=(null===(a=i()(d,e=>u(e.role)))||void 0===a?void 0:a.role)||null,f=(null===(s=i()(p,e=>u(e.role)))||void 0===s?void 0:s.role)||null,g=c.some(e=>{switch(e.billing_type){case"usage_based":return!0;case"prepaid":if(!e.rate_limit_tier)return!0;return"auto_prepaid_tier_0"!==e.rate_limit_tier&&"auto_prepaid_tier_1"!==e.rate_limit_tier;default:return!1}}),h=c.map(e=>{var t;let n=null===(t=e.rate_limit_tier)||void 0===t?void 0:t.match(/(auto_prepaid|manual)_tier_(\d)/);switch(e.billing_type){case"usage_based":return[100,"scale"];case"prepaid":if(n){let e=parseInt(n[2])+1;return[e,"build_tier_".concat(e)]}return[99,"build_custom"];case"api_evaluation":return[0,"evaluation"];default:return[-1,null]}}),v={paidAccount:g,apiTier:null!==(l=null===(o=i()(h,e=>e[0]))||void 0===o?void 0:o[1])&&void 0!==l?l:null,claudePro:c.some(e=>e.capabilities.includes("claude_pro")),hasTeamPlan:d.length>0,isAdminForTeamPlan:d.some(e=>["admin","owner","primary_owner"].includes(e.role)),teamRole:m,hasEnterprisePlan:p.length>0,enterpriseRole:f};e.boot({hideDefaultLauncher:!0,userId:t.uuid,email:t.email_address,name:t.full_name,userHash:null!=n?n:void 0,customAttributes:v})},[e,t,n]),(0,s.useEffect)(()=>{!(0,r.yG)()&&a&&e&&e.update({customAttributes:{lastActiveOrgUUID:a.uuid,lastActiveOrgName:a.name,lastActiveOrgCapabilities:a.capabilities.join(",")}})},[e,a]),null}},3053:function(e,t,n){"use strict";n.d(t,{YZ:function(){return f},q$:function(){return g},z$:function(){return m}});var r=n(30947),a=n(14448),i=n(7653),s=n(77879),o=n(5362),l=n(8571),c=n(50862),u=n(27218),d=n(91447);let p={snippets_command_used:{version:1},file_upload_too_large:{version:1},sse_interrupted:{version:1},snippets_suggestion_selected:{version:1},payment_modal_open:{version:1},pro_waitlist_requested:{version:1},payment_state_poll_for_invoice:{version:1},payment_state_poll_for_payment:{version:1},payment_state_poll_for_capability:{version:1},payment_state_payment_requires_confirmation:{version:1},payment_state_payment_failed:{version:1},payment_state_success:{version:1},payment_state_timed_out:{version:1},"onboarding.phone_verification.start":{version:1},"onboarding.phone_verification.sent_code":{version:1},"onboarding.phone_verification.invalid_code":{version:1},"onboarding.phone_verification.verified_code":{version:1},"onboarding.age_verification.start":{version:1},"onboarding.age_verification.complete":{version:1},"onboarding.name_input.started":{version:1},"onboarding.name_input.finished":{version:1},"onboarding.acceptable_use.started":{version:1},"onboarding.acceptable_use.finished":{version:1},"onboarding.disclaimers.started":{version:1},"onboarding.disclaimers.finished":{version:1},"onboarding.completed":{version:1},"extended_onboarding.started":{version:1},"extended_onboarding.disclaimer.completed":{version:1},"extended_onboarding.name_input.completed":{version:1},"extended_onboarding.work_function.completed":{version:1},"extended_onboarding.drive_integration.started":{version:1},"extended_onboarding.drive_integration.skipped":{version:1},"extended_onboarding.drive_integration.completed":{version:1},"extended_onboarding.topics.completed":{version:1},"extended_onboarding.example_prompts.completed":{version:1},"extended_onboarding.completed":{version:1},"pro_subscription.upgraded_to_annual":{version:1},"chat.conversation.first_conversation_created":{version:1},"chat.conversation.too_long_prompt:loaded":{version:1},"chat.conversation.too_long_prompt:accepted":{version:1},"chat.conversation.too_long_prompt:dismissed":{version:1},"chat.conversation.too_long_prompt_warning:loaded":{version:1},"chat.conversation.too_long_prompt_warning:accepted":{version:1},"chat.conversation.too_long_prompt_warning:dismissed":{version:1},"chat.share.button.clicked":{version:1},"chat.share.modal.share_button.clicked":{version:1},"chat.share.modal.unshare_button.clicked":{version:1},"chat.share.open_link.clicked":{version:1},"chat.share.copy_link.clicked":{version:1},"ts.eu_election_banner.shown":{version:1,data_team_tag:"ts"},"ts.banner.shown":{version:1,data_team_tag:"ts"},"billing.credit_card.submitted":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.contact_sales.completed":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.contact_sales.loaded":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.payment_info.loaded":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.payment_info.submitted":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.setup_billing.loaded":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.setup_billing.submitted":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.ts_questionnaire.loaded":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.ts_questionnaire.submitted":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.ts_questionnaire_manual.loaded":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.select_plan.loaded":{version:1,data_team_tag:"billing_funnel"},"billing.upgrade.select_plan.completed":{version:1,data_team_tag:"billing_funnel"},"console.feedback.opened":{version:1},"console.feedback.submitted":{version:1},"evals.revision.created":{version:1},"evals.show_prompt_preview.toggled":{version:1},"evals.show_golden_answers.toggled":{version:1},"evals.run_all.submitted":{version:1},"evals.run_all.stopped":{version:1},"evals.test_case.run.submitted":{version:1},"evals.test_case.run.completed":{version:1},"evals.test_case.run.failed":{version:1},"evals.test_case.create.submitted":{version:1},"evals.test_case.delete.submitted":{version:1},"evals.select_revision.clicked":{version:1},"evals.test_case.update_rating.submitted":{version:1},"evals.test_case.import.show_modal.clicked":{version:1},"evals.test_case.import.browse.clicked":{version:1},"evals.test_case.import.success":{version:1},"evals.comparison.add_button.clicked":{version:1},"evals.comparison.remove_button.clicked":{version:1},"evals.comparison.other_revision.selected":{version:1},"test_case_generator.generate.started":{version:1},"test_case_generator.edit_logic.clicked":{version:1},"metaprompter.modal.loaded":{version:2},"metaprompter.example.selected":{version:1},"metaprompter.generate.started":{version:1},"metaprompter.generate.stop.attempted":{version:1},"metaprompter.generate.finished":{version:1},"metaprompter.start_editing.clicked":{version:1},"onboarding.invite_accept_page.invite_accept.submitted":{version:1,data_team_tag:"onboarding_funnel"},"onboarding.invite_accept_page.invite_reject.submitted":{version:1,data_team_tag:"onboarding_funnel"},"onboarding.invite_accept_page.loaded":{version:1,data_team_tag:"onboarding_funnel"},"onboarding.organization_create.loaded":{version:1,data_team_tag:"onboarding_funnel"},"onboarding.organization_create.submitted":{version:1,data_team_tag:"onboarding_funnel"},"workbench.prompt.new":{version:1},"workbench.completion.started":{version:2},"workbench.completion.finished":{version:2},"workbench.completion_stream.started":{version:1},"workbench.completion_stream.finished":{version:1},"workbench.tab_selection.clicked":{version:1},"workbench.variables_pane.loaded":{version:1},"workbench.examples.pane.loaded":{version:1},"workbench.examples.example.added":{version:2},"workbench.examples.example.deleted":{version:1},"workbench.examples.converted_to_raw_text":{version:1},"workbench.code_modal.opened":{version:1},"workbench.code_modal.code_copied":{version:1},"claudeai.projects.list_view.switched":{version:1},"claudeai.artifacts.execution.error":{version:1},"claudeai.conversation.suggestion.applied":{version:1},"claudeai.conversation.recent.opened":{version:1},"claudeai.projects.activity.shared_conversation.opened":{version:1},"claudeai.projects.project_starter.clicked":{version:1},"claudeai.projects.chat_starter.clicked":{version:1},"claudeai.conversation.artifact.vscode_exported":{version:1},"claudeai.settings.preview_feature.toggled":{version:1},"claudeai.settings.preview_feature.opened":{version:1},"claudeai.support.opened":{version:1},"claudeai.get_help.clicked":{version:1},"claudeai.conversation.feedback.sent":{version:1},"claudeai.conversation.setting.toggled":{version:1},"claudeai.thinking_cell.clicked":{version:1},"ccos.chatsuggestion.clicked":{version:1},"ccos.chatsuggestions.loaded":{version:1},"ccos.chatsuggestions.feedback":{version:1},"styles.selector.clicked":{version:1},"styles.modal":{version:1},"styles.nux.create_clicked":{version:1},"styles.custom.create_clicked":{version:1},"styles.custom.describe_clicked":{version:1},"styles.custom.describe_selected":{version:1},"styles.selected":{version:1},"artifact.feedback.sentiment":{version:1},"artifact.feedback.note":{version:1},"mcp.servers.listed":{version:1},"mcp.servers.connected":{version:1},"mcp.resources.listed":{version:1},"mcp.resources.read":{version:1},"mcp.prompts.listed":{version:1},"mcp.prompts.used":{version:1},"mcp.tools.listed":{version:1},"mcp.tools.called":{version:1},"mcp.sampling.requested":{version:1},"harmony.file.saved":{version:1},"harmony.suggestion.accepted":{version:1},"harmony.suggestion.rejected":{version:1},"hfi.comparative_model.selected":{version:1},"hfi.comparative_model.selection_cleared":{version:1},"hfi.comparative_model.streaming.started":{version:1},"hfi.comparative_model.streaming.finished":{version:1},"hfi.comparative_model.feedback_bar.switch_response.clicked":{version:1},"hfi.comparative_model.feedback_bar.prefer_response.clicked":{version:1},"apps.download_button_clicked":{version:1},"apps.nudge_banner_shown":{version:1},"apps.nudge_banner_clicked":{version:1},"apps.harmony_directory_synced":{version:1},"console.onboarding.name_and_legal.loaded":{version:1,data_team_tag:"onboarding_funnel"},"console.onboarding.name_and_legal.submitted":{version:1,data_team_tag:"onboarding_funnel"},"tengu.waitlist.update":{version:1},"tengu.waitlist.details":{version:1},"oauth.authorize.approved":{version:1},"oauth.authorize.denied":{version:1},"oauth.authorize.success":{version:1},"oauth.authorize.failure":{version:1},"console.onboarding.account_type.loaded":{version:1,data_team_tag:"onboarding_funnel"},"console.onboarding.account_type.individual_selected":{version:1,data_team_tag:"onboarding_funnel"},"console.onboarding.account_type.organization_selected":{version:1,data_team_tag:"onboarding_funnel"},"console.onboarding.organization_details.loaded":{version:1,data_team_tag:"onboarding_funnel"},"console.onboarding.organization_details.submitted":{version:1,data_team_tag:"onboarding_funnel"},"login.account.created":{version:1},"login.email.sending_magic_link":{version:1},"login.email.magic_link_sent":{version:1},"login.email.magic_link_send_error":{version:1},"login.email.verifying_magic_link":{version:1},"login.email.magic_link_success":{version:1},"login.email.magic_link_verification_error":{version:1},"login.email.sso_initiated":{version:1},"login.email.sso_success":{version:1},"login.email.sso_verifying_callback":{version:1},"login.email.sso_verification_error":{version:1},"login.email.verifying_code":{version:1},"login.email.code_verification_error":{version:1},"login.email.finished":{version:1},"login.google.started":{version:1},"login.google.error":{version:1},"login.google.verifying":{version:1},"login.google.verification_error":{version:1},"login.google.finished":{version:1},"landing.section.features.viewed":{version:1},"landing.section.pricing.viewed":{version:1},"spotlight.shown":{version:1},"spotlight.dismissed":{version:1},"spotlight.action_clicked":{version:1},"claudeai.conversation.citation_clicked":{version:1},"claudeai.cardamom_prompts.category_displayed":{version:1},"claudeai.cardamom_prompts.prompt_displayed":{version:1},"claudeai.cardamom_prompts.prompt_used":{version:1}},m=()=>{let{analytics:e}=(0,d.K)(),{preferences:t}=(0,c.C)(),{value:n}=(0,a.F)("log_segment_events"),{value:r}=(0,a.F)("claude_ai_segment_enabled"),{value:s}=(0,a.F)("send_email_to_segment"),{account:o,activeOrganization:m,isLoading:f}=(0,u.t)(),g=(0,l.Z)(),h=(0,i.useCallback)((a,i)=>{var l,c,u,d;if(f)return;let{event_key:h,...v}=a,b={account_uuid:null!==(u=null==i?void 0:i.uuid)&&void 0!==u?u:null==o?void 0:o.uuid,organization_uuid:null==m?void 0:m.uuid,billing_type:null==m?void 0:m.billing_type,surface:g?"claude-ai":"console",...p[h],...v},x={context:{traits:{email:s?null!==(d=null==i?void 0:i.email_address)&&void 0!==d?d:null==o?void 0:o.email_address:null,userAgent:null===(c=window)||void 0===c?void 0:null===(l=c.navigator)||void 0===l?void 0:l.userAgent}}};if(n&&console.log("[Segment Event] ".concat(h),b,x),e&&r&&(g||o)&&(o||t.analytics))return e.track(h,b,x)},[e,o,m,g,f,n,r,s,t.analytics]);return(0,i.useMemo)(()=>({track:h}),[h])},f=(e,t,n,a)=>{let i=m(),{onSuccess:s,onError:l,...c}=a||{};return(0,o.uC)(t,n,{onSuccess(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];i.track({event_key:e,result:"success"}),null==s||s(...n)},onError(t){let n;for(var a=arguments.length,s=Array(a>1?a-1:0),o=1;o<a;o++)s[o-1]=arguments[o];t instanceof r.Hx&&(n=t.type),i.track({event_key:e,result:"failure",failure_reason:n}),null==l||l(t,...s)},...c})},g=e=>{let{eventKey:t,threshold:n=.5,freezeOnceVisible:r=!0}=e,{track:a}=m(),{ref:o,isIntersecting:l}=(0,s.S1)({threshold:n,freezeOnceVisible:r});return(0,i.useEffect)(()=>{l&&a({event_key:t})},[l,a,t]),o}},50862:function(e,t,n){"use strict";n.d(t,{ConsentProvider:function(){return f},C:function(){return g}});var r=n(27573),a=n(45144),i=n(7653),s=n(40287),o=n(94872),l=n(53500),c=n(6385),u=n(35919),d=n.n(u),p=n(95407);let m=(0,i.createContext)({showConsentBanner:!1,openConsentBanner:d(),preferences:p.iw,savePreferences:d()}),f=e=>{let{children:t,requiresExplicitConsent:n}=e,[u,d,f]=function(e,t){let n=(0,s.f)(),[r,o]=(0,i.useState)(()=>!!n.get(e)),[l,c]=(0,i.useState)(()=>{try{let r=n.get(e);return r?JSON.parse(r):t}catch(n){return(0,a.Tb)(n,{extra:{message:"Malformed JSON cookie",cookieKey:e}}),t}});return[l,(0,i.useCallback)(t=>{n.set(e,JSON.stringify(t)),c(t),o(!0)},[n,e]),r]}(c.cn.CONSENT_PREFERENCES,n?p.iw:p.DF),[g,h]=(0,i.useState)(n&&!f),v=(0,i.useCallback)(e=>{d(e),(0,o.HF)(e),h(!1),l.u.updateCategories(e)},[d,h]);return(0,r.jsx)(m.Provider,{value:{showConsentBanner:g,openConsentBanner:()=>h(!0),preferences:u,savePreferences:v},children:t})};function g(){return(0,i.useContext)(m)}},27895:function(e,t,n){"use strict";n.d(t,{ErrorsProvider:function(){return v},e:function(){return b},v:function(){return x}});var r=n(27573),a=n(35228),i=n(97975),s=n.n(i),o=n(55854),l=n.n(o),c=n(78646),u=n.n(c),d=n(81695),p=n(7653),m=n(15992),f=n(27218),g=n(18013);let h=(0,p.createContext)(void 0);function v(e){let{children:t}=e,n=(0,p.useRef)(1),[a,i]=(0,p.useState)([]),s=(0,p.useCallback)(e=>{i(t=>t.filter(t=>t.id!==e))},[]),o=(0,p.useCallback)((e,t)=>{let r=n.current++;return i(t=>[...t,{id:r,message:e instanceof Error?e.message:e,toastType:"error"}]),t&&setTimeout(()=>s(r),t),r},[s]),l=(0,p.useCallback)((e,t)=>{let r=n.current++;return i(t=>[...t,{id:r,message:e,toastType:"success"}]),t&&setTimeout(()=>s(r),t),r},[s]),c=(0,p.useMemo)(()=>({toasts:a,addError:o,addSuccess:l,clearToast:s}),[a,o,l,s]);return(0,r.jsx)(h.Provider,{value:c,children:t})}function b(){let e=(0,p.useContext)(h);if(!e)throw Error("Must be called within ErrorsProvider");return e}function x(){var e;let{config:t}=(0,m.useConfig)("claude_system_message"),n=t.get("id",null),r=t.get("title",null),i=t.get("message",null),o=t.get("displayFrequencyHours",null),{account:c}=(0,f.t)(),h=null!==(e=null==c?void 0:c.uuid)&&void 0!==e?e:"logged-out",v=(0,d.usePathname)(),[b,x]=(0,g.R)("dismissed-system-messages",{}),_=(0,p.useMemo)(()=>()=>x(e=>{let t=s()(e);return n&&u()(t,[h,n],Date.now()),t}),[x,h,n]);return(0,p.useMemo)(()=>{if(!c||(0,a.cG)(c,!0)||"/download"===v||!n)return!1;let e=l()(b,[h,n]);return!e||"boolean"==typeof e||!!o&&Date.now()-e>=36e5*o},[c,n,h,b,o,v])?{currentSystemMessageId:n,currentSystemMessageTitle:r,currentSystemMessageContent:i,dismissCurrentSystemMessage:_}:{currentSystemMessageId:null,currentSystemMessageTitle:null,currentSystemMessageContent:null,dismissCurrentSystemMessage:()=>null}}},94872:function(e,t,n){"use strict";n.d(t,{HF:function(){return i},bq:function(){return a}});var r=n(45144);let a=function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),a=1;a<t;a++)n[a-1]=arguments[a];try{"function"==typeof window.gtag&&gtag(e,...n)}catch(e){(0,r.Tb)(e)}},i=e=>{a("consent","update",s(e))},s=e=>({ad_personalization:e.marketing?"granted":"denied",ad_user_data:e.marketing?"granted":"denied",ad_storage:e.marketing?"granted":"denied",analytics_storage:e.analytics?"granted":"denied",functionality_storage:"granted",personalization_storage:"granted",security_storage:"granted"})},20823:function(e,t,n){"use strict";n.d(t,{IntercomProvider:function(){return i},o:function(){return s}});var r=n(27573),a=n(66606);function i(e){let{children:t,...n}=e;return(0,r.jsx)(a.H,{...n,children:t})}function s(){return(0,a.o)()}},18850:function(e,t,n){"use strict";n.d(t,{LegalDocsProvider:function(){return s},q:function(){return o}});var r=n(27573),a=n(7653);let i=(0,a.createContext)(void 0),s=e=>{let{value:t,children:n}=e;return(0,r.jsx)(i.Provider,{value:t,children:n})};function o(){return(0,a.useContext)(i)}},79483:function(e,t,n){"use strict";n.d(t,{LogHiring:function(){return i}});var r=n(66894),a=n(7653);let i=()=>((0,a.useEffect)(()=>{(0,r.yG)()||console.log("%c\n         &&&&&   &&&&&\n        &&&&&&&   &&&&&\n       &&&&&&&&&   &&&&&\n      &&&&  &&&&&   &&&&&\n     &&&&    &&&&&   &&&&&\n    &&&&&     &&&&&   &&&&&\n   &&&&&&&&&&&&&&&&&   &&&&&\n  &&&&&         &&&&&   &&&&&\n &&&&&           &&&&&   &&&&&\n\n We’re hiring! https://anthropic.com/careers","font-family: Menlo, monospace; font-weight: bold; font-size: 14px; color: #cda177")},[]),null)},80386:function(e,t,n){"use strict";n.d(t,{MazeScript:function(){return o}});var r=n(27573),a=n(14448),i=n(81695),s=n(27522);function o(){let e=(0,i.useSearchParams)(),{value:t}=(0,a.F)("maze_enabled");return t&&e.has("maze")?(0,r.jsx)(s.default,{id:"maze-script",dangerouslySetInnerHTML:{__html:"\n(function (m, a, z, e) {\n  var s, t;\n  try {\n    t = m.sessionStorage.getItem('maze-us');\n  } catch (err) {}\n if (!t) {\n    t = new Date().getTime();\n    try {\n      m.sessionStorage.setItem('maze-us', t);\n    } catch (err) {}\n  }\n s = a.createElement('script');\n  s.src = z + '?apiKey=' + e;\n  s.async = true;\n  a.getElementsByTagName('head')[0].appendChild(s);\n  m.mazeUniversalSnippetApiKey = e;\n})(window, document, 'https://snippet.maze.co/maze-universal-loader.js', '27d4ca78-9def-4e9f-914c-46e488fae304');\n"}}):null}},36720:function(e,t,n){"use strict";n.d(t,{MotionConfig:function(){return i}});var r=n(27573),a=n(46250);let i=e=>{let{children:t}=e;return(0,r.jsx)(a.A,{reducedMotion:"user",children:t})}},11019:function(e,t,n){"use strict";n.d(t,{QueryClientProvider:function(){return g}});var r=n(27573),a=n(30947),i=n(13262),s=n(96723),o=n(62716),l=n(77481),c=n(77930),u=n(24173),d=n(7653),p=n(27895),m=n(77879);let f=()=>{let[e,t]=(0,d.useState)(null),[n,a]=(0,m._)("ant-devtools",!1);return((0,d.useEffect)(()=>{},[n]),e)?(0,r.jsx)(e,{initialIsOpen:!1,position:"bottom"}):null};function g(e){let{state:t,children:n}=e,{addError:m}=(0,p.e)(),[g]=(0,d.useState)(()=>{let e=(e,t,n)=>{if(e instanceof a.Hx&&403===e.statusCode&&e.message.includes("Not authenticated"))g.getQueryData([i.aY])&&g.invalidateQueries({queryKey:[i.aY]});else if(e instanceof Error){if("function"==typeof t?!t(e):!t){let t=n||e.message||e;"string"==typeof t&&t.slice(0,50).includes("html>")?m("We are experiencing technical difficulties. Some functionality may be temporarily unavailable."):t&&m(t)}}else throw Error("Unexpected query error type: ".concat(typeof e),{cause:e})};return new s.S({queryCache:new o.t({onError:(t,n)=>{let r=n.meta;e(t,null==r?void 0:r.noToast,null==r?void 0:r.errorMessage)}}),mutationCache:new l.L({onError:(t,n,r,a)=>{let i=a.meta;e(t,!!(null==i?void 0:i.noToast),null==i?void 0:i.errorMessage)}})})});return(0,r.jsxs)(c.QueryClientProvider,{client:g,children:[(0,r.jsx)(f,{}),(0,r.jsx)(u.HydrationBoundary,{state:t,children:n})]})}},58574:function(e,t,n){"use strict";n.d(t,{AnalyticsIdentifier:function(){return m}});var r=n(50862),a=n(27218),i=n(66894),s=n(14448),o=n(45144),l=n(35919),c=n.n(l),u=n(81695),d=n(7653),p=n(91447);function m(){let{analytics:e}=(0,p.K)(),{account:t}=(0,a.t)(),n=(0,u.usePathname)(),{value:l}=(0,s.F)("claude_ai_segment_enabled"),{preferences:m}=(0,r.C)();return(0,d.useEffect)(()=>{if(!t)return;(0,o.av)({id:t.uuid});let n="none";for(let e of["commercial_use","claude_pro","api"])if(t.memberships.some(t=>t.organization.capabilities.includes(e))){n="commercial_use";break}null==e||e.identify(t.uuid,{plan:n}).catch(c())},[t,e]),(0,d.useEffect)(()=>{!(0,i.yG)()&&l&&n&&void 0!==t&&(t||m.analytics)&&(null==e||e.page(n).catch(c()))},[n,l,e,t,m.analytics]),null}},91447:function(e,t,n){"use strict";n.d(t,{K:function(){return o}});var r=n(8571),a=n(14448),i=n(7653),s=n(53500);let o=()=>{let{segmentKey:e,segmentCdnHost:t,segmentApiHost:n}=(0,r.m)(),{value:o}=(0,a.F)("requires_explicit_consent"),l=(0,i.useCallback)(()=>{s.u.reset()},[]);return{analytics:s.u.loadIfNecessary({segmentKey:e,segmentCdnHost:t,segmentApiHost:n,requiresExplicitConsent:o}),reset:l}}},16369:function(e,t,n){"use strict";n.d(t,{SiftEvents:function(){return i}});var r=n(81695),a=n(7653);let i=()=>{let e=(0,r.usePathname)(),t=(0,a.useRef)(!0);return(0,a.useEffect)(()=>{if(t.current){t.current=!1;return}let e=window._sift;e&&e.push(["_trackPageview"])},[e]),null}},17089:function(e,t,n){"use strict";n.d(t,{StatsigProvider:function(){return m}});var r=n(27573),a=n(13262),i=n(7653),s=n(43860),o=n.n(s),l=n(15992),c=n(5362),u=n(8571),d=n(27218);let p={disableAutoMetricsLogging:!0,environment:{tier:"production"},api:"https://statsig.anthropic.com/v1/"};function m(e){var t;let{children:n}=e,{statsigClientKey:i}=(0,u.m)(),{statsig:s,activeOrganization:o,account:l}=(0,d.t)(),m=(0,d.ZJ)(),g=(0,d.Cf)(),h=null!==(t=null==o?void 0:o.raven_type)&&void 0!==t?t:void 0,v=l?Date.parse(l.created_at):void 0,{data:b}=(0,c.WE)("/api/bootstrap/".concat(null==o?void 0:o.uuid,"/statsig"),{queryKey:[a.eW,null==o?void 0:o.uuid],enabled:!!o,staleTime:0,meta:{noToast:!0}}),x=(null==b?void 0:b.user)||(null==s?void 0:s.user);x&&(x.custom={...x.custom,isPro:m,isRaven:g,ravenType:h,accountCreatedAt:v});let _=(null==b?void 0:b.values)||(null==s?void 0:s.values),y=(null==b?void 0:b.values_hash)||(null==s?void 0:s.values_hash);if(x&&(!_||0===Object.keys(_).length))throw Error("no statsig values for user");return(0,r.jsx)(f,{sdkKey:i,user:x,values:_,valuesHash:y,options:p,children:n})}function f(e){let{children:t,sdkKey:n,user:a,options:s,values:c,valuesHash:u}=e,[d,p]=(0,i.useReducer)(e=>e+1,0),m=(0,i.useRef)(!0);(0,i.useState)(()=>l.Statsig.bootstrap(n,c||{},a,s)),(0,i.useEffect)(()=>{if(m.current){m.current=!1,window.__STATSIG_SDK__=l.Statsig,window.__STATSIG_JS_SDK__=o(),window.__STATSIG_RERENDER_OVERRIDE__=()=>{p()};return}a&&c&&(l.Statsig.updateUserWithValues(a,c),p())},[a,c]),(0,i.useEffect)(()=>(l.Statsig.setReactContextUpdater(()=>p()),()=>{l.Statsig.setReactContextUpdater(null)}),[]);let f=l.Statsig.initializeCalled(),g=(0,i.useMemo)(()=>({initialized:!0,statsigPromise:null,userVersion:d,initStarted:f,hooksMemoKey:u||"no_values_hash",updateUser:()=>{}}),[d,f,u]);return(0,r.jsx)(l.StatsigContext.Provider,{value:g,children:t})}},18013:function(e,t,n){"use strict";n.d(t,{A:function(){return a},R:function(){return i}});var r=n(77879);function a(e,t){return(0,r.Xs)("SSS-".concat(e),t,{serializer:JSON.stringify,deserializer:JSON.parse})}function i(e,t){return(0,r._)("LSS-".concat(e),t,{serializer:JSON.stringify,deserializer:JSON.parse})}},53746:function(e,t,n){"use strict";n.d(t,{WorkerUpdater:function(){return i}});var r=n(7653);let a="undefined"!=typeof navigator&&"serviceWorker"in navigator;function i(e){let{updateType:t}=e;return(0,r.useEffect)(()=>{a&&(async()=>{let e=await navigator.serviceWorker.getRegistrations();0!==e.length&&(console.warn("Found ".concat(e.length," service worker(s), which are no longer used. Unregistering.")),e.forEach(e=>void e.unregister()))})()},[]),null}},9677:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(27573),a=n(10607);function i(e){let{className:t,variant:n,width:i=36,height:o=20,...l}=e,c="number"==typeof o?"".concat(o,"px"):o,u="calc(".concat(c," - 4px");return(0,r.jsxs)("div",{className:(0,a.Z)("group/switch relative select-none cursor-pointer",t),children:[(0,r.jsx)("input",{type:"checkbox",className:"peer sr-only",...l}),(0,r.jsx)("div",{style:{width:i,height:c},className:(0,a.Z)("border-border-300 rounded-full peer:can-focus peer-disabled:opacity-50",s.pill[n||"default"])}),(0,r.jsx)("div",{style:{height:u,width:u},className:(0,a.Z)("absolute start-[2px] top-[2px] rounded-full transition-all peer-checked:translate-x-full rtl:peer-checked:-translate-x-full",s.circle[n||"default"])})]})}let s={pill:{default:"\n    bg-bg-500\n    transition-colors\n    peer-checked:bg-accent-secondary-100\n    "},circle:{default:"\n    group-hover/switch:opacity-80\n    bg-white\n    transition\n    "}}},9788:function(e,t,n){"use strict";n.d(t,{q:function(){return s}});var r=n(10607),a=n(7653);function i(e){return function(t){for(var n=arguments.length,i=Array(n>1?n-1:0),s=1;s<n;s++)i[s-1]=arguments[s];let o=t.map(e=>e.replace(/\n/g,"").trim()),l=a.forwardRef((t,n)=>{let{className:s,...l}=t,c=i.map(e=>"function"==typeof e?e(t):e),u=Object.fromEntries(Object.entries(l).filter(e=>{let[t]=e;return!t.startsWith("$")}));return a.createElement(e,{...u,ref:n,className:(0,r.Z)(o,c,"string"==typeof s?s:"")})});return l.displayName="string"==typeof e?e:e.displayName,l}}function s(e){return i(e)}s.a=i("a"),s.aside=i("aside"),s.button=i("button"),s.main=i("main"),s.div=i("div"),s.form=i("form"),s.nav=i("nav"),s.fieldset=i("fieldset"),s.header=i("header"),s.h1=i("h1"),s.h2=i("h2"),s.h3=i("h3"),s.h4=i("h4"),s.h5=i("h5"),s.th=i("th"),s.td=i("td"),s.input=i("input"),s.label=i("label"),s.p=i("p"),s.section=i("section"),s.span=i("span"),s.li=i("li")},69321:function(e,t,n){"use strict";n.d(t,{ClientIntlProvider:function(){return u},Gc:function(){return p},d7:function(){return d},lF:function(){return m}});var r=n(27573),a=n(13784),i=n(7653),s=n(22247),o=n(45790),l=n(72358);let c=()=>void 0;function u(e){let{locale:t,messages:n,children:o,hideErrors:l}=e;return(0,i.useEffect)(()=>{a.Zr.defaultLocale=t},[t]),(0,r.jsx)(s.Z,{locale:t,messages:n,onError:l?c:void 0,fallbackOnEmptyString:!1,children:o})}function d(e){let{id:t}=e;return(0,r.jsx)(o.Z,{id:(0,l.g2)(t),defaultMessage:" "})}function p(e){let{id:t,intl:n}=e;return n.formatMessage({id:(0,l.g2)(t),defaultMessage:" "})}function m(e){let{id:t,intl:n}=e;return n.formatMessage({id:(0,l.Y_)(t),defaultMessage:" "})}},72358:function(e,t,n){"use strict";n.d(t,{GN:function(){return o},Y_:function(){return l},Yr:function(){return u},ZW:function(){return s},g2:function(){return c}});var r=n(79939),a=n.n(r),i=n(68571);a().join(i.cwd(),"public/i18n"),a().join(i.cwd(),"public/i18n/secret");let s="en-US",o={"xx-LS":{name:"Long stringsSSSSSSSS"},"xx-AC":{name:"ALL CAPS"},"xx-HA":{name:"[javascript] prefixed strings"},"en-XA":{name:"Ȧȧƈƈḗḗƞŧḗḗḓ Ḗḗƞɠŀīīşħ"},"en-XB":{name:"ɥsıʅƃuƎ ıpıԐ"}};function l(e){return"".concat("secret",":").concat(e)}function c(e){return"".concat("statsig",":").concat(e)}Object.keys(o);let u=["en-US","de-DE","fr-FR","ko-KR","ja-JP","es-419","es-ES","it-IT","hi-IN","pt-BR","id-ID"]},64483:function(e,t,n){"use strict";n.d(t,{I:function(){return a}});var r=n(27573);function a(e){let{height:t=14,className:n}=e;return(0,r.jsxs)("svg",{height:t,className:n,viewBox:"0 0 110 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","aria-label":"Anthropic",children:[(0,r.jsx)("path",{d:"M26.92 2.43646H30.929V11.8011H33.4879V2.43646H37.4969V0.198895H26.92V2.43646Z"}),(0,r.jsx)("path",{d:"M22.3992 8.32044L17.0254 0.198895H14.1253V11.8011H16.5989V3.67956L21.9727 11.8011H24.8728V0.198895H22.3992V8.32044Z"}),(0,r.jsx)("path",{d:"M47.7326 4.8232H42.103V0.198895H39.544V11.8011H42.103V7.06077H47.7326V11.8011H50.2916V0.198895H47.7326V4.8232Z"}),(0,r.jsx)("path",{d:"M4.75962 0.198895L0 11.8011H2.66129L3.63471 9.36464H8.61422L9.58747 11.8011H12.2488L7.48914 0.198895H4.75962ZM4.49553 7.20994L6.12438 3.1326L7.75323 7.20994H4.49553Z"}),(0,r.jsx)("path",{d:"M71.4966 0C68.0506 0 65.611 2.48619 65.611 6.01657C65.611 9.51381 68.0506 12 71.4966 12C74.9256 12 77.348 9.51381 77.348 6.01657C77.348 2.48619 74.9256 0 71.4966 0ZM71.4966 9.67956C69.4836 9.67956 68.2553 8.28729 68.2553 6.01657C68.2553 3.71271 69.4836 2.32044 71.4966 2.32044C73.4926 2.32044 74.7038 3.71271 74.7038 6.01657C74.7038 8.28729 73.4926 9.67956 71.4966 9.67956Z"}),(0,r.jsx)("path",{d:"M107.27 7.90608C106.827 9.03315 105.94 9.67956 104.729 9.67956C102.716 9.67956 101.487 8.28729 101.487 6.01657C101.487 3.71271 102.716 2.32044 104.729 2.32044C105.94 2.32044 106.827 2.96685 107.27 4.09392H109.983C109.318 1.60773 107.322 0 104.729 0C101.283 0 98.843 2.48619 98.843 6.01657C98.843 9.51381 101.283 12 104.729 12C107.339 12 109.335 10.3757 110 7.90608H107.27Z"}),(0,r.jsx)("path",{d:"M90.9615 0.198895L95.7212 11.8011H98.3313L93.5717 0.198895H90.9615Z"}),(0,r.jsx)("path",{d:"M85.5707 0.198895H79.7364V11.8011H82.2953V7.59116H85.5707C88.2832 7.59116 89.938 6.19889 89.938 3.89503C89.938 1.59116 88.2832 0.198895 85.5707 0.198895ZM85.4513 5.35359H82.2953V2.43646H85.4513C86.7137 2.43646 87.379 2.9337 87.379 3.89503C87.379 4.85635 86.7137 5.35359 85.4513 5.35359Z"}),(0,r.jsx)("path",{d:"M63.6492 3.72928C63.6492 1.54144 61.9944 0.198895 59.2819 0.198895H53.4476V11.8011H56.0065V7.25967H58.8553L61.4144 11.8011H64.2463L61.4127 6.91376C62.8349 6.38254 63.6492 5.26392 63.6492 3.72928ZM56.0065 2.43646H59.1625C60.4249 2.43646 61.0903 2.88398 61.0903 3.72928C61.0903 4.57459 60.4249 5.0221 59.1625 5.0221H56.0065V2.43646Z"})]})}},22769:function(e,t,n){"use strict";n.d(t,{s:function(){return i}});var r=n(27573),a=n(10607);function i(e){let{className:t}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 184 40",className:(0,a.Z)("text-text-000",t),fill:"currentColor",children:[(0,r.jsx)("path",{shapeRendering:"optimizeQuality",fill:"#D97757",d:"m7.75 26.27 7.77-4.36.13-.38-.13-.21h-.38l-1.3-.08-4.44-.12-3.85-.16-3.73-.2-.94-.2L0 19.4l.09-.58.79-.53 1.13.1 2.5.17 3.75.26 2.72.16 4.03.42h.64l.09-.26-.22-.16-.17-.16-3.88-2.63-4.2-2.78-2.2-1.6L3.88 11l-.6-.76-.26-1.66L4.1 7.39l1.45.1.37.1 1.47 1.13 3.14 2.43 4.1 3.02.6.5.24-.17.03-.12-.27-.45L13 9.9l-2.38-4.1-1.06-1.7-.28-1.02c-.1-.42-.17-.77-.17-1.2L10.34.21l.68-.22 1.64.22.69.6 1.02 2.33 1.65 3.67 2.56 4.99.75 1.48.4 1.37.15.42h.26v-.24l.21-2.81.39-3.45.38-4.44.13-1.25.62-1.5L23.1.57l.96.46.79 1.13-.11.73-.47 3.05-.92 4.78-.6 3.2h.35l.4-.4 1.62-2.15 2.72-3.4 1.2-1.35 1.4-1.49.9-.71h1.7l1.25 1.86-.56 1.92-1.75 2.22-1.45 1.88-2.08 2.8-1.3 2.24.12.18.31-.03 4.7-1 2.54-.46 3.03-.52 1.37.64.15.65-.54 1.33-3.24.8-3.8.76-5.66 1.34-.07.05.08.1 2.55.24 1.09.06h2.67l4.97.37 1.3.86.78 1.05-.13.8-2 1.02-2.7-.64-6.3-1.5-2.16-.54h-.3v.18l1.8 1.76 3.3 2.98 4.13 3.84.21.95-.53.75-.56-.08-3.63-2.73-1.4-1.23-3.17-2.67h-.21v.28l.73 1.07 3.86 5.8.2 1.78-.28.58-1 .35-1.1-.2L26 33.14l-2.33-3.57-1.88-3.2-.23.13-1.11 11.95-.52.61-1.2.46-1-.76-.53-1.23.53-2.43.64-3.17.52-2.52.47-3.13.28-1.04-.02-.07-.23.03-2.36 3.24-3.59 4.85-2.84 3.04-.68.27-1.18-.61.11-1.09.66-.97 3.93-5 2.37-3.1 1.53-1.79-.01-.26h-.09L6.8 30.56l-1.86.24-.8-.75.1-1.23.38-.4 3.14-2.16Z"}),(0,r.jsx)("path",{shapeRendering:"optimizeQuality",d:"M64.48 33.54c-5.02 0-8.45-2.8-10.07-7.11a19.19 19.19 0 0 1-1.23-7.03c0-7.23 3.24-12.25 10.4-12.25 4.81 0 7.78 2.1 9.47 7.11h2.06l-.28-6.91c-2.88-1.86-6.48-2.8-10.86-2.8-6.17 0-11.42 2.76-14.34 7.74a16.77 16.77 0 0 0-2.22 8.65c0 5.53 2.61 10.43 7.51 13.15a17.51 17.51 0 0 0 8.73 2.06c4.78 0 8.57-.91 11.93-2.5l.87-7.62h-2.1c-1.26 3.48-2.76 5.57-5.25 6.68-1.22.55-2.76.83-4.62.83ZM86.13 7.15l.2-3.4h-1.42l-6.32 1.9v1.03l2.8 1.3v23.78c0 1.62-.83 1.98-3 2.25v1.74h10.75v-1.74c-2.18-.27-3-.63-3-2.25V7.16Zm42.75 29h.83l7.27-1.38v-1.78l-1.02-.08c-1.7-.16-2.14-.51-2.14-1.9V18.33l.2-4.07h-1.15l-6.87.99v1.74l.67.12c1.86.27 2.41.79 2.41 2.09v11.3c-1.78 1.38-3.48 2.25-5.5 2.25-2.24 0-3.63-1.14-3.63-3.8V18.34l.2-4.07h-1.18l-6.88.99v1.74l.71.12c1.86.27 2.41.79 2.41 2.09v10.43c0 4.42 2.5 6.52 6.48 6.52 3.04 0 5.53-1.62 7.4-3.87l-.2 3.87ZM108.9 22.08c0-5.65-3-7.82-8.42-7.82-4.78 0-8.25 1.98-8.25 5.26 0 .98.35 1.73 1.06 2.25l3.64-.48c-.16-1.1-.24-1.77-.24-2.05 0-1.86.99-2.8 3-2.8 2.97 0 4.47 2.09 4.47 5.45v1.1l-7.5 2.25c-2.5.68-3.92 1.27-4.87 2.65a5 5 0 0 0-.7 2.8c0 3.2 2.2 5.46 5.96 5.46 2.72 0 5.13-1.23 7.23-3.56.75 2.33 1.9 3.56 3.95 3.56 1.66 0 3.16-.67 4.5-1.98l-.4-1.38c-.58.16-1.14.24-1.73.24-1.15 0-1.7-.91-1.7-2.69v-8.26Zm-9.6 10.87c-2.05 0-3.32-1.19-3.32-3.28 0-1.42.67-2.25 2.1-2.73l6.08-1.93v5.84c-1.94 1.47-3.08 2.1-4.86 2.1Zm63.3 1.82v-1.78l-1.03-.08c-1.7-.16-2.13-.51-2.13-1.9V7.15l.2-3.4h-1.43l-6.32 1.9v1.03l2.8 1.3v7.82a8.83 8.83 0 0 0-5.37-1.54c-6.28 0-11.18 4.78-11.18 11.93 0 5.89 3.52 9.96 9.32 9.96 3 0 5.61-1.46 7.23-3.72l-.2 3.72h.84l7.27-1.38Zm-13.16-18.14c3 0 5.25 1.74 5.25 4.94v9a7.2 7.2 0 0 1-5.21 2.1c-4.3 0-6.48-3.4-6.48-7.94 0-5.1 2.49-8.1 6.44-8.1Zm28.53 4.5c-.56-2.64-2.18-4.14-4.43-4.14-3.36 0-5.69 2.53-5.69 6.16 0 5.37 2.84 8.85 7.43 8.85a8.6 8.6 0 0 0 7.39-4.35l1.34.36c-.6 4.66-4.82 8.14-10 8.14-6.08 0-10.27-4.5-10.27-10.9 0-6.45 4.55-10.99 10.63-10.99 4.54 0 7.74 2.73 8.77 7.47l-15.84 4.86v-2.14l10.67-3.31Z"})]})}},70354:function(e,t,n){"use strict";n.d(t,{z:function(){return p}});var r=n(27573),a=n(11607),i=n(88755),s=n(49289),o=n(10607),l=n(88146),c=n(7653);let u="\n    text-text-000\n    border-0.5\n    border-border-300\n    relative\n    overflow-hidden\n    font-styrene\n    font-medium\n    transition\n    duration-100\n    hover:border-border-300/0\n    bg-bg-300/0\n    hover:bg-bg-400\n    backface-hidden",d=(0,s.j)("inline-flex\n  items-center\n  justify-center\n  relative\n  shrink-0\n  can-focus\n  select-none\n  disabled:pointer-events-none\n  disabled:opacity-50\n  disabled:shadow-none\n  disabled:drop-shadow-none",{variants:{variant:{primary:"bg-text-000\n        text-bg-000\n        relative\n        overflow-hidden\n        font-medium\n        font-styrene\n        transition-transform\n        will-change-transform\n        ease-[cubic-bezier(0.165,0.85,0.45,1)]\n        duration-150\n        hover:scale-y-[1.015]\n        hover:scale-x-[1.005]\n        backface-hidden\n        after:absolute\n        after:inset-0\n        after:bg-[radial-gradient(at_bottom,hsla(var(--bg-000)/20%),hsla(var(--bg-000)/0%))]\n        after:opacity-0\n        after:transition\n        after:duration-200\n        after:translate-y-2\n        hover:after:opacity-100\n        hover:after:translate-y-0",flat:"bg-accent-main-000\n          text-oncolor-100\n          font-styrene\n          font-medium\n          transition-colors\n          hover:bg-accent-main-200",secondary:u,outline:u,ghost:"text-text-300\n          border-transparent\n          transition\n          font-styrene\n          duration-300\n          ease-[cubic-bezier(0.165,0.85,0.45,1)]\n          hover:bg-bg-400\n          aria-pressed:bg-bg-400\n          aria-checked:bg-bg-400\n          aria-expanded:bg-bg-300\n          hover:text-text-100\n          aria-pressed:text-text-100\n          aria-checked:text-text-100\n          aria-expanded:text-text-100",underline:"opacity-80\n          transition-all\n          active:scale-[0.985]\n          hover:opacity-100\n          hover:underline\n          underline-offset-3",danger:"bg-danger-200\n          text-oncolor-100\n          font-styrene\n          font-medium\n          transition\n          hover:scale-y-[1.015]\n          hover:scale-x-[1.005]\n          hover:opacity-95",unstyled:""},size:{default:"h-9 px-4 py-2 rounded-lg min-w-[5rem] active:scale-[0.985] whitespace-nowrap text-sm",sm:"h-8 rounded-md px-3 text-xs min-w-[4rem] active:scale-[0.985] whitespace-nowrap",lg:"h-11 rounded-[0.6rem] px-5 min-w-[6rem] active:scale-[0.985] whitespace-nowrap",icon:"h-9 w-9 rounded-md active:scale-95 shrink-0",icon_xs:"h-6 w-6 rounded-md active:scale-95",icon_sm:"h-8 w-8 rounded-md active:scale-95",icon_lg:"h-11 w-11 rounded-[0.6rem] active:scale-95",inline:"px-0.5 rounded-[0.25rem]",unset:""},option:{rounded:"!rounded-full",prepend:"",append:""},state:{active:""}},compoundVariants:[{size:"default",option:"prepend",class:"pl-2 pr-3 gap-1"},{size:"lg",option:"prepend",class:"pl-2.5 pr-3.5 gap-1"},{size:"sm",option:"prepend",class:" pl-2 pr-2.5 gap-1"},{size:"default",option:"append",class:"pl-3 pr-2 gap-1"},{size:"lg",option:"append",class:"pl-3.5 pr-2.5 gap-1"},{size:"sm",option:"append",class:"pl-2.5 pr-2 gap-1"},{variant:"ghost",state:"active",class:"!bg-bg-400"}],defaultVariants:{variant:"primary",size:"default"}}),p=(0,c.forwardRef)((e,t)=>{let{className:n,variant:s,size:c,option:u,loading:p,href:m,onLinkClick:f,target:g,prepend:h,append:v,state:b,disabled:x,children:_,type:y="button",...w}=e;h&&(u="prepend"),v&&(u="append");let j=(0,i.useIsClaudeApp)(),C=(0,o.Z)(d({variant:s,size:c,option:u,state:b,className:n}),p&&"text-transparent ![text-shadow:_none]",j&&"cursor-default"),k=(0,r.jsxs)(r.Fragment,{children:[p&&(0,r.jsx)("div",{className:(0,o.Z)("absolute inset-0 flex items-center justify-center",s&&"flat"!==s&&"danger"!==s?"text-bg-300":"text-oncolor-100"),children:(0,r.jsx)(a.Loading,{size:"sm",inheritColor:!0,delay:0})}),h,_,v]});return m?(0,r.jsx)(l.default,{href:m,target:g||"_self",className:C,"aria-label":w["aria-label"],onClick:f,children:k}):(0,r.jsx)("button",{className:C,ref:t,disabled:x||p,type:y,...w,children:k})});p.displayName="Button"},11607:function(e,t,n){"use strict";n.r(t),n.d(t,{Loading:function(){return o}});var r=n(27573),a=n(10607),i=n(7653),s=n(45790);let o=(0,i.memo)(function(e){let{size:t="md",fullscreen:n=!1,inheritColor:o,delay:l=0}=e,[c,u]=(0,i.useState)(l>0);return(0,i.useEffect)(()=>{if(!l)return;let e=setTimeout(()=>u(!1),l);return()=>clearTimeout(e)},[l]),(0,r.jsx)("div",{className:(0,a.Z)(n?"fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2":"m-auto"),children:(0,r.jsx)("div",{className:(0,a.Z)("sm"===t&&"h-4 w-4 border-2","md"===t&&"h-20 w-20 border-8",o?"border-current":"border-border-200","text-secondary inline-block animate-spin rounded-full border-solid border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",c&&"hidden"),role:"status",children:(0,r.jsx)("span",{className:"sr-only",children:(0,r.jsx)(s.Z,{defaultMessage:"Loading...",id:"gjBiyjshwX"})})})})})},22889:function(e,t,n){"use strict";n.d(t,{Toasts:function(){return v}});var r=n(56683),a=n(27573),i=n(8571),s=n(27895),o=n(9788),l=n(10484),c=n(80151),u=n(62168),d=n(11510),p=n(22136),m=n(10607);function f(){let e=(0,r._)(['\n  flex\n  justify-end\n  data-[state="closed"]:animate-[fade_200ms_ease-in_forwards]\n  data-[state="closed"]:[animation-direction:reverse]\n  data-[swipe="move"]:translate-x-[var(--radix-toast-swipe-move-x)]\n  data-[swipe="cancel"]:transform-[translate-x_0]\n  data-[swipe="cancel"]:transition-transform\n  data-[swipe="cancel"]:duration-200\n  data-[swipe="cancel"]:ease-out\n  data-[swipe="end"]:animate-[translate-x_100ms_ease-out]\n']);return f=function(){return e},e}let g=e=>{let t,{toast:n,count:r=1}=e,{message:i,toastType:s}=n;switch(s){case"error":t=(0,a.jsxs)("div",{className:"text-danger-000 flex flex-row items-center gap-2",children:[(0,a.jsx)("div",{children:(0,a.jsx)(l.Q,{size:18})}),(0,a.jsxs)(p.Dx,{className:"flex items-center",children:["string"==typeof i?(0,a.jsx)("p",{children:i}):i,r>1&&(0,a.jsx)("span",{className:"bg-danger-100 text-danger-900 -my-1 ml-2 rounded-full px-2 py-1 text-xs font-bold",children:r})]}),(0,a.jsx)(p.x8,{className:"ml-auto",children:(0,a.jsx)(c.X,{size:18})})]});break;case"success":t=(0,a.jsxs)("div",{className:"text-accent-secondary-100 flex flex-row items-center gap-2",children:[(0,a.jsx)("div",{children:(0,a.jsx)(u.f,{size:18})}),(0,a.jsxs)(p.Dx,{className:"flex items-center",children:["string"==typeof i?(0,a.jsx)("p",{children:i}):i,r>1&&(0,a.jsx)("span",{className:"bg-accent-secondary-100 text-accent-secondary-900 -my-1 ml-2 rounded-full px-2 py-1 text-xs font-bold",children:r})]}),(0,a.jsx)(p.x8,{className:"ml-auto",children:(0,a.jsx)(c.X,{size:18})})]})}return(0,a.jsx)(b,{children:t})};function h(){let{currentSystemMessageId:e,currentSystemMessageTitle:t,currentSystemMessageContent:n,dismissCurrentSystemMessage:r}=(0,s.v)();return e?(0,a.jsx)(p.fC,{asChild:!0,duration:1/0,onOpenChange:e=>{e||setTimeout(()=>r(),200)},children:(0,a.jsx)(x,{className:'data-[state="open"]:animate-[translate-x_200ms_cubic-bezier(0.16,1,0.3,1)_forwards] data-[state="open"]:[animation-direction:reverse]',children:(0,a.jsx)(b,{children:(0,a.jsxs)("div",{className:"flex flex-row items-top gap-2",children:[(0,a.jsx)("div",{className:"text-danger-000 pt-0.5",children:(0,a.jsx)(d.k,{size:16})}),(0,a.jsxs)("div",{className:"text-text-200 text-sm font-styrene max-w-60",children:[(0,a.jsx)(p.Dx,{className:"font-medium mb-1",children:t}),(0,a.jsx)(p.dk,{children:n})]}),(0,a.jsx)("div",{className:"pt-0.5",children:(0,a.jsx)(p.x8,{className:"ml-auto","data-testid":"system-message-close-button",children:(0,a.jsx)(c.X,{size:16})})})]})})})},"system-message"):null}function v(){let{toasts:e,clearToast:t}=(0,s.e)(),n=function(e){let t=new Map;return e.forEach(e=>{var n,r;let a=null!==(r=null===(n=e.message)||void 0===n?void 0:n.toString())&&void 0!==r?r:"",i=t.get(a);i?(i.count+=1,i.toastIds.push(e.id)):t.set(a,{count:1,toast:e,toastIds:[e.id]})}),Array.from(t.values())}(e),r=(0,i.Z)();return(0,a.jsxs)(p.zt,{swipeDirection:"right",label:"Error",children:[r?(0,a.jsx)(h,{}):null,n.map(e=>{let n=e.toastIds.join("-");return(0,a.jsx)(p.fC,{asChild:!0,duration:6500,onOpenChange:n=>{n||setTimeout(()=>{e.toastIds.forEach(t)},200)},children:(0,a.jsx)(x,{className:(0,m.Z)(1===e.toastIds.length&&'\n                data-[state="open"]:animate-[translate-x_200ms_cubic-bezier(0.16,1,0.3,1)_forwards]\n                data-[state="open"]:[animation-direction:reverse]\n              '),children:(0,a.jsx)(g,{toast:e.toast,count:e.toastIds.length})})},n)}),(0,a.jsx)(p.l_,{className:"fixed right-0 top-0 z-toast flex flex-col gap-4 p-4"})]})}let b=e=>{let{children:t}=e;return(0,a.jsx)("div",{className:"justify-self-right bg-bg-100 border-border-300 z-toast flex max-w-lg rounded-xl border shadow-lg",children:(0,a.jsx)("div",{className:"flex flex-row px-3 py-4 text-left text-sm",children:t})})},x=o.q.li(f())},32737:function(e,t,n){"use strict";n.d(t,{u:function(){return m}});var r=n(56683),a=n(27573),i=n(9788),s=n(85638),o=n(10607),l=n(68425);function c(){let e=(0,r._)(["\n  px-2\n  py-1\n  text-xs\n  font-medium\n  font-sans\n  leading-tight\n  rounded-md\n  shadow-md\n  text-white\n  bg-black/80\n  backdrop-blur\n  break-words\n  z-tooltip\n  max-w-[13rem]\n  [*:disabled_&]:hidden\n"]);return c=function(){return e},e}function u(){let e=(0,r._)(["\n  max-w-[310px]\n  z-tooltip\n  [*:disabled_&]:hidden\n"]);return u=function(){return e},e}function d(){let e=(0,r._)(["\n  p-1\n  text-xs\n  font-medium\n  font-sans\n  leading-tight\n  rounded-lg\n  shadow-md\n  text-white\n  bg-black/80\n  backdrop-blur\n  break-words\n  z-tooltip\n  max-w-[13rem]\n  [*:disabled_&]:hidden\n"]);return d=function(){return e},e}function p(){let e=(0,r._)(["\n  p-1\n  text-sm\n  font-normal\n  font-styrene\n  leading-tight\n  border-border-300\n  border-0.5\n  rounded-lg\n  shadow-diffused \n  shadow-[hsl(var(--always-black)/4%)]\n  text-text-100\n  bg-bg-100\n  break-words\n  z-tooltip\n  max-w-[13rem]\n  [*:disabled_&]:hidden\n"]);return p=function(){return e},e}function m(e){let{children:t,tooltipContent:n,contentStyle:r="default",side:i="top",sideOffset:l,align:c,className:u,delayDuration:d=200,...p}=e,m=h[r];return(0,a.jsx)(s.zt,{delayDuration:d,children:(0,a.jsxs)(s.fC,{...p,children:[(0,a.jsx)(s.xz,{asChild:!0,children:t}),(0,a.jsx)(s.h_,{children:(0,a.jsx)("span",{children:(0,a.jsx)(m,{className:(0,o.Z)(u,!n&&"hidden"),sideOffset:null!=l?l:5,side:i,align:c,children:n})})})]})})}let f=(0,i.q)(s.VY)(c()),g=(0,i.q)(s.VY)(u()),h={default:f,citation:e=>{let{children:t,...n}=e;return(0,a.jsx)(g,{...n,children:(0,a.jsx)(l.E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.2},className:"p-3 text-sm font-normal font-styrene leading-tight rounded-lg border-0.5 shadow-diffused shadow-[hsl(var(--always-black)/4%)] border-border-300/25 text-text-200 bg-bg-100 break-words",children:t})})},inputMenu:(0,i.q)(s.VY)(d()),sampleImage:(0,i.q)(s.VY)(p())}},39465:function(e,t,n){"use strict";n.d(t,{ConsentBanner:function(){return _}});var r=n(56683),a=n(27573),i=n(50862),s=n(95407),o=n(9677),l=n(9788),c=n(70354),u=n(67587),d=n(80727),p=n(35919),m=n.n(p),f=n(88146),g=n(7653),h=n(45790);function v(){let e=(0,r._)(["\n  fixed right-2 bottom-2 z-toast\n  max-h-[calc(100vh-1rem)]\n  max-w-[calc(100vw-1rem)] sm:max-w-md\n  rounded-3xl\n  font-styrene\n  bg-bg-500\n  p-4\n  sm:p-8 \n"]);return v=function(){return e},e}function b(){let e=(0,r._)(["text-xl col-span-2 mb-2 sm:mb-4 text-text-000"]);return b=function(){return e},e}function x(){let e=(0,r._)(["text-sm text-text-300 mb-4"]);return x=function(){return e},e}function _(){let{showConsentBanner:e,savePreferences:t,preferences:n}=(0,i.C)(),[r,o]=(0,g.useState)(!1),{theme:l}=(0,u.F)();return e?(0,a.jsx)("div",{children:(0,a.jsxs)(j,{className:"overflow-auto","data-theme":l,"data-mode":"dark",children:[(0,a.jsx)(C,{children:(0,a.jsx)(h.Z,{defaultMessage:"Cookie settings",id:"KS0bwV0zAX"})}),r?(0,a.jsx)(w,{savePreferences:t,existingPreferences:n}):(0,a.jsx)(y,{acceptAll:()=>t(s.DF),rejectAll:()=>t(s.iw),openCustomOptions:()=>o(!0)})]})}):null}function y(e){let{acceptAll:t,rejectAll:n,openCustomOptions:r}=e,i=(0,d.Z)("sm");return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k,{children:(0,a.jsx)(h.Z,{defaultMessage:"We use cookies to deliver and improve our services, analyze site usage, and if you agree, to customize or personalize your experience and market our services to you. You can read our Cookie Policy <link>here</link>.",id:"2c0cESbicz",values:{link:e=>(0,a.jsx)(f.default,{className:"underline",href:"https://www.anthropic.com/legal/cookies",children:e})}})}),(0,a.jsxs)("div",{className:"flex grid-cols-3 grid-rows-1 justify-between gap-2 pb-1 sm:grid sm:grid-cols-2 sm:grid-rows-2 sm:pb-0",children:[(0,a.jsx)(c.z,{size:i?"lg":void 0,variant:"outline",className:"col-span-1 grow sm:col-span-2",onClick:r,children:(0,a.jsxs)("span",{className:"text-center",children:[(0,a.jsx)("span",{className:"sm:hidden",children:(0,a.jsx)(h.Z,{defaultMessage:"Customize",id:"TXpOBiuxud"})}),(0,a.jsx)("span",{className:"hidden sm:inline",children:(0,a.jsx)(h.Z,{defaultMessage:"Customize Cookie Settings",id:"yeUvAx5cQ0"})})]})}),(0,a.jsxs)(c.z,{size:i?"lg":void 0,variant:"outline",className:"col-span-1 grow sm:col-span-1",onClick:n,children:[(0,a.jsx)("span",{className:"sm:hidden",children:(0,a.jsx)(h.Z,{defaultMessage:"Reject",id:"6fHmL0DmS8",description:"Button to reject cookies"})}),(0,a.jsx)("span",{className:"hidden sm:inline",children:(0,a.jsx)(h.Z,{defaultMessage:"Reject All Cookies",id:"3AryFa8L4s"})})]}),(0,a.jsxs)(c.z,{size:i?"lg":void 0,className:"col-span-1 grow sm:col-span-1",onClick:t,children:[(0,a.jsx)("span",{className:"sm:hidden",children:(0,a.jsx)(h.Z,{defaultMessage:"Accept",id:"+A0Z2qWPAJ",description:"Button to accept cookies"})}),(0,a.jsx)("span",{className:"hidden sm:inline",children:(0,a.jsx)(h.Z,{defaultMessage:"Accept All Cookies",id:"JyT5uF/dGC"})})]})]})]})}function w(e){let{savePreferences:t,existingPreferences:n}=e,[r,i]=(0,g.useState)(n);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(k,{children:[(0,a.jsx)(h.Z,{defaultMessage:"Our website uses cookies to distinguish you from other users of our website. This helps us provide you with a more personalized experience when you browse our website and also allows us to improve our site.",id:"xjMAP6wxO4"})," ",(0,a.jsx)(h.Z,{defaultMessage:"Cookies may collect information that is used to tailor ads shown to you on our website and other websites. The information might be about you, your preferences or your device.",id:"Esa62Eh0ZL"})," ",(0,a.jsx)(h.Z,{defaultMessage:"The information does not usually directly identify you, but it can give you a more personalized web experience.",id:"xVPw3sZ0pj"})," ",(0,a.jsx)(h.Z,{defaultMessage:"You can choose not to allow some types of cookies.",id:"igZPARqXBw"})]}),(0,a.jsxs)("ul",{className:"mb-8 flex flex-col gap-2",children:[(0,a.jsx)(z,{id:"necessary",name:(0,a.jsx)(h.Z,{defaultMessage:"Necessary",id:"BJEfuCJaFk"}),required:!0,description:(0,a.jsx)(h.Z,{defaultMessage:"Enables security and basic functionality.",id:"Ti95YCDMOG"}),checked:!0,setPreference:m()}),(0,a.jsx)(z,{id:"analytics",name:(0,a.jsx)(h.Z,{defaultMessage:"Analytics",id:"GZJpDfFNLG"}),required:!1,description:(0,a.jsx)(h.Z,{defaultMessage:"Enables tracking of site performance.",id:"vXDIeSbnfA"}),checked:r.analytics,setPreference:e=>i(t=>({...t,analytics:e}))}),(0,a.jsx)(z,{id:"marketing",name:(0,a.jsx)(h.Z,{defaultMessage:"Marketing",id:"O9mDkAC7UB"}),required:!1,description:(0,a.jsx)(h.Z,{defaultMessage:"Enables ads personalization and tracking.",id:"Uv2yd0aQ3g"}),checked:r.marketing,setPreference:e=>i(t=>({...t,marketing:e}))})]}),(0,a.jsx)(c.z,{size:"lg",className:"w-full",onClick:()=>t(r),children:(0,a.jsx)(h.Z,{defaultMessage:"Save preferences",id:"H1eTZT/qIz"})})]})}let j=l.q.div(v()),C=l.q.h3(b()),k=l.q.p(x());function z(e){let{id:t,name:n,required:r,description:i,checked:s,setPreference:l}=e;return(0,a.jsx)("li",{className:"bg-bg-300 rounded-lg p-3",children:(0,a.jsxs)("label",{className:"flex justify-between gap-2",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,a.jsx)("h6",{className:"text-text-000 text-xs",children:n}),(0,a.jsx)("p",{className:"text-text-500 text-xs",children:i})]}),(0,a.jsxs)("div",{className:"flex gap-2 self-center",children:[(0,a.jsx)("span",{className:"text-text-000 self-center text-xs",children:r?(0,a.jsx)(h.Z,{defaultMessage:"Required",id:"Seanpxav9K"}):s?(0,a.jsx)(h.Z,{defaultMessage:"On",id:"Zh+5A6yahu"}):(0,a.jsx)(h.Z,{defaultMessage:"Off",id:"OvzONl52rs"})}),(0,a.jsx)(o.Z,{disabled:r,checked:s,onChange:()=>l(!s)})]})]})},t)}},44602:function(e,t,n){"use strict";n.d(t,{C:function(){return m}});var r=n(56683),a=n(27573),i=n(9788),s=n(64483),o=n(22769),l=n(11607);function c(){let e=(0,r._)(["\n  mx-0\n  mt-4\n  min-w-[16rem]\n"]);return c=function(){return e},e}function u(){let e=(0,r._)(["\n  grid\n  place-content-center\n  min-h-min\n  text-center\n  gap-2\n  pt-24\n  pb-32\n  px-4\n  mx-auto\n  h-screen\n"]);return u=function(){return e},e}function d(){let e=(0,r._)(["\n  font-copernicus\n  font-medium\n  tracking-tighter\n  text-4xl\n"]);return d=function(){return e},e}function p(){let e=(0,r._)(["\n  font-styrene\n  text-text-300\n  text-lg\n"]);return p=function(){return e},e}function m(e){let{productSurface:t,headline:n,subheading:r,button:i,testId:c,compact:u,loading:d}=e,p="claude-ai"===t?o.s:s.I;return(0,a.jsx)(g,{"data-testid":c,className:u?"max-w-min":"w-fit",children:d?(0,a.jsx)(l.Loading,{delay:1e3}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"mb-10 text-center",children:(0,a.jsx)(p,{className:"inline-block h-6 -ml-1"})}),(0,a.jsx)(h,{children:n}),r&&(0,a.jsx)(v,{children:r}),(0,a.jsx)(f,{children:i})]})})}n(7653);let f=i.q.div(c()),g=i.q.div(u()),h=i.q.h2(d()),v=i.q.h3(p())},67587:function(e,t,n){"use strict";n.d(t,{ThemeProvider:function(){return u},F:function(){return g}});var r=n(27573),a=n(40287),i=n(18013),s=n(6385),o=n(7653);let l="(prefers-color-scheme: dark)",c=(0,o.createContext)(void 0);function u(e){let{initialTheme:t,children:n}=e,[s,u]=(0,o.useState)(t),m=(0,a.f)(),[g,h]=(0,i.R)("userThemeMode","auto"),[v,b]=(0,o.useState)(f(g));(0,o.useEffect)(()=>{b(f(g))},[g]),(0,o.useEffect)(()=>d(s),[s]),(0,o.useEffect)(()=>p(g,m),[m,g]);let x=(0,o.useCallback)(()=>{p(g,m),b(f(g))},[m,g]);return(0,o.useEffect)(()=>{if("auto"!==g)return;let e=window.matchMedia(l);return e.addEventListener("change",x),()=>e.removeEventListener("change",x)},[g,x]),(0,o.useEffect)(()=>{var e,t;null===(t=window.electronWindowControl)||void 0===t||null===(e=t.setThemeMode)||void 0===e||e.call(t,"auto"===g?"system":g)},[g]),(0,r.jsx)(c.Provider,{value:{theme:s,mode:g,setMode:h,setTheme:u,resolvedMode:v},children:n})}let d=e=>{"undefined"!=typeof document&&(document.documentElement.dataset.theme=e,m())},p=(e,t)=>{if("undefined"==typeof document)return;let n=f(e);t.set(s.cn.COLOR_MODE,n),document.documentElement.dataset.mode=n,m()},m=()=>{let[e,t,n]=getComputedStyle(document.documentElement).getPropertyValue("--bg-200").split(" "),r="hsl(".concat(e,",").concat(t,",").concat(n,")"),a=document.querySelector('meta[name="theme-color"]');a||((a=document.createElement("meta")).setAttribute("name","theme-color"),document.head.appendChild(a)),a.setAttribute("content",r)},f=e=>{var t;return"auto"!==e?"auto"===e?"light":e:(null===(t=window)||void 0===t?void 0:t.matchMedia(l).matches)?"dark":"light"},g=()=>{let e=(0,o.useContext)(c);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},80727:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(77879);let a={sm:"(min-width: 640px)",md:"(min-width: 768px)",lg:"(min-width: 1024px)",xl:"(min-width: 1280px)","2xl":"(min-width: 1536px)"};function i(e){return(0,r.ac)(a[e])}},35228:function(e,t,n){"use strict";n.d(t,{D_:function(){return s},c6:function(){return a},cG:function(){return o},kK:function(){return i},wJ:function(){return r}});let r=e=>{let{account:t,isClaudeDot:n}=e;return n?!1===t.settings.has_finished_claudeai_onboarding:!t.full_name||!t.display_name},a=(e,t)=>t&&!e.is_verified,i=(e,t)=>!!function(e,t){for(let n of e.invites)if(n.organization.capabilities&&n.organization.capabilities.includes(t))return n}(e,t?"raven":"api"),s=(e,t)=>!t&&0===e.invites.length&&0===e.memberships.filter(e=>e.organization.capabilities.includes("api")).length,o=(e,t)=>r({account:e,isClaudeDot:t})||a(e,t)||i(e,t)||s(e,t)},82083:function(e,t,n){"use strict";n.d(t,{An:function(){return j},HZ:function(){return k},JP:function(){return a},Km:function(){return x},VA:function(){return p},kE:function(){return u},l2:function(){return _},tE:function(){return C},w0:function(){return w},yc:function(){return d}});var r,a,i,s=n(41270),o=n(98731),l=n(53462);(r=a||(a={})).Text="text/plain",r.Markdown="text/markdown",r.Html="text/html",r.Code="application/vnd.ant.code",r.Svg="image/svg+xml",r.Mermaid="application/vnd.ant.mermaid",r.React="application/vnd.ant.react",(i||(i={})).Repl="application/vnd.ant.repl";let c=s.z.string().nullish().transform(e=>null!=e?e:void 0),u=s.z.object({version_uuid:c,command:s.z.enum(["create","update","rewrite"]).optional().catch(void 0),id:c,title:c,type:s.z.nativeEnum(a).optional().catch(void 0),language:c,content:c,old_str:c,new_str:c,md_citations:s.z.unknown()}),d={"text/plain":{syntaxName:"plaintext",extension:()=>"txt"},"text/markdown":{syntaxName:"markdown",extension:()=>"md"},"text/html":{syntaxName:"html",extension:()=>"html"},"image/svg+xml":{syntaxName:"svg",extension:()=>"svg"},"application/vnd.ant.code":{syntaxName:"plaintext",extension:e=>{switch(null==e?void 0:e.language){case"python":return"py";case"javascript":return"js";case"typescript":return"ts";case"java":return"java";case"csharp":return"cs";case"go":return"go";case"rust":return"rs";case"ruby":return"rb";case"php":return"php";case"swift":return"swift";case"kotlin":return"kt";case"dart":return"dart";case"scala":return"scala";case"perl":return"pl";case"elixir":return"ex";case"haskell":return"hs";case"clojure":return"clj";case"r":return"r";case"groovy":return"groovy";case"bash":return"sh";case"css":return"css";case"scss":return"scss";case"less":return"less";case"sql":return"sql";case"graphql":return"graphql";case"json":return"json";case"html":return"html";default:return"txt"}}},"application/vnd.ant.mermaid":{syntaxName:"mermaid",extension:()=>"mermaid"},"application/vnd.ant.react":{syntaxName:"tsx",extension:()=>"tsx"}},p=e=>{let t=d[e.type];return"application/vnd.ant.code"!==e.type?t.extension():t.extension(e)},m="antArtifact",f="<".concat(m,"(?:\\s+([^>]*)>|(?!>)\\S*)"),g="</".concat(m,">"),h=RegExp("".concat(f,"([\\s\\S]*?)(?:").concat(g,"|$)"),"g"),v=RegExp("<(?:a(?:n(?:t(?:A(?:r(?:t(?:i(?:f(?:a(?:c(?:t)?)?)?)?)?)?)?)?)?)?)?$","gm"),b=RegExp("".concat(f,"[\\s\\S]*?(?:").concat(g,"|$)"),"g"),x="ANTARTIFACTLINK";function _(e){var t;let n=e.match(/identifier="([^"]*)"/),r=n?n[1]:"",i=e.match(/type="([^"]*)"/),s=i?(t=i[1],Object.values(a).includes(t)?t:"text/plain"):"text/plain",o=e.match(/title="([^"]*)"/),l=o?o[1]:"Untitled artifact",c=e.match(/language="([^"]*)"/),u=c?c[1]:void 0,d=e.match(/isClosed="(true|false)"/);return{id:r,type:s,title:l,language:u,isClosed:d?"true"===d[1]:void 0}}function y(e){return e.replace(v,"")}function w(e){return M(y(e)).replace(h,(e,t,n)=>{if(!t||"string"!=typeof t)return"";{let{type:e,language:r}=_(t);return"text/markdown"!==e&&"string"==typeof n?"```".concat(r||d[e].syntaxName,"\n").concat(n.trimStart(),"\n```"):"\n".concat(n)}})}function j(e){return y(M(e).replace(b,(e,t)=>{let n=e.endsWith(g);return"<".concat(x," ").concat(t," isClosed=“").concat(n,"” />")}))}let C=(e,t)=>"create"===e.command||"rewrite"===e.command?e.content:"update"===e.command?void 0===t?e.new_str:t.includes(e.old_str)?t.replace(e.old_str,e.new_str):t.replace(e.old_str.trim(),e.new_str):null!=t?t:"";function k(e){var t;let n=(0,o.K)(e).matchAll(h),r=[];for(let t of n){let[n,a,i]=t,s=n.endsWith(g);if(!a||!i)continue;let{id:o,type:l,title:c,language:u}=_(a),d={command:"create",message:e.uuid||"",created_at:e.created_at,content:i.trimStart(),isClosed:s};r.push({id:o,currentVersion:d,type:l,title:c,language:u})}return null===(t=e.content)||void 0===t||t.map((t,n,a)=>{if("tool_use"===t.type&&"artifacts"===t.name&&t.input){let i;let s=(0,l.n)(t),o=n===a.length-1,{version_uuid:c,command:d,id:p,type:m="text/plain",title:f="",language:g,content:h="",old_str:v="",new_str:b="",md_citations:x}=u.parse(s),_=(null==x?void 0:x.length)?h:h.trimStart();"create"===d||"rewrite"===d?i={uuid:c,command:d,citations:x,content:_,message:e.uuid||"",created_at:e.created_at,isClosed:!o}:"update"===d&&""!==h?i={uuid:c,command:"rewrite",message:e.uuid||"",created_at:e.created_at,isClosed:!o,content:h}:"update"===d&&(i={uuid:c,command:"update",message:e.uuid||"",created_at:e.created_at,isClosed:!o,old_str:v,new_str:b}),void 0!==p&&void 0!==i&&r.push({id:p,currentVersion:i,type:m,title:f,language:g})}}),r}let z="antThinking",S="<".concat(z,">"),P=RegExp("".concat(S,"[\\s\\S]*?(?:</").concat(z,">|$)"),"g"),E=RegExp("<(?:a(?:n(?:t(?:T(?:h(?:i(?:n(?:k(?:i(?:n(?:g)?)?)?)?)?)?)?)?)?)?)?$","gm");function M(e){return e.replace(E,"").replace(P,"")}},53949:function(e,t,n){"use strict";var r,a;n.d(t,{Y:function(){return r}}),(a=r||(r={})).Image="image",a.Blob="blob",a.SanitizedDocument="document"},37458:function(e,t,n){"use strict";function r(e){let t=function(e){let t=[],n=0;for(;n<e.length;){let r=e[n];if(/\s/.test(r)){n++;continue}if("{[]}:,".includes(r)){t.push(r),n++;continue}if('"'===r){let a=r;n++;let i=!1,s=!1;for(;n<e.length;){if(a+=r=e[n],'"'===r&&!i){s=!0;break}i="\\"===r&&!i,n++}if(!s){if(a.slice(-5).includes("\\"))for(a=a.slice(0,a.lastIndexOf("\\"));a.endsWith("\\");)a=a.slice(0,-1);a+='"'}t.push(a),n++;continue}if(/[-0-9]/.test(r)){let r="";for(;n<e.length&&/[-0-9.eE+]/.test(e[n]);)r+=e[n],n++;if(!r.match(/\d$/))for(;r.length>0&&!r[r.length-1].match(/\d/);)r=r.slice(0,-1);t.push(r);continue}if(/[tfn]/.test(r)){let a="",i=["true","false","null"],s=i.filter(e=>e.startsWith(r));if(1===s.length)n+=(a=s[0]).length;else for(;n<e.length&&/[a-z]/.test(e[n])&&(a+=e[n],n++,!i.includes(a)););i.includes(a)&&t.push(a);continue}n++}return t}(e),n=[];for(let e of t)"{"===e||"["===e?n.push("{"===e?"}":"]"):("}"===e||"]"===e)&&n.length>0&&n.pop();n.reverse(),","===t[t.length-1]&&t.pop();let r=t.concat(n).join("");if(""===r)return null;try{return JSON.parse(r)}catch(r){let e=Math.max(t.lastIndexOf(","),t.lastIndexOf("{"),t.lastIndexOf("["));if(-1!==e){let r=t.slice(0,e+1);return","===r[r.length-1]&&r.pop(),JSON.parse(r.concat(n).join(""))}throw Error("Invalid JSON")}}n.d(t,{g:function(){return r}})},53462:function(e,t,n){"use strict";n.d(t,{n:function(){return a}});var r=n(37458);let a=e=>{if(e.input&&Object.keys(e.input).length>0)return e.input;if(e.partial_json){let t={};try{t=(0,r.g)(e.partial_json)}catch(e){}return null!=t?t:{}}return{}}},36529:function(){},85903:function(){}},function(e){e.O(0,[9427,5284,6483,6841,5790,3279,6947,6340,5992,7423,1270,7879,6971,1211,7265,1121,3097,9505,3290,8381,2970,223,2282,4102,8877,6933,9205,1293,1362,4856,1744],function(){return e(e.s=31431)}),_N_E=e.O()}]);