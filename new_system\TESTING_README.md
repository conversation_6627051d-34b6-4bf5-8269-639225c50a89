# Instagram Management System - Testing Guide

This document explains how to use the testing programs to verify that all bots and core functionality are working correctly.

## Test Programs Overview

We have created three comprehensive testing programs:

1. **Quick System Test** (`quick_test.py`) - Fast verification of basic functionality
2. **Bot Functionality Test** (`test_bots_functionality.py`) - Test each bot's core capabilities
3. **Full System Integration Test** (`test_system_integration.py`) - Comprehensive end-to-end testing

## Easy Test Runner

Use `run_tests.py` for a simple menu-driven interface to run any test:

```bash
python run_tests.py
```

This provides a menu with options:
- Quick System Test
- Bot Functionality Test  
- Full System Integration Test
- Run All Tests Sequentially
- Check System Status

## Individual Test Programs

### 1. Quick System Test (`quick_test.py`)

**Purpose**: Fast verification that the system can start up and basic components work.

**What it tests**:
- Module imports
- Database connection
- Instagram Manager initialization
- Bot initialization
- Configuration files existence

**Usage**:
```bash
python quick_test.py
```

**Best for**: Quick verification after system changes or startup.

### 2. Bot Functionality Test (`test_bots_functionality.py`)

**Purpose**: Test each bot's actual working capabilities, not just initialization.

**What it tests**:
- **Bio Scanner Bot**: CSV loading, user data retrieval, database saving
- **Data Retriever Bot**: User search, user info retrieval, media retrieval
- **Message Bot**: Message template loading, message formatting
- **Account Warmup Bot**: Action selection, configuration validation

**Usage**:
```bash
python test_bots_functionality.py
```

**Best for**: Verifying that bots can actually perform their intended functions.

### 3. Full System Integration Test (`test_system_integration.py`)

**Purpose**: Comprehensive testing of the entire system working together.

**What it tests**:
- All quick test items
- Account login and session management
- Complete bot workflows
- Database operations
- System performance metrics
- Error handling and recovery

**Usage**:
```bash
python test_system_integration.py
```

**Best for**: Full system validation before production use.

## Test Results Interpretation

### Success Indicators (✅)

- **All tests passed**: System is working correctly
- **High success rate**: Minor issues that don't affect core functionality
- **Green checkmarks**: Individual components working as expected

### Warning Indicators (⚠️)

- **Some tests failed**: Check specific error messages
- **Partial functionality**: Some bots working, others need attention
- **Database issues**: Connection or schema problems

### Error Indicators (❌)

- **Critical failures**: Major system issues that need immediate attention
- **Import errors**: Missing dependencies or broken modules
- **Configuration errors**: Missing or incorrect settings

## Common Test Scenarios

### After System Startup
```bash
python quick_test.py
```

### After Bot Modifications
```bash
python test_bots_functionality.py
```

### Before Production Use
```bash
python test_system_integration.py
```

### Regular Health Check
```bash
python run_tests.py
# Choose option 5: Check System Status
```

## Troubleshooting Common Issues

### Import Errors
- Check that all required Python packages are installed
- Verify file paths and directory structure
- Ensure Python environment is correct

### Database Connection Issues
- Verify MySQL service is running
- Check database credentials in `config.py`
- Ensure database schema is correct

### Instagram API Issues
- Check account credentials
- Verify proxy configuration
- Ensure Instagram accounts are not blocked

### Bot Initialization Failures
- Check bot dependencies
- Verify configuration files
- Review error logs for specific issues

## Test Output Files

- **`system_test.log`**: Detailed logging from integration tests
- **Console output**: Real-time test results and status
- **Exit codes**: 0 = success, 1 = failure (useful for automation)

## Automation

You can run tests automatically:

```bash
# Run quick test and check exit code
python quick_test.py
if [ $? -eq 0 ]; then
    echo "System is healthy"
else
    echo "System has issues"
fi
```

## Performance Considerations

- **Quick Test**: ~5-10 seconds
- **Bot Functionality Test**: ~15-30 seconds  
- **Full Integration Test**: ~1-3 minutes

## Best Practices

1. **Run quick test first** after any system changes
2. **Use bot functionality test** when modifying bot code
3. **Run full integration test** before important operations
4. **Check system status** regularly for preventive maintenance
5. **Review logs** when tests fail to understand root causes

## Support

If tests consistently fail:
1. Check the error messages and logs
2. Verify system configuration
3. Ensure all dependencies are installed
4. Review recent system changes
5. Check Instagram account status and proxy configuration

---

**Note**: These tests are designed to be safe and won't perform actual Instagram actions that could trigger security measures. They only test the system's ability to prepare and execute operations.

