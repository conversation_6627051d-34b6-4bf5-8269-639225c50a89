2025-08-27 10:51:23,160 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 10:51:24,668 - core.database - INFO - Database connection successful
2025-08-27 10:51:24,670 - core.database - INFO - Database Manager initialized
2025-08-27 10:51:24,696 - __main__ - INFO - Logging system initialized
2025-08-27 10:51:24,752 - core.database - INFO - Table message_logs created/verified
2025-08-27 10:51:24,774 - core.database - INFO - Table scraped_users created/verified
2025-08-27 10:51:24,792 - core.database - INFO - Table message_logs created/verified
2025-08-27 10:51:24,806 - core.database - INFO - Table scraped_users created/verified
2025-08-27 10:51:24,807 - __main__ - INFO - System initialization complete
2025-08-27 10:51:24,899 - app - INFO - Loading 5 accounts from database
2025-08-27 10:51:24,901 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 10:51:24,902 - core.instagram_manager - INFO - Added account 1: k<PERSON><PERSON><PERSON><PERSON><PERSON> (new session)
2025-08-27 10:51:24,905 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-27 10:51:24,906 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-27 10:51:24,908 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-27 10:51:24,908 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-27 10:51:24,910 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-27 10:51:24,910 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-27 10:51:24,911 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-27 10:51:24,912 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-27 10:51:24,912 - app - INFO - Successfully loaded 5 accounts
2025-08-27 10:51:24,912 - app - INFO - Instagram Management Application initialized
2025-08-27 10:51:24,912 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-27 10:51:24,968 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-27 10:51:24,969 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 10:51:30,480 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:30] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-27 10:51:30,534 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:30] "GET /login/ HTTP/1.1" 200 -
2025-08-27 10:51:31,276 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,312 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1" 200 -
2025-08-27 10:51:31,335 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/core/popper.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,376 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/moment.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,380 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/sweetalert2.js HTTP/1.1" 200 -
2025-08-27 10:51:31,387 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,421 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1" 200 -
2025-08-27 10:51:31,427 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,431 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1" 200 -
2025-08-27 10:51:31,433 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/core/jquery.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,436 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,437 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/demo/demo.css HTTP/1.1" 200 -
2025-08-27 10:51:31,445 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,455 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1" 200 -
2025-08-27 10:51:31,460 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,496 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,502 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1" 200 -
2025-08-27 10:51:31,513 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/nouislider.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,517 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/arrive.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,549 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/chartist.min.js HTTP/1.1" 200 -
2025-08-27 10:51:31,564 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1" 200 -
2025-08-27 10:51:31,566 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1" 200 -
2025-08-27 10:51:31,569 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:31] "GET /static/assets/demo/demo.js HTTP/1.1" 200 -
2025-08-27 10:51:32,456 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:32] "GET /static/assets/img/favicon.png HTTP/1.1" 200 -
2025-08-27 10:51:41,588 - app - INFO - User admin logged in successfully
2025-08-27 10:51:41,594 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:41] "[32mPOST /login/ HTTP/1.1[0m" 302 -
2025-08-27 10:51:41,684 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:41] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-27 10:51:41,770 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:41] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-27 10:51:41,771 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:41] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 10:51:41,802 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:41] "GET /static/assets/img/inst.png HTTP/1.1" 200 -
2025-08-27 10:51:42,009 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,009 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,012 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,013 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,014 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,017 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,042 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,047 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,060 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,060 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,062 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,063 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,075 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,077 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,098 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,101 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,108 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,121 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,123 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,139 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,139 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:42,717 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:42] "GET /check_status/?_=1756263102701 HTTP/1.1" 200 -
2025-08-27 10:51:43,069 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:43] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,760 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "GET /api_run/ HTTP/1.1" 200 -
2025-08-27 10:51:46,873 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,879 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,880 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,898 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,902 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "GET /static/assets/img/transformation.png HTTP/1.1" 200 -
2025-08-27 10:51:46,914 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,915 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,917 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,919 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,925 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,928 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,934 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,937 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,940 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,943 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,947 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,953 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,962 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,967 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,970 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,978 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,986 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,992 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:46,998 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:46] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 10:51:47,001 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:47] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-27 10:51:47,281 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:47] "GET /check_status/?_=1756263107259 HTTP/1.1" 200 -
2025-08-27 10:51:47,452 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:51:47] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-27 10:52:17,284 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:52:17] "GET /check_status/?_=1756263107260 HTTP/1.1" 200 -
2025-08-27 10:52:47,291 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:52:47] "GET /check_status/?_=1756263107261 HTTP/1.1" 200 -
2025-08-27 10:53:17,290 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:53:17] "GET /check_status/?_=1756263107262 HTTP/1.1" 200 -
2025-08-27 10:53:47,295 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:53:47] "GET /check_status/?_=1756263107263 HTTP/1.1" 200 -
2025-08-27 10:54:17,297 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:54:17] "GET /check_status/?_=1756263107264 HTTP/1.1" 200 -
2025-08-27 10:54:47,284 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:54:47] "GET /check_status/?_=1756263107265 HTTP/1.1" 200 -
2025-08-27 10:55:17,298 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:55:17] "GET /check_status/?_=1756263107266 HTTP/1.1" 200 -
2025-08-27 10:55:47,800 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:55:47] "GET /check_status/?_=1756263107267 HTTP/1.1" 200 -
2025-08-27 10:56:17,796 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:56:17] "GET /check_status/?_=1756263107268 HTTP/1.1" 200 -
2025-08-27 10:56:38,654 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 10:56:38,933 - core.database - INFO - Database connection successful
2025-08-27 10:56:38,935 - core.database - INFO - Database Manager initialized
2025-08-27 10:56:38,936 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 10:56:38,950 - core.database - INFO - Database connection successful
2025-08-27 10:56:38,951 - core.database - INFO - Database Manager initialized
2025-08-27 10:56:38,962 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-27 10:56:38,963 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-27 10:56:38,964 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-27 10:56:39,978 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-27 10:56:40,767 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:56:41,784 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-27 10:56:42,105 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:56:59,016 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-27 10:56:59,318 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:56:59,319 - core.instagram_manager - ERROR - Error getting media for instagram: challenge_required
2025-08-27 10:57:00,334 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-27 10:57:00,611 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:57:01,624 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-27 10:57:01,934 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:57:10,785 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:57:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 10:57:18,831 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-27 10:57:19,116 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:57:19,117 - core.instagram_manager - ERROR - Account 5: Error searching users - challenge_required
2025-08-27 10:58:10,797 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:58:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 10:58:10,969 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 10:58:11,243 - core.database - INFO - Database connection successful
2025-08-27 10:58:11,244 - core.database - INFO - Database Manager initialized
2025-08-27 10:58:11,245 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 10:58:11,258 - core.database - INFO - Database connection successful
2025-08-27 10:58:11,259 - core.database - INFO - Database Manager initialized
2025-08-27 10:58:11,272 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-27 10:58:11,273 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-27 10:58:11,275 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-27 10:58:12,285 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-27 10:58:13,031 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:58:14,044 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-27 10:58:14,443 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:58:31,539 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-27 10:58:31,820 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.0.0.18.75, OnePlus 6T Dev)
2025-08-27 10:58:31,821 - core.instagram_manager - ERROR - Error getting user info for adelkatarina123: challenge_required
2025-08-27 10:59:10,788 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 10:59:10] "GET /check_status/?_=1756263107271 HTTP/1.1" 200 -
2025-08-27 11:00:10,789 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:00:10] "GET /check_status/?_=1756263107272 HTTP/1.1" 200 -
2025-08-27 11:00:36,224 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:00:36] "GET /check_status/?_=1756263107273 HTTP/1.1" 200 -
2025-08-27 11:00:47,296 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:00:47] "GET /check_status/?_=1756********* HTTP/1.1" 200 -
2025-08-27 11:01:17,282 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:01:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:01:47,295 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:01:47] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:02:17,291 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:02:36,808 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /account_list/ HTTP/1.1" 200 -
2025-08-27 11:02:36,893 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,894 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,895 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1" 200 -
2025-08-27 11:02:36,895 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,926 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /static/assets/img/csv-import.png HTTP/1.1" 200 -
2025-08-27 11:02:36,927 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /static/assets/img/clear.png HTTP/1.1" 200 -
2025-08-27 11:02:36,937 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /static/assets/img/all-delete.png HTTP/1.1" 200 -
2025-08-27 11:02:36,944 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,945 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,946 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,957 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,959 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,962 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,967 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,972 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,972 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,977 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /static/assets/img/eye.png HTTP/1.1" 200 -
2025-08-27 11:02:36,983 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "GET /static/assets/img/edit-icon.png HTTP/1.1" 200 -
2025-08-27 11:02:36,986 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,987 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,989 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,993 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:36,996 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:36] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,002 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,005 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,008 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,010 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,013 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,017 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,022 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:37,027 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,030 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1" 200 -
2025-08-27 11:02:37,032 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,035 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,036 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,038 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,057 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1" 200 -
2025-08-27 11:02:37,062 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,068 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,099 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,103 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,103 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1" 200 -
2025-08-27 11:02:37,104 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /static/assets/js/datatables.js HTTP/1.1" 200 -
2025-08-27 11:02:37,417 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "GET /check_status/?_=1756263757298 HTTP/1.1" 200 -
2025-08-27 11:02:37,460 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:37] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,236 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "GET /api_run/ HTTP/1.1" 200 -
2025-08-27 11:02:56,283 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,289 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,290 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,292 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,315 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,318 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,327 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,334 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,335 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,337 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,338 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,340 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,350 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,356 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,360 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,362 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,364 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,369 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,378 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,379 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,385 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,392 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,395 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,398 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,399 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-27 11:02:56,589 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "GET /check_status/?_=1756263776564 HTTP/1.1" 200 -
2025-08-27 11:02:56,592 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:02:56] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-27 11:03:47,645 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:03:47,918 - core.database - INFO - Database connection successful
2025-08-27 11:03:47,919 - core.database - INFO - Database Manager initialized
2025-08-27 11:03:47,925 - __main__ - INFO - Logging system initialized
2025-08-27 11:03:47,957 - core.database - INFO - Table message_logs created/verified
2025-08-27 11:03:47,972 - core.database - INFO - Table scraped_users created/verified
2025-08-27 11:03:47,985 - core.database - INFO - Table message_logs created/verified
2025-08-27 11:03:47,997 - core.database - INFO - Table scraped_users created/verified
2025-08-27 11:03:47,997 - __main__ - INFO - System initialization complete
2025-08-27 11:03:48,053 - app - INFO - Loading 5 accounts from database
2025-08-27 11:03:48,054 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:03:48,055 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-27 11:03:48,056 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-27 11:03:48,056 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-27 11:03:48,057 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-27 11:03:48,058 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-27 11:03:48,059 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-27 11:03:48,059 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-27 11:03:48,061 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-27 11:03:48,061 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-27 11:03:48,062 - app - INFO - Successfully loaded 5 accounts
2025-08-27 11:03:48,062 - app - INFO - Instagram Management Application initialized
2025-08-27 11:03:48,062 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-27 11:03:48,075 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-27 11:03:48,076 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-27 11:03:56,600 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:03:56] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:04:26,603 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:04:26] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:04:56,791 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:04:56] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:05:26,793 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:05:26] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:06:10,786 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:06:10] "GET /check_status/?_=1756263776570 HTTP/1.1" 200 -
2025-08-27 11:07:10,794 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:07:10] "GET /check_status/?_=1756263776571 HTTP/1.1" 200 -
2025-08-27 11:08:10,790 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:08:10] "GET /check_status/?_=1756263776572 HTTP/1.1" 200 -
2025-08-27 11:08:48,034 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:08:48] "GET /check_status/?_=1756263776573 HTTP/1.1" 200 -
2025-08-27 11:08:56,783 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:08:56] "GET /check_status/?_=1756263776574 HTTP/1.1" 200 -
2025-08-27 11:09:26,786 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:09:26] "GET /check_status/?_=1756263776575 HTTP/1.1" 200 -
2025-08-27 11:10:10,794 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:10:10] "GET /check_status/?_=1756263776576 HTTP/1.1" 200 -
2025-08-27 11:11:10,790 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:11:10] "GET /check_status/?_=1756263776577 HTTP/1.1" 200 -
2025-08-27 11:11:26,783 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:11:26] "GET /check_status/?_=1756263776578 HTTP/1.1" 200 -
2025-08-27 11:11:56,787 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:11:56] "GET /check_status/?_=1756263776579 HTTP/1.1" 200 -
2025-08-27 11:12:24,270 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:12:24,607 - core.database - INFO - Database connection successful
2025-08-27 11:12:24,608 - core.database - INFO - Database Manager initialized
2025-08-27 11:12:26,144 - core.database - INFO - Database connection successful
2025-08-27 11:12:26,146 - core.database - INFO - Database Manager initialized
2025-08-27 11:12:26,148 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:12:26,790 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:12:26] "GET /check_status/?_=1756263776580 HTTP/1.1" 200 -
2025-08-27 11:13:10,789 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:13:10] "GET /check_status/?_=1756263776581 HTTP/1.1" 200 -
2025-08-27 11:14:10,787 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:14:10] "GET /check_status/?_=1756263776582 HTTP/1.1" 200 -
2025-08-27 11:14:47,162 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:14:47,473 - core.database - ERROR - Database connection failed: 'CMySQLConnection' object is not an iterator
2025-08-27 11:14:47,488 - core.database - ERROR - Database connection failed: 'CMySQLConnection' object is not an iterator
2025-08-27 11:14:47,491 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:14:47,492 - core.instagram_manager - ERROR - Error getting all accounts: 'InstagramManager' object has no attribute 'db_manager'
2025-08-27 11:14:47,503 - core.database - ERROR - Database connection failed: 'CMySQLConnection' object is not an iterator
2025-08-27 11:15:10,786 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:15:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:16:10,785 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:16:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:16:54,006 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:16:54,025 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:16:54,026 - core.instagram_manager - ERROR - Error getting all accounts: 'InstagramManager' object has no attribute 'db_manager'
2025-08-27 11:17:10,793 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:17:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:18:10,778 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:18:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:19:10,785 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:19:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:20:10,785 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:20:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:21:10,785 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:21:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:22:10,788 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:22:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:22:29,877 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:22:31,120 - core.database - ERROR - Database connection error: 1045 (28000): Access denied for user 'root'@'localhost' (using password: NO)
2025-08-27 11:22:31,120 - core.database - ERROR - Database connection failed: Failed to get database connection
2025-08-27 11:22:31,121 - core.database - ERROR - Database initialization failed: Failed to get database connection
2025-08-27 11:22:31,123 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:22:31,124 - core.instagram_manager - ERROR - Error getting all accounts: 'InstagramManager' object has no attribute 'db_manager'
2025-08-27 11:23:10,777 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:23:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:24:10,791 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:24:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:25:10,780 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:25:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:25:51,180 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:25:52,067 - core.database - INFO - Database connection successful
2025-08-27 11:25:52,068 - core.database - INFO - Database Manager initialized
2025-08-27 11:25:52,138 - core.database - ERROR - Query execution error: 1054 (42S22): Unknown column 'scan_date' in 'order clause'
2025-08-27 11:25:52,138 - core.database - ERROR - Query: SELECT * FROM scraped_users ORDER BY scan_date DESC LIMIT %s
2025-08-27 11:25:52,139 - core.database - ERROR - Params: (100,)
2025-08-27 11:25:52,139 - core.database - ERROR - Error fetching scraped users: 1054 (42S22): Unknown column 'scan_date' in 'order clause'
2025-08-27 11:25:52,152 - core.database - INFO - Database connection successful
2025-08-27 11:25:52,152 - core.database - INFO - Database Manager initialized
2025-08-27 11:25:52,154 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:25:52,176 - core.database - INFO - Database connection successful
2025-08-27 11:25:52,177 - core.database - INFO - Database Manager initialized
2025-08-27 11:25:52,178 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:25:52,188 - core.database - INFO - Database connection successful
2025-08-27 11:25:52,188 - core.database - INFO - Database Manager initialized
2025-08-27 11:26:10,791 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:26:10] "GET /check_status/?_=1756263776594 HTTP/1.1" 200 -
2025-08-27 11:26:34,531 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:26:35,388 - core.database - INFO - Database connection successful
2025-08-27 11:26:35,389 - core.database - INFO - Database Manager initialized
2025-08-27 11:26:35,460 - core.database - INFO - Database connection successful
2025-08-27 11:26:35,460 - core.database - INFO - Database Manager initialized
2025-08-27 11:26:35,462 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:26:35,484 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:26:35,484 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:26:35,495 - core.database - INFO - Database connection successful
2025-08-27 11:26:35,496 - core.database - INFO - Database Manager initialized
2025-08-27 11:26:35,496 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:26:35,509 - core.database - INFO - Database connection successful
2025-08-27 11:26:35,509 - core.database - INFO - Database Manager initialized
2025-08-27 11:27:10,788 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:27:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:27:51,676 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:27:52,502 - core.database - INFO - Database connection successful
2025-08-27 11:27:52,502 - core.database - INFO - Database Manager initialized
2025-08-27 11:27:52,573 - core.database - INFO - Database connection successful
2025-08-27 11:27:52,573 - core.database - INFO - Database Manager initialized
2025-08-27 11:27:52,575 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:27:52,601 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:27:52,602 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:27:52,613 - core.database - INFO - Database connection successful
2025-08-27 11:27:52,614 - core.database - INFO - Database Manager initialized
2025-08-27 11:27:52,615 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:27:52,625 - core.database - INFO - Database connection successful
2025-08-27 11:27:52,626 - core.database - INFO - Database Manager initialized
2025-08-27 11:28:10,785 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:28:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:29:10,791 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:29:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:30:10,798 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:30:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:31:10,793 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:31:10] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-27 11:32:10,787 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:32:10] "GET /check_status/?_=1756263776600 HTTP/1.1" 200 -
2025-08-27 11:33:10,790 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:33:10] "GET /check_status/?_=1756263776601 HTTP/1.1" 200 -
2025-08-27 11:34:10,782 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:34:10] "GET /check_status/?_=1756263776602 HTTP/1.1" 200 -
2025-08-27 11:35:10,786 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:35:10] "GET /check_status/?_=1756263776603 HTTP/1.1" 200 -
2025-08-27 11:35:36,323 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:35:36] "GET /check_status/?_=1756263776604 HTTP/1.1" 200 -
2025-08-27 11:35:56,587 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:35:56] "GET /check_status/?_=1756263776605 HTTP/1.1" 200 -
2025-08-27 11:36:26,598 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:36:26] "GET /check_status/?_=1756263776606 HTTP/1.1" 200 -
2025-08-27 11:36:56,595 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:36:56] "GET /check_status/?_=1756263776607 HTTP/1.1" 200 -
2025-08-27 11:37:26,601 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:37:26] "GET /check_status/?_=1756263776608 HTTP/1.1" 200 -
2025-08-27 11:37:56,599 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:37:56] "GET /check_status/?_=1756263776609 HTTP/1.1" 200 -
2025-08-27 11:38:26,591 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:38:26] "GET /check_status/?_=1756263776610 HTTP/1.1" 200 -
2025-08-27 11:38:56,599 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:38:56] "GET /check_status/?_=1756263776611 HTTP/1.1" 200 -
2025-08-27 11:39:26,596 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:39:26] "GET /check_status/?_=1756263776612 HTTP/1.1" 200 -
2025-08-27 11:39:56,593 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:39:56] "GET /check_status/?_=1756263776613 HTTP/1.1" 200 -
2025-08-27 11:40:26,593 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:40:26] "GET /check_status/?_=1756263776614 HTTP/1.1" 200 -
2025-08-27 11:40:56,605 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:40:56] "GET /check_status/?_=1756263776615 HTTP/1.1" 200 -
2025-08-27 11:41:26,598 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:41:26] "GET /check_status/?_=1756263776616 HTTP/1.1" 200 -
2025-08-27 11:41:56,787 - werkzeug - INFO - 127.0.0.1 - - [27/Aug/2025 11:41:56] "GET /check_status/?_=1756263776617 HTTP/1.1" 200 -
2025-08-27 11:47:48,638 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:47:48,648 - core.database - INFO - Database connection successful
2025-08-27 11:47:48,649 - core.database - INFO - Database Manager initialized
2025-08-27 11:47:48,659 - core.database - INFO - Database connection successful
2025-08-27 11:47:48,659 - core.database - INFO - Database Manager initialized
2025-08-27 11:47:48,661 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:47:49,757 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:47:49,757 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:47:49,770 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-27 11:47:49,770 - core.instagram_manager - INFO - Loaded account 2 from database
2025-08-27 11:47:49,782 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-27 11:47:49,783 - core.instagram_manager - INFO - Loaded account 3 from database
2025-08-27 11:53:09,729 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:53:09,740 - core.database - INFO - Database connection successful
2025-08-27 11:53:09,741 - core.database - INFO - Database Manager initialized
2025-08-27 11:53:09,751 - core.database - INFO - Database connection successful
2025-08-27 11:53:09,751 - core.database - INFO - Database Manager initialized
2025-08-27 11:53:09,752 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:53:10,360 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:53:10,360 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:53:10,376 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-27 11:53:10,376 - core.instagram_manager - INFO - Loaded account 2 from database
2025-08-27 11:53:10,390 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-27 11:53:10,391 - core.instagram_manager - INFO - Loaded account 3 from database
2025-08-27 11:54:02,782 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:54:02,795 - core.database - INFO - Database connection successful
2025-08-27 11:54:02,795 - core.database - INFO - Database Manager initialized
2025-08-27 11:54:02,809 - core.database - INFO - Database connection successful
2025-08-27 11:54:02,809 - core.database - INFO - Database Manager initialized
2025-08-27 11:54:02,812 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:54:03,422 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:54:03,423 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:54:03,435 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-27 11:54:03,435 - core.instagram_manager - INFO - Loaded account 2 from database
2025-08-27 11:54:03,447 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-27 11:54:03,447 - core.instagram_manager - INFO - Loaded account 3 from database
2025-08-27 11:56:01,618 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:56:02,457 - core.database - INFO - Database connection successful
2025-08-27 11:56:02,457 - core.database - INFO - Database Manager initialized
2025-08-27 11:56:02,527 - core.database - INFO - Database connection successful
2025-08-27 11:56:02,527 - core.database - INFO - Database Manager initialized
2025-08-27 11:56:02,528 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:56:02,552 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:56:02,552 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:56:02,564 - core.database - INFO - Database connection successful
2025-08-27 11:56:02,564 - core.database - INFO - Database Manager initialized
2025-08-27 11:56:02,565 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:56:02,576 - core.database - INFO - Database connection successful
2025-08-27 11:56:02,576 - core.database - INFO - Database Manager initialized
2025-08-27 11:56:20,404 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:56:20,418 - core.database - INFO - Database connection successful
2025-08-27 11:56:20,418 - core.database - INFO - Database Manager initialized
2025-08-27 11:56:20,433 - core.database - INFO - Database connection successful
2025-08-27 11:56:20,433 - core.database - INFO - Database Manager initialized
2025-08-27 11:56:20,435 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-27 11:56:21,034 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-27 11:56:21,035 - core.instagram_manager - INFO - Loaded account 1 from database
2025-08-27 11:56:21,047 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-27 11:56:21,047 - core.instagram_manager - INFO - Loaded account 2 from database
2025-08-27 11:56:21,063 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-27 11:56:21,065 - core.instagram_manager - INFO - Loaded account 3 from database
