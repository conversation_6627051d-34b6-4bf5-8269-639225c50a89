#!/usr/bin/env python3
"""
Test Runner - Simple menu to run different types of tests
Provides easy access to all testing options
"""

import os
import sys
import subprocess
from datetime import datetime

def clear_screen():
    """Clear the terminal screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """Print the test runner header"""
    print("="*60)
    print("INSTAGRAM MANAGEMENT SYSTEM - TEST RUNNER")
    print("="*60)
    print(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    print("="*60)

def print_menu():
    """Print the main menu"""
    print("\nAvailable Tests:")
    print("1. Quick System Test (Fast verification)")
    print("2. Bot Functionality Test (Test each bot)")
    print("3. Full System Integration Test (Comprehensive)")
    print("4. Run All Tests Sequentially")
    print("5. Check System Status")
    print("0. Exit")
    print("-"*60)

def run_quick_test():
    """Run the quick system test"""
    print("\n🚀 Running Quick System Test...")
    print("This will test basic functionality quickly")
    print("-"*40)
    
    try:
        result = subprocess.run([sys.executable, "quick_test.py"], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running quick test: {e}")
        return False

def run_bot_functionality_test():
    """Run the bot functionality test"""
    print("\n🤖 Running Bot Functionality Test...")
    print("This will test each bot's core capabilities")
    print("-"*40)
    
    try:
        result = subprocess.run([sys.executable, "test_bots_functionality.py"], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running bot functionality test: {e}")
        return False

def run_full_integration_test():
    """Run the full system integration test"""
    print("\n🔍 Running Full System Integration Test...")
    print("This will test everything comprehensively")
    print("-"*40)
    
    try:
        result = subprocess.run([sys.executable, "test_system_integration.py"], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running full integration test: {e}")
        return False

def run_all_tests():
    """Run all tests sequentially"""
    print("\n🔄 Running All Tests Sequentially...")
    print("This will run all tests in order")
    print("-"*40)
    
    tests = [
        ("Quick System Test", run_quick_test),
        ("Bot Functionality Test", run_bot_functionality_test),
        ("Full System Integration Test", run_full_integration_test)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- Running {test_name} ---")
        success = test_func()
        results.append((test_name, success))
        print(f"--- {test_name} {'PASSED' if success else 'FAILED'} ---")
    
    # Summary
    print("\n" + "="*60)
    print("ALL TESTS SUMMARY")
    print("="*60)
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nTotal: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! System is working correctly.")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")
    
    return passed == total

def check_system_status():
    """Check basic system status"""
    print("\n📊 Checking System Status...")
    print("-"*40)
    
    # Check if required files exist
    required_files = [
        "core/instagram_manager.py",
        "core/database.py",
        "bots/bio_scanner_bot.py",
        "bots/message_bot.py",
        "bots/data_retriever_bot.py",
        "bots/account_warmup_bot.py",
        "config.py",
        "app.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
    
    # Check if database can be accessed
    try:
        from core.database import DatabaseManager
        db = DatabaseManager()
        connection = db.get_connection()
        if connection:
            connection.close()
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
    except Exception as e:
        print(f"❌ Database check failed: {e}")
    
    # Check if Instagram Manager can be initialized
    try:
        from core.instagram_manager import InstagramManager
        im = InstagramManager()
        accounts = im.get_all_accounts()
        print(f"✅ Instagram Manager: {len(accounts)} accounts found")
    except Exception as e:
        print(f"❌ Instagram Manager check failed: {e}")
    
    if missing_files:
        print(f"\n⚠️  Missing {len(missing_files)} required files")
    else:
        print("\n✅ All required files present")

def main():
    """Main test runner loop"""
    while True:
        clear_screen()
        print_header()
        print_menu()
        
        try:
            choice = input("Enter your choice (0-5): ").strip()
            
            if choice == "0":
                print("\n👋 Goodbye!")
                break
            elif choice == "1":
                run_quick_test()
            elif choice == "2":
                run_bot_functionality_test()
            elif choice == "3":
                run_full_integration_test()
            elif choice == "4":
                run_all_tests()
            elif choice == "5":
                check_system_status()
            else:
                print("❌ Invalid choice. Please enter 0-5.")
            
            if choice in ["1", "2", "3", "4"]:
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Test runner interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()

