#!/usr/bin/env python3
"""
System Integration Test - Comprehensive testing of all bots and core functionality
Tests the entire Instagram management system end-to-end
"""

import os
import sys
import time
import logging
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.instagram_manager import InstagramManager
from core.database import DatabaseManager
from bots.bio_scanner_bot import BioScannerBot
from bots.message_bot import MessageBot
from bots.data_retriever_bot import DataRetrieverBot
from bots.account_warmup_bot import AccountWarmupBot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SystemIntegrationTest:
    """Comprehensive system integration testing"""
    
    def __init__(self):
        self.instagram_manager = InstagramManager()
        self.db_manager = DatabaseManager()
        self.test_results = {}
        self.test_account_id = 5  # Use account 5 for testing
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all system tests"""
        logger.info("Starting comprehensive system integration test")
        start_time = datetime.now()
        
        try:
            # Test 1: Database connectivity
            self.test_database_connection()
            
            # Test 2: Instagram Manager initialization
            self.test_instagram_manager()
            
            # Test 3: Account login and session management
            self.test_account_login()
            
            # Test 4: Bio Scanner Bot
            self.test_bio_scanner_bot()
            
            # Test 5: Data Retriever Bot
            self.test_data_retriever_bot()
            
            # Test 6: Message Bot
            self.test_message_bot()
            
            # Test 7: Account Warmup Bot
            self.test_account_warmup_bot()
            
            # Test 8: Database operations
            self.test_database_operations()
            
            # Test 9: System performance
            self.test_system_performance()
            
        except Exception as e:
            logger.error(f"Critical error during testing: {e}")
            self.test_results['critical_error'] = str(e)
            self.test_results['traceback'] = traceback.format_exc()
        
        # Calculate test summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        self.test_results['summary'] = {
            'total_tests': len([k for k in self.test_results.keys() if k.startswith('test_')]),
            'passed_tests': len([k for k, v in self.test_results.items() if k.startswith('test_') and v.get('success', False)]),
            'failed_tests': len([k for k, v in self.test_results.items() if k.startswith('test_') and not v.get('success', False)]),
            'total_duration': duration,
            'test_timestamp': start_time.isoformat()
        }
        
        logger.info("System integration test completed")
        return self.test_results
    
    def test_database_connection(self):
        """Test database connectivity"""
        logger.info("Testing database connection...")
        try:
            # Test basic connection
            connection = self.db_manager.get_connection()
            if connection:
                connection.close()
                
            # Test basic queries
            accounts = self.db_manager.get_all_accounts()
            messages = self.db_manager.get_all_messages()
            
            self.test_results['test_database_connection'] = {
                'success': True,
                'accounts_count': len(accounts),
                'messages_count': len(messages),
                'message': 'Database connection successful'
            }
            logger.info("Database connection test passed")
            
        except Exception as e:
            self.test_results['test_database_connection'] = {
                'success': False,
                'error': str(e),
                'message': 'Database connection failed'
            }
            logger.error(f"Database connection test failed: {e}")
    
    def test_instagram_manager(self):
        """Test Instagram Manager initialization"""
        logger.info("Testing Instagram Manager...")
        try:
            # Test manager creation
            if self.instagram_manager:
                # Test getting accounts
                accounts = self.instagram_manager.get_all_accounts()
                
                self.test_results['test_instagram_manager'] = {
                    'success': True,
                    'accounts_count': len(accounts),
                    'message': 'Instagram Manager initialized successfully'
                }
                logger.info("Instagram Manager test passed")
            else:
                raise Exception("Instagram Manager not initialized")
                
        except Exception as e:
            self.test_results['test_instagram_manager'] = {
                'success': False,
                'error': str(e),
                'message': 'Instagram Manager initialization failed'
            }
            logger.error(f"Instagram Manager test failed: {e}")
    
    def test_account_login(self):
        """Test account login and session management"""
        logger.info(f"Testing account login for account {self.test_account_id}...")
        try:
            # Get account
            account = self.instagram_manager.get_account(self.test_account_id)
            if not account:
                raise Exception(f"Account {self.test_account_id} not found")
            
            # Test session loading
            if account.is_logged_in():
                logger.info(f"Account {self.test_account_id} already logged in")
                login_success = True
                login_message = "Account already logged in (session loaded)"
            else:
                # Try to login
                logger.info(f"Attempting to login account {self.test_account_id}")
                login_result = self.instagram_manager.login_account(self.test_account_id)
                login_success = login_result.get('success', False)
                login_message = login_result.get('message', 'Login attempt completed')
            
            self.test_results['test_account_login'] = {
                'success': login_success,
                'account_id': self.test_account_id,
                'message': login_message,
                'is_logged_in': account.is_logged_in() if account else False
            }
            
            if login_success:
                logger.info("Account login test passed")
            else:
                logger.warning("Account login test completed with warnings")
                
        except Exception as e:
            self.test_results['test_account_login'] = {
                'success': False,
                'error': str(e),
                'message': 'Account login test failed'
            }
            logger.error(f"Account login test failed: {e}")
    
    def test_bio_scanner_bot(self):
        """Test Bio Scanner Bot"""
        logger.info("Testing Bio Scanner Bot...")
        try:
            # Create test CSV file
            test_csv_path = "C:/files/test_bio_scan.csv"
            test_csv_content = "username,id1,id3,title\ninstagram,cristiano,leomessi,test_user"
            
            os.makedirs("C:/files", exist_ok=True)
            with open(test_csv_path, 'w', encoding='utf-8') as f:
                f.write(test_csv_content)
            
            # Initialize bot
            bot = BioScannerBot(
                account_id=self.test_account_id,
                csv_file_path=test_csv_path,
                instagram_manager=self.instagram_manager,
                db_manager=self.db_manager
            )
            
            # Test bot initialization
            if bot:
                self.test_results['test_bio_scanner_bot'] = {
                    'success': True,
                    'message': 'Bio Scanner Bot initialized successfully',
                    'csv_file_created': True
                }
                logger.info("Bio Scanner Bot test passed")
            else:
                raise Exception("Bio Scanner Bot initialization failed")
                
        except Exception as e:
            self.test_results['test_bio_scanner_bot'] = {
                'success': False,
                'error': str(e),
                'message': 'Bio Scanner Bot test failed'
            }
            logger.error(f"Bio Scanner Bot test failed: {e}")
    
    def test_data_retriever_bot(self):
        """Test Data Retriever Bot"""
        logger.info("Testing Data Retriever Bot...")
        try:
            # Initialize bot
            bot = DataRetrieverBot(
                account_id=self.test_account_id,
                instagram_manager=self.instagram_manager,
                db_manager=self.db_manager
            )
            
            if bot:
                self.test_results['test_data_retriever_bot'] = {
                    'success': True,
                    'message': 'Data Retriever Bot initialized successfully'
                }
                logger.info("Data Retriever Bot test passed")
            else:
                raise Exception("Data Retriever Bot initialization failed")
                
        except Exception as e:
            self.test_results['test_data_retriever_bot'] = {
                'success': False,
                'error': str(e),
                'message': 'Data Retriever Bot test failed'
            }
            logger.error(f"Data Retriever Bot test failed: {e}")
    
    def test_message_bot(self):
        """Test Message Bot"""
        logger.info("Testing Message Bot...")
        try:
            # Initialize bot
            bot = MessageBot(
                account_id=self.test_account_id,
                instagram_manager=self.instagram_manager,
                db_manager=self.db_manager
            )
            
            if bot:
                self.test_results['test_message_bot'] = {
                    'success': True,
                    'message': 'Message Bot initialized successfully'
                }
                logger.info("Message Bot test passed")
            else:
                raise Exception("Message Bot initialization failed")
                
        except Exception as e:
            self.test_results['test_message_bot'] = {
                'success': False,
                'error': str(e),
                'message': 'Message Bot test failed'
            }
            logger.error(f"Message Bot test failed: {e}")
    
    def test_account_warmup_bot(self):
        """Test Account Warmup Bot"""
        logger.info("Testing Account Warmup Bot...")
        try:
            # Initialize bot
            bot = AccountWarmupBot(
                account_id=self.test_account_id,
                instagram_manager=self.instagram_manager,
                db_manager=self.db_manager
            )
            
            if bot:
                self.test_results['test_account_warmup_bot'] = {
                    'success': True,
                    'message': 'Account Warmup Bot initialized successfully'
                }
                logger.info("Account Warmup Bot test passed")
            else:
                raise Exception("Account Warmup Bot initialization failed")
                
        except Exception as e:
            self.test_results['test_account_warmup_bot'] = {
                'success': False,
                'error': str(e),
                'message': 'Account Warmup Bot test failed'
            }
            logger.error(f"Account Warmup Bot test failed: {e}")
    
    def test_database_operations(self):
        """Test database operations"""
        logger.info("Testing database operations...")
        try:
            # Test scraped users table
            scraped_users = self.db_manager.get_scraped_users()
            
            # Test messages table
            messages = self.db_manager.get_all_messages()
            
            # Test message logs table
            message_logs = self.db_manager.get_message_logs()
            
            self.test_results['test_database_operations'] = {
                'success': True,
                'scraped_users_count': len(scraped_users),
                'messages_count': len(messages),
                'message_logs_count': len(message_logs),
                'message': 'Database operations test successful'
            }
            logger.info("Database operations test passed")
            
        except Exception as e:
            self.test_results['test_database_operations'] = {
                'success': False,
                'error': str(e),
                'message': 'Database operations test failed'
            }
            logger.error(f"Database operations test failed: {e}")
    
    def test_system_performance(self):
        """Test system performance metrics"""
        logger.info("Testing system performance...")
        try:
            # Test Instagram API response time
            start_time = time.time()
            account = self.instagram_manager.get_account(self.test_account_id)
            if account and account.is_logged_in():
                # Simple API call to test response time
                try:
                    user_info = account.get_user_info('instagram')
                    api_response_time = time.time() - start_time
                except:
                    api_response_time = None
            else:
                api_response_time = None
            
            # Test database query time
            start_time = time.time()
            accounts = self.db_manager.get_all_accounts()
            db_query_time = time.time() - start_time
            
            self.test_results['test_system_performance'] = {
                'success': True,
                'api_response_time': api_response_time,
                'db_query_time': db_query_time,
                'message': 'System performance test completed'
            }
            logger.info("System performance test passed")
            
        except Exception as e:
            self.test_results['test_system_performance'] = {
                'success': False,
                'error': str(e),
                'message': 'System performance test failed'
            }
            logger.error(f"System performance test failed: {e}")
    
    def print_test_results(self):
        """Print formatted test results"""
        print("\n" + "="*60)
        print("SYSTEM INTEGRATION TEST RESULTS")
        print("="*60)
        
        # Print individual test results
        for test_name, result in self.test_results.items():
            if test_name.startswith('test_'):
                status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
                print(f"{test_name.replace('test_', '').replace('_', ' ').title()}: {status}")
                
                if not result.get('success', False) and 'error' in result:
                    print(f"  Error: {result['error']}")
                
                if 'message' in result:
                    print(f"  Message: {result['message']}")
        
        # Print summary
        if 'summary' in self.test_results:
            summary = self.test_results['summary']
            print("\n" + "-"*60)
            print("TEST SUMMARY")
            print("-"*60)
            print(f"Total Tests: {summary['total_tests']}")
            print(f"Passed: {summary['passed_tests']}")
            print(f"Failed: {summary['failed_tests']}")
            print(f"Duration: {summary['total_duration']:.2f} seconds")
            print(f"Timestamp: {summary['test_timestamp']}")
        
        # Print critical errors if any
        if 'critical_error' in self.test_results:
            print("\n" + "!"*60)
            print("CRITICAL ERROR")
            print("!"*60)
            print(f"Error: {self.test_results['critical_error']}")
            if 'traceback' in self.test_results:
                print(f"Traceback: {self.test_results['traceback']}")
        
        print("\n" + "="*60)
    
    def cleanup_test_files(self):
        """Clean up test files"""
        try:
            test_csv_path = "C:/files/test_bio_scan.csv"
            if os.path.exists(test_csv_path):
                os.remove(test_csv_path)
                logger.info("Test CSV file cleaned up")
        except Exception as e:
            logger.warning(f"Could not cleanup test files: {e}")

def main():
    """Main test execution"""
    print("Starting System Integration Test...")
    print("This will test all bots and core functionality")
    print("="*60)
    
    # Create test instance
    tester = SystemIntegrationTest()
    
    try:
        # Run all tests
        results = tester.run_all_tests()
        
        # Print results
        tester.print_test_results()
        
        # Cleanup
        tester.cleanup_test_files()
        
        # Return exit code based on results
        if 'summary' in results:
            failed_tests = results['summary']['failed_tests']
            if failed_tests == 0:
                print("\n🎉 All tests passed! System is working correctly.")
                return 0
            else:
                print(f"\n⚠️  {failed_tests} tests failed. Please check the results above.")
                return 1
        else:
            print("\n❌ Test execution failed. Check logs for details.")
            return 1
            
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error during testing: {e}")
        logger.error(f"Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

