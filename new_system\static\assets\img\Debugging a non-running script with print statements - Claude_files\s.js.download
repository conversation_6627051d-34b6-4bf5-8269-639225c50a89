/**
 * Includes code from:
 * Crypto-js
 * https://github.com/brix/crypto-js
 * Licensed under the MIT (http://www.opensource.org/licenses/mit-license.php) license.
 * Includes code from:
 * PluginDetect v0.9.1
 * www.pinlady.net/PluginDetect/license/
 * [ QuickTime Flash Shockwave WindowsMediaPlayer Silverlight VLC AdobeReader RealPlayer ]
 * [ isMinVersion getVersion hasMimeType onDetectionDone onWindowLoaded ]
 * [ AllowActiveX ]
 * Includes code from:
 * http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript
 * Includes code from:
 * http://stackoverflow.com/questions/8253136/how-to-get-domain-name-only-using-javascript/8253221#8253221
 * Includes code from:
 * https://github.com/Valve/fingerprintjs2
 * Copyright (c) 2018? <PERSON> (<EMAIL>)
 * Copyright (c) 2015? Valentin <PERSON> (<EMAIL>)
 * Licensed under the MIT (http://www.opensource.org/licenses/mit-license.php) license.
 * Includes code from:
 * https://github.com/fingerprintjs/fingerprintjs/tree/v3
 * Licensed under the MIT (http://www.opensource.org/licenses/mit-license.php) license.
 */

!function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={exports:{}};var i,r,o={exports:{}};function a(){return i||(i=1,o.exports=(n=n||function(n,t){var i;if("undefined"!=typeof window&&window.crypto&&(i=window.crypto),"undefined"!=typeof self&&self.crypto&&(i=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(i=globalThis.crypto),!i&&"undefined"!=typeof window&&window.msCrypto&&(i=window.msCrypto),!i&&void 0!==e&&e.crypto&&(i=e.crypto),!i)try{i=require("crypto")}catch(e){}var r=function(){if(i){if("function"==typeof i.getRandomValues)try{return i.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof i.randomBytes)try{return i.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(n){var t;return e.prototype=n,t=new e,e.prototype=null,t}}(),a={},s=a.lib={},u=s.Base={extend:function(e){var n=o(this);return e&&n.mixIn(e),n.hasOwnProperty("init")&&this.init!==n.init||(n.init=function(){n.$super.init.apply(this,arguments)}),n.init.prototype=n,n.$super=this,n},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var n in e)e.hasOwnProperty(n)&&(this[n]=e[n]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},l=s.WordArray=u.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:4*e.length},toString:function(e){return(e||d).stringify(this)},concat:function(e){var n=this.words,t=e.words,i=this.sigBytes,r=e.sigBytes;if(this.clamp(),i%4)for(var o=0;o<r;o++){var a=t[o>>>2]>>>24-o%4*8&255;n[i+o>>>2]|=a<<24-(i+o)%4*8}else for(var s=0;s<r;s+=4)n[i+s>>>2]=t[s>>>2];return this.sigBytes+=r,this},clamp:function(){var e=this.words,t=this.sigBytes;e[t>>>2]&=4294967295<<32-t%4*8,e.length=n.ceil(t/4)},clone:function(){var e=u.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var n=[],t=0;t<e;t+=4)n.push(r());return new l.init(n,e)}}),c=a.enc={},d=c.Hex={stringify:function(e){for(var n=e.words,t=e.sigBytes,i=[],r=0;r<t;r++){var o=n[r>>>2]>>>24-r%4*8&255;i.push((o>>>4).toString(16)),i.push((15&o).toString(16))}return i.join("")},parse:function(e){for(var n=e.length,t=[],i=0;i<n;i+=2)t[i>>>3]|=parseInt(e.substr(i,2),16)<<24-i%8*4;return new l.init(t,n/2)}},f=c.Latin1={stringify:function(e){for(var n=e.words,t=e.sigBytes,i=[],r=0;r<t;r++){var o=n[r>>>2]>>>24-r%4*8&255;i.push(String.fromCharCode(o))}return i.join("")},parse:function(e){for(var n=e.length,t=[],i=0;i<n;i++)t[i>>>2]|=(255&e.charCodeAt(i))<<24-i%4*8;return new l.init(t,n)}},g=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},p=s.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=g.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(e){var t,i=this._data,r=i.words,o=i.sigBytes,a=this.blockSize,s=o/(4*a),u=(s=e?n.ceil(s):n.max((0|s)-this._minBufferSize,0))*a,c=n.min(4*u,o);if(u){for(var d=0;d<u;d+=a)this._doProcessBlock(r,d);t=r.splice(0,u),i.sigBytes-=c}return new l.init(t,c)},clone:function(){var e=u.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});s.Hasher=p.extend({cfg:u.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(n,t){return new e.init(t).finalize(n)}},_createHmacHelper:function(e){return function(n,t){return new m.HMAC.init(e,t).finalize(n)}}});var m=a.algo={};return a}(Math),n)),o.exports;var n}t.exports=(r=a(),function(e){var n=r,t=n.lib,i=t.WordArray,o=t.Hasher,a=n.algo,s=[];!function(){for(var n=0;n<64;n++)s[n]=4294967296*e.abs(e.sin(n+1))|0}();var u=a.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,n){for(var t=0;t<16;t++){var i=n+t,r=e[i];e[i]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8)}var o=this._hash.words,a=e[n+0],u=e[n+1],g=e[n+2],p=e[n+3],m=e[n+4],v=e[n+5],h=e[n+6],y=e[n+7],b=e[n+8],w=e[n+9],x=e[n+10],D=e[n+11],P=e[n+12],S=e[n+13],M=e[n+14],T=e[n+15],O=o[0],_=o[1],N=o[2],C=o[3];O=l(O,_,N,C,a,7,s[0]),C=l(C,O,_,N,u,12,s[1]),N=l(N,C,O,_,g,17,s[2]),_=l(_,N,C,O,p,22,s[3]),O=l(O,_,N,C,m,7,s[4]),C=l(C,O,_,N,v,12,s[5]),N=l(N,C,O,_,h,17,s[6]),_=l(_,N,C,O,y,22,s[7]),O=l(O,_,N,C,b,7,s[8]),C=l(C,O,_,N,w,12,s[9]),N=l(N,C,O,_,x,17,s[10]),_=l(_,N,C,O,D,22,s[11]),O=l(O,_,N,C,P,7,s[12]),C=l(C,O,_,N,S,12,s[13]),N=l(N,C,O,_,M,17,s[14]),O=c(O,_=l(_,N,C,O,T,22,s[15]),N,C,u,5,s[16]),C=c(C,O,_,N,h,9,s[17]),N=c(N,C,O,_,D,14,s[18]),_=c(_,N,C,O,a,20,s[19]),O=c(O,_,N,C,v,5,s[20]),C=c(C,O,_,N,x,9,s[21]),N=c(N,C,O,_,T,14,s[22]),_=c(_,N,C,O,m,20,s[23]),O=c(O,_,N,C,w,5,s[24]),C=c(C,O,_,N,M,9,s[25]),N=c(N,C,O,_,p,14,s[26]),_=c(_,N,C,O,b,20,s[27]),O=c(O,_,N,C,S,5,s[28]),C=c(C,O,_,N,g,9,s[29]),N=c(N,C,O,_,y,14,s[30]),O=d(O,_=c(_,N,C,O,P,20,s[31]),N,C,v,4,s[32]),C=d(C,O,_,N,b,11,s[33]),N=d(N,C,O,_,D,16,s[34]),_=d(_,N,C,O,M,23,s[35]),O=d(O,_,N,C,u,4,s[36]),C=d(C,O,_,N,m,11,s[37]),N=d(N,C,O,_,y,16,s[38]),_=d(_,N,C,O,x,23,s[39]),O=d(O,_,N,C,S,4,s[40]),C=d(C,O,_,N,a,11,s[41]),N=d(N,C,O,_,p,16,s[42]),_=d(_,N,C,O,h,23,s[43]),O=d(O,_,N,C,w,4,s[44]),C=d(C,O,_,N,P,11,s[45]),N=d(N,C,O,_,T,16,s[46]),O=f(O,_=d(_,N,C,O,g,23,s[47]),N,C,a,6,s[48]),C=f(C,O,_,N,y,10,s[49]),N=f(N,C,O,_,M,15,s[50]),_=f(_,N,C,O,v,21,s[51]),O=f(O,_,N,C,P,6,s[52]),C=f(C,O,_,N,p,10,s[53]),N=f(N,C,O,_,x,15,s[54]),_=f(_,N,C,O,u,21,s[55]),O=f(O,_,N,C,b,6,s[56]),C=f(C,O,_,N,T,10,s[57]),N=f(N,C,O,_,h,15,s[58]),_=f(_,N,C,O,S,21,s[59]),O=f(O,_,N,C,m,6,s[60]),C=f(C,O,_,N,D,10,s[61]),N=f(N,C,O,_,g,15,s[62]),_=f(_,N,C,O,w,21,s[63]),o[0]=o[0]+O|0,o[1]=o[1]+_|0,o[2]=o[2]+N|0,o[3]=o[3]+C|0},_doFinalize:function(){var n=this._data,t=n.words,i=8*this._nDataBytes,r=8*n.sigBytes;t[r>>>5]|=128<<24-r%32;var o=e.floor(i/4294967296),a=i;t[15+(r+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t[14+(r+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),n.sigBytes=4*(t.length+1),this._process();for(var s=this._hash,u=s.words,l=0;l<4;l++){var c=u[l];u[l]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}return s},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function l(e,n,t,i,r,o,a){var s=e+(n&t|~n&i)+r+a;return(s<<o|s>>>32-o)+n}function c(e,n,t,i,r,o,a){var s=e+(n&i|t&~i)+r+a;return(s<<o|s>>>32-o)+n}function d(e,n,t,i,r,o,a){var s=e+(n^t^i)+r+a;return(s<<o|s>>>32-o)+n}function f(e,n,t,i,r,o,a){var s=e+(t^(n|~i))+r+a;return(s<<o|s>>>32-o)+n}n.MD5=o._createHelper(u),n.HmacMD5=o._createHmacHelper(u)}(Math),r.MD5);var s=n(t.exports),u=function(){return u=Object.assign||function(e){for(var n,t=1,i=arguments.length;t<i;t++)for(var r in n=arguments[t])Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},u.apply(this,arguments)};function l(e,n,t,i){return new(t||(t=Promise))((function(r,o){function a(e){try{u(i.next(e))}catch(e){o(e)}}function s(e){try{u(i.throw(e))}catch(e){o(e)}}function u(e){var n;e.done?r(e.value):(n=e.value,n instanceof t?n:new t((function(e){e(n)}))).then(a,s)}u((i=i.apply(e,n||[])).next())}))}function c(e,n){var t,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(u){return function(s){if(t)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(o=0)),o;)try{if(t=1,i&&(r=2&s[0]?i.return:s[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,s[1])).done)return r;switch(i=0,r&&(s=[2&s[0],r.value]),s[0]){case 0:case 1:r=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,i=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!r||s[1]>r[0]&&s[1]<r[3])){o.label=s[1];break}if(6===s[0]&&o.label<r[1]){o.label=r[1],r=s;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(s);break}r[2]&&o.ops.pop(),o.trys.pop();continue}s=n.call(e,o)}catch(e){s=[6,e],i=0}finally{t=r=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,u])}}}function d(e,n){return new Promise((function(t){return setTimeout(t,e,n)}))}function f(e){return!!e&&"function"==typeof e.then}function g(e,n){try{var t=e();f(t)?t.then((function(e){return n(!0,e)}),(function(e){return n(!1,e)})):n(!0,t)}catch(e){n(!1,e)}}function p(e,n){return l(this,arguments,void 0,(function(e,n,t){var i,r,o,a;return void 0===t&&(t=16),c(this,(function(s){switch(s.label){case 0:i=Array(e.length),r=Date.now(),o=0,s.label=1;case 1:return o<e.length?(i[o]=n(e[o],o),(a=Date.now())>=r+t?(r=a,[4,d(0)]):[3,3]):[3,4];case 2:s.sent(),s.label=3;case 3:return++o,[3,1];case 4:return[2,i]}}))}))}function m(e){e.then(void 0,(function(){}))}function v(e,n){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],n=[n[0]>>>16,65535&n[0],n[1]>>>16,65535&n[1]];var t=[0,0,0,0];return t[3]+=e[3]+n[3],t[2]+=t[3]>>>16,t[3]&=65535,t[2]+=e[2]+n[2],t[1]+=t[2]>>>16,t[2]&=65535,t[1]+=e[1]+n[1],t[0]+=t[1]>>>16,t[1]&=65535,t[0]+=e[0]+n[0],t[0]&=65535,[t[0]<<16|t[1],t[2]<<16|t[3]]}function h(e,n){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],n=[n[0]>>>16,65535&n[0],n[1]>>>16,65535&n[1]];var t=[0,0,0,0];return t[3]+=e[3]*n[3],t[2]+=t[3]>>>16,t[3]&=65535,t[2]+=e[2]*n[3],t[1]+=t[2]>>>16,t[2]&=65535,t[2]+=e[3]*n[2],t[1]+=t[2]>>>16,t[2]&=65535,t[1]+=e[1]*n[3],t[0]+=t[1]>>>16,t[1]&=65535,t[1]+=e[2]*n[2],t[0]+=t[1]>>>16,t[1]&=65535,t[1]+=e[3]*n[1],t[0]+=t[1]>>>16,t[1]&=65535,t[0]+=e[0]*n[3]+e[1]*n[2]+e[2]*n[1]+e[3]*n[0],t[0]&=65535,[t[0]<<16|t[1],t[2]<<16|t[3]]}function y(e,n){return 32===(n%=64)?[e[1],e[0]]:n<32?[e[0]<<n|e[1]>>>32-n,e[1]<<n|e[0]>>>32-n]:(n-=32,[e[1]<<n|e[0]>>>32-n,e[0]<<n|e[1]>>>32-n])}function b(e,n){return 0===(n%=64)?e:n<32?[e[0]<<n|e[1]>>>32-n,e[1]<<n]:[e[1]<<n-32,0]}function w(e,n){return[e[0]^n[0],e[1]^n[1]]}function x(e){return e=w(e,[0,e[0]>>>1]),e=w(e=h(e,[4283543511,3981806797]),[0,e[0]>>>1]),e=w(e=h(e,[3301882366,444984403]),[0,e[0]>>>1])}function D(e){return parseInt(e)}function P(e){return parseFloat(e)}function S(e,n){return"number"==typeof e&&isNaN(e)?n:e}function M(e){return e.reduce((function(e,n){return e+(n?1:0)}),0)}function T(e){return e&&"object"==typeof e&&"message"in e?e:{message:e}}function O(e,n,t){var i=Object.keys(e).filter((function(e){return!function(e,n){for(var t=0,i=e.length;t<i;++t)if(e[t]===n)return!0;return!1}(t,e)})),r=p(i,(function(t){return function(e,n){var t=new Promise((function(t){var i=Date.now();g(e.bind(null,n),(function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var r=Date.now()-i;if(!e[0])return t((function(){return{error:T(e[1]),duration:r}}));var o=e[1];if(function(e){return"function"!=typeof e}(o))return t((function(){return{value:o,duration:r}}));t((function(){return new Promise((function(e){var n=Date.now();g(o,(function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];var o=r+Date.now()-n;if(!t[0])return e({error:T(t[1]),duration:o});e({value:t[1],duration:o})}))}))}))}))}));return m(t),function(){return t.then((function(e){return e()}))}}(e[t],n)}));return m(r),function(){return l(this,void 0,void 0,(function(){var e,n,t,o;return c(this,(function(a){switch(a.label){case 0:return[4,r];case 1:return[4,p(a.sent(),(function(e){var n=e();return m(n),n}))];case 2:return e=a.sent(),[4,Promise.all(e)];case 3:for(n=a.sent(),t={},o=0;o<i.length;++o)t[i[o]]=n[o];return[2,t]}}))}))}}function _(){var e=window,n=navigator;return M(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in n,"msPointerEnabled"in n])>=4}function N(){var e=window,n=navigator;return M(["webkitPersistentStorage"in n,"webkitTemporaryStorage"in n,0===n.vendor.indexOf("Google"),"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])>=5}function C(){var e=window,n=navigator;return M(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,0===n.vendor.indexOf("Apple"),"getStorageUpdates"in n,"WebKitMediaKeys"in e])>=4}function A(){var e=window;return M(["safari"in e,!("DeviceMotionEvent"in e),!("ongestureend"in e),!("standalone"in navigator)])>=3}function E(){var e=N(),n=function(){var e,n,t=window;return M(["buildID"in navigator,"MozAppearance"in(null!==(n=null===(e=document.documentElement)||void 0===e?void 0:e.style)&&void 0!==n?n:{}),"onmozfullscreenchange"in t,"mozInnerScreenX"in t,"CSSMozDocumentRule"in t,"CanvasCaptureMediaStream"in t])>=4}();if(!e&&!n)return!1;var t=window;return M(["onorientationchange"in t,"orientation"in t,e&&!("SharedWorker"in t),n&&/android/i.test(navigator.appVersion)])>=2}function R(e){var n=new Error(e);return n.name=e,n}function I(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}function k(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}"function"==typeof SuppressedError&&SuppressedError;function L(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}function j(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}function H(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}var F=Math,V=function(){return 0};var q={audio:function(){var e=window,n=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!n)return-2;if(C()&&!A()&&!function(){var e=window;return M(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])>=3}())return-1;var t=new n(1,5e3,44100),i=t.createOscillator();i.type="triangle",i.frequency.value=1e4;var r=t.createDynamicsCompressor();r.threshold.value=-50,r.knee.value=40,r.ratio.value=12,r.attack.value=0,r.release.value=.25,i.connect(r),r.connect(t.destination),i.start(0);var o=function(e){var n=3,t=500,i=500,r=5e3,o=function(){},a=new Promise((function(a,s){var u=!1,l=0,c=0;e.oncomplete=function(e){return a(e.renderedBuffer)};var d=function(){setTimeout((function(){return s(R("timeout"))}),Math.min(i,c+r-Date.now()))},g=function(){try{var i=e.startRendering();switch(f(i)&&m(i),e.state){case"running":c=Date.now(),u&&d();break;case"suspended":document.hidden||l++,u&&l>=n?s(R("suspended")):setTimeout(g,t)}}catch(e){s(e)}};g(),o=function(){u||(u=!0,c>0&&d())}}));return[a,o]}(t),a=o[0],s=o[1],u=a.then((function(e){return function(e){for(var n=0,t=0;t<e.length;++t)n+=Math.abs(e[t]);return n}(e.getChannelData(0).subarray(4500))}),(function(e){if("timeout"===e.name||"suspended"===e.name)return-3;throw e}));return m(u),function(){return s(),u}},osCpu:function(){return navigator.oscpu},languages:function(){var e,n=navigator,t=[],i=n.language||n.userLanguage||n.browserLanguage||n.systemLanguage;if(void 0!==i&&t.push([i]),Array.isArray(n.languages))N()&&M([!("MediaSettingsRange"in(e=window)),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])>=3||t.push(n.languages);else if("string"==typeof n.languages){var r=n.languages;r&&t.push(r.split(","))}return t},colorDepth:function(){return window.screen.colorDepth},deviceMemory:function(){return S(P(navigator.deviceMemory),void 0)},screenResolution:function(){var e=screen,n=function(e){return S(D(e),null)},t=[n(e.width),n(e.height)];return t.sort().reverse(),t},hardwareConcurrency:function(){return S(D(navigator.hardwareConcurrency),void 0)},timezone:function(){var e,n=null===(e=window.Intl)||void 0===e?void 0:e.DateTimeFormat;if(n){var t=(new n).resolvedOptions().timeZone;if(t)return t}var i,r=(i=(new Date).getFullYear(),-Math.max(P(new Date(i,0,1).getTimezoneOffset()),P(new Date(i,6,1).getTimezoneOffset())));return"UTC".concat(r>=0?"+":"").concat(Math.abs(r))},sessionStorage:function(){try{return!!window.sessionStorage}catch(e){return!0}},localStorage:function(){try{return!!window.localStorage}catch(e){return!0}},indexedDB:function(){var e,n;if(!(_()||(e=window,n=navigator,M(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in n,"msSaveBlob"in n])>=3&&!_())))try{return!!window.indexedDB}catch(e){return!0}},cpuClass:function(){return navigator.cpuClass},platform:function(){var e=navigator.platform;return"MacIntel"===e&&C()&&!A()?function(){if("iPad"===navigator.platform)return!0;var e=screen,n=e.width/e.height;return M(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,n>.65&&n<1.53])>=2}()?"iPad":"iPhone":e},plugins:function(){var e=navigator.plugins;if(e){for(var n=[],t=0;t<e.length;++t){var i=e[t];if(i){for(var r=[],o=0;o<i.length;++o){var a=i[o];r.push({type:a.type,suffixes:a.suffixes})}n.push({name:i.name,description:i.description,mimeTypes:r})}}return n}},touchSupport:function(){var e,n=navigator,t=0;void 0!==n.maxTouchPoints?t=D(n.maxTouchPoints):void 0!==n.msMaxTouchPoints&&(t=n.msMaxTouchPoints);try{document.createEvent("TouchEvent"),e=!0}catch(n){e=!1}return{maxTouchPoints:t,touchEvent:e,touchStart:"ontouchstart"in window}},vendorFlavors:function(){for(var e=[],n=0,t=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];n<t.length;n++){var i=t[n],r=window[i];r&&"object"==typeof r&&e.push(i)}return e.sort()},cookiesEnabled:function(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var n=-1!==e.cookie.indexOf("cookietest=");return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",n}catch(e){return!1}},colorGamut:function(){for(var e=0,n=["rec2020","p3","srgb"];e<n.length;e++){var t=n[e];if(matchMedia("(color-gamut: ".concat(t,")")).matches)return t}},invertedColors:function(){return!!I("inverted")||!I("none")&&void 0},forcedColors:function(){return!!k("active")||!k("none")&&void 0},monochrome:function(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=100;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}},contrast:function(){return L("no-preference")?0:L("high")||L("more")?1:L("low")||L("less")?-1:L("forced")?10:void 0},reducedMotion:function(){return!!j("reduce")||!j("no-preference")&&void 0},hdr:function(){return!!H("high")||!H("standard")&&void 0},math:function(){var e,n=F.acos||V,t=F.acosh||V,i=F.asin||V,r=F.asinh||V,o=F.atanh||V,a=F.atan||V,s=F.sin||V,u=F.sinh||V,l=F.cos||V,c=F.cosh||V,d=F.tan||V,f=F.tanh||V,g=F.exp||V,p=F.expm1||V,m=F.log1p||V;return{acos:n(.12312423423423424),acosh:t(1e308),acoshPf:(e=1e154,F.log(e+F.sqrt(e*e-1))),asin:i(.12312423423423424),asinh:r(1),asinhPf:function(e){return F.log(e+F.sqrt(e*e+1))}(1),atanh:o(.5),atanhPf:function(e){return F.log((1+e)/(1-e))/2}(.5),atan:a(.5),sin:s(-1e300),sinh:u(1),sinhPf:function(e){return F.exp(e)-1/F.exp(e)/2}(1),cos:l(10.000000000123),cosh:c(1),coshPf:function(e){return(F.exp(e)+1/F.exp(e))/2}(1),tan:d(-1e300),tanh:f(1),tanhPf:function(e){return(F.exp(2*e)-1)/(F.exp(2*e)+1)}(1),exp:g(1),expm1:p(1),expm1Pf:function(e){return F.exp(e)-1}(1),log1p:m(10),log1pPf:function(e){return F.log(1+e)}(10),powPI:function(e){return F.pow(F.PI,e)}(-100)}},videoCard:function(){var e,n=document.createElement("canvas"),t=null!==(e=n.getContext("webgl"))&&void 0!==e?e:n.getContext("experimental-webgl");if(t&&"getExtension"in t){var i=t.getExtension("WEBGL_debug_renderer_info");if(i)return{vendor:(t.getParameter(i.UNMASKED_VENDOR_WEBGL)||"").toString(),renderer:(t.getParameter(i.UNMASKED_RENDERER_WEBGL)||"").toString()}}},pdfViewerEnabled:function(){return navigator.pdfViewerEnabled},architecture:function(){var e=new Float32Array(1),n=new Uint8Array(e.buffer);return e[0]=1/0,e[0]=e[0]-e[0],n[3]}};var B="$ if upgrade to Pro: https://fpjs.dev/pro";function $(e){var n=function(e){if(E())return.4;if(C())return A()?.5:.3;var n=e.platform.value||"";if(/^Win/.test(n))return.6;if(/^Mac/.test(n))return.5;return.7}(e),t=function(e){return function(e,n){if(Math.abs(n)>=1)return Math.round(e/n)*n;var t=1/n;return Math.round(e*t)/t}(.99+.01*e,1e-4)}(n);return{score:n,comment:B.replace(/\$/g,"".concat(t))}}function z(e){return JSON.stringify(e,(function(e,n){return n instanceof Error?u({name:(t=n).name,message:t.message,stack:null===(i=t.stack)||void 0===i?void 0:i.split("\n")},t):n;var t,i}),2)}function W(e){return function(e,n){n=n||0;var t,i=(e=e||"").length%16,r=e.length-i,o=[0,n],a=[0,n],s=[0,0],u=[0,0],l=[2277735313,289559509],c=[1291169091,658871167];for(t=0;t<r;t+=16)s=[255&e.charCodeAt(t+4)|(255&e.charCodeAt(t+5))<<8|(255&e.charCodeAt(t+6))<<16|(255&e.charCodeAt(t+7))<<24,255&e.charCodeAt(t)|(255&e.charCodeAt(t+1))<<8|(255&e.charCodeAt(t+2))<<16|(255&e.charCodeAt(t+3))<<24],u=[255&e.charCodeAt(t+12)|(255&e.charCodeAt(t+13))<<8|(255&e.charCodeAt(t+14))<<16|(255&e.charCodeAt(t+15))<<24,255&e.charCodeAt(t+8)|(255&e.charCodeAt(t+9))<<8|(255&e.charCodeAt(t+10))<<16|(255&e.charCodeAt(t+11))<<24],s=y(s=h(s,l),31),o=v(o=y(o=w(o,s=h(s,c)),27),a),o=v(h(o,[0,5]),[0,1390208809]),u=y(u=h(u,c),33),a=v(a=y(a=w(a,u=h(u,l)),31),o),a=v(h(a,[0,5]),[0,944331445]);switch(s=[0,0],u=[0,0],i){case 15:u=w(u,b([0,e.charCodeAt(t+14)],48));case 14:u=w(u,b([0,e.charCodeAt(t+13)],40));case 13:u=w(u,b([0,e.charCodeAt(t+12)],32));case 12:u=w(u,b([0,e.charCodeAt(t+11)],24));case 11:u=w(u,b([0,e.charCodeAt(t+10)],16));case 10:u=w(u,b([0,e.charCodeAt(t+9)],8));case 9:u=h(u=w(u,[0,e.charCodeAt(t+8)]),c),a=w(a,u=h(u=y(u,33),l));case 8:s=w(s,b([0,e.charCodeAt(t+7)],56));case 7:s=w(s,b([0,e.charCodeAt(t+6)],48));case 6:s=w(s,b([0,e.charCodeAt(t+5)],40));case 5:s=w(s,b([0,e.charCodeAt(t+4)],32));case 4:s=w(s,b([0,e.charCodeAt(t+3)],24));case 3:s=w(s,b([0,e.charCodeAt(t+2)],16));case 2:s=w(s,b([0,e.charCodeAt(t+1)],8));case 1:s=h(s=w(s,[0,e.charCodeAt(t)]),l),o=w(o,s=h(s=y(s,31),c))}return o=v(o=w(o,[0,e.length]),a=w(a,[0,e.length])),a=v(a,o),o=v(o=x(o),a=x(a)),a=v(a,o),("00000000"+(o[0]>>>0).toString(16)).slice(-8)+("00000000"+(o[1]>>>0).toString(16)).slice(-8)+("00000000"+(a[0]>>>0).toString(16)).slice(-8)+("00000000"+(a[1]>>>0).toString(16)).slice(-8)}(function(e){for(var n="",t=0,i=Object.keys(e).sort();t<i.length;t++){var r=i[t],o=e[r],a=o.error?"error":JSON.stringify(o.value);n+="".concat(n?"|":"").concat(r.replace(/([:|\\])/g,"\\$1"),":").concat(a)}return n}(e))}function X(e){return void 0===e&&(e=50),function(e,n){void 0===n&&(n=1/0);var t=window.requestIdleCallback;return t?new Promise((function(e){return t.call(window,(function(){return e()}),{timeout:n})})):d(Math.min(e,n))}(e,2*e)}function U(e,n){var t=Date.now();return{get:function(i){return l(this,void 0,void 0,(function(){var r,o,a;return c(this,(function(s){switch(s.label){case 0:return r=Date.now(),[4,e()];case 1:return o=s.sent(),a=function(e){var n,t=$(e);return{get visitorId(){return void 0===n&&(n=W(this.components)),n},set visitorId(e){n=e},confidence:t,components:e,version:"3.4.2"}}(o),(n||(null==i?void 0:i.debug))&&console.log("Copy the text below to get the debug data:\n\n```\nversion: ".concat(a.version,"\nuserAgent: ").concat(navigator.userAgent,"\ntimeBetweenLoadAndGet: ").concat(r-t,"\nvisitorId: ").concat(a.visitorId,"\ncomponents: ").concat(z(o),"\n```")),[2,a]}}))}))}}}var G={load:function(){return l(this,arguments,void 0,(function(e){var n=void 0===e?{}:e,t=n.delayFallback,i=n.debug;return c(this,(function(e){switch(e.label){case 0:return[4,X(t)];case 1:return e.sent(),[2,U(O(q,{debug:i},[]),i)]}}))}))},hashComponents:W,componentsToDebugString:z};const Q=e=>{try{if(null==e)return null;let t=e?.toString();return"[object Object]"===t&&(n=e,t=Object.keys(n).reduce(((e,t,i)=>(i>0&&(e+=";"),e+`${t}=${n[t]}`)),"")),s(t).toString()}catch(e){return null}var n},J=e=>{try{const n=(e=>{const n=e?.value?.map((e=>e.name));return Q(n)})(e.plugins),t=e.screenResolution.value[0],i=e.screenResolution.value[1],r=e.colorDepth.value,o=e.platform.value,a=e.touchSupport.value.maxTouchPoints,s=e.hardwareConcurrency.value,u=e.sessionStorage.value,l=e.localStorage.value,c=e.indexedDB.value,d=window.encodeURI(e.cpuClass.value),f=Q(e.videoCard.value),g=e.audio.value,p=e.deviceMemory.value,m=e.vendorFlavors.value,v=e.colorGamut.value,h=e.invertedColors.value,y=e.forcedColors.value,b=e.monochrome.value,w=e.contrast.value,x=e.reducedMotion.value,D=e.hdr.value,P=Q(e.math.value),S=e.architecture.value,M=e.touchSupport.value.touchEvent,T=e.touchSupport.value.touchStart,O=e.cookiesEnabled.value,_=e.pdfViewerEnabled.value,N=Q(e.languages.value),C=e.timezone.value;return{fph:n,fsh:i,fsw:t,fcd:r,fp:o,ftp:a,fhc:s,fss:u,fls:l,fin:c,fcp:d,fvch:f,fad:g,fdm:p,fvf:m,fcg:v,fic:h,ffc:y,fm:b,fc:w,frm:x,fhdr:D,fmf:P,fa:S,fte:M,fts:T,fce:O,fpdf:_,fl:N,ft:C}}catch(e){return{}}};"Sift"===window.Sift?console.warn("It's possible that you are loading beacon script multiple times. Please, check your code."):window.Sift="Sift",window._sift=window?._sift||[];var _sift=window._sift;!function(){function e(){return null!=PluginDetect&&"0.9.1"==PluginDetect.version&&"function"==typeof PluginDetect.isMinVersion&&"function"==typeof PluginDetect.getVersion}async function n(){return new Promise((async e=>{const n=setTimeout((()=>{e({})}),1e4),t=await async function(){const e=await G.load();return await e.get()}();clearTimeout(n);const i=J(t.components);e(i)}))}!function(){var e={version:"0.9.1",name:"PluginDetect",addPlugin:function(n,t){n&&e.isString(n)&&t&&e.isFunc(t.getVersion)&&(n=n.replace(/\s/g,"").toLowerCase(),e.Plugins[n]=t,e.isDefined(t.getVersionDone)||(t.installed=null,t.version=null,t.version0=null,t.getVersionDone=null,t.pluginName=n))},uniqueName:function(){return e.name+"998"},openTag:"<",hasOwnPROP:{}.constructor.prototype.hasOwnProperty,hasOwn:function(n,t){var i;try{i=e.hasOwnPROP.call(n,t)}catch(e){}return!!i},rgx:{str:/string/i,num:/number/i,fun:/function/i,arr:/array/i},toString:{}.constructor.prototype.toString,isDefined:function(e){return void 0!==e},isArray:function(n){return e.rgx.arr.test(e.toString.call(n))},isString:function(n){return e.rgx.str.test(e.toString.call(n))},isNum:function(n){return e.rgx.num.test(e.toString.call(n))},isStrNum:function(n){return e.isString(n)&&/\d/.test(n)},isFunc:function(n){return e.rgx.fun.test(e.toString.call(n))},getNumRegx:/[\d][\d\.\_,\-]*/,splitNumRegx:/[\.\_,\-]/g,getNum:function(n,t){var i=e.isStrNum(n)?(t&&e.isString(t)?new RegExp(t):e.getNumRegx).exec(n):null;return i?i[0]:null},compareNums:function(n,t,i){var r,o,a,s=parseInt;if(e.isStrNum(n)&&e.isStrNum(t)){if(e.isDefined(i)&&i.compareNums)return i.compareNums(n,t);for(r=n.split(e.splitNumRegx),o=t.split(e.splitNumRegx),a=0;a<Math.min(r.length,o.length);a++){if(s(r[a],10)>s(o[a],10))return 1;if(s(r[a],10)<s(o[a],10))return-1}}return 0},formatNum:function(n,t){var i,r;if(!e.isStrNum(n))return null;for(e.isNum(t)||(t=4),t--,r=n.replace(/\s/g,"").split(e.splitNumRegx).concat(["0","0","0","0"]),i=0;i<4;i++)/^(0+)(.+)$/.test(r[i])&&(r[i]=RegExp.$2),(i>t||!/\d/.test(r[i]))&&(r[i]="0");return r.slice(0,4).join(",")},pd:{getPROP:function(e,n,t){try{e&&(t=e[n])}catch(e){this.errObj=e}return t},findNavPlugin:function(n){if(n.dbug)return n.dbug;var t=null;if(window.navigator){var i,r,o,a,s,u,l={Find:e.isString(n.find)?new RegExp(n.find,"i"):n.find,Find2:e.isString(n.find2)?new RegExp(n.find2,"i"):n.find2,Avoid:n.avoid?e.isString(n.avoid)?new RegExp(n.avoid,"i"):n.avoid:0,Num:n.num?/\d/:0},c=navigator.mimeTypes,d=navigator.plugins;if(n.mimes&&c)for(a=e.isArray(n.mimes)?[].concat(n.mimes):e.isString(n.mimes)?[n.mimes]:[],i=0;i<a.length;i++){r=0;try{e.isString(a[i])&&/[^\s]/.test(a[i])&&(r=c[a[i]].enabledPlugin)}catch(e){}if(r&&((o=this.findNavPlugin_(r,l)).obj&&(t=o.obj),t&&!e.dbug))return t}if(n.plugins&&d){for(s=e.isArray(n.plugins)?[].concat(n.plugins):e.isString(n.plugins)?[n.plugins]:[],i=0;i<s.length;i++){r=0;try{s[i]&&e.isString(s[i])&&(r=d[s[i]])}catch(e){}if(r&&((o=this.findNavPlugin_(r,l)).obj&&(t=o.obj),t&&!e.dbug))return t}if(u=d.length,e.isNum(u))for(i=0;i<u;i++){r=0;try{r=d[i]}catch(e){}if(r&&((o=this.findNavPlugin_(r,l)).obj&&(t=o.obj),t&&!e.dbug))return t}}}return t},findNavPlugin_:function(e,n){var t=e.description||"",i=e.name||"",r={};return(!n.Find.test(t)||n.Find2&&!n.Find2.test(i)||n.Num&&!n.Num.test(RegExp.leftContext+RegExp.rightContext))&&(!n.Find.test(i)||n.Find2&&!n.Find2.test(t)||n.Num&&!n.Num.test(RegExp.leftContext+RegExp.rightContext))||n.Avoid&&(n.Avoid.test(t)||n.Avoid.test(i))||(r.obj=e),r},getVersionDelimiter:",",findPlugin:function(n){var t,i={status:-3,plugin:0};return e.isString(n)?1==n.length?(this.getVersionDelimiter=n,i):(n=n.toLowerCase().replace(/\s/g,""),(t=e.Plugins[n])&&t.getVersion?(i.plugin=t,i.status=1,i):i):i}},getPluginFileVersion:function(n,t,i,r){var o,a,s,u,l=-1;if(!n)return t;if(n[r=r||"version"]&&(o=e.getNum(n[r]+"",i)),!o||!t)return t||o||null;for(a=e.formatNum(t).split(e.splitNumRegx),s=e.formatNum(o).split(e.splitNumRegx),u=0;u<a.length;u++){if(l>-1&&u>l&&"0"!=a[u])return t;if(s[u]!=a[u]&&(-1==l&&(l=u),"0"!=a[u]))return t}return o},AXO:function(){var e;try{e=new window.ActiveXObject}catch(e){}return e?null:window.ActiveXObject}(),getAXO:function(n){var t=null;try{t=new e.AXO(n)}catch(n){e.errObj=n}return t&&(e.browser.ActiveXEnabled=!0),t},browser:{detectPlatform:function(){var n,t=window.navigator&&navigator.platform||"";if(e.OS=100,t){var i=["Win",1,"Mac",2,"Linux",3,"FreeBSD",4,"iPhone",21.1,"iPod",21.2,"iPad",21.3,"Win.*CE",22.1,"Win.*Mobile",22.2,"Pocket\\s*PC",22.3,"",100];for(n=i.length-2;n>=0;n-=2)if(i[n]&&new RegExp(i[n],"i").test(t)){e.OS=i[n+1];break}}},detectIE:function(){var n,t,i,r,o=this,a=document,s=window.navigator&&navigator.userAgent||"";o.ActiveXFilteringEnabled=!1,o.ActiveXEnabled=!1;try{o.ActiveXFilteringEnabled=!!window.external.msActiveXFilteringEnabled()}catch(e){}for(i=["Msxml2.XMLHTTP","Msxml2.DOMDocument","Microsoft.XMLDOM","TDCCtl.TDCCtl","Shell.UIHelper","HtmlDlgSafeHelper.HtmlDlgSafeHelper","Scripting.Dictionary"].concat(r=["WMPlayer.OCX","ShockwaveFlash.ShockwaveFlash","AgControl.AgControl"]),n=0;n<i.length&&(!e.getAXO(i[n])||e.dbug);n++);if(o.ActiveXEnabled&&o.ActiveXFilteringEnabled)for(n=0;n<r.length;n++)if(e.getAXO(r[n])){o.ActiveXFilteringEnabled=!1;break}t=a.documentMode;try{a.documentMode=""}catch(e){}o.isIE=o.ActiveXEnabled,o.isIE=o.isIE||e.isNum(a.documentMode)||/*@cc_on!@*/!1;try{a.documentMode=t}catch(e){}o.verIE=null,o.isIE&&(o.verIE=(e.isNum(a.documentMode)&&a.documentMode>=7?a.documentMode:0)||(/^(?:.*?[^a-zA-Z])??(?:MSIE|rv\s*\:)\s*(\d+\.?\d*)/i.test(s)?parseFloat(RegExp.$1,10):7))},detectNonIE:function(){var n=this,t=0,i=window.navigator?navigator:{},r=n.isIE?"":i.userAgent||"",o=i.vendor||"",a=i.product||"";n.isGecko=!t&&/Gecko/i.test(a)&&/Gecko\s*\/\s*\d/i.test(r),t=t||n.isGecko,n.verGecko=n.isGecko?e.formatNum(/rv\s*\:\s*([\.\,\d]+)/i.test(r)?RegExp.$1:"0.9"):null,n.isOpera=!t&&/(OPR\s*\/|Opera\s*\/\s*\d.*\s*Version\s*\/|Opera\s*[\/]?)\s*(\d+[\.,\d]*)/i.test(r),t=t||n.isOpera,n.verOpera=n.isOpera?e.formatNum(RegExp.$2):null,n.isEdge=!t&&/(Edge)\s*\/\s*(\d[\d\.]*)/i.test(r),t=t||n.isEdge,n.verEdgeHTML=n.isEdge?e.formatNum(RegExp.$2):null,n.isChrome=!t&&/(Chrome|CriOS)\s*\/\s*(\d[\d\.]*)/i.test(r),t=t||n.isChrome,n.verChrome=n.isChrome?e.formatNum(RegExp.$2):null,n.isSafari=!t&&(/Apple/i.test(o)||!o)&&/Safari\s*\/\s*(\d[\d\.]*)/i.test(r),t=t||n.isSafari,n.verSafari=n.isSafari&&/Version\s*\/\s*(\d[\d\.]*)/i.test(r)?e.formatNum(RegExp.$1):null},init:function(){var e=this;e.detectPlatform(),e.detectIE(),e.detectNonIE()}},init:{hasRun:0,library:function(){window[e.name]=e;var n=document;e.win.init(),e.head=n.getElementsByTagName("head")[0]||n.getElementsByTagName("body")[0]||n.body||null,e.browser.init(),this.hasRun=1}},ev:{addEvent:function(e,n,t){e&&n&&t&&(e.addEventListener?e.addEventListener(n,t,!1):e.attachEvent?e.attachEvent("on"+n,t):e["on"+n]=this.concatFn(t,e["on"+n]))},removeEvent:function(e,n,t){e&&n&&t&&(e.removeEventListener?e.removeEventListener(n,t,!1):e.detachEvent&&e.detachEvent("on"+n,t))},concatFn:function(e,n){return function(){e(),"function"==typeof n&&n()}},handler:function(e,n,t,i,r){return function(){e(n,t,i,r)}},handlerOnce:function(n,t,i,r){return function(){var o=e.uniqueName();n[o]||(n[o]=1,n(t,i,r))}},handlerWait:function(e,n,t,i,r){var o=this;return function(){o.setTimeout(o.handler(n,t,i,r),e)}},setTimeout:function(n,t){e.win&&e.win.pagehide||setTimeout(n,t)},fPush:function(n,t){e.isArray(t)&&(e.isFunc(n)||e.isArray(n)&&n.length>0&&e.isFunc(n[0]))&&t.push(n)},call0:function(n){var t=e.isArray(n)?n.length:-1;t>0&&e.isFunc(n[0])?n[0](e,t>1?n[1]:0,t>2?n[2]:0,t>3?n[3]:0):e.isFunc(n)&&n(e)},callArray0:function(n){var t;if(e.isArray(n))for(;n.length;)t=n[0],n.splice(0,1),e.win&&e.win.pagehide&&n!==e.win.pagehideHndlrs||this.call0(t)},call:function(e){this.call0(e),this.ifDetectDoneCallHndlrs()},callArray:function(e){this.callArray0(e),this.ifDetectDoneCallHndlrs()},allDoneHndlrs:[],ifDetectDoneCallHndlrs:function(){var n,t,i=this;if(i.allDoneHndlrs.length&&(!e.win||e.win.loaded&&!e.win.loadPrvtHndlrs.length&&!e.win.loadPblcHndlrs.length)){if(e.Plugins)for(n in e.Plugins)if(e.hasOwn(e.Plugins,n)&&(t=e.Plugins[n])&&e.isFunc(t.getVersion)&&(3==t.OTF||t.DoneHndlrs&&t.DoneHndlrs.length||t.BIHndlrs&&t.BIHndlrs.length))return;i.callArray0(i.allDoneHndlrs)}}},isMinVersion:function(n,t,i,r){var o,a=e.pd.findPlugin(n),s=-1;return a.status<0?a.status:(o=a.plugin,t=e.formatNum(e.isNum(t)?t.toString():e.isStrNum(t)?e.getNum(t):"0"),1!=o.getVersionDone&&(o.getVersion(t,i,r),null===o.getVersionDone&&(o.getVersionDone=1)),null!==o.installed&&(s=o.installed<=.5?o.installed:.7==o.installed?1:null===o.version?0:e.compareNums(o.version,t,o)>=0?1:-.1),s)},getVersion:function(n,t,i){var r,o,a=e.pd.findPlugin(n);return a.status<0?null:(1!=(r=a.plugin).getVersionDone&&(r.getVersion(null,t,i),null===r.getVersionDone&&(r.getVersionDone=1)),o=(o=r.version||r.version0)?o.replace(e.splitNumRegx,e.pd.getVersionDelimiter):o)},hasMimeType:function(n){if(n&&window.navigator&&navigator.mimeTypes){var t,i,r,o,a=navigator.mimeTypes,s=e.isArray(n)?[].concat(n):e.isString(n)?[n]:[];for(o=s.length,r=0;r<o;r++){t=0;try{e.isString(s[r])&&/[^\s]/.test(s[r])&&(t=a[s[r]])}catch(e){}if((i=t?t.enabledPlugin:0)&&(i.name||i.description))return t}}return null},onDetectionDone:function(n,t,i,r){var o,a=e.pd.findPlugin(n);return-3==a.status?-1:(o=a.plugin,e.isArray(o.DoneHndlrs)||(o.DoneHndlrs=[]),1!=o.getVersionDone&&(e.getVersion?e.getVersion(n,i,r):e.isMinVersion(n,"0",i,r)),-.5!=o.installed&&.5!=o.installed?(e.ev.call(t),1):(e.ev.fPush(t,o.DoneHndlrs),0))},onWindowLoaded:function(n){e.win.loaded?e.ev.call(n):e.ev.fPush(n,e.win.loadPblcHndlrs)},codebase:{isDisabled:function(){return e.browser.ActiveXEnabled&&e.isDefined(e.pd.getPROP(document.createElement("object"),"object"))?0:1},isMin:function(n,t,i){var r,o,a=this,s=0;if(!e.isStrNum(t)||a.isDisabled())return s;if(a.init(n),!i||a.isActiveXObject(n,e.formatNum(n.DIGITMIN.join(",")))){if(!n.L)for(n.L={},r=0;r<n.Lower.length;r++)if(a.isActiveXObject(n,n.Lower[r])){n.L=a.convert(n,n.Lower[r]);break}n.L.v&&(o=a.convert(n,t,1)).x>=0&&(s=(n.L.x==o.x?a.isActiveXObject(n,o.v):e.compareNums(t,n.L.v)<=0)?1:-1)}return s},search:function(n,t){var i,r=this,o=(n.$$,0);if(i=n.searchHasRun||r.isDisabled()?1:0,n.searchHasRun=1,i)return n.version||null;if(r.init(n),!t||r.isActiveXObject(n,e.formatNum(n.DIGITMIN.join(",")))){var a,s,u,l,c,d=n.DIGITMAX,f=99999999,g=[0,0,0,0],p=[0,0,0,0],m=function(e,t){var i,a=[].concat(g);return a[e]=t,(i=r.isActiveXObject(n,a.join(",")))?(o=1,g[e]=t):p[e]=t,i};for(a=0;a<p.length;a++){for(g[a]=Math.floor(n.DIGITMIN[a])||0,l=g.join(","),c=g.slice(0,a).concat([f,f,f,f]).slice(0,g.length).join(","),u=0;u<d.length;u++)e.isArray(d[u])&&(d[u].push(0),d[u][a]>p[a]&&e.compareNums(c,n.Lower[u])>=0&&e.compareNums(l,n.Upper[u])<0&&(p[a]=Math.floor(d[u][a])));for(s=0;s<30;s++){if(p[a]-g[a]<=16){for(u=p[a];u>=g[a]+(a?1:0)&&!m(a,u);u--);break}m(a,Math.round((p[a]+g[a])/2))}if(!o)break;p[a]=g[a]}o&&(n.version=r.convert(n,g.join(",")).v)}return n.version||null},emptyNode:function(e){try{e.innerHTML=""}catch(e){}},HTML:[],len:0,onPagehide:function(e,n){var t,i,r=n.HTML;for(t=0;t<r.length;t++)(i=r[t])&&(r[t]=0,n.emptyNode(i.span()),i.span=0,i.spanObj=0,i=0);n.iframe=0},init:function(n){var t,i,r=this;if(!r.iframe){var o,a=e.DOM;o=a.iframe.insert(0,"$.codebase{ }"),r.iframe=o,a.iframe.write(o," "),a.iframe.close(o)}if(!n.init)for(n.init=1,e.ev.fPush([r.onPagehide,r],e.win.pagehideHndlrs),n.tagA='<object width="1" height="1" style="display:none;" codebase="#version=',i=n.classID||n.$$.classID||"",n.tagB='" '+(/clsid\s*:/i.test(i)?'classid="':'type="')+i+'">'+(n.ParamTags?n.ParamTags:"")+e.openTag+"/object>",t=0;t<n.Lower.length;t++)n.Lower[t]=e.formatNum(n.Lower[t]),n.Upper[t]=e.formatNum(n.Upper[t])},isActiveXObject:function(n,t){var i=this,r=0,o=(n.$$,(e.DOM.iframe.doc(i.iframe)||document).createElement("span"));return n.min&&e.compareNums(t,n.min)<=0?1:n.max&&e.compareNums(t,n.max)>=0?0:(o.innerHTML=n.tagA+t+n.tagB,e.pd.getPROP(o.firstChild,"object")&&(r=1),r?(n.min=t,i.HTML.push({spanObj:o,span:i.span})):(n.max=t,o.innerHTML=""),r)},span:function(){return this.spanObj},convert_:function(n,t,i,r){var o=n.convert[t];return o?e.isFunc(o)?e.formatNum(o(i.split(e.splitNumRegx),r).join(",")):i:o},convert:function(n,t,i){var r,o,a,s=this;if(o={v:t=e.formatNum(t),x:-1},t)for(r=0;r<n.Lower.length;r++)if((a=s.convert_(n,r,n.Lower[r]))&&e.compareNums(t,i?a:n.Lower[r])>=0&&(!r||e.compareNums(t,i?s.convert_(n,r,n.Upper[r]):n.Upper[r])<0)){o.v=s.convert_(n,r,t,i),o.x=r;break}return o},z:0},win:{disable:function(){this.cancel=!0},cancel:!1,loaded:!1,pagehide:!1,hasRun:0,init:function(){var n=this;n.hasRun||(n.hasRun=1,/complete/i.test(document.readyState||"")?n.loaded=!0:e.ev.addEvent(window,"load",n.onLoad),e.ev.addEvent(window,"pagehide",n.onPagehide))},loadPrvtHndlrs:[],loadPblcHndlrs:[],pagehideHndlrs:[],onPagehide:function(){var n=e.win;n.pagehide||(n.pagehide=!0,e.ev.removeEvent(window,"load",n.onLoad),e.ev.removeEvent(window,"pagehide",n.onPagehide),e.ev.callArray(n.pagehideHndlrs))},onLoad:function(){var n=e.win;n.loaded||n.pagehide||n.cancel||(n.loaded=!0,e.ev.callArray(n.loadPrvtHndlrs),e.ev.callArray(n.loadPblcHndlrs))}},DOM:{isEnabled:{objectTag:function(){var n=e.browser,t=n.isIE?0:1;return n.ActiveXEnabled&&(t=1),!!t},objectTagUsingActiveX:function(){var n=0;return e.browser.ActiveXEnabled&&(n=1),!!n},objectProperty:function(n){return n&&n.tagName&&e.browser.isIE?/applet/i.test(n.tagName)?!this.objectTag()||e.isDefined(e.pd.getPROP(document.createElement("object"),"object"))?1:0:e.isDefined(e.pd.getPROP(document.createElement(n.tagName),"object"))?1:0:0}},HTML:[],div:null,divID:"plugindetect",divWidth:500,getDiv:function(){return this.div||document.getElementById(this.divID)||null},initDiv:function(){var n,t=this;t.div||((n=t.getDiv())?t.div=n:(t.div=document.createElement("div"),t.div.id=t.divID,t.setStyle(t.div,t.getStyle.div()),t.insertDivInBody(t.div)),e.ev.fPush([t.onPagehide,t],e.win.pagehideHndlrs)),n=0},pluginSize:1,iframeWidth:40,iframeHeight:10,altHTML:"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;",emptyNode:function(n){if(n&&/div|span/i.test(n.tagName||"")){e.browser.isIE&&this.setStyle(n,["display","none"]);try{n.innerHTML=""}catch(e){}}},removeNode:function(e){try{e&&e.parentNode&&e.parentNode.removeChild(e)}catch(e){}},onPagehide:function(e,n){var t,i,r,o=n.HTML,a=o.length;if(a)for(i=a-1;i>=0;i--)(r=o[i])&&(o[i]=0,n.emptyNode(r.span()),n.removeNode(r.span()),r.span=0,r.spanObj=0,r.doc=0,r.objectProperty=0);t=n.getDiv(),n.emptyNode(t),n.removeNode(t),r=0,t=0,n.div=0},span:function(){var e=this;return e.spanObj||(e.spanObj=e.doc.getElementById(e.spanId)),e.spanObj||null},width:function(){var n,t,i=this.span();return n=i&&e.isNum(i.scrollWidth)?i.scrollWidth:-1,t=i&&e.isNum(i.offsetWidth)?i.offsetWidth:-1,i=0,t>0?t:n>0?n:Math.max(t,n)},obj:function(){var e=this.span();return e&&e.firstChild||null},readyState:function(){return e.browser.isIE&&e.isDefined(e.pd.getPROP(this.span(),"readyState"))?e.pd.getPROP(this.obj(),"readyState"):e.UNDEFINED},objectProperty:function(){var n,t=this;return t.DOM.isEnabled.objectProperty(t)&&(n=e.pd.getPROP(t.obj(),"object")),n},onLoadHdlr:function(e,n){n.loaded=1},getTagStatus:function(n,t,i,r,o,a,s){if(!n||!n.span())return-2;var u=n.width(),l=n.obj()?1:0,c=n.readyState(),d=n.objectProperty();if(d)return 1.5;var f=/clsid\s*\:/i,g=i&&f.test(i.outerHTML||"")?i:r&&f.test(r.outerHTML||"")?r:0,p=i&&!f.test(i.outerHTML||"")?i:r&&!f.test(r.outerHTML||"")?r:0,m=n&&f.test(n.outerHTML||"")?g:p;if(!(t&&t.span()&&m&&m.span()))return-2;var v=m.width(),h=t.width(),y=m.readyState();return u<0||v<0||h<=this.pluginSize?0:(s&&!n.pi&&e.isDefined(d)&&e.browser.isIE&&n.tagName==m.tagName&&n.time<=m.time&&u===v&&0===c&&0!==y&&(n.pi=1),v<h||!n.loaded||!t.loaded||!m.loaded?n.pi?-.1:0:u!=h&&l?u!=this.pluginSize||!l||e.isNum(c)&&4!==c?n.pi?-.5:-1:1:n.pi?-.5:-1)},setStyle:function(e,n){var t,i=e.style;if(i&&n)for(t=0;t<n.length;t+=2)try{i[n[t]]=n[t+1]}catch(e){}e=0,i=0},getStyle:{iframe:function(){return this.span()},span:function(n){var t=e.DOM;return n?this.plugin():[].concat(this.Default).concat(["display","inline","fontSize",t.pluginSize+3+"px","lineHeight",t.pluginSize+3+"px"])},div:function(){var n=e.DOM;return[].concat(this.Default).concat(["display","block","width",n.divWidth+"px","height",n.pluginSize+3+"px","fontSize",n.pluginSize+3+"px","lineHeight",n.pluginSize+3+"px","position","absolute","right","9999px","top","-9999px"])},plugin:function(n){var t=e.DOM;return"background-color:transparent;background-image:none;vertical-align:baseline;outline-style:none;border-style:none;padding:0px;margin:0px;visibility:"+(n?"hidden;":"visible;")+"display:inline;font-size:"+(t.pluginSize+3)+"px;line-height:"+(t.pluginSize+3)+"px;"},Default:["backgroundColor","transparent","backgroundImage","none","verticalAlign","baseline","outlineStyle","none","borderStyle","none","padding","0px","margin","0px","visibility","visible"]},insertDivInBody:function(n,t){var i="pd33993399",r=null,o=t?window.top.document:window.document,a=o.getElementsByTagName("body")[0]||o.body;if(!a)try{o.write('<div id="'+i+'">.'+e.openTag+"/div>"),r=o.getElementById(i)}catch(e){}(a=o.getElementsByTagName("body")[0]||o.body)&&(a.insertBefore(n,a.firstChild),r&&a.removeChild(r)),n=0},iframe:{onLoad:function(n,t){e.ev.callArray(n)},insert:function(n,t){var i,r,o=this,a=e.DOM,s=document.createElement("iframe");a.setStyle(s,a.getStyle.iframe()),s.width=a.iframeWidth,s.height=a.iframeHeight,a.initDiv(),a.getDiv().appendChild(s);try{o.doc(s).open()}catch(e){}return s[e.uniqueName()]=[],i=e.ev.handlerOnce(e.isNum(n)&&n>0?e.ev.handlerWait(n,o.onLoad,s[e.uniqueName()],t):e.ev.handler(o.onLoad,s[e.uniqueName()],t)),e.ev.addEvent(s,"load",i),s.onload||(s.onload=i),r=o.win(s),e.ev.addEvent(r,"load",i),r&&!r.onload&&(r.onload=i),s},addHandler:function(n,t){n&&e.ev.fPush(t,n[e.uniqueName()])},close:function(e){try{this.doc(e).close()}catch(e){}},write:function(e,n){var t,i=this.doc(e),r=-1;try{t=(new Date).getTime(),i.write(n),r=(new Date).getTime()-t}catch(e){}return r},win:function(e){try{return e.contentWindow}catch(e){}return null},doc:function(e){var n;try{n=e.contentWindow.document}catch(e){}try{n||(n=e.contentDocument)}catch(e){}return n||null}},insert:function(n,t,i,r,o,a,s){var u,l,c,d,f,g,p=this;if(s||(p.initDiv(),s=p.getDiv()),s&&(/div/i.test(s.tagName)&&(d=s.ownerDocument),/iframe/i.test(s.tagName)&&(d=p.iframe.doc(s))),d&&d.createElement||(d=document),e.isDefined(r)||(r=""),e.isString(n)&&/[^\s]/.test(n)){n=n.toLowerCase().replace(/\s/g,""),u=e.openTag+n+" ",u+='style="'+p.getStyle.plugin(a)+'" ';var m=1,v=1;for(f=0;f<t.length;f+=2)/[^\s]/.test(t[f+1])&&(u+=t[f]+'="'+t[f+1]+'" '),/width/i.test(t[f])&&(m=0),/height/i.test(t[f])&&(v=0);if(u+=(m?'width="'+p.pluginSize+'" ':"")+(v?'height="'+p.pluginSize+'" ':""),"embed"==n||"img"==n)u+=" />";else{for(u+=">",f=0;f<i.length;f+=2)/[^\s]/.test(i[f+1])&&(u+=e.openTag+'param name="'+i[f]+'" value="'+i[f+1]+'" />');u+=r+e.openTag+"/"+n+">"}}else n="",u=r;if(l={spanId:"",spanObj:null,span:p.span,loaded:null,tagName:n,outerHTML:u,DOM:p,time:(new Date).getTime(),insertDomDelay:-1,width:p.width,obj:p.obj,readyState:p.readyState,objectProperty:p.objectProperty,doc:d},s&&s.parentNode)if(/iframe/i.test(s.tagName))p.iframe.addHandler(s,[p.onLoadHdlr,l]),l.loaded=0,l.spanId=e.name+"Span"+p.HTML.length,c='<span id="'+l.spanId+'" style="'+p.getStyle.span(1)+'">'+u+"</span>",l.time=(new Date).getTime(),(g=p.iframe.write(s,c))>=0&&(l.insertDomDelay=g);else if(/div/i.test(s.tagName)){c=d.createElement("span"),p.setStyle(c,p.getStyle.span()),s.appendChild(c);try{l.time=(new Date).getTime(),c.innerHTML=u,l.insertDomDelay=(new Date).getTime()-l.time}catch(e){}l.spanObj=c}return c=0,s=0,p.HTML.push(l),l}},file:{any:"fileStorageAny999",valid:"fileStorageValid999",save:function(n,t,i){var r,o=this;n&&e.isDefined(i)&&(n[o.any]||(n[o.any]=[]),n[o.valid]||(n[o.valid]=[]),n[o.any].push(i),(r=o.split(t,i))&&n[o.valid].push(r))},getValidLength:function(e){return e&&e[this.valid]?e[this.valid].length:0},getAnyLength:function(e){return e&&e[this.any]?e[this.any].length:0},getValid:function(e,n){var t=this;return e&&e[t.valid]?t.get(e[t.valid],n):null},getAny:function(e,n){var t=this;return e&&e[t.any]?t.get(e[t.any],n):null},get:function(n,t){var i=n.length-1,r=e.isNum(t)?t:i;return r<0||r>i?null:n[r]},split:function(n,t){var i,r,o=null;return n=n?n.replace(".","\\."):"",r=new RegExp("^(.*[^\\/])("+n+"\\s*)$"),e.isString(t)&&r.test(t)&&(o={name:(i=RegExp.$1.split("/"))[i.length-1],ext:RegExp.$2,full:t},i[i.length-1]="",o.path=i.join("/")),o}},Plugins:{}};e.init.library();var n={setPluginStatus:function(n,t,i){var r=this;r.version=t?e.formatNum(t,3):null,r.installed=r.version?1:i?i>0?.7:-.1:n?0:-1,r.getVersionDone=.7==r.installed||-.1==r.installed||0===r.nav.done?0:1},getVersion:function(n,t){var i,r=this,o=null,a=0;t=e.browser.isIE?0:t,a&&!e.dbug||!r.nav.query(t).installed||(a=1),o&&!e.dbug||!r.nav.query(t).version||(o=r.nav.version),(i=o?0:r.codebase.isMin(n))?r.setPluginStatus(0,0,i):(o&&!e.dbug||(i=r.codebase.search())&&(a=1,o=i),a&&!e.dbug||!r.axo.query().installed||(a=1),o&&!e.dbug||!r.axo.query().version||(o=r.axo.version),r.setPluginStatus(a,o))},nav:{done:null,installed:0,version:null,result:[0,0],mimeType:["video/quicktime","application/x-quicktimeplayer","image/x-macpaint","image/x-quicktime","application/x-rtsp","application/x-sdp","application/sdp","audio/vnd.qcelp","video/sd-video","audio/mpeg","video/mp4","video/3gpp2","application/x-mpeg","audio/x-m4b","audio/x-aac","video/flc"],find:"QuickTime.*Plug-?in",find2:"QuickTime.*Plug-?in",find3filename:"QuickTime|QT",avoid:"Totem|VLC|RealPlayer|Helix|MPlayer|Windows\\s*Media\\s*Player",plugins:"QuickTime Plug-in",detect:function(n){var t,i,r=this,o={installed:0,version:null,plugin:null};return(t=e.pd.findNavPlugin({find:r.find,find2:n?0:r.find2,avoid:n?0:r.avoid,mimes:r.mimeType,plugins:r.plugins}))&&(o.plugin=t,o.installed=1,i=new RegExp(r.find,"i"),t.name&&i.test(t.name+"")&&(o.version=e.getNum(t.name+""))),o},query:function(n){var t,i,r=this;if(n=n?1:0,null===r.done){if(e.hasMimeType(r.mimeType)){(i=r.detect(1)).installed&&(t=r.detect(0),r.result=[t,t.installed?t:i]);var o,a=r.result[0],s=r.result[1],u=new RegExp(r.avoid,"i"),l=new RegExp(r.find3filename,"i");a=a?a.plugin:0,s=s?s.plugin:0,a||!s||!s.name||s.description&&!/^[\s]*$/.test(s.description+"")||u.test(s.name+"")||(o=(s.filename||"")+"",/^.*[\\\/]([^\\\/]*)$/.test(o)&&(o=RegExp.$1),o&&l.test(o)&&!u.test(o)&&(r.result[0]=r.result[1]))}r.done=r.result[0]===r.result[1]?1:0}return r.result[n]&&(r.installed=r.result[n].installed,r.version=r.result[n].version),r}},codebase:{classID:"clsid:02BF25D5-8C17-4B23-BC80-D3488ABDDC6B",isMin:function(t){var i,r=0;return this.$$=n,e.isStrNum(t)&&((i=t.split(e.splitNumRegx)).length>3&&parseInt(i[3],10)>0&&(i[3]="9999"),t=i.join(","),r=e.codebase.isMin(this,t)),r},search:function(){return this.$$=n,e.codebase.search(this)},DIGITMAX:[[12,11,11],[7,60],[7,11,11],0,[7,11,11]],DIGITMIN:[5,0,0,0],Upper:["999","7,60","7,50","7,6","7,5"],Lower:["7,60","7,50","7,6","7,5","0"],convert:[1,function(e,n){return n?[e[0],e[1]+e[2],e[3],"0"]:[e[0],e[1].charAt(0),e[1].charAt(1),e[2]]},1,0,1]},axo:{hasRun:0,installed:0,version:null,progID:["QuickTimeCheckObject.QuickTimeCheck","QuickTimeCheckObject.QuickTimeCheck.1"],progID0:"QuickTime.QuickTime",query:function(){var n,t,i,r=this,o=r.hasRun||!e.browser.ActiveXEnabled;if(r.hasRun=1,o)return r;for(t=0;t<r.progID.length&&(!(n=e.getAXO(r.progID[t]))||(r.installed=1,!(i=e.pd.getPROP(n,"QuickTimeVersion"))||!i.toString||(i=i.toString(16),r.version=parseInt(i.charAt(0)||"0",16)+"."+parseInt(i.charAt(1)||"0",16)+"."+parseInt(i.charAt(2)||"0",16),e.dbug)));t++);return r}}};e.addPlugin("quicktime",n);var t={mimeType:"application/x-shockwave-flash",setPluginStatus:function(n,t,i){var r,o=this;o.installed=t?1:n?0:-1,o.precision=i,o.version=e.formatNum(t),r=(r=-1==o.installed||o.instance.version)||o.axo.version,o.getVersionDone=r?1:0},getPrecision:function(n){if(e.isString(n)){var t,i="\\d+",r=[i,i,i,i];for(t=4;t>0;t--)if(new RegExp(r.slice(0,t).join("[\\._,]")).test(n))return t}return 0},getVersion:function(n,t){var i=this,r=null,o=0,a=0;o&&!e.dbug||!i.navPlugin.query().installed||(o=1),r&&!e.dbug||!i.navPlugin.query().version||(r=i.navPlugin.version,a=i.navPlugin.precision),o&&!e.dbug||!i.axo.query().installed||(o=1),r&&!e.dbug||!i.axo.query().version||(r=i.axo.version,a=i.axo.precision),(!o&&!r||t||e.dbug)&&i.instance.query().version&&(o=1,r=i.instance.version,a=i.instance.precision),i.setPluginStatus(o,r,a)},navPlugin:{hasRun:0,installed:0,precision:0,version:null,getNum:function(e){if(!e)return null;var n=/[\d][\d\,\.\s]*[rRdD]{0,1}[\d\,]*/.exec(e);return n?n[0].replace(/[rRdD\.]/g,",").replace(/\s/g,""):null},query:function(){var n,i,r=this,o=t,a=r.hasRun||!e.hasMimeType(o.mimeType);return r.hasRun=1,a||((i=e.pd.findNavPlugin({find:"Shockwave.*Flash",mimes:o.mimeType,plugins:["Shockwave Flash"]}))&&(r.installed=1,i.description&&(n=r.getNum(i.description))),n&&(n=e.getPluginFileVersion(i,n)),n&&/(\d+[_,]\d+[_,]\d+[_,]\d+)[^\d]+$/.test(i.filename)&&(n=e.getPluginFileVersion({filename:RegExp.$1,name:i.name,description:i.description},n,0,"filename")),n&&(r.version=n,r.precision=o.getPrecision(n))),r}},axo:{hasRun:0,installed:0,precision:0,version:null,progID:"ShockwaveFlash.ShockwaveFlash",classID:"clsid:D27CDB6E-AE6D-11CF-96B8-444553540000",query:function(){var n,i,r,o=this,a=o.hasRun;if(o.hasRun=1,a)return o;for(i=0;i<10;i++)if(r=e.getAXO(o.progID+(i?"."+i:""))){o.installed=1,n=0;try{n=e.getNum(r.GetVariable("$version")+"")}catch(e){}if(n&&(o.version=n,o.precision=t.getPrecision(n),!e.dbug))break}return o}},instance:{hasRun:0,precision:0,version:null,HTML:null,HTML2:null,isEnabled:function(){var n=t,i=1;return!this.hasRun&&!e.DOM.isEnabled.objectTagUsingActiveX()&&e.DOM.isEnabled.objectTag()&&e.hasMimeType(n.mimeType)||(i=0),i},query:function(){var n,i=this,r=t,o=i.isEnabled();if(i.hasRun=1,o){i.HTML=e.DOM.insert("object",["type",r.mimeType],["play","false","menu","false"],"",r);try{n=i.HTML.obj().GetVariable("$version")+"",i.version=e.getNum(n)}catch(e){}if(!i.version||e.dbug){i.HTML2=e.DOM.insert("embed",["type",r.mimeType,"play","false","menu","false"],[],"",r);try{n=i.HTML2.obj().GetVariable("$version")+"",i.version=e.getNum(n)}catch(e){}}i.precision=r.getPrecision(i.version)}return i}}};e.addPlugin("flash",t);var i={getVersion:function(){var n,t=this,i=null;n&&!e.dbug||!t.nav.query().installed||(n=1),i&&!e.dbug||!t.nav.query().version||(i=t.nav.version),n&&!e.dbug||!t.axo.query().installed||(n=1),i&&!e.dbug||!t.axo.query().version||(i=t.axo.version),t.installed=i?1:n?0:-1,t.version=e.formatNum(i)},nav:{hasRun:0,installed:0,version:null,mimeType:"application/x-director",query:function(){var n,t,i=this,r=i.hasRun||!e.hasMimeType(i.mimeType);return i.hasRun=1,r||((t=e.pd.findNavPlugin({find:"Shockwave\\s*for\\s*Director",mimes:i.mimeType,plugins:"Shockwave for Director"}))&&t.description&&(n=e.getNum(t.description+"")),n&&(n=e.getPluginFileVersion(t,n)),t&&(i.installed=1),n&&(i.version=n)),i}},axo:{hasRun:0,installed:null,version:null,progID:["SWCtl.SWCtl","SWCtl.SWCtl.1","SWCtl.SWCtl.7","SWCtl.SWCtl.8","SWCtl.SWCtl.11","SWCtl.SWCtl.12"],classID:"clsid:166B1BCA-3F9C-11CF-8075-444553540000",query:function(){var n,t,i,r,o,a=this,s=!a.hasRun;if(a.hasRun=1,s)for(t=0;t<a.progID.length;t++)if(n=e.getAXO(a.progID[t])){a.installed=1,r="";try{r=n.ShockwaveVersion("")+""}catch(e){}if(/(\d[\d\.\,]*)(?:\s*r\s*(\d+))?/i.test(r)&&(o=RegExp.$2,i=e.formatNum(RegExp.$1),o&&((i=i.split(e.splitNumRegx))[3]=o,i=i.join(","))),i&&(a.version=i,!e.dbug))break}return a}}};e.addPlugin("shockwave",i);var r={setPluginStatus:function(n,t){var i=this;n&&(i.version=e.formatNum(n)),i.installed=i.version?1:t?0:-1,i.getVersionDone=0===i.installed?0:1},getVersion:function(n,t){var i,r=this,o=null;i&&!e.dbug||!r.nav.query().installed||(i=1),i&&!e.dbug||!r.axo.query().installed||(i=1),o&&!e.dbug||!r.axo.query().version||(o=r.axo.version),(!i&&!o||t||e.dbug)&&r.FirefoxPlugin.query().version&&(i=1,o=r.FirefoxPlugin.version),r.setPluginStatus(o,i)},mimeType:["application/x-ms-wmp","application/asx","application/x-mplayer2","video/x-ms-asf","video/x-ms-wm","video/x-ms-asf-plugin"],find:["Microsoft.*Windows\\s*Media\\s*Player.*Firefox.*Plug-?in","Windows\\s*Media\\s*Player\\s*Plug-?in\\s*Dynamic\\s*Link\\s*Library","Flip4Mac.*Windows\\s*Media.*Plug-?in|Flip4Mac.*WMV.*Plug-?in"],avoid:"Totem|VLC|RealPlayer|Helix",plugins:["Microsoft"+String.fromCharCode(174)+" Windows Media Player Firefox Plugin","Windows Media Player Plug-in Dynamic Link Library"],nav:{hasRun:0,installed:0,query:function(){var n=this,t=r,i=n.hasRun||!e.hasMimeType(t.mimeType);return n.hasRun=1,i||e.pd.findNavPlugin({find:t.find.join("|"),avoid:t.avoid,mimes:t.mimeType,plugins:t.plugins})&&(n.installed=1),n}},FirefoxPlugin:{hasRun:0,version:null,isDisabled:function(){var n=r,t=e.browser;return this.hasRun||t.isGecko&&e.compareNums(t.verGecko,e.formatNum("1.8"))<0||t.isOpera&&e.compareNums(t.verOpera,e.formatNum("10"))<0||e.DOM.isEnabled.objectTagUsingActiveX()||!e.hasMimeType(n.mimeType)||!e.pd.findNavPlugin({find:n.find[0],avoid:n.avoid,mimes:n.mimeType,plugins:n.plugins[0]})?1:0},query:function(){var n,t=this,i=r,o=t.isDisabled();return t.hasRun=1,o||(n=e.pd.getPROP(e.DOM.insert("object",["type",e.hasMimeType(i.mimeType).type,"data",""],["src",""],"",i).obj(),"versionInfo"))&&(t.version=e.getNum(n)),t}},axo:{hasRun:0,installed:null,version:null,progID:["WMPlayer.OCX","WMPlayer.OCX.7"],classID:"clsid:6BF52A52-394A-11D3-B153-00C04F79FAA6",query:function(){var n,t,i,r=this,o=!r.hasRun;if(r.hasRun=1,o)for(t=0;t<r.progID.length&&(!(n=e.getAXO(r.progID[t]))||(r.installed=1,(i=e.pd.getPROP(n,"versionInfo",0))&&(i=e.getNum(i)),!i||(r.version=i,e.dbug)));t++);return r}}};e.addPlugin("windowsmediaplayer",r);var o={getVersion:function(){var n=this,t=null,i=0;i&&!e.dbug||!n.nav.query().installed||(i=1),t&&!e.dbug||!n.nav.query().version||(t=n.nav.version),i&&!e.dbug||!n.axo.query().installed||(i=1),t&&!e.dbug||!n.axo.query().version||(t=n.axo.version),n.version=e.formatNum(t),n.installed=t?1:i?0:-1},nav:{hasRun:0,installed:0,version:null,mimeType:["application/x-silverlight","application/x-silverlight-2"],query:function(){var n,t,i,r=this,o=r.hasRun||!e.hasMimeType(r.mimeType);return r.hasRun=1,o||((i=e.pd.findNavPlugin({find:"Silverlight.*Plug-?in",mimes:r.mimeType,plugins:"Silverlight Plug-In"}))&&(r.installed=1),i&&i.description&&(t=e.formatNum(e.getNum(i.description+""))),t&&(n=t.split(e.splitNumRegx),parseInt(n[0],10)<2&&parseInt(n[2],10)>=30226&&(n[0]="2"),t=n.join(",")),t&&(r.version=t)),r}},axo:{hasRun:0,installed:0,version:null,progID:"AgControl.AgControl",maxdigit:[20,10,10,100,100,10],mindigit:[0,0,0,0,0,0],IsVersionSupported:function(n,t){var i=this;try{return i.testVersion?e.compareNums(e.formatNum(i.testVersion.join(",")),e.formatNum(t.join(",")))>=0:n.IsVersionSupported(i.format(t))}catch(e){}return 0},format:function(e){return e[0]+"."+e[1]+"."+e[2]+this.make2digits(e[3])+this.make2digits(e[4])+"."+e[5]},make2digits:function(e){return(e<10?"0":"")+e},query:function(){var n,t,i=this,r=i.hasRun;if(i.hasRun=1,r)return i;if((t=e.getAXO(i.progID))&&(i.installed=1),t&&i.IsVersionSupported(t,i.mindigit)){var o,a=[].concat(i.mindigit);for(n=0;n<i.maxdigit.length;n++){for(o=0;i.maxdigit[n]-i.mindigit[n]>1&&o<20;)o++,a[n]=Math.round((i.maxdigit[n]+i.mindigit[n])/2),i.IsVersionSupported(t,a)?i.mindigit[n]=a[n]:i.maxdigit[n]=a[n];a[n]=i.mindigit[n]}i.version=i.format(a)}return i}}};e.addPlugin("silverlight",o);var a={compareNums:function(n,t){var i,r,o,a,s,u=n.split(e.splitNumRegx),l=t.split(e.splitNumRegx);for(i=0;i<Math.min(u.length,l.length);i++){if(/([\d]+)([a-z]?)/.test(u[i]),r=parseInt(RegExp.$1,10),a=2==i&&RegExp.$2.length>0?RegExp.$2.charCodeAt(0):-1,/([\d]+)([a-z]?)/.test(l[i]),o=parseInt(RegExp.$1,10),s=2==i&&RegExp.$2.length>0?RegExp.$2.charCodeAt(0):-1,r!=o)return r>o?1:-1;if(2==i&&a!=s)return a>s?1:-1}return 0},setPluginStatus:function(n,t,i){var r=this;r.installed=t?1:i?i>0?.7:-.1:n?0:-1,t&&(r.version=e.formatNum(t)),r.getVersionDone=1==r.installed||-1==r.installed||r.instance.hasRun?1:0},getVersion:function(n,t){var i,r,o=this,a=null;i&&!e.dbug||!o.nav.query().installed||(i=1),a&&!e.dbug||!o.nav.query().version||(a=o.nav.version),i&&!e.dbug||!o.axo.query().installed||(i=1),a&&!e.dbug||!o.axo.query().version||(a=o.axo.version),a&&!e.dbug||!(r=o.codebase.isMin(n))?(a&&!e.dbug||(r=o.codebase.search())&&(i=1,a=r),(!a&&t||e.dbug)&&(r=o.instance.query().version)&&(i=1,a=r),o.setPluginStatus(i,a,0)):o.setPluginStatus(0,0,r)},nav:{hasRun:0,installed:0,version:null,mimeType:["application/x-vlc-plugin","application/x-google-vlc-plugin","application/mpeg4-muxcodetable","application/x-matroska","application/xspf+xml","video/divx","video/webm","video/x-mpeg","video/x-msvideo","video/ogg","audio/x-flac","audio/amr","audio/amr"],find:"VLC.*Plug-?in",find2:"VLC|VideoLAN",avoid:"Totem|Helix",plugins:["VLC Web Plugin","VLC Multimedia Plug-in","VLC Multimedia Plugin","VLC multimedia plugin"],query:function(){var n,t,i=this,r=i.hasRun||!e.hasMimeType(i.mimeType);return i.hasRun=1,r||(t=e.pd.findNavPlugin({find:i.find,avoid:i.avoid,mimes:i.mimeType,plugins:i.plugins}))&&(i.installed=1,t.description&&(n=e.getNum(t.description+"","[\\d][\\d\\.]*[a-z]*")),(n=e.getPluginFileVersion(t,n))&&(i.version=n)),i}},instance:{hasRun:0,installed:0,version:null,mimeType:"application/x-vlc-plugin",HTML:0,isDisabled:function(){var n=1;return this.hasRun||(e.dbug||a.nav.installed&&e.hasMimeType(this.mimeType))&&(n=0),n},query:function(){var n=this,t=e.DOM.altHTML,i=null,r=0,o=n.isDisabled();if(n.hasRun=1,o)return n;if(n.HTML=e.DOM.insert("object",["type",n.mimeType],["autoplay","no","loop","no","volume","0"],t,a),r=n.HTML.obj()){try{i||(i=e.getNum(r.VersionInfo))}catch(e){}try{i||(i=e.getNum(r.versionInfo()))}catch(e){}}return i&&(n.version=i,n.installed=1),n}},axo:{hasRun:0,installed:0,version:null,progID:"VideoLAN.VLCPlugin",query:function(){var n,t,i=this,r=i.hasRun;return i.hasRun=1,r||(n=e.getAXO(i.progID))&&(i.installed=1,(t=e.getNum(e.pd.getPROP(n,"VersionInfo"),"[\\d][\\d\\.]*[a-z]*"))&&(i.version=t)),i}},codebase:{classID:"clsid:9BE31822-FDAD-461B-AD51-BE1D1C159921",isMin:function(n){return this.$$=a,e.codebase.isMin(this,n)},search:function(){return this.$$=a,e.codebase.search(this)},DIGITMAX:[[11,11,16]],DIGITMIN:[0,0,0,0],Upper:["999"],Lower:["0"],convert:[1]}};e.addPlugin("vlc",a);var s={OTF:null,setPluginStatus:function(){var n=this,t=n.OTF,i=n.nav.detected,r=n.nav.version,o=n.nav.precision,a=r,s=i>0,u=n.axo.detected,l=n.axo.version,c=n.axo.precision,d=n.doc.detected,f=n.doc.version,g=n.doc.precision,p=n.doc2.detected,m=n.doc2.version;o=n.doc2.precision||o||c||g,s=p>0||s||u>0||d>0,a=(a=m||a||l||f)||null,n.version=e.formatNum(a),n.precision=o;var v=-1;if(3==t?v=n.version?.5:-.5:a?v=1:s?v=0:-.5==u||-.5==d?v=-.15:!e.browser.isIE||e.browser.ActiveXEnabled&&!e.browser.ActiveXFilteringEnabled||(v=-1.5),n.installed=v,1!=n.getVersionDone){var h=1;(n.verify&&n.verify.isEnabled()||.5==n.installed||-.5==n.installed||1==n.doc2.isDisabled())&&(h=0),n.getVersionDone=h}},getVersion:function(n,t){var i=this,r=0,o=i.verify;if(null===i.getVersionDone&&(i.OTF=0,o&&o.init()),e.file.save(i,".pdf",t),0===i.getVersionDone)return i.doc2.insertHTMLQuery(),void i.setPluginStatus();r&&!e.dbug||!i.nav.query().version||(r=1),r&&!e.dbug||!i.axo.query().version||(r=1),r&&!e.dbug||!i.doc.query().version||(r=1),i.doc2.insertHTMLQuery(),i.setPluginStatus()},getPrecision:function(n,t,i){if(e.isString(n)){t=t||"",i=i||"";var r,o="\\d+",a=[o,o,o,o];for(r=4;r>0;r--)if(new RegExp(t+a.slice(0,r).join("[\\.]")+i).test(n))return r}return 0},nav:{detected:0,version:null,precision:0,mimeType:["application/pdf","application/vnd.adobe.pdfxml"],find:"Adobe.*PDF.*Plug-?in|Adobe.*Acrobat.*Plug-?in|Adobe.*Reader.*Plug-?in",plugins:["Adobe Acrobat","Adobe Acrobat and Reader Plug-in","Adobe Reader Plugin"],query:function(){var n,t=this,i=null;return t.detected||!e.hasMimeType(t.mimeType)||(n=e.pd.findNavPlugin({find:t.find,mimes:t.mimeType,plugins:t.plugins}),t.detected=n?1:-1,n&&(i=e.getNum(n.description)||e.getNum(n.name),(i=e.getPluginFileVersion(n,i))||(i=t.attempt3()),i&&(t.version=i,t.precision=s.getPrecision(i)))),t},attempt3:function(){var n=null;return 1==e.OS&&(e.hasMimeType("application/vnd.adobe.pdfxml")?n="9":e.hasMimeType("application/vnd.adobe.x-mars")?n="8":e.hasMimeType("application/vnd.adobe.xfdf")&&(n="6")),n}},activexQuery:function(n){var t,i,r,o,a="",u={precision:0,version:null};try{n&&(a=n.GetVersions()+"")}catch(e){}if(a&&e.isString(a)&&(t=/\=\s*[\d\.]+/g,o=a.match(t))){for(i=0;i<o.length;i++)(r=e.formatNum(e.getNum(o[i])))&&(!u.version||e.compareNums(r,u.version)>0)&&(u.version=r);u.precision=s.getPrecision(a,"\\=\\s*")}return u},axo:{detected:0,version:null,precision:0,progID:["AcroPDF.PDF","AcroPDF.PDF.1","PDF.PdfCtrl","PDF.PdfCtrl.5","PDF.PdfCtrl.1"],progID_dummy:"AcroDUMMY.DUMMY",query:function(){var n,t,i,r,o,a,u=this,l=s;if(u.detected)return u;for(u.detected=-1,(t=e.getAXO(u.progID_dummy))||(a=e.errObj),o=0;o<u.progID.length;o++)if(t=e.getAXO(u.progID[o])){if(u.detected=1,i=(n=l.activexQuery(t)).version,r=n.precision,!e.dbug&&i)break}else a&&e.errObj&&a!==e.errObj&&a.message!==e.errObj.message&&(u.detected=-.5);return i&&(u.version=i),r&&(u.precision=r),u}},doc:{detected:0,version:null,precision:0,classID:"clsid:CA8A9780-280D-11CF-A24D-444553540000",classID_dummy:"clsid:CA8A9780-280D-11CF-A24D-BA9876543210",DummySpanTagHTML:0,HTML:0,DummyObjTagHTML1:0,DummyObjTagHTML2:0,isDisabled:function(){var n=0;return this.HTML?n=1:e.dbug||e.DOM.isEnabled.objectTagUsingActiveX()||(n=1),n},query:function(){var n,t,i,r,o,a=this,u=s,l=e.DOM.altHTML;return a.isDisabled()||(n=e.DOM.iframe.insert(99,"Adobe Reader"),a.DummySpanTagHTML=e.DOM.insert("",[],[],l,u,1,n),a.HTML=e.DOM.insert("object",["classid",a.classID],[],l,u,1,n),a.DummyObjTagHTML2=e.DOM.insert("object",["classid",a.classID_dummy],[],l,u,1,n),e.DOM.iframe.close(n),o=e.DOM.getTagStatus(a.HTML,a.DummySpanTagHTML,a.DummyObjTagHTML1,a.DummyObjTagHTML2,0,0,1),i=(t=u.activexQuery(a.HTML.obj())).version,r=t.precision,a.detected=o>0||i?1:-.1==o||-.5==o?-.5:-1,i&&(a.version=i),r&&(a.precision=r)),a}},doc2:{detected:0,version:null,precision:0,classID:"clsid:CA8A9780-280D-11CF-A24D-444553540000",mimeType:"application/pdf",HTML:0,count:0,count2:0,time2:0,intervalLength:25,maxCount:350,isDisabled:function(){var n,t,i,r=s,o=r.axo,a=r.nav,u=r.doc,l=0;return this.HTML?l=2:e.dbug||(e.DOM.isEnabled.objectTagUsingActiveX()?(n=(a?a.version:0)||(o?o.version:0)||(u?u.version:0)||0,t=(a?a.precision:0)||(o?o.precision:0)||(u?u.precision:0)||0,(!n||!t||t>2||e.compareNums(e.formatNum(n),e.formatNum("11"))<0)&&(l=2)):l=2),l<2&&((i=e.file.getValid(r))&&i.full||(l=1)),l},handlerSet:0,onMessage:function(){var n=this;return function(t){n.version||(n.detected=1,e.isArray(t)&&(t=t[0]),(t=e.getNum(t+""))&&(/^(\d+)[.,_]?$/.test(t)?(t=RegExp.$1+",0,0,0",n.precision=3):/^(\d+)[.,_](\d)(\d\d)$/.test(t)||/^(\d+)[.,_](\d\d\d)(\d\d\d\d\d)$/.test(t)?(t=RegExp.$1+","+RegExp.$2+","+RegExp.$3+",0",n.precision=3):/^(\d+)[.,_](\d\d\d)(\d\d\d\d\d)(\d+)$/.test(t)?(t=RegExp.$1+","+RegExp.$2+","+RegExp.$3+","+RegExp.$4,n.precision=4):/^(\d+)[.,_](\d)(\d)$/.test(t)?(t=RegExp.$1+","+RegExp.$2+","+RegExp.$3+",0",n.precision=3):/^(\d+)[.,_](\d)$/.test(t)&&(t=RegExp.$1+","+RegExp.$2+",0,0",n.precision=3),n.version=e.formatNum(t),s.setPluginStatus()))}},isDefinedMsgHandler:function(e,n){try{return e?e.messageHandler!==n:0}catch(e){}return 1},queryObject:function(){var e=this,n=e.HTML,t=n?n.obj():0;if(t){if(!e.handlerSet&&e.isDefinedMsgHandler(t)){try{t.messageHandler={onMessage:e.onMessage()}}catch(e){}e.handlerSet=1,e.count2=e.count,e.time2=(new Date).getTime()}e.detected||(e.count>3&&!e.handlerSet?e.detected=-1:e.time2&&e.count-e.count2>=e.maxCount&&(new Date).getTime()-e.time2>=e.intervalLength*e.maxCount&&(e.detected=-.5)),e.detected&&e.detected}},insertHTMLQuery:function(){var n,t,i=this,r=s,o=e.DOM.altHTML;return i.isDisabled()||(r.OTF<2&&(r.OTF=2),n=e.file.getValid(r).full,t=e.DOM.iframe.insert(0,"Adobe Reader"),e.DOM.iframe.write(t,'<script type="text/javascript"><\/script>'),i.HTML=e.DOM.insert("object",["data",n].concat(e.browser.isIE?["classid",i.classID]:["type",i.mimeType]),["src",n],o,r,0,t),e.DOM.iframe.addHandler(t,i.onIntervalQuery),r.OTF<3&&i.HTML&&(r.OTF=3),e.DOM.iframe.close(t)),i},onIntervalQuery:function(){var n=s,t=n.doc2;t.count++,3==n.OTF&&(t.queryObject(),t.detected&&t.queryCompleted()),3==n.OTF&&e.ev.setTimeout(t.onIntervalQuery,t.intervalLength)},queryCompleted:function(){var n=s;4!=n.OTF&&(n.OTF=4,n.setPluginStatus(),e.ev.callArray(n.DoneHndlrs))},z:0}};e.addPlugin("adobereader",s);var u={mimeType:["audio/x-pn-realaudio-plugin","audio/x-pn-realaudio"],classID:"clsid:CFCDAA03-8BE4-11cf-B84B-0020AFBBCCFA",setPluginStatus:function(n,t){var i,r=this;t&&(r.version=e.formatNum(e.getNum(t))),r.installed=r.version?1:n?0:-1,i=(i=-1==r.installed||r.instance.version)||r.axo.version,r.getVersionDone=i?1:0},navObj:{hasRun:0,installed:null,version:null,find:"RealPlayer.*Plug-?in",avoid:"Totem|QuickTime|Helix|VLC|Download",plugins:["RealPlayer(tm) G2 LiveConnect-Enabled Plug-In (32-bit) ","RealPlayer(tm) G2 LiveConnect-Enabled Plug-In (64-bit) ","RealPlayer Plugin"],query:function(){var n,t=this,i=u,r=!t.hasRun&&e.hasMimeType(i.mimeType);return t.hasRun=1,r&&(n=e.pd.findNavPlugin({find:t.find,avoid:t.avoid,mimes:i.mimeType,plugins:t.plugins}),t.installed=n?1:0,(n=e.getPluginFileVersion(n))&&e.compareNums(e.formatNum(n),e.formatNum("15"))>=0&&(t.version=n)),t}},JS:{hasRun:0,version:null,regStr:"RealPlayer.*Version.*Plug-?in",mimetype:"application/vnd.rn-realplayer-javascript",q1:[[11,0,0],[999],[663],[663],[663],[660],[468],[468],[468],[468],[468],[468],[431],[431],[431],[372],[180],[180],[172],[172],[167],[114],[0]],q3:[[6,0],[12,99],[12,69],[12,69],[12,69],[12,69],[12,69],[12,69],[12,69],[12,69],[12,69],[12,69],[12,46],[12,46],[12,46],[11,3006],[11,2806],[11,2806],[11,2804],[11,2804],[11,2799],[11,2749],[11,2700]],compare:function(e,n){var t,i,r,o=e.length,a=n.length;for(t=0;t<Math.max(o,a);t++){if((i=t<o?e[t]:0)>(r=t<a?n[t]:0))return 1;if(i<r)return-1}return 0},convertNum:function(n,t,i){var r,o,a,s=this,u=null;if(!n||!(r=e.formatNum(n)))return u;for(r=r.split(e.splitNumRegx),a=0;a<r.length;a++)r[a]=parseInt(r[a],10);if(0!==s.compare(r.slice(0,Math.min(t[0].length,r.length)),t[0]))return u;if(o=r.length>t[0].length?r.slice(t[0].length):[],s.compare(o,t[1])>0||s.compare(o,t[t.length-1])<0)return u;for(a=t.length-1;a>=1&&1!=a&&(0!==s.compare(t[a],o)||0!==s.compare(t[a],t[a-1]))&&!(s.compare(o,t[a])>=0&&s.compare(o,t[a-1])<0);a--);return i[0].join(".")+"."+i[a].join(".")},isEnabled:function(){return!this.hasRun&&1==e.OS&&e.hasMimeType(this.mimetype)?1:0},query:function(){var n,t,i,r=this,o=r.isEnabled();if(r.hasRun=1,o&&((t=e.pd.findNavPlugin({find:r.regStr,mimes:r.mimetype}))&&(n=e.formatNum(e.getNum(t.description))),n)){var a=n.split(e.splitNumRegx);i=1,(r.compare(a,[6,0,12,200])<0||r.compare(a,[6,0,12,1739])<=0&&r.compare(a,[6,0,12,857])>=0)&&(i=-1),i<0&&(t=r.convertNum(n,r.q3,r.q1),r.version=t||n)}return r}},instance:{hasRun:0,version:null,HTML:null,isEnabled:function(){var n=u,t=1;return e.DOM.isEnabled.objectTag()?e.dbug||(this.hasRun||e.DOM.isEnabled.objectTagUsingActiveX()||!e.hasMimeType(n.mimeType)||e.browser.isGecko&&e.compareNums(e.browser.verGecko,e.formatNum("1,8"))<0||e.browser.isOpera&&e.compareNums(e.browser.verOpera,e.formatNum("10"))<0)&&(t=0):t=0,t},query:function(){var n,t=this,i=u,r=t.isEnabled();if(t.hasRun=1,r){t.HTML=e.DOM.insert("object",["type",i.mimeType[0]],["src","","autostart","false","imagestatus","false","controls","stopbutton"],"",i),n=t.HTML.obj();try{t.version=e.getNum(n.GetVersionInfo())}catch(e){}e.DOM.setStyle(n,["display","none"])}return t}},axo:{hasRun:0,installed:null,version:null,progID:["rmocx.RealPlayer G2 Control","rmocx.RealPlayer G2 Control.1","RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)","RealVideo.RealVideo(tm) ActiveX Control (32-bit)","RealPlayer"],query:function(){var n,t,i,r=this;if(!r.hasRun)for(r.hasRun=1,t=0;t<r.progID.length;t++)if(n=e.getAXO(r.progID[t])){r.installed=1,i=0;try{i=n.GetVersionInfo()+""}catch(e){}if(i&&(r.version=i,!e.dbug))break}return r}},getVersion:function(n,t){var i=this,r=null,o=0;o&&!e.dbug||!i.axo.query().installed||(o=1),r&&!e.dbug||!i.axo.query().version||(r=i.axo.version),o&&!e.dbug||!i.navObj.query().installed||(o=1),r&&!e.dbug||!i.navObj.query().version||(r=i.navObj.version),r&&!e.dbug||!i.JS.query().version||(o=1,r=i.JS.version),(!o&&!r||t||e.dbug)&&i.instance.query().version&&(o=1,r=i.instance.version),i.setPluginStatus(o,r)}};e.addPlugin("realplayer",u)}();var t,i,r,o,a,u=(new Date).getTime(),l=document,c=navigator,d=screen,f=window,g=f.encodeURIComponent,p=f.decodeURIComponent,m="hexagon-analytics.com",v=100,h="__ssid",y=4,b=!1,w=200;function x(e,n){for(var t=g(e),i=e.length;0<t.length&&t.length>n;)i=Math.floor(i/2),t=g(e.substring(0,i));return t}function D(e){var n="",t=0;for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];null!=r&&"undefined"!==r&&"null"!==r&&(t>0&&(n+="&"),n+=i+"="+r,t++)}return n}var P=function(){for(var e=3,n=document.createElement("b"),t=n.all||[];n.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i><![endif]--\x3e",t[0];);return e>4?e:document.documentMode}();function S(e,n,t,i){var r=void 0===a?function(){var e=window.location.hostname,n=e.split(".");if(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(e))return e;if(1==n.length)return null;for(var t="",i=0;i<2;){i++;var r=n[n.length-i];if(t=r+(i>1?".":"")+t,-1!=";ac;ad;ae;aero;af;ag;ai;al;am;an;ao;aq;ar;arpa;as;asia;at;au;aw;ax;az;ba;bb;bd;be;bf;bg;bh;bi;biz;bj;bm;bn;bo;br;bs;bt;bv;bw;by;bz;ca;cat;cc;cd;cf;cg;ch;ci;ck;cl;cm;cn;co;com;coop;cr;cu;cv;cx;cy;cz;de;dj;dk;dm;do;dz;ec;edu;ee;eg;er;es;et;eu;fi;fj;fk;fm;fo;fr;ga;gb;gd;ge;gf;gg;gh;gi;gl;gm;gn;gov;gp;gq;gr;gs;gt;gu;gw;gy;hk;hm;hn;hr;ht;hu;id;ie;il;im;in;info;int;io;iq;ir;is;it;je;jm;jo;jobs;jp;ke;kg;kh;ki;km;kn;kp;kr;kw;ky;kz;la;lb;lc;li;lk;lr;ls;lt;lu;lv;ly;ma;mc;md;me;mg;mh;mil;mk;ml;mm;mn;mo;mobi;mp;mq;mr;ms;mt;mu;museum;mv;mw;mx;my;mz;na;name;nc;ne;net;nf;ng;ni;nl;no;np;nr;nu;nz;om;org;pa;pe;pf;pg;ph;pk;pl;pm;pn;pr;pro;ps;pt;pw;py;qa;re;ro;rs;ru;rw;sa;sb;sc;sd;se;sg;sh;si;sj;sk;sl;sm;sn;so;sr;st;su;sv;sy;sz;tc;td;tel;tf;tg;th;tj;tk;tl;tm;tn;to;tp;tr;travel;tt;tv;tw;tz;ua;ug;uk;us;uy;uz;va;vc;ve;vg;vi;vn;vu;wf;ws;ye;yt;za;zm;zw;".indexOf(";"+r+";")){t=n[n.length-i-1]+"."+t;break}}return t}():a;document.cookie=e+"="+n+(t?";expires="+t:"")+";path=/"+(r?";domain=."+r:"")}function M(n,t,i){if(e()){var r=PluginDetect.getVersion(i);r?n[t]=r:PluginDetect.isMinVersion(i,0)>=-.25&&(n[t]="unk")}else n[t]="err"}function T(a){let T,O,_;function N(){C.sessionId_||(t?C.sessionId_=s(t.toString())?.toString():C.userUuid_&&(C.sessionId_=s(C.userUuid_)?.toString()))}performance&&(T=performance.now());var C={random_:Math.ceil(1e9*Math.random()),version_:115,beaconKey_:o};r&&(C.userId_=g(r)),N(),i&&(C.partnerUserId_=g(i)),"highPriority"!==a?.priority?"normalPriority"!==a?.priority?b&&(C.priority_="high"):delete C.priority_:C.priority_="high";var A={},E=[function(){var e,n,t=function(e){var n=document.cookie,t=e+"=",i=n.indexOf("; "+t);if(-1==i){if(0!==(i=n.indexOf(t)))return}else i+=2;var r=n.indexOf(";",i);return-1==r&&(r=n.length),p(n.substring(i+t.length,r))}(h);(!t||t.length<15||"undefined"===t||"null"===t)&&(t=function(e,n){var t=f&&(f.crypto||f.msCrypto)||{};if("function"==typeof t.getRandomValues){function i(e){for(var n="",i=["8","9","a","b"],r=new Uint8Array(1),o=0;o<e.length;o+=1){var a=e.charAt(o);"x"===a?(t.getRandomValues(r),n+=(r[0]%16).toString(16)):"y"===a&&(n+=Math.floor(Math.random()*i.length))}return n}return i("xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx")}for(n=e="";e++<36;n+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return n}(),e=t,(n=new Date).setFullYear(n.getFullYear()+y),S(h,g(e),n.toGMTString())),C.userUuid_=t},function(){var e=function(){var e="";try{e=f.top.document.referrer}catch(n){if(f.parent)try{e=f.parent.document.referrer}catch(n){e=""}}return""===e&&(e=l.referrer),e}();""!==e&&(C.referrer_=x(e,w))},function(){l&&(C.title_=x(l.title,100),C.characterSet_=g(l.characterSet||l.charset))},function(){if(f){C.url_=x(f.location.href,w),C.hostName_=x(f.location.hostname,w);try{C.sessionStorage_=!!f.sessionStorage}catch(e){C.sessionStorage_=!0}try{C.localStorage_=!!f.localStorage}catch(e){C.localStorage_=!0}try{C.indexedDB_=!!f.indexedDB}catch(e){C.indexedDB_=!0}C.openDB_=1==f.openDatabase}},function(){d&&(C.screenHeight_=d.height,C.screenWidth_=d.width,C.colorDepth_=d.colorDepth)},function(){c&&(C.platform_=g(c.platform),C.language_=g(c.language||c.browserLanguage),C.userAgent_=x(c.userAgent,w),C.cookieEnabled_=c.cookieEnabled,C.doNotTrack_=c.doNotTrack,C.maxTouchPoints_=c.maxTouchPoints,C.online_=c.onLine,C.product_=g(c.product),C.productSub_=g(c.productSub),C.vendor_=g(c.vendor),C.hardwareConcurrency_=c.hardwareConcurrency,C.javaEnabled_=c.javaEnabled(),C.cpuClass_=g(c.cpuClass))},function(){var e=new Date;C.timezoneOffset_=e.getTimezoneOffset(),e.setDate(1),e.setMonth(6);var n=e.getTimezoneOffset();e.setMonth(12);var t=e.getTimezoneOffset();C.dstOffset_=Math.abs(Math.abs(t)-Math.abs(n))},N,function(){for(var e=c.mimeTypes,n=e?e.length:0,t="",i=0;i<n;i++)t+=e[i].type;C.numMimeTypes_=n,C.mimeTypesHash_=s(t)?.toString()},function(){for(var e=c.plugins,n=e?e.length:0,t="",i=0;i<n;i++){var r=e[i];t+=r.name+r.description+r.filename+r.length}C.numPlugins_=n,C.pluginsHash_=s(t)?.toString()},function(){e()&&PluginDetect.getVersion(".")},function(){M(A,"sv","shockwave")},function(){M(A,"q","quicktime")},function(){M(A,"w","windowsmediaplayer")},function(){M(A,"sl","silverlight")},function(){(void 0===P||P<8)&&M(A,"vl","vlc")},function(){M(A,"pd","adobereader")},function(){M(A,"rp","realplayer")},function(){if(c&&c.languages){try{if(navigator.languages[0].substr(0,2)!==navigator.language.substr(0,2))return void(C.tamperedLanguage_=!0)}catch(e){return void(C.tamperedLanguage_=!0)}if(!C.tamperedLanguage_)return void(C.tamperedLanguage_=!1)}},function(){d&&(C.tamperedResolution_=d.width<d.availWidth||d.height<d.availHeight)},function(){if(c){var e,n=c.userAgent.toLowerCase(),t=c.oscpu,i=c.platform.toLowerCase();if(e=n.indexOf("windows phone")>=0?"Windows Phone":n.indexOf("win")>=0?"Windows":n.indexOf("android")>=0?"Android":n.indexOf("linux")>=0?"Linux":n.indexOf("iphone")>=0||n.indexOf("ipad")>=0?"iOS":n.indexOf("mac")>=0?"Mac":"Other",("ontouchstart"in window||c.maxTouchPoints>0||c.msMaxTouchPoints>0)&&"Windows Phone"!==e&&"Android"!==e&&"iOS"!==e&&"Other"!==e)return void(C.tamperedOS_=!0);if(void 0!==t){if((t=t.toLowerCase()).indexOf("win")>=0&&"Windows"!==e&&"Windows Phone"!==e)return void(C.tamperedOS_=!0);if(t.indexOf("linux")>=0&&"Linux"!==e&&"Android"!==e)return void(C.tamperedOS_=!0);if(t.indexOf("mac")>=0&&"Mac"!==e&&"iOS"!==e)return void(C.tamperedOS_=!0);if((-1===t.indexOf("win")&&-1===t.indexOf("linux")&&-1===t.indexOf("mac"))!=("Other"===e))return void(C.tamperedOS_=!0)}if(i.indexOf("win")>=0&&"Windows"!==e&&"Windows Phone"!==e)return void(C.tamperedOS_=!0);if((i.indexOf("linux")>=0||i.indexOf("android")>=0||i.indexOf("pike")>=0)&&"Linux"!==e&&"Android"!==e)return void(C.tamperedOS_=!0);if((i.indexOf("mac")>=0||i.indexOf("ipad")>=0||i.indexOf("ipod")>=0||i.indexOf("iphone")>=0)&&"Mac"!==e&&"iOS"!==e)return void(C.tamperedOS_=!0);if((-1===i.indexOf("win")&&-1===i.indexOf("linux")&&-1===i.indexOf("mac"))!=("Other"===e))return void(C.tamperedOS_=!0);if(void 0===c.plugins&&"Windows"!==e&&"Windows Phone"!==e)return void(C.tamperedOS_=!0);C.tamperedOS_=!1}},function(){var e,n=navigator.userAgent.toLowerCase(),t=navigator.productSub;if("Chrome"!==(e=n.indexOf("firefox")>=0?"Firefox":n.indexOf("opera")>=0||n.indexOf("opr")>=0?"Opera":n.indexOf("chrome")>=0?"Chrome":n.indexOf("safari")>=0?"Safari":n.indexOf("trident")>=0?"Internet Explorer":"Other")&&"Safari"!==e&&"Opera"!==e||"20030107"===t){var i=eval.toString().length;if(37!==i||"Safari"===e||"Firefox"===e||"Other"===e)if(39!==i||"Internet Explorer"===e||"Other"===e)if(33!==i||"Chrome"===e||"Opera"===e||"Other"===e){var r;try{throw"a"}catch(e){try{e.toSource(),r=!0}catch(e){r=!1}}C.tamperedBrowser_=!(!r||"Firefox"===e||"Other"===e)}else C.tamperedBrowser_=!0;else C.tamperedBrowser_=!0;else C.tamperedBrowser_=!0}else C.tamperedBrowser_=!0},function(){var e=document.createElement("div");e.innerHTML="&nbsp;",e.className="adsbox";var n=!1;try{document.body.appendChild(e),n=0===document.getElementsByClassName("adsbox")[0].offsetHeight,document.body.removeChild(e)}catch(e){n=!1}C.adBlock_=n},function(){var e=[],n=document.createElement("canvas");if(!(!n.getContext||!n.getContext("2d"))){n.width=2e3,n.height=200,n.style.display="inline";var t=n.getContext("2d");t.rect(0,0,10,10),t.rect(2,2,6,6),e.push("canvas winding:"+(!1===t.isPointInPath(5,5,"evenodd")?"yes":"no")),t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(125,1,62,20),t.fillStyle="#069",t.font="11pt Arial",t.fillText("Cwm fjordbank glyphs vext quiz, 😃",2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText("Cwm fjordbank glyphs vext quiz, 😃",4,45),t.globalCompositeOperation="multiply",t.fillStyle="rgb(255,0,255)",t.beginPath(),t.arc(50,50,50,0,2*Math.PI,!0),t.closePath(),t.fill(),t.fillStyle="rgb(0,255,255)",t.beginPath(),t.arc(100,50,50,0,2*Math.PI,!0),t.closePath(),t.fill(),t.fillStyle="rgb(255,255,0)",t.beginPath(),t.arc(75,100,50,0,2*Math.PI,!0),t.closePath(),t.fill(),t.fillStyle="rgb(255,0,255)",t.arc(75,75,75,0,2*Math.PI,!0),t.arc(75,75,25,0,2*Math.PI,!0),t.fill("evenodd"),n.toDataURL&&e.push("canvas fp:"+n.toDataURL()),C.canvasFingerprint_=s(e.join("~"))?.toString()}},function(){(async function(){if(R)return;R=!0;performance&&(O=performance.now());let e={};try{e=await n()}catch(e){}performance&&(_=performance.now());C.time_=(new Date).getTime()-u;var t=D((r=C,C={bk:r.beaconKey_,tm:r.time_,r:r.random_,v:r.version_,cs:r.characterSet_,h:r.hostName_,l:r.language_,P:r.partnerUserId_,S:r.sessionId_,ui:r.userId_,uu:r.userUuid_,t:r.title_,u:r.url_,rf:r.referrer_,ua:r.userAgent_,nm:r.numMimeTypes_,mh:r.mimeTypesHash_,np:r.numPlugins_,ph:r.pluginsHash_,sh:r.screenHeight_,sw:r.screenWidth_,cd:r.colorDepth_,p:r.platform_,to:r.timezoneOffset_,d:r.dstOffset_,ce:r.cookieEnabled_,dt:r.doNotTrack_,tp:r.maxTouchPoints_,ol:r.online_,pr:r.product_,ps:r.productSub_,vd:r.vendor_,hc:r.hardwareConcurrency_,je:r.javaEnabled_,ss:r.sessionStorage_,ls:r.localStorage_,in:r.indexedDB_,db:r.openDB_,cp:r.cpuClass_,tl:r.tamperedLanguage_,tr:r.tamperedResolution_,ts:r.tamperedOS_,tb:r.tamperedBrowser_,ab:r.adBlock_,cf:r.canvasFingerprint_,pri:r.priority_})),i=D(A);var r;const o=D(e);i&&(t+="&"+i);o&&(t+="&"+o);if(T&&O&&_)try{const e=Math.round(_-O),n=Math.round(_-T);n&&(t+=`&pf=${n}`),e&&(t+=`&pfe=${e}`)}catch(e){}t+="&z=z";var a=l.getElementById("plugindetect");a&&a.parentNode.removeChild(a);!function(e){var n=new Image(1,1);n.onload=function(){n.onload=null,n.onerror=null},n.onerror=function(){n.onload=null,n.onerror=null};var t=Math.ceil(1e6*Math.random()).toString();n.src="https://"+m+"/images/"+t+".gif?"+e}(t)})().then()}],R=!1;!function(e,n){var t=0;setTimeout((function i(){var r=(new Date).getTime(),o=!1;do{if(!(t<e.length)){o=!0;break}window.SIFT_DEBUG&&window.performance&&window.performance.mark(e[t].toString().substring(0,50)),e[t](),t+=1,window.SIFT_DEBUG&&window.performance&&e[t]&&(window.performance.mark(e[t].toString().substring(0,50)),window.performance.measure(e[t-1].toString().substring(0,50),e[t-1].toString().substring(0,50),e[t].toString().substring(0,50)))}while((new Date).getTime()-r<v);o||setTimeout(i,n)}),n)}(E,0)}var O={_trackPageview:function(e={}){T(e)},_setAccount:function(e){o=e},_setUserId:function(e){r=e},_setPartnerUserId:function(e){i=e},_setSessionId:function(e){t=e},_setTrackerUrl:function(e){m=e},_setCookieDomain:function(e){a=e},_setPriority:function(e){"highPriority"===e&&(b=!0),"normalPriority"===e&&(b=!1)}};function _(){var e,n,t,i;for(e=0;e<arguments.length;e+=1)n=(t=arguments[e]).shift(),"string"==typeof(i=n)||i instanceof String?O[n]&&O[n].apply(O,t):n&&n.apply(O,t)}for(var N=0;N<_sift.length;N++)_(_sift[N]);window._sift={push:_}}()}();
