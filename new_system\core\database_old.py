#!/usr/bin/env python3
"""
Database Manager - MySQL database operations
Compatible with existing instaadmin database structure
"""

import os
import logging
import mysql.connector
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manager for MySQL database operations"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize database manager with configuration"""
        self.config = config or {
            'host': 'localhost',
            'user': 'root',
            'password': '8915841@@',  # From original system
            'database': 'instaadmin',
            'port': 3306,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        
        # Test connection
        self._test_connection()
        
        logger.info("Database Manager initialized")
    
    def _test_connection(self) -> bool:
        """Test database connection"""
        try:
            conn = self.get_connection()
            if not conn:
                raise Exception("Failed to get database connection")
            
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            conn.close()
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get a database connection"""
        try:
            conn = mysql.connector.connect(**self.config)
            return conn
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            return None
    
    def get_connection_context(self):
        """Context manager for database connections"""
        conn = None
        try:
            conn = mysql.connector.connect(**self.config)
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn and conn.is_connected():
                conn.close()
    
    def execute_query(self, query: str, params: Tuple = None, fetch: bool = False) -> Optional[List[Any]]:
        """Execute SQL query with optional parameters"""
        try:
            with self.get_connection_context() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or ())
                
                if fetch:
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return None
                    
        except Exception as e:
            logger.error(f"Query execution error: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
    
    # Account Management
    def get_all_accounts(self) -> List[Dict[str, Any]]:
        """Get all Instagram accounts from database"""
        query = """
        SELECT id, username, password, secretkey, executive, 
               timestamp, isDelete
        FROM users 
        WHERE isDelete = 0
        ORDER BY id
        """
        
        try:
            results = self.execute_query(query, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching accounts: {e}")
            return []
    
    def get_account_by_id(self, account_id: int) -> Optional[Dict[str, Any]]:
        """Get account by ID"""
        query = "SELECT * FROM users WHERE id = %s"
        
        try:
            results = self.execute_query(query, (account_id,), fetch=True)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error fetching account {account_id}: {e}")
            return None
    
    def update_account_status(self, account_id: int, status: str) -> bool:
        """Update account status"""
        # Note: users table doesn't have status field, using isDelete for now
        query = "UPDATE users SET timestamp = %s WHERE id = %s"
        
        try:
            self.execute_query(query, (datetime.now(), account_id))
            return True
        except Exception as e:
            logger.error(f"Error updating account status: {e}")
            return False
    
    # Message Management
    def get_message_templates(self) -> List[Dict[str, Any]]:
        """Get message templates"""
        query = "SELECT * FROM messages ORDER BY id"
        
        try:
            results = self.execute_query(query, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching message templates: {e}")
            return []
    
    def get_message_template_by_id(self, template_id: int) -> Optional[Dict[str, Any]]:
        """Get message template by ID"""
        query = "SELECT * FROM messages WHERE id = %s"
        
        try:
            results = self.execute_query(query, (template_id,), fetch=True)
            return results[0] if results else None
        except Exception as e:
            logger.error(f"Error fetching message template: {e}")
            return None
    
    def save_message_log(self, account_id: int, recipient_username: str, 
                        message_content: str, status: str, 
                        response_data: str = None) -> bool:
        """Save message sending log"""
        query = """
        INSERT INTO message_logs 
        (account_id, recipient_username, message_content, status, response_data, created_at)
        VALUES (%s, %s, %s, %s, %s, %s)
        """
        
        try:
            self.execute_query(query, (
                account_id, recipient_username, message_content, 
                status, response_data, datetime.now()
            ))
            return True
        except Exception as e:
            logger.error(f"Error saving message log: {e}")
            return False
    
    def get_message_logs(self, account_id: int = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get message logs"""
        if account_id:
            query = """
            SELECT * FROM message_logs 
            WHERE account_id = %s 
            ORDER BY created_at DESC 
            LIMIT %s
            """
            params = (account_id, limit)
        else:
            query = """
            SELECT * FROM message_logs 
            ORDER BY created_at DESC 
            LIMIT %s
            """
            params = (limit,)
        
        try:
            results = self.execute_query(query, params, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching message logs: {e}")
            return []
    
    # User Data Management
    def save_user_data(self, account_id: int, user_data: Dict[str, Any]) -> bool:
        """Save scraped user data"""
        query = """
        INSERT INTO scraped_users 
        (account_id, user_id, username, full_name, biography, 
         follower_count, following_count, media_count, is_private, 
         is_verified, profile_pic_url, scraped_at)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
        full_name = VALUES(full_name),
        biography = VALUES(biography),
        follower_count = VALUES(follower_count),
        following_count = VALUES(following_count),
        media_count = VALUES(media_count),
        is_private = VALUES(is_private),
        is_verified = VALUES(is_verified),
        profile_pic_url = VALUES(profile_pic_url),
        scraped_at = VALUES(scraped_at)
        """
        
        try:
            self.execute_query(query, (
                account_id,
                user_data.get('user_id'),
                user_data.get('username'),
                user_data.get('full_name'),
                user_data.get('biography'),
                user_data.get('follower_count', 0),
                user_data.get('following_count', 0),
                user_data.get('media_count', 0),
                user_data.get('is_private', False),
                user_data.get('is_verified', False),
                user_data.get('profile_pic_url'),
                datetime.now()
            ))
            return True
        except Exception as e:
            logger.error(f"Error saving user data: {e}")
            return False
    
    def get_scraped_users(self, account_id: int = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get scraped user data"""
        if account_id:
            query = """
            SELECT * FROM scraped_users 
            WHERE account_id = %s 
            ORDER BY scraped_at DESC 
            LIMIT %s
            """
            params = (account_id, limit)
        else:
            query = """
            SELECT * FROM scraped_users 
            ORDER BY scraped_at DESC 
            LIMIT %s
            """
            params = (limit,)
        
        try:
            results = self.execute_query(query, params, fetch=True)
            return results or []
        except Exception as e:
            logger.error(f"Error fetching scraped users: {e}")
            return []
    
    # Statistics
    def get_account_statistics(self, account_id: int) -> Dict[str, Any]:
        """Get account statistics"""
        try:
            # Message counts
            message_query = """
            SELECT 
                COUNT(*) as total_messages,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_messages,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_messages,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_messages
            FROM message_logs 
            WHERE account_id = %s
            """
            
            message_stats = self.execute_query(message_query, (account_id,), fetch=True)
            message_data = message_stats[0] if message_stats else {}
            
            # Scraped users count
            scraped_query = """
            SELECT 
                COUNT(*) as total_scraped,
                COUNT(CASE WHEN DATE(scraped_at) = CURDATE() THEN 1 END) as today_scraped
            FROM scraped_users 
            WHERE account_id = %s
            """
            
            scraped_stats = self.execute_query(scraped_query, (account_id,), fetch=True)
            scraped_data = scraped_stats[0] if scraped_stats else {}
            
            return {
                'account_id': account_id,
                'total_messages': message_data.get('total_messages', 0),
                'successful_messages': message_data.get('successful_messages', 0),
                'failed_messages': message_data.get('failed_messages', 0),
                'today_messages': message_data.get('today_messages', 0),
                'total_scraped': scraped_data.get('total_scraped', 0),
                'today_scraped': scraped_data.get('today_scraped', 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting account statistics: {e}")
            return {'account_id': account_id}
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """Get overall system statistics"""
        try:
            # Account counts
            account_query = """
            SELECT 
                COUNT(*) as total_accounts,
                COUNT(CASE WHEN isDelete = 0 THEN 1 END) as active_accounts
            FROM users
            """
            
            account_stats = self.execute_query(account_query, fetch=True)
            account_data = account_stats[0] if account_stats else {}
            
            # Message counts
            message_query = """
            SELECT 
                COUNT(*) as total_messages,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_messages,
                SUM(CASE WHEN DATE(created_at) = CURDATE() THEN 1 ELSE 0 END) as today_messages
            FROM message_logs
            """
            
            message_stats = self.execute_query(message_query, fetch=True)
            message_data = message_stats[0] if message_stats else {}
            
            # Scraped users
            scraped_query = """
            SELECT 
                COUNT(*) as total_scraped,
                COUNT(CASE WHEN DATE(scraped_at) = CURDATE() THEN 1 END) as today_scraped
            FROM scraped_users
            """
            
            scraped_stats = self.execute_query(scraped_query, fetch=True)
            scraped_data = scraped_stats[0] if scraped_stats else {}
            
            return {
                'total_accounts': account_data.get('total_accounts', 0),
                'active_accounts': account_data.get('active_accounts', 0),
                'total_messages': message_data.get('total_messages', 0),
                'successful_messages': message_data.get('successful_messages', 0),
                'today_messages': message_data.get('today_messages', 0),
                'total_scraped': scraped_data.get('total_scraped', 0),
                'today_scraped': scraped_data.get('today_scraped', 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting system statistics: {e}")
            return {}
    
    # Database maintenance
    def create_missing_tables(self) -> bool:
        """Create missing tables for new system functionality"""
        tables = {
            'message_logs': """
            CREATE TABLE IF NOT EXISTS message_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT NOT NULL,
                recipient_username VARCHAR(255) NOT NULL,
                message_content TEXT,
                status ENUM('success', 'failed', 'pending') DEFAULT 'pending',
                response_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_account_id (account_id),
                INDEX idx_created_at (created_at),
                FOREIGN KEY (account_id) REFERENCES users(id) ON DELETE CASCADE
            )
            """,
            
            'scraped_users': """
            CREATE TABLE IF NOT EXISTS scraped_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                account_id INT NOT NULL,
                user_id VARCHAR(50) NOT NULL,
                username VARCHAR(255) NOT NULL,
                full_name VARCHAR(255),
                biography TEXT,
                follower_count INT DEFAULT 0,
                following_count INT DEFAULT 0,
                media_count INT DEFAULT 0,
                is_private BOOLEAN DEFAULT FALSE,
                is_verified BOOLEAN DEFAULT FALSE,
                profile_pic_url TEXT,
                scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_user_account (account_id, user_id),
                INDEX idx_username (username),
                INDEX idx_scraped_at (scraped_at),
                FOREIGN KEY (account_id) REFERENCES users(id) ON DELETE CASCADE
            )
            """
        }
        
        try:
            for table_name, create_sql in tables.items():
                self.execute_query(create_sql)
                logger.info(f"Table {table_name} created/verified")
            return True
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def get_all_messages(self) -> List[Dict[str, Any]]:
        """Get all message templates"""
        try:
            conn = self.get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT * FROM messages ORDER BY id")
            messages = cursor.fetchall()
            cursor.close()
            conn.close()
            return messages
            cursor.close()
            conn.close()
                
        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            return []
    
    def get_message_by_id(self, message_id: int) -> Optional[Dict[str, Any]]:
        """Get message template by ID"""
        try:
            conn = self.get_connection()
            if not conn:
                return None
            
            cursor = conn.cursor(dictionary=True)
            cursor.execute("SELECT * FROM messages WHERE id = %s", (message_id,))
            message = cursor.fetchone()
            cursor.close()
            conn.close()
            return message
                
        except Exception as e:
            logger.error(f"Error getting message {message_id}: {e}")
            return None
    
    def get_message_images(self) -> List[Dict[str, Any]]:
        """Get all message images"""
        try:
            conn = self.get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor(dictionary=True)
                cursor = conn.cursor(dictionary=True)
                cursor.execute("SELECT * FROM message_images ORDER BY id")
                images = cursor.fetchall()
                return images
                
        except Exception as e:
            logger.error(f"Error getting message images: {e}")
            return []
    
    def get_message_image_by_id(self, image_id: int) -> Optional[Dict[str, Any]]:
        """Get message image by ID"""
        try:
            conn = self.get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor(dictionary=True)
                cursor = conn.cursor(dictionary=True)
                cursor.execute("SELECT * FROM message_images WHERE id = %s", (image_id,))
                image = cursor.fetchone()
                return image
                
        except Exception as e:
            logger.error(f"Error getting message image {image_id}: {e}")
            return None
    
    def log_media_message(self, account_id: int, recipient_username: str, media_type: str, 
                         media_path: str, caption: str, status: str) -> bool:
        """Log media message sending attempt"""
        try:
            conn = self.get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor(dictionary=True)
                cursor = conn.cursor()
                query = """
                INSERT INTO message_logs (account_id, recipient_username, message_content, status, response_data)
                VALUES (%s, %s, %s, %s, %s)
                """
                content = f"{media_type}: {media_path} | Caption: {caption}"
                cursor.execute(query, (account_id, recipient_username, content, status, media_type))
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error logging media message: {e}")
            return False
    
    def log_thread_deletion(self, account_id: int, thread_id: str, status: str, 
                           thread_title: str = "", username: str = "") -> bool:
        """Log thread deletion attempt"""
        try:
            conn = self.get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor(dictionary=True)
                cursor = conn.cursor()
                query = """
                INSERT INTO message_logs (account_id, recipient_username, message_content, status, response_data)
                VALUES (%s, %s, %s, %s, %s)
                """
                content = f"Thread deletion: {thread_title} (ID: {thread_id})"
                cursor.execute(query, (account_id, username, content, status, "thread_deletion"))
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"Error logging thread deletion: {e}")
            return False
    
    def get_instagram_user_threads(self, account_id: int = None) -> List[Dict[str, Any]]:
        """Get threads with 'Instagram User' title from captured data"""
        try:
            conn = self.get_connection()
            if not conn:
                return []
            
            cursor = conn.cursor(dictionary=True)
                cursor = conn.cursor(dictionary=True)
                
                # Query for Instagram User entries
                if account_id:
                    # If specific account, look for account-specific data
                    query = """
                    SELECT DISTINCT id1 as thread_id, id3 as user_id, username, title, full_name
                    FROM (
                        SELECT id1, id3, username, title, full_name FROM authentication_scanning
                        WHERE title LIKE '%Instagram User%'
                        UNION
                        SELECT id1, id3, username, title, full_name FROM users 
                        WHERE title LIKE '%Instagram User%' AND isDelete = 0
                    ) as combined_data
                    ORDER BY thread_id
                    """
                    cursor.execute(query)
                else:
                    # Generic query for all Instagram User entries
                    query = """
                    SELECT DISTINCT id1 as thread_id, id3 as user_id, username, title, full_name
                    FROM (
                        SELECT id1, id3, username, title, full_name FROM authentication_scanning
                        WHERE title LIKE '%Instagram User%'
                        UNION  
                        SELECT id1, id3, username, title, full_name FROM users
                        WHERE title LIKE '%Instagram User%' AND isDelete = 0
                    ) as combined_data
                    ORDER BY thread_id
                    """
                    cursor.execute(query)
                
                results = cursor.fetchall()
                logger.info(f"Found {len(results)} Instagram User threads")
                return results
                
        except Exception as e:
            logger.error(f"Error getting Instagram User threads: {e}")
            return []


# Global database manager instance
db_manager = DatabaseManager()

# Convenience functions
def get_db() -> DatabaseManager:
    """Get global database manager instance"""
    return db_manager

def init_database() -> DatabaseManager:
    """Initialize database manager and create missing tables"""
    db = get_db()
    db.create_missing_tables()
    return db
