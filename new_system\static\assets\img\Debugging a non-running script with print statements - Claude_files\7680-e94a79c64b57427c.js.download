"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7680],{18187:function(e,t,n){n.d(t,{HN:function(){return u},SE:function(){return s},Tx:function(){return l}});var r=n(5362),i=n(27218),a=n(13262),o=n(77930),c=n(32559);let u=(e,t)=>{let{account:n,activeOrganization:u}=(0,i.t)(),s=t===(null==n?void 0:n.uuid),l=null==u?void 0:u.uuid,d=(0,o.useQueryClient)(),f=(0,c.Gm)();return(0,r.uC)(()=>"/api/organizations/".concat(l,"/projects/").concat(e,"/accounts/").concat(t),"PUT",{onSuccess:()=>{d.invalidateQueries({queryKey:[a.l3,{projectUuid:e}]}),s&&(f(e),d.invalidateQueries({queryKey:[a.VH,{orgUUID:l}]}),d.invalidateQueries({queryKey:[a.tv,{orgUUID:l}]}))}})},s=function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1],{activeOrganization:n}=(0,i.t)(),o=null==n?void 0:n.uuid;return(0,r.WE)("/api/organizations/".concat(o,"/projects/").concat(e,"/accounts"),{queryKey:[a.l3,{projectUuid:e}],enabled:!!(t&&n&&e),staleTime:0})},l=e=>{let{activeOrganization:t}=(0,i.t)(),n=null==t?void 0:t.uuid,c=(0,o.useQueryClient)();return(0,r.uC)(t=>"/api/organizations/".concat(n,"/projects/").concat(e,"/accounts/").concat(t.accountUuid),"PUT",{onSuccess:()=>{c.invalidateQueries({queryKey:[a.l3,{projectUuid:e}]})},transformVariables:e=>({role:e.role})})}},47082:function(e,t,n){n.d(t,{M:function(){return d},Y:function(){return f}});var r=n(27573),i=n(35919),a=n.n(i),o=n(7653);function c(e,t){switch(t.type){case"DRAW_SVG":return{...e,selectedItem:{type:"svg",data:t.svg}};case"OPEN_COMPASS_STATUS":return{...e,selectedItem:{type:"compass",data:t.data}};case"OPEN_COMPASS_RESULT":return{...e,selectedItem:{type:"compass_result",data:t.data}};case"SELECT_ATTACHMENT":return{...e,selectedItem:{type:"attachment",attachment:t.attachment}};case"SELECT_ARTIFACT":return{...e,selectedItem:{type:"artifact",id:t.id,versionUUID:t.versionUUID}};case"SELECT_DOC":return{...e,selectedItem:{type:"doc",id:t.id}};case"SELECT_SYNCED_DIRECTORY":return{...e,selectedItem:{type:"syncedDirectory",id:t.id}};case"CLEAR_SELECTED":return{...e,selectedItem:null,activeMessageUUID:void 0};case"SET_ACTIVE_MESSAGE_UUID":return{...e,activeMessageUUID:t.uuid};case"UPDATE_ARTIFACTS":return{...e,artifacts:t.artifacts};case"UPDATE_ATTACHMENTS":return{...e,attachments:t.attachments};case"UPDATE_FILES":return{...e,files:t.files};case"UPDATE_SYNC_SOURCES":return{...e,syncSources:t.syncSources};case"UPDATE_DOC_UPDATES":return{...e,docUpdates:t.updates};case"UPDATE_FILE_EDITS":return{...e,fileEdits:t.edits};default:return e}}let u={selectedItem:null,artifacts:{},attachments:[],files:[],syncSources:[],docUpdates:[],fileEdits:new Map,activeMessageUUID:void 0,conversationUUID:void 0},s={chatResourceState:u,dispatchChatResource:a()},l=(0,o.createContext)(s);function d(e){let{children:t,conversationUUID:n}=e,[i,a]=(0,o.useReducer)(c,{...u,conversationUUID:n});return(0,r.jsx)(l.Provider,{value:{chatResourceState:i,dispatchChatResource:a},children:t})}function f(){return(0,o.useContext)(l)}},7680:function(e,t,n){n.d(t,{r:function(){return g},s:function(){return y}});var r=n(27573),i=n(3053),a=n(80762),o=n(28346),c=n(24558),u=n.n(c),s=n(35919),l=n.n(s),d=n(7653),f=n(47082),p=n(40030);let m=(0,d.createContext)(null),h={getInstance:()=>void 0,setBaseContent:l(),flagForDeletion:l(),getCurrentContent:()=>void 0,hasUnsavedChanges:()=>!1,registerEditor:l(),unregisterEditor:l(),setMergeState:()=>l(),subscribeToMergeStateChanges:()=>l(),acceptAllChanges:l(),rejectAllChanges:l(),deactivateChangesMode:l(),activeEditors:[],changesMode:!1},w=(e,t)=>{var n,r;let i=null===(r=(0,o.Sk)(e.state))||void 0===r?void 0:null===(n=r.chunks)||void 0===n?void 0:n[0];return!!i&&("accept"===t?o.bN:o.A)(e,i.fromB)};function g(e){let{children:t}=e,{track:n}=(0,i.z$)(),{openedFiles:o}=(0,p.b)(),{dispatchChatResource:c}=(0,f.Y)(),s=(0,d.useRef)(new Map),l=(0,d.useRef)(new Set),h=(0,d.useCallback)((e,t)=>{s.current.set(e,{view:t})},[]),g=(0,d.useCallback)(e=>{s.current.delete(e)},[]),y=(0,d.useCallback)((e,t)=>{let n=s.current.get(e);if(!n){a.v("Attempted to update unknown editor: ".concat(e));return}s.current.set(e,{...n,...t})},[]),v=(0,d.useCallback)((e,t)=>y(e,{baseContent:t}),[y]),E=(0,d.useCallback)(e=>y(e,{filePendingDeletion:!0}),[y]),C=(0,d.useCallback)((e,t)=>{y(e,{mergeState:t}),l.current.forEach(n=>n(e,t))},[y]),b=(0,d.useCallback)(e=>(l.current.add(e),()=>{l.current.delete(e)}),[]),U=(0,d.useCallback)(e=>s.current.get(e),[]),[x,S]=(0,d.useState)([]),[I,_]=(0,d.useState)(!1),T=(0,d.useCallback)(()=>{c({type:"UPDATE_FILE_EDITS",edits:new Map}),_(!1)},[c]);(0,d.useEffect)(()=>{x.length&&_(!0)},[x]),(0,d.useEffect)(()=>(S(o.filter(e=>{var t;let n=U(e);return!!n&&(null===(t=n.mergeState)||void 0===t?void 0:t.isActive)&&!!n.view})),b((e,t)=>{S(n=>{var r;let i=n.includes(e),a=t.isActive&&!!(null===(r=U(e))||void 0===r?void 0:r.view);return i===a?n:a?[...n,e]:n.filter(t=>t!==e)})})),[o,b,U,I]);let D=(0,d.useCallback)(()=>{x.forEach(e=>{var t;let n=null===(t=U(e))||void 0===t?void 0:t.view;if(n)for(;w(n,"accept"););}),n({event_key:"harmony.suggestion.accepted"})},[x,n,U]),j=(0,d.useCallback)(()=>{x.forEach(e=>{var t;let r=null===(t=U(e))||void 0===t?void 0:t.view;if(r){for(;w(r,"reject"););C(e,{isActive:!1}),n({event_key:"harmony.suggestion.rejected"})}})},[x,n,U,C]),A=(0,d.useCallback)(e=>{var t,n;return null===(n=U(e))||void 0===n?void 0:null===(t=n.view)||void 0===t?void 0:t.state.doc.toString()},[U]),P=(0,d.useCallback)(e=>{var t;let n=A(e);return u()(n)&&n!==(null===(t=U(e))||void 0===t?void 0:t.baseContent)},[A,U]),M=(0,d.useMemo)(()=>({getInstance:U,setBaseContent:v,flagForDeletion:E,getCurrentContent:A,hasUnsavedChanges:P,registerEditor:h,unregisterEditor:g,setMergeState:C,subscribeToMergeStateChanges:b,acceptAllChanges:D,rejectAllChanges:j,deactivateChangesMode:T,activeEditors:x,changesMode:I}),[U,v,E,A,P,h,g,C,b,D,j,T,x,I]);return(0,r.jsx)(m.Provider,{value:M,children:t})}function y(){return(0,d.useContext)(m)||h}},71844:function(e,t,n){n.d(t,{KA:function(){return s},Nc:function(){return u},YB:function(){return c},b8:function(){return a},h4:function(){return o}});var r=n(39160),i=n.n(r);function a(e,t){return i()(e,t)}function o(e){let t=[],n=0;for(let[r,i]of e)0!==r&&t.push({op:r,pos:n,text:i}),-1!==r&&(n+=i.length);return t}function c(e){if(!Array.isArray(e))throw Error("Input must be an array");e.forEach((e,t)=>{if(!("object"==typeof e&&null!==e&&"op"in e&&"number"==typeof e.op))throw Error("Invalid change object at index ".concat(t));if(1!==e.op&&-1!==e.op)throw Error("Invalid 'op' value at index ".concat(t));if("number"!=typeof e.pos||!Number.isInteger(e.pos))throw Error("Invalid 'pos' value at index ".concat(t));if("string"!=typeof e.text)throw Error("Invalid 'text' value at index ".concat(t))})}function u(e,t){return function(e,t){let n=e,r=0;for(let e of t){let t=e.pos+r;1===e.op?(n=n.slice(0,t)+e.text+n.slice(t),r+=e.text.length):-1===e.op&&(n=n.slice(0,t)+n.slice(t+e.text.length),r-=e.text.length)}return n}(e,t.map(e=>({op:-e.op,pos:e.pos,text:e.text})).reverse())}function s(e){for(let[t]of e)if(0!==t)return!1;return!0}},28096:function(e,t,n){function r(e){var t;return(null===(t=e.mergeState)||void 0===t?void 0:t.isActive)?e.mergeState.diffSource:e.view.state.doc.toString()}function i(e){return r(e)!==e.baseContent}n.d(t,{li:function(){return r},oI:function(){return i}}),n(71844)},44506:function(e,t,n){n.d(t,{G:function(){return r}});async function r(e){let{createHash:t}=await Promise.all([n.e(2044),n.e(1919)]).then(n.t.bind(n,76736,23));return t("sha256").update(e.trim()).digest("hex").slice(0,6)}},18701:function(e,t,n){n.d(t,{Y:function(){return u}});var r=n(27218),i=n(14448),a=n(15992),o=n(18187),c=n(32559);let u=e=>{var t;let{account:n}=(0,r.t)(),{value:u}=(0,i.F)("force_harmony"),{data:s,isLoading:l}=(0,c.Yc)(e),{data:d,isLoading:f}=(0,o.SE)(e||""),p=null!==(t=null==d?void 0:d.some(e=>e.account.uuid===(null==n?void 0:n.uuid)&&"user"===e.role))&&void 0!==t&&t,m="showDirectoryPicker"in window&&"FileSystemHandle"in window,h=(0,a.useConfig)("harmony").config;return{userHasHarmony:m&&((null==n?void 0:n.settings.preview_feature_uses_harmony)||u)&&(null==h?void 0:h.get("harmonize",!1)),projectHasHarmony:!l&&!f&&p&&!!(null==s?void 0:s.is_harmony_project),config:h}}},40030:function(e,t,n){n.d(t,{C:function(){return C},b:function(){return b}});var r=n(27573),i=n(7653),a=n(35919),o=n.n(a);let c=(0,i.createContext)(null),u={openedFiles:[],projectDocs:[],files:[],setFiles:o(),projectUuid:"",syncedDirectories:[],setSyncedDirectories:o()};function s(e){let{children:t,onLoad:n}=e;return(0,i.useEffect)(n,[n]),(0,r.jsx)(c.Provider,{value:u,children:t})}var l=n(80762),d=n(87590),f=n.n(d),p=n(77879),m=n(95899),h=n(85017),w=n(10690),g=n(52902);let y=[];function v(e){let{children:t,onLoad:n,projectUuid:a}=e,{isLoading:o,unfilteredData:u=y}=(0,m.Kf)(a),[s,d]=(0,i.useState)([]),[v,E]=(0,i.useState)([]),C=(0,i.useMemo)(()=>[...(function(e,t){let n=new Map,r=t.filter(g.IQ).map(e=>e.uri);if(!r.length)return n;for(let t of r){let r=new Set;for(let n of e){if(!(0,g.Wx)(n.file_name))continue;let{projectUuid:e,directoryUuid:i}=(0,g.Xp)(n.file_name),a=(0,g.Xp)(t);e===a.projectUuid&&i===a.directoryUuid&&r.add(n.file_name)}n.set(t,r)}return n})(u,s).values()].flatMap(e=>[...e]),[u,s]);(0,i.useEffect)(()=>{(0,h.Zz)(a).then(E).catch(e=>l.v("Harmony: Error initializing synced directories from IndexedDB",e))},[a]);let{mutateAsync:b}=(0,m.u3)(a),{mutateAsync:U}=(0,m.Zy)(a),{mutateAsync:x}=(0,m.ER)(a),S=(0,i.useCallback)(()=>{(0,w.tt)(a,o,s,u,b,U,x).then(e=>{e&&(f()(s,e.updatedFiles)||(d(e.updatedFiles),l.f("Harmony: Resynced ".concat(e.updatedFiles.filter(g.zE).length," files in ").concat(e.updatedFiles.filter(g.IQ).length," directories")),n()))}).catch(e=>l.v("Harmony: Error during resync:",e))},[a,o,s,u,b,U,x,n]),I=(0,i.useRef)(!1);(0,i.useEffect)(()=>{!a||o||I.current||(I.current=!0,S())},[S,a,o]),(0,p.Yz)(S,3e4);let _=(0,i.useMemo)(()=>({projectUuid:a,openedFiles:C,projectDocs:u,files:s,setFiles:d,syncedDirectories:v,setSyncedDirectories:E}),[a,C,u,s,v,E]);return(0,r.jsx)(c.Provider,{value:_,children:t})}var E=n(18701);function C(e){let{children:t,onLoad:n,projectUuid:i}=e,{userHasHarmony:a}=(0,E.Y)(i);return a?(0,r.jsx)(v,{onLoad:n,projectUuid:i,children:t}):(0,r.jsx)(s,{onLoad:n,children:t})}function b(){return(0,i.useContext)(c)||u}},85017:function(e,t,n){n.d(t,{Gz:function(){return p},U4:function(){return m},Zz:function(){return d},sN:function(){return f},y3:function(){return h}});var r=n(80762),i=n(51432),a=n(52902);let o="directories",c="projectUuid",u=null;async function s(){return u||new Promise((e,t)=>{let n=indexedDB.open("harmony",1);n.onerror=()=>t(n.error),n.onsuccess=()=>{(u=n.result).onerror=e=>{r.v("Database error:",e)},u.onclose=()=>{u=null},e(u)},n.onupgradeneeded=e=>{e.target.result.createObjectStore(o,{keyPath:"uuid"}).createIndex(c,"projectUuid",{unique:!1})}})}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"readonly";if(!u)throw Error("Database not initialized");return u.transaction(o,e)}async function d(e){await s();let t=l("readonly").objectStore(o).index(c);return new Promise((n,r)=>{let i=t.getAll(e);i.onerror=()=>r(i.error),i.onsuccess=()=>{n(i.result.map(e=>({...e,uri:(0,a.cT)(e)})))}})}async function f(e){await s();let t=l("readonly").objectStore(o);return new Promise((n,r)=>{let i=t.get(e);i.onerror=()=>r(i.error),i.onsuccess=()=>{if(i.result){let e=i.result;n({...e,uri:(0,a.cT)(e)})}else n(null)}})}async function p(e){await s();let t={...e,uuid:(0,i.H)()};return new Promise((e,n)=>{let i=l("readwrite").objectStore(o).add(t);i.onerror=()=>n(i.error),i.onsuccess=()=>{let n=(0,a.cT)(t);e({...t,uri:n}),r.f("Harmony: Synced directory added to IndexedDB",t)}})}async function m(e){return await s(),new Promise((t,n)=>{let r=l("readwrite").objectStore(o).put(e);r.onerror=()=>n(r.error),r.onsuccess=()=>t()})}async function h(e){return await s(),new Promise((t,n)=>{let r=l("readwrite").objectStore(o).delete(e);r.onerror=()=>n(r.error),r.onsuccess=()=>t()})}},10690:function(e,t,n){n.d(t,{Jl:function(){return m},wC:function(){return C},T7:function(){return x},bE:function(){return f},lz:function(){return U},xF:function(){return w},Zn:function(){return b},cM:function(){return E},tt:function(){return S},KT:function(){return p},ec:function(){return h}});var r=n(80762),i=n(87590),a=n.n(i),o=n(95899),c=n(71844),u=n(28096),s=n(44506),l=n(85017),d=n(52902);async function f(e){let t=await (0,l.Zz)(e);return await (0,d.C7)(t)}async function p(e,t,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=await Promise.all(t.filter(d.zE).map(async t=>({path:t.uri,content:await m(e,t.uri)})));return new Promise((e,t)=>{let c=new Worker(n.tu(new URL(n.p+n.u(231),n.b))),u=setTimeout(()=>{t(Error("Search timeout"))},3e4),s=()=>{c.terminate(),clearTimeout(u)};c.onerror=e=>{s(),t(e)};let l=[];c.onmessage=t=>{let{type:n,results:r}=t.data;l.push(...r),"complete"===n&&(s(),e(l))},c.postMessage({files:o,searchTerm:r,isRegex:i,caseSensitive:a})})}async function m(e,t){return(await (0,d.pJ)(e,t)).text}async function h(e,t,n,i,a,o){let s=await (0,l.Zz)(t),f=Array.from(new Set([...e])).map(async e=>{if((0,d.Wx)(e))try{let t=o(e),l=t?(0,u.li)(t):void 0,d=t?t.baseContent:void 0;if(void 0===d||void 0===l){let t=await m(s,e);if(!l){if(null===t){r.v(Error("File content not found for ".concat(e)));return}return await g(n,i,a,e,t,[])}return await g(n,i,a,e,l,(0,c.b8)(t||"",l))}let f=(0,c.b8)(d,l);return await g(n,i,a,e,l,f)}catch(t){r.v("Error processing file ".concat(e,":"),t)}});await Promise.all(f)}async function w(e,t,n,r,i){let a=await m(e,i);if(null===a)throw Error("File content not found for ".concat(i));if(new TextEncoder().encode(a).length>102400)throw Error("File ".concat(i," is too large and will degrade the experience. Pass a specific view_range when using the view tool for this file."));return await g(t,n,r,i,a)}async function g(e,t,n,r,i,a){let o=e.find(e=>e.file_name===r);if(o&&function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.match(/<content>([\s\S]*)<\/content>/),r=n&&n[1]?n[1].trim():e;if(t){let t=e.match(/<changes>(.*?)<\/changes>/);if(t&&t[1]){let e=JSON.parse(t[1]);(0,c.YB)(e),r=(0,c.Nc)(r,e)}}return r}(o.content)===i)return o;let u=await y(i,a);return o?await n({docUuid:o.uuid,content:u}):await t({file_name:r,content:u})}async function y(e,t){let n=await (0,s.G)(e),r=void 0!==t&&!(0,c.KA)(t);return"\n<metadata>\n<file_hash>".concat(n,"</file_hash>\n<file_state>\n  ").concat(r&&void 0!==t?"\n  <status>includes_unsaved_changes</status>\n  <message>This content includes modifications since it was last synchronized. You can and should continue to suggest changes using write or replace operations. Your suggestions will be applied to the latest version of the file. THIS IS THE MOST UP TO DATE VERSION OF THE FILE.</message>\n  <changes>".concat(JSON.stringify((0,c.h4)(t)),"</changes>\n"):"<status>synced</status>","\n</file_state>\n</metadata>\n<content>").concat(e,"</content>\n").trim()}async function v(e,t,n,i,a){if(0===e.length){let e=t.find(e=>e.file_name===o.i9);e&&await a({docUuid:e.uuid});return}let c=JSON.stringify(e.map(e=>e.uri),null,2).split("\n"),u=c.slice(0,50).join("\n"),s=c.length-50,l="<synced_resources>\n".concat(u,"\n").concat(s>0?"\n... plus ".concat(s," additional files."):"","\n</synced_resources>"),d=t.filter(e=>e.file_name===o.i9);if(d.length>0){let[e,...n]=d;if(n.length>0&&r.f("Harmony: Cleaning up ".concat(n.length," extra project docs")),await Promise.all(n.map(e=>E(t,e.file_name,a))),e.content===l){r.f("Harmony: Project doc already up to date",e.uuid);return}await i({docUuid:e.uuid,content:l}),r.f("Harmony: Updated project doc",e.uuid)}else{let e=await n({file_name:o.i9,content:l});r.f("Harmony: Created project doc",e.uuid)}}async function E(e,t,n){let r=e.find(e=>e.file_name===t);r&&await n({docUuid:r.uuid})}async function C(e,t,n,r){let{projectUuid:i,directoryUuid:a}=(0,d.Xp)(r),o=(0,d.wq)(e,i,a);for(let e of(await (0,l.y3)(a),t.filter(e=>U(e,o))))await n({docUuid:e.uuid});return e.filter(e=>e!==o)}async function b(e,t,n){let r=await (0,d.mf)(e,null!=n?n:[]);return await (0,l.Gz)({projectUuid:t,root:r,ignorePatterns:null!=n?n:[],lastUpdated:Date.now()})}function U(e,t){if(!(0,d.Wx)(e.file_name))return!1;let{projectUuid:n,directoryUuid:r}=(0,d.Xp)(t.uri),{projectUuid:i,directoryUuid:a}=(0,d.Xp)(e.file_name);return n===i&&r===a}function x(e){let t=e.split("/"),n=t[t.length-1],r=t[t.length-2];return""===n||"."===n?"".concat(r,"/"):n}async function S(e,t,n,r,i,o,c){if(!e||t)return;let u=await (0,l.Zz)(e),s=await (0,d.C7)(u);if(!a()(n.map(e=>e.uri),s.map(e=>e.uri))){for(let e of u)e.root.descendants.some(t=>t.lastModified>e.lastUpdated)&&await (0,l.U4)({...e,lastUpdated:Date.now()});return await v(s,r,i,o,c),{updatedFiles:s}}}},52902:function(e,t,n){n.d(t,{O:function(){return v},cn:function(){return w},_I:function(){return g},wq:function(){return b},sy:function(){return U},IQ:function(){return E},zE:function(){return C},Wx:function(){return R},C7:function(){return y},mf:function(){return T},Xp:function(){return M},pJ:function(){return f},cT:function(){return S},NC:function(){return p}});var r=n(5161),i=n(80762),a=n(98940);class o{static getInstance(){return o.instance||(o.instance=new o),o.instance}getCacheKey(e,t){return"".concat(e.name,"-").concat(t)}isPermissionExpired(e){return Date.now()-e>this.CACHE_DURATION}async checkPermission(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read",n=this.getCacheKey(e,t),r=this.permissionCache.get(n);if(r&&!this.isPermissionExpired(r.timestamp))return"granted"===r.state;try{if("queryPermission"in e){let r=await e.queryPermission({mode:t});if("granted"===r)return this.permissionCache.set(n,{state:"granted",timestamp:Date.now(),mode:t}),!0}if("requestPermission"in e){let r=await e.requestPermission({mode:t});return"granted"===r&&this.permissionCache.set(n,{state:r,timestamp:Date.now(),mode:t}),"granted"===r}return this.permissionCache.set(n,{state:"granted",timestamp:Date.now(),mode:t}),!0}catch(e){return i.v("Permission check failed:",e),!1}}constructor(){this.permissionCache=new Map,this.CACHE_DURATION=36e5}}let c=o.getInstance();async function u(e,t){return c.checkPermission(e,t)}var s=n(87376).Buffer;let l="inode/directory";async function d(e,t){let n=await e.getFile();return{handle:e,path:"".concat(t,"/").concat(n.name),lastModified:n.lastModified}}async function f(e,t){let n,i;let{directory:a,file:o}=U(e,t);if(!o)throw Error("File not found: ".concat(t));let c=await o.handle.getFile(),u=null;try{u=await (0,r.CL)(c),i=c.type||"text/plain"}catch(t){let e=await c.arrayBuffer();n=e.byteLength>1048576?"[File truncated due to size]":s.from(e).toString("base64"),i=c.type||"application/octet-stream"}return{uri:x(a,o),mimeType:i,text:u,blob:n}}async function p(e,t){if(!await u(e.handle,"readwrite"))throw Error("Permission denied");let n=await e.handle.getFile();try{await (0,r.CL)(n)}catch(e){throw Error("Cannot write to non-text files")}let a=await e.handle.createWritable();try{await a.write(t)}finally{await a.close()}i.f("Harmony: Wrote file ".concat(e.path," to local file system")),e.lastModified=Date.now()}async function m(e,t){try{return await e.getDirectoryHandle(t,{create:!0})}catch(e){throw Error("Failed to create directory ".concat(t))}}async function h(e,t){let n=e;for(let e=0;e<t.length-1;e++)n=await m(n,t[e]);return n}async function w(e,t,n){let{pathComponents:r}=k(t);if(!await u(e.root.handle,"readwrite"))throw Error("Permission denied");let i=await h(e.root.handle,r),a=r[r.length-1];try{let t={handle:await i.getFileHandle(a,{create:!0}),path:"".concat(e.root.name,"/").concat(r.join("/")),lastModified:Date.now()};return await p(t,n),e.root.descendants.push(t),!0}catch(e){throw Error("Failed to create file ".concat(t," - ").concat(e))}}async function g(e,t){let{pathComponents:n}=k(t);if(!await u(e.root.handle,"readwrite"))throw Error("Permission denied to delete file in the directory");let r=await h(e.root.handle,n),i=n[n.length-1];try{return await r.removeEntry(i),e.root.descendants=e.root.descendants.filter(t=>t.path!=="".concat(e.root.name,"/").concat(n.join("/"))),!0}catch(e){throw Error("Failed to delete file ".concat(i))}}async function y(e){return(await Promise.all(e.map(v))).flat()}async function v(e){try{if(await u(e.root.handle,"read")){let t=await D(e);t&&(e.root=t.updatedDirectoryRoot)}}catch(t){i.v("Harmony: Could not resync ".concat(e.uuid),t)}let t=e.root.descendants.map(t=>({uri:x(e,t)}));return t.unshift({uri:S(e),mimeType:l}),t}function E(e){return e.mimeType===l}function C(e){return!E(e)}function b(e,t,n){let r=e.find(e=>e.projectUuid===t&&e.uuid===n);if(!r)throw Error("Resource not found: ".concat(t,"/").concat(n));return r}function U(e,t){let{projectUuid:n,directoryUuid:r,pathComponents:i}=M(t),a=b(e,n,r);try{let e=function(e,t){let n="".concat(e.root.name,"/").concat(t.join("/")),r=e.root.descendants.find(e=>e.path===n);if(!r)throw Error("File not found: ".concat(n));return r}(a,i);return{directory:a,file:e}}catch(e){return{directory:a,file:null}}}function x(e,t){let n=t.path.split("/").map(encodeURIComponent);return"file://".concat(e.projectUuid,"/").concat(e.uuid,"/")+n.join("/")}function S(e){return"file://".concat(e.projectUuid,"/").concat(e.uuid,"/").concat(encodeURIComponent(e.root.name),"/")}async function I(e,t,n,r){let i=n.flatMap(e=>{if(""===(e=e.trim())||e.startsWith("#"))return[];let t=e.startsWith("!");return t&&(e=e.slice(1)),(e=e.replace(/^\//,"")).endsWith("/")&&(e+="*"),t?["!".concat(e)]:[e]}),o=[],c=[[e,t]],u=e=>{let t=i.filter(e=>!e.startsWith("!")).some(t=>(0,a.s7)(e,t,{dot:!0,matchBase:!0})),n=i.filter(e=>e.startsWith("!")).some(t=>(0,a.s7)(e,t.slice(1),{dot:!0,matchBase:!0}));return t&&!n};for(;c.length>0;){let[e,n]=c.shift();for await(let i of e.values()){if(o.length>=r)throw Error("Maximum number of files (".concat(r,") exceeded"));if(i.name.startsWith(".")&&".gitignore"!==i.name)continue;let e="".concat(n,"/").concat(i.name);if(!u(e.slice(t.length+1))){if("file"===i.kind){let e=await d(i,n);o.push(e)}else c.push([i,e])}}}return o}async function _(e){try{let t=await e.getFileHandle(".gitignore",{create:!1}),n=await t.getFile();return(await n.text()).split("\n").filter(e=>""!==e.trim()&&!e.startsWith("#"))}catch(e){return[]}}async function T(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:2500;try{let r=[...await _(e),...t],i=await I(e,e.name,r,n);return{handle:e,name:e.name,descendants:i}}catch(e){if(e instanceof Error&&e.message.includes("Maximum number of files"))throw Error("The size of this directory may cause performance degradation. Please choose a smaller directory.");throw e}}async function D(e){let t;let n=e.root;try{t=await T(e.root.handle,e.ignorePatterns)}catch(e){return null}let r=j(n.descendants),i=j(t.descendants),a=[];for(let[e,t]of r.entries()){let n=i.get(e);if(void 0===n){a.push({type:"removed",file:t});continue}n.lastModified!==t.lastModified&&a.push({type:"modified",file:n}),i.delete(e)}for(let e of i.values())a.push({type:"added",file:e});return{updatedDirectoryRoot:t,fileChanges:a}}function j(e){let t=new Map;for(let n of e)t.set(n.path,n);return t}let A=new Map,P=(e,t)=>{A.size>=1e3&&Array.from(A.keys()).slice(0,Math.floor(200)).forEach(e=>A.delete(e)),A.set(e,t)};function M(e){if(A.has(e))return A.get(e);if(!e||""===e.trim())throw Error("Invalid URI: URI is undefined or empty");if(!e.startsWith("file://"))throw Error('Invalid URI: Must start with "file://"');let t=new URL(e),n=t.host,[r,i,a,...o]=t.pathname.split("/").map(decodeURIComponent);if(!(n&&o.length))throw Error("Invalid URI: Missing required components");let c={projectUuid:n,directoryUuid:i,directoryName:a,pathComponents:o};return P(e,c),c}function R(e){try{return M(e),!0}catch(e){return!1}}function k(e){if(!e||""===e.trim())throw Error("Invalid URI: URI is undefined or empty");if(!e.startsWith("file://"))throw Error('Invalid URI: Must start with "file://"');try{let t=new URL(e),n=t.host,[r,i,a,...o]=t.pathname.split("/").map(decodeURIComponent);if(!n||!i||!a)throw Error("Invalid URI: Missing required components");return{projectUuid:n,directoryUuid:i,directoryName:a,pathComponents:o}}catch(e){throw Error("Invalid URI: ".concat(e.message))}}},5161:function(e,t,n){n.d(t,{CL:function(){return D},Jw:function(){return I},MR:function(){return g},Wr:function(){return U},dF:function(){return _},eh:function(){return j},mD:function(){return C},tB:function(){return y},wI:function(){return l}});var r=n(27218),i=n(1812),a=n(14448),o=n(18916);let c=[".DS_Store"],u=["jpg","jpeg","png","gif","webp"],s=["image/jpeg","image/png","image/gif","image/webp"],l={"image/jpeg":"jpg","image/png":"png","image/gif":"gif","image/webp":"webp"},d=["bmp","ico","tiff","tif","psd","raw","cr2","nef","orf","sr2","mobi","jp2","jpx","jpm","mj2","svg","svgz","ai","eps","ps","indd","heic","mp4","mov","avi","mkv","wmv","flv","webm","mpeg","mpg","m4v","3gp","ogv","ogg","rm","rmvb","asf","amv","mpe","m1v","m2v","svi","3g2","roq","nsv","f4v","f4p","f4a","f4b","qt","hdmov","divx","div","m2ts","mts","vob","mp3","wav","wma","aac","flac","alac","aiff","ogg","opus","m4a","amr","awb","wmv","ra","rm","mid","midi","mka","ttf","otf","woff","woff2","eot","sfnt","ttc","suit","zip"],f=["xls","xlsx","xlsb","xlm","xlsm","xlt","xltm","xltx","ods"],p=["txt","py","ipynb","js","jsx","html","css","java","cs","php","c","cc","cpp","cxx","cts","h","hh","hpp","rs","R","Rmd","swift","go","rb","kt","kts","ts","tsx","m","mm","mts","scala","rs","dart","lua","pl","pm","t","sh","bash","zsh","csv","log","ini","cfg","config","json","proto","yaml","yml","toml","lua","sql","bat","md","coffee","tex","latex","gd","gdshader","tres","tscn"],m=["docx","rtf","epub","odt","odp","pdf"],h=["application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/rtf","application/epub+zip","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.presentation","application/pdf"],w=["csv"],g=3e7,y=e=>{let{imagesEnabled:t=!1,outOfContextFilesEnabled:n=!1}=e;return[".pdf",".doc",".docx",".rtf",".epub",".odt",".odp",".pptx",...E(),...t?v():[],...n?f.map(e=>".".concat(e)):[]]},v=()=>u.map(e=>".".concat(e)),E=()=>p.map(e=>".".concat(e)),C=e=>e.split(".").pop().toLowerCase(),b=(e,t)=>{let{imagesEnabled:n=!1,blobFileUploadsEnabled:r=!1}=t,i=C(e.name);return!!(!n&&(u.includes(i)||s.includes(e.type)))||(r?d.includes(i):[...d,...f].includes(i))},U=e=>{let t=C(e.name);return u.includes(t)||s.includes(e.type)},x=e=>[...w,...f].includes(C(e.name)),S=e=>!!c.includes(e.name),I=(e,t)=>{let{imagesEnabled:n=!1,blobFileUploadsEnabled:r=!1,rasterizePdfUploadsEnabled:i}=t,a=[],o=[],c=[],u=[],s=[],l=[];return e.filter(e=>!S(e)).forEach(e=>{let t=n&&U(e);b(e,{imagesEnabled:n,blobFileUploadsEnabled:r})?s.push(e):e.size>g?l.push(e):r&&x(e)?c.push(e):i&&("pdf"===C(e.name)||"application/pdf"===e.type)?u.push(e):_(e)?a.push(e):t?o.push(e):a.push(e)}),{attachmentUploads:a,imageUploads:o,rasterizedDocumentUploads:u,unsupportedUploads:s,tooLargeUploads:l,outOfContextFileUploads:c}},_=e=>{let t=C(e.name);return m.includes(t)||h.includes(e.type)},T=e=>{let t=new Uint8Array(e);if(t.length>=4){if(239===t[0]&&187===t[1]&&191===t[2])return"utf-8";if(254===t[0]&&255===t[1]||255===t[0]&&254===t[1])return"utf-16";if(0===t[0]&&0===t[1]&&254===t[2]&&255===t[3]||255===t[0]&&254===t[1]&&0===t[2]&&0===t[3])return"utf-32"}let n=!0,r=Math.min(t.length-1,1e3);for(let e=0;e<r;e+=2)if(0===t[e]&&0===t[e+1]||0!==t[e]&&0!==t[e+1]){n=!1;break}if(n)return"utf-16";let i=!0,a=!1;for(let e=0;e<t.length;e++){let n=t[e];if(n>127){if(a=!0,(224&n)==192){if(e+1>=t.length||(192&t[e+1])!=128){i=!1;break}e+=1}else if((240&n)==224){if(e+2>=t.length||(192&t[e+1])!=128||(192&t[e+2])!=128){i=!1;break}e+=2}else if((248&n)==240){if(e+3>=t.length||(192&t[e+1])!=128||(192&t[e+2])!=128||(192&t[e+3])!=128){i=!1;break}e+=3}else{i=!1;break}}}if(i&&a)return"utf-8";let o=(e=>{let t={ascii:0,nonAscii:0,cyrillic:0,latin1:0,control:0,total:Math.min(e.length,4e3)};for(let n=0;n<t.total;n++){let r=e[n];r<32?t.control++:r<127?t.ascii++:r>127&&(t.nonAscii++,(r>=192&&r<=255||r>=168&&r<=183)&&t.cyrillic++,(r>=128&&r<=159||r>=160&&r<=255)&&t.latin1++)}return t})(t),c=o.cyrillic/o.total*100,u=o.latin1/o.total*100,s=o.ascii/o.total*100;if(o.nonAscii>0){if(c>.3)return"windows-1251";if(u>o.control/o.total*100)return"windows-1252"}return s>90&&o.control/o.total<.1?"ascii":"utf-8"},D=async e=>{let t=await e.arrayBuffer(),n=T(t);try{return new TextDecoder(n,{fatal:!0}).decode(t)}catch(e){if("utf-8"!==n)return new TextDecoder("utf-8",{fatal:!0}).decode(t)}throw Error("Failed to decode file as plain text")};function j(){var e;let{value:t}=(0,a.F)("ooc_attachments"),{account:n}=(0,r.t)(),c=(0,i.T)(),u=null!==(e=null!=c?c:null==n?void 0:n.settings)&&void 0!==e?e:{},{value:s}=(0,o.h)(u);return!!t&&s}},80762:function(e,t,n){function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]}function i(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n]}n.d(t,{f:function(){return r},v:function(){return i}})}}]);