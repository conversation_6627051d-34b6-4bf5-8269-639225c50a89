2025-08-16 18:00:46,030 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:00:46,045 - core.database - INFO - Database connection successful
2025-08-16 18:00:46,045 - core.database - INFO - Database Manager initialized
2025-08-16 18:00:54,786 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:02:28,288 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:08:28,359 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:16:58,963 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:16:59,241 - core.database - INFO - Database connection successful
2025-08-16 18:16:59,242 - core.database - INFO - Database Manager initialized
2025-08-16 18:16:59,249 - __main__ - INFO - Logging system initialized
2025-08-16 18:19:23,954 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:19:24,218 - core.database - INFO - Database connection successful
2025-08-16 18:19:24,219 - core.database - INFO - Database Manager initialized
2025-08-16 18:19:24,224 - __main__ - INFO - Logging system initialized
2025-08-16 18:23:00,859 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:23:01,169 - core.database - INFO - Database connection successful
2025-08-16 18:23:01,170 - core.database - INFO - Database Manager initialized
2025-08-16 18:23:01,176 - __main__ - INFO - Logging system initialized
2025-08-16 18:23:31,240 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:23:31,503 - core.database - INFO - Database connection successful
2025-08-16 18:23:31,504 - core.database - INFO - Database Manager initialized
2025-08-16 18:23:31,511 - __main__ - INFO - Logging system initialized
2025-08-16 18:23:31,592 - core.database - INFO - Table message_logs created/verified
2025-08-16 18:23:31,642 - core.database - INFO - Table scraped_users created/verified
2025-08-16 18:23:31,658 - core.database - INFO - Table message_logs created/verified
2025-08-16 18:23:31,679 - core.database - INFO - Table scraped_users created/verified
2025-08-16 18:23:31,679 - __main__ - INFO - System initialization complete
2025-08-16 18:23:31,705 - app - INFO - Loading 5 accounts from database
2025-08-16 18:23:31,707 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-16 18:23:31,707 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-16 18:23:31,707 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-16 18:23:31,710 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-16 18:23:31,710 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-16 18:23:31,710 - app - INFO - Successfully loaded 5 accounts
2025-08-16 18:23:31,710 - app - INFO - Instagram Management Application initialized
2025-08-16 18:23:31,712 - __main__ - ERROR - Application startup error: InstagramApp.run() got an unexpected keyword argument 'use_reloader'
2025-08-16 18:24:01,813 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:24:02,069 - core.database - INFO - Database connection successful
2025-08-16 18:24:02,070 - core.database - INFO - Database Manager initialized
2025-08-16 18:24:02,076 - __main__ - INFO - Logging system initialized
2025-08-16 18:24:02,100 - core.database - INFO - Table message_logs created/verified
2025-08-16 18:24:02,112 - core.database - INFO - Table scraped_users created/verified
2025-08-16 18:24:02,123 - core.database - INFO - Table message_logs created/verified
2025-08-16 18:24:02,138 - core.database - INFO - Table scraped_users created/verified
2025-08-16 18:24:02,139 - __main__ - INFO - System initialization complete
2025-08-16 18:24:02,163 - app - INFO - Loading 5 accounts from database
2025-08-16 18:24:02,163 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-16 18:24:02,165 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-16 18:24:02,165 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-16 18:24:02,165 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-16 18:24:02,167 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-16 18:24:02,167 - app - INFO - Successfully loaded 5 accounts
2025-08-16 18:24:02,168 - app - INFO - Instagram Management Application initialized
2025-08-16 18:24:02,168 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-16 18:24:02,216 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-16 18:24:02,216 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-16 18:24:02,223 - werkzeug - INFO -  * Restarting with stat
2025-08-16 18:24:03,714 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-16 18:24:03,955 - core.database - INFO - Database connection successful
2025-08-16 18:24:03,955 - core.database - INFO - Database Manager initialized
2025-08-16 18:24:03,960 - __main__ - INFO - Logging system initialized
2025-08-16 18:24:03,985 - core.database - INFO - Table message_logs created/verified
2025-08-16 18:24:03,998 - core.database - INFO - Table scraped_users created/verified
2025-08-16 18:24:04,012 - core.database - INFO - Table message_logs created/verified
2025-08-16 18:24:04,025 - core.database - INFO - Table scraped_users created/verified
2025-08-16 18:24:04,025 - __main__ - INFO - System initialization complete
2025-08-16 18:24:04,047 - app - INFO - Loading 5 accounts from database
2025-08-16 18:24:04,049 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-16 18:24:04,050 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-16 18:24:04,050 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-16 18:24:04,050 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-16 18:24:04,053 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-16 18:24:04,053 - app - INFO - Successfully loaded 5 accounts
2025-08-16 18:24:04,053 - app - INFO - Instagram Management Application initialized
2025-08-16 18:24:04,054 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-16 18:24:04,062 - werkzeug - WARNING -  * Debugger is active!
2025-08-16 18:24:04,071 - werkzeug - INFO -  * Debugger PIN: 125-732-573
