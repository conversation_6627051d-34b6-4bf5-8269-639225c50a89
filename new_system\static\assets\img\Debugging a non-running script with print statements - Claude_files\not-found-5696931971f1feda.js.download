(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9160],{30488:function(e,n,t){Promise.resolve().then(t.bind(t,85749))},85749:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return l}});var r=t(27573),i=t(70354),a=t(44602),s=t(45790);function l(){return(0,r.jsx)(a.C,{productSurface:"claude-ai",headline:(0,r.jsx)(s.Z,{defaultMessage:"Page not found",id:"QRccCMoxuD"}),subheading:(0,r.jsx)(s.Z,{defaultMessage:"<PERSON> can help with many things, but finding this page isn’t one of them.",id:"k7xSM0388d"}),button:(0,r.jsx)(i.z,{href:"/new",children:(0,r.jsx)(s.Z,{defaultMessage:"Go back home",id:"DupNoSk4qt"})})})}},88146:function(e,n,t){"use strict";t.d(n,{default:function(){return i.a}});var r=t(16340),i=t.n(r)},9788:function(e,n,t){"use strict";t.d(n,{q:function(){return s}});var r=t(10607),i=t(7653);function a(e){return function(n){for(var t=arguments.length,a=Array(t>1?t-1:0),s=1;s<t;s++)a[s-1]=arguments[s];let l=n.map(e=>e.replace(/\n/g,"").trim()),o=i.forwardRef((n,t)=>{let{className:s,...o}=n,c=a.map(e=>"function"==typeof e?e(n):e),u=Object.fromEntries(Object.entries(o).filter(e=>{let[n]=e;return!n.startsWith("$")}));return i.createElement(e,{...u,ref:t,className:(0,r.Z)(l,c,"string"==typeof s?s:"")})});return o.displayName="string"==typeof e?e:e.displayName,o}}function s(e){return a(e)}s.a=a("a"),s.aside=a("aside"),s.button=a("button"),s.main=a("main"),s.div=a("div"),s.form=a("form"),s.nav=a("nav"),s.fieldset=a("fieldset"),s.header=a("header"),s.h1=a("h1"),s.h2=a("h2"),s.h3=a("h3"),s.h4=a("h4"),s.h5=a("h5"),s.th=a("th"),s.td=a("td"),s.input=a("input"),s.label=a("label"),s.p=a("p"),s.section=a("section"),s.span=a("span"),s.li=a("li")},64483:function(e,n,t){"use strict";t.d(n,{I:function(){return i}});var r=t(27573);function i(e){let{height:n=14,className:t}=e;return(0,r.jsxs)("svg",{height:n,className:t,viewBox:"0 0 110 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","aria-label":"Anthropic",children:[(0,r.jsx)("path",{d:"M26.92 2.43646H30.929V11.8011H33.4879V2.43646H37.4969V0.198895H26.92V2.43646Z"}),(0,r.jsx)("path",{d:"M22.3992 8.32044L17.0254 0.198895H14.1253V11.8011H16.5989V3.67956L21.9727 11.8011H24.8728V0.198895H22.3992V8.32044Z"}),(0,r.jsx)("path",{d:"M47.7326 4.8232H42.103V0.198895H39.544V11.8011H42.103V7.06077H47.7326V11.8011H50.2916V0.198895H47.7326V4.8232Z"}),(0,r.jsx)("path",{d:"M4.75962 0.198895L0 11.8011H2.66129L3.63471 9.36464H8.61422L9.58747 11.8011H12.2488L7.48914 0.198895H4.75962ZM4.49553 7.20994L6.12438 3.1326L7.75323 7.20994H4.49553Z"}),(0,r.jsx)("path",{d:"M71.4966 0C68.0506 0 65.611 2.48619 65.611 6.01657C65.611 9.51381 68.0506 12 71.4966 12C74.9256 12 77.348 9.51381 77.348 6.01657C77.348 2.48619 74.9256 0 71.4966 0ZM71.4966 9.67956C69.4836 9.67956 68.2553 8.28729 68.2553 6.01657C68.2553 3.71271 69.4836 2.32044 71.4966 2.32044C73.4926 2.32044 74.7038 3.71271 74.7038 6.01657C74.7038 8.28729 73.4926 9.67956 71.4966 9.67956Z"}),(0,r.jsx)("path",{d:"M107.27 7.90608C106.827 9.03315 105.94 9.67956 104.729 9.67956C102.716 9.67956 101.487 8.28729 101.487 6.01657C101.487 3.71271 102.716 2.32044 104.729 2.32044C105.94 2.32044 106.827 2.96685 107.27 4.09392H109.983C109.318 1.60773 107.322 0 104.729 0C101.283 0 98.843 2.48619 98.843 6.01657C98.843 9.51381 101.283 12 104.729 12C107.339 12 109.335 10.3757 110 7.90608H107.27Z"}),(0,r.jsx)("path",{d:"M90.9615 0.198895L95.7212 11.8011H98.3313L93.5717 0.198895H90.9615Z"}),(0,r.jsx)("path",{d:"M85.5707 0.198895H79.7364V11.8011H82.2953V7.59116H85.5707C88.2832 7.59116 89.938 6.19889 89.938 3.89503C89.938 1.59116 88.2832 0.198895 85.5707 0.198895ZM85.4513 5.35359H82.2953V2.43646H85.4513C86.7137 2.43646 87.379 2.9337 87.379 3.89503C87.379 4.85635 86.7137 5.35359 85.4513 5.35359Z"}),(0,r.jsx)("path",{d:"M63.6492 3.72928C63.6492 1.54144 61.9944 0.198895 59.2819 0.198895H53.4476V11.8011H56.0065V7.25967H58.8553L61.4144 11.8011H64.2463L61.4127 6.91376C62.8349 6.38254 63.6492 5.26392 63.6492 3.72928ZM56.0065 2.43646H59.1625C60.4249 2.43646 61.0903 2.88398 61.0903 3.72928C61.0903 4.57459 60.4249 5.0221 59.1625 5.0221H56.0065V2.43646Z"})]})}},22769:function(e,n,t){"use strict";t.d(n,{s:function(){return a}});var r=t(27573),i=t(10607);function a(e){let{className:n}=e;return(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 184 40",className:(0,i.Z)("text-text-000",n),fill:"currentColor",children:[(0,r.jsx)("path",{shapeRendering:"optimizeQuality",fill:"#D97757",d:"m7.75 26.27 7.77-4.36.13-.38-.13-.21h-.38l-1.3-.08-4.44-.12-3.85-.16-3.73-.2-.94-.2L0 19.4l.09-.58.79-.53 1.13.1 2.5.17 3.75.26 2.72.16 4.03.42h.64l.09-.26-.22-.16-.17-.16-3.88-2.63-4.2-2.78-2.2-1.6L3.88 11l-.6-.76-.26-1.66L4.1 7.39l1.45.1.37.1 1.47 1.13 3.14 2.43 4.1 3.02.6.5.24-.17.03-.12-.27-.45L13 9.9l-2.38-4.1-1.06-1.7-.28-1.02c-.1-.42-.17-.77-.17-1.2L10.34.21l.68-.22 1.64.22.69.6 1.02 2.33 1.65 3.67 2.56 4.99.75 1.48.4 1.37.15.42h.26v-.24l.21-2.81.39-3.45.38-4.44.13-1.25.62-1.5L23.1.57l.96.46.79 1.13-.11.73-.47 3.05-.92 4.78-.6 3.2h.35l.4-.4 1.62-2.15 2.72-3.4 1.2-1.35 1.4-1.49.9-.71h1.7l1.25 1.86-.56 1.92-1.75 2.22-1.45 1.88-2.08 2.8-1.3 2.24.12.18.31-.03 4.7-1 2.54-.46 3.03-.52 1.37.64.15.65-.54 1.33-3.24.8-3.8.76-5.66 1.34-.07.05.08.1 2.55.24 1.09.06h2.67l4.97.37 1.3.86.78 1.05-.13.8-2 1.02-2.7-.64-6.3-1.5-2.16-.54h-.3v.18l1.8 1.76 3.3 2.98 4.13 3.84.21.95-.53.75-.56-.08-3.63-2.73-1.4-1.23-3.17-2.67h-.21v.28l.73 1.07 3.86 5.8.2 1.78-.28.58-1 .35-1.1-.2L26 33.14l-2.33-3.57-1.88-3.2-.23.13-1.11 11.95-.52.61-1.2.46-1-.76-.53-1.23.53-2.43.64-3.17.52-2.52.47-3.13.28-1.04-.02-.07-.23.03-2.36 3.24-3.59 4.85-2.84 3.04-.68.27-1.18-.61.11-1.09.66-.97 3.93-5 2.37-3.1 1.53-1.79-.01-.26h-.09L6.8 30.56l-1.86.24-.8-.75.1-1.23.38-.4 3.14-2.16Z"}),(0,r.jsx)("path",{shapeRendering:"optimizeQuality",d:"M64.48 33.54c-5.02 0-8.45-2.8-10.07-7.11a19.19 19.19 0 0 1-1.23-7.03c0-7.23 3.24-12.25 10.4-12.25 4.81 0 7.78 2.1 9.47 7.11h2.06l-.28-6.91c-2.88-1.86-6.48-2.8-10.86-2.8-6.17 0-11.42 2.76-14.34 7.74a16.77 16.77 0 0 0-2.22 8.65c0 5.53 2.61 10.43 7.51 13.15a17.51 17.51 0 0 0 8.73 2.06c4.78 0 8.57-.91 11.93-2.5l.87-7.62h-2.1c-1.26 3.48-2.76 5.57-5.25 6.68-1.22.55-2.76.83-4.62.83ZM86.13 7.15l.2-3.4h-1.42l-6.32 1.9v1.03l2.8 1.3v23.78c0 1.62-.83 1.98-3 2.25v1.74h10.75v-1.74c-2.18-.27-3-.63-3-2.25V7.16Zm42.75 29h.83l7.27-1.38v-1.78l-1.02-.08c-1.7-.16-2.14-.51-2.14-1.9V18.33l.2-4.07h-1.15l-6.87.99v1.74l.67.12c1.86.27 2.41.79 2.41 2.09v11.3c-1.78 1.38-3.48 2.25-5.5 2.25-2.24 0-3.63-1.14-3.63-3.8V18.34l.2-4.07h-1.18l-6.88.99v1.74l.71.12c1.86.27 2.41.79 2.41 2.09v10.43c0 4.42 2.5 6.52 6.48 6.52 3.04 0 5.53-1.62 7.4-3.87l-.2 3.87ZM108.9 22.08c0-5.65-3-7.82-8.42-7.82-4.78 0-8.25 1.98-8.25 5.26 0 .98.35 1.73 1.06 2.25l3.64-.48c-.16-1.1-.24-1.77-.24-2.05 0-1.86.99-2.8 3-2.8 2.97 0 4.47 2.09 4.47 5.45v1.1l-7.5 2.25c-2.5.68-3.92 1.27-4.87 2.65a5 5 0 0 0-.7 2.8c0 3.2 2.2 5.46 5.96 5.46 2.72 0 5.13-1.23 7.23-3.56.75 2.33 1.9 3.56 3.95 3.56 1.66 0 3.16-.67 4.5-1.98l-.4-1.38c-.58.16-1.14.24-1.73.24-1.15 0-1.7-.91-1.7-2.69v-8.26Zm-9.6 10.87c-2.05 0-3.32-1.19-3.32-3.28 0-1.42.67-2.25 2.1-2.73l6.08-1.93v5.84c-1.94 1.47-3.08 2.1-4.86 2.1Zm63.3 1.82v-1.78l-1.03-.08c-1.7-.16-2.13-.51-2.13-1.9V7.15l.2-3.4h-1.43l-6.32 1.9v1.03l2.8 1.3v7.82a8.83 8.83 0 0 0-5.37-1.54c-6.28 0-11.18 4.78-11.18 11.93 0 5.89 3.52 9.96 9.32 9.96 3 0 5.61-1.46 7.23-3.72l-.2 3.72h.84l7.27-1.38Zm-13.16-18.14c3 0 5.25 1.74 5.25 4.94v9a7.2 7.2 0 0 1-5.21 2.1c-4.3 0-6.48-3.4-6.48-7.94 0-5.1 2.49-8.1 6.44-8.1Zm28.53 4.5c-.56-2.64-2.18-4.14-4.43-4.14-3.36 0-5.69 2.53-5.69 6.16 0 5.37 2.84 8.85 7.43 8.85a8.6 8.6 0 0 0 7.39-4.35l1.34.36c-.6 4.66-4.82 8.14-10 8.14-6.08 0-10.27-4.5-10.27-10.9 0-6.45 4.55-10.99 10.63-10.99 4.54 0 7.74 2.73 8.77 7.47l-15.84 4.86v-2.14l10.67-3.31Z"})]})}},70354:function(e,n,t){"use strict";t.d(n,{z:function(){return f}});var r=t(27573),i=t(11607),a=t(88755),s=t(49289),l=t(10607),o=t(88146),c=t(7653);let u="\n    text-text-000\n    border-0.5\n    border-border-300\n    relative\n    overflow-hidden\n    font-styrene\n    font-medium\n    transition\n    duration-100\n    hover:border-border-300/0\n    bg-bg-300/0\n    hover:bg-bg-400\n    backface-hidden",d=(0,s.j)("inline-flex\n  items-center\n  justify-center\n  relative\n  shrink-0\n  can-focus\n  select-none\n  disabled:pointer-events-none\n  disabled:opacity-50\n  disabled:shadow-none\n  disabled:drop-shadow-none",{variants:{variant:{primary:"bg-text-000\n        text-bg-000\n        relative\n        overflow-hidden\n        font-medium\n        font-styrene\n        transition-transform\n        will-change-transform\n        ease-[cubic-bezier(0.165,0.85,0.45,1)]\n        duration-150\n        hover:scale-y-[1.015]\n        hover:scale-x-[1.005]\n        backface-hidden\n        after:absolute\n        after:inset-0\n        after:bg-[radial-gradient(at_bottom,hsla(var(--bg-000)/20%),hsla(var(--bg-000)/0%))]\n        after:opacity-0\n        after:transition\n        after:duration-200\n        after:translate-y-2\n        hover:after:opacity-100\n        hover:after:translate-y-0",flat:"bg-accent-main-000\n          text-oncolor-100\n          font-styrene\n          font-medium\n          transition-colors\n          hover:bg-accent-main-200",secondary:u,outline:u,ghost:"text-text-300\n          border-transparent\n          transition\n          font-styrene\n          duration-300\n          ease-[cubic-bezier(0.165,0.85,0.45,1)]\n          hover:bg-bg-400\n          aria-pressed:bg-bg-400\n          aria-checked:bg-bg-400\n          aria-expanded:bg-bg-300\n          hover:text-text-100\n          aria-pressed:text-text-100\n          aria-checked:text-text-100\n          aria-expanded:text-text-100",underline:"opacity-80\n          transition-all\n          active:scale-[0.985]\n          hover:opacity-100\n          hover:underline\n          underline-offset-3",danger:"bg-danger-200\n          text-oncolor-100\n          font-styrene\n          font-medium\n          transition\n          hover:scale-y-[1.015]\n          hover:scale-x-[1.005]\n          hover:opacity-95",unstyled:""},size:{default:"h-9 px-4 py-2 rounded-lg min-w-[5rem] active:scale-[0.985] whitespace-nowrap text-sm",sm:"h-8 rounded-md px-3 text-xs min-w-[4rem] active:scale-[0.985] whitespace-nowrap",lg:"h-11 rounded-[0.6rem] px-5 min-w-[6rem] active:scale-[0.985] whitespace-nowrap",icon:"h-9 w-9 rounded-md active:scale-95 shrink-0",icon_xs:"h-6 w-6 rounded-md active:scale-95",icon_sm:"h-8 w-8 rounded-md active:scale-95",icon_lg:"h-11 w-11 rounded-[0.6rem] active:scale-95",inline:"px-0.5 rounded-[0.25rem]",unset:""},option:{rounded:"!rounded-full",prepend:"",append:""},state:{active:""}},compoundVariants:[{size:"default",option:"prepend",class:"pl-2 pr-3 gap-1"},{size:"lg",option:"prepend",class:"pl-2.5 pr-3.5 gap-1"},{size:"sm",option:"prepend",class:" pl-2 pr-2.5 gap-1"},{size:"default",option:"append",class:"pl-3 pr-2 gap-1"},{size:"lg",option:"append",class:"pl-3.5 pr-2.5 gap-1"},{size:"sm",option:"append",class:"pl-2.5 pr-2 gap-1"},{variant:"ghost",state:"active",class:"!bg-bg-400"}],defaultVariants:{variant:"primary",size:"default"}}),f=(0,c.forwardRef)((e,n)=>{let{className:t,variant:s,size:c,option:u,loading:f,href:p,onLinkClick:h,target:m,prepend:v,append:x,state:g,disabled:b,children:w,type:j="button",...y}=e;v&&(u="prepend"),x&&(u="append");let C=(0,a.useIsClaudeApp)(),H=(0,l.Z)(d({variant:s,size:c,option:u,state:g,className:t}),f&&"text-transparent ![text-shadow:_none]",C&&"cursor-default"),Z=(0,r.jsxs)(r.Fragment,{children:[f&&(0,r.jsx)("div",{className:(0,l.Z)("absolute inset-0 flex items-center justify-center",s&&"flat"!==s&&"danger"!==s?"text-bg-300":"text-oncolor-100"),children:(0,r.jsx)(i.Loading,{size:"sm",inheritColor:!0,delay:0})}),v,w,x]});return p?(0,r.jsx)(o.default,{href:p,target:m||"_self",className:H,"aria-label":y["aria-label"],onClick:h,children:Z}):(0,r.jsx)("button",{className:H,ref:n,disabled:b||f,type:j,...y,children:Z})});f.displayName="Button"},11607:function(e,n,t){"use strict";t.r(n),t.d(n,{Loading:function(){return l}});var r=t(27573),i=t(10607),a=t(7653),s=t(45790);let l=(0,a.memo)(function(e){let{size:n="md",fullscreen:t=!1,inheritColor:l,delay:o=0}=e,[c,u]=(0,a.useState)(o>0);return(0,a.useEffect)(()=>{if(!o)return;let e=setTimeout(()=>u(!1),o);return()=>clearTimeout(e)},[o]),(0,r.jsx)("div",{className:(0,i.Z)(t?"fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2":"m-auto"),children:(0,r.jsx)("div",{className:(0,i.Z)("sm"===n&&"h-4 w-4 border-2","md"===n&&"h-20 w-20 border-8",l?"border-current":"border-border-200","text-secondary inline-block animate-spin rounded-full border-solid border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",c&&"hidden"),role:"status",children:(0,r.jsx)("span",{className:"sr-only",children:(0,r.jsx)(s.Z,{defaultMessage:"Loading...",id:"gjBiyjshwX"})})})})})},44602:function(e,n,t){"use strict";t.d(n,{C:function(){return p}});var r=t(56683),i=t(27573),a=t(9788),s=t(64483),l=t(22769),o=t(11607);function c(){let e=(0,r._)(["\n  mx-0\n  mt-4\n  min-w-[16rem]\n"]);return c=function(){return e},e}function u(){let e=(0,r._)(["\n  grid\n  place-content-center\n  min-h-min\n  text-center\n  gap-2\n  pt-24\n  pb-32\n  px-4\n  mx-auto\n  h-screen\n"]);return u=function(){return e},e}function d(){let e=(0,r._)(["\n  font-copernicus\n  font-medium\n  tracking-tighter\n  text-4xl\n"]);return d=function(){return e},e}function f(){let e=(0,r._)(["\n  font-styrene\n  text-text-300\n  text-lg\n"]);return f=function(){return e},e}function p(e){let{productSurface:n,headline:t,subheading:r,button:a,testId:c,compact:u,loading:d}=e,f="claude-ai"===n?l.s:s.I;return(0,i.jsx)(m,{"data-testid":c,className:u?"max-w-min":"w-fit",children:d?(0,i.jsx)(o.Loading,{delay:1e3}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"mb-10 text-center",children:(0,i.jsx)(f,{className:"inline-block h-6 -ml-1"})}),(0,i.jsx)(v,{children:t}),r&&(0,i.jsx)(x,{children:r}),(0,i.jsx)(h,{children:a})]})})}t(7653);let h=a.q.div(c()),m=a.q.div(u()),v=a.q.h2(d()),x=a.q.h3(f())},85069:function(e,n,t){"use strict";t.d(n,{D0:function(){return l},Gd:function(){return o},Gg:function(){return a},Rx:function(){return s},eM:function(){return i},t6:function(){return c}});var r=t(66947);let i="!h-[52px] !border-0",a="shadow-[0px_1px_0px_0px_theme(colors.border-300)]";function s(e){let{version:n,platform:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.toLowerCase(),a=i.match(/claude(?:nest)?\/([^ ]+)/i),s=a?a[1]:null;if(!s)return!1;let l=!n||(0,r.satisfies)(s,n,{includePrerelease:!0}),o=!t||"mac"===t&&i.includes("macintosh")||"windows"===t&&i.includes("windows");return l&&o&&!!window.claudeAppBindings}let l={version:">=0.3.7"},o={version:">=0.9.0"},c={...o,platform:"mac"}},43965:function(e,n,t){"use strict";t.d(n,{ServerUserAgentProvider:function(){return s},q:function(){return l}});var r=t(27573),i=t(7653);let a=(0,i.createContext)(null);function s(e){let{userAgent:n,children:t}=e;return(0,r.jsx)(a.Provider,{value:n,children:t})}function l(){return(0,i.useContext)(a)}},88755:function(e,n,t){"use strict";t.d(n,{useIsClaudeApp:function(){return s}});var r=t(7653),i=t(85069),a=t(43965);function s(){let{version:e,platform:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,a.q)();let t=window.navigator.userAgent;return(0,r.useMemo)(()=>!!t&&(0,i.Rx)(t,{version:e,platform:n}),[t,e,n])}},56683:function(e,n,t){"use strict";function r(e,n){return n||(n=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(n)}}))}t.d(n,{_:function(){return r}})},49289:function(e,n,t){"use strict";t.d(n,{j:function(){return s}});var r=t(10607);let i=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,a=r.W,s=(e,n)=>t=>{var r;if((null==n?void 0:n.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:l}=n,o=Object.keys(s).map(e=>{let n=null==t?void 0:t[e],r=null==l?void 0:l[e];if(null===n)return null;let a=i(n)||i(r);return s[e][a]}),c=t&&Object.entries(t).reduce((e,n)=>{let[t,r]=n;return void 0===r||(e[t]=r),e},{});return a(e,o,null==n?void 0:null===(r=n.compoundVariants)||void 0===r?void 0:r.reduce((e,n)=>{let{class:t,className:r,...i}=n;return Object.entries(i).every(e=>{let[n,t]=e;return Array.isArray(t)?t.includes({...l,...c}[n]):({...l,...c})[n]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},10607:function(e,n,t){"use strict";function r(){for(var e,n,t=0,r="";t<arguments.length;)(e=arguments[t++])&&(n=function e(n){var t,r,i="";if("string"==typeof n||"number"==typeof n)i+=n;else if("object"==typeof n){if(Array.isArray(n))for(t=0;t<n.length;t++)n[t]&&(r=e(n[t]))&&(i&&(i+=" "),i+=r);else for(t in n)n[t]&&(i&&(i+=" "),i+=t)}return i}(e))&&(r&&(r+=" "),r+=n);return r}t.d(n,{W:function(){return r}}),n.Z=r}},function(e){e.O(0,[5790,6947,6340,1293,1362,4856,1744],function(){return e(e.s=30488)}),_N_E=e.O()}]);