#!/usr/bin/env python3
"""
Quick System Test - Fast verification of core functionality
Quick test to verify if the system is working properly
"""

import os
import sys
import time
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing module imports...")
    
    try:
        from core.instagram_manager import InstagramManager
        print("✅ InstagramManager imported successfully")
    except Exception as e:
        print(f"❌ InstagramManager import failed: {e}")
        return False
    
    try:
        from core.database import DatabaseManager
        print("✅ DatabaseManager imported successfully")
    except Exception as e:
        print(f"❌ DatabaseManager import failed: {e}")
        return False
    
    try:
        from bots.bio_scanner_bot import BioScannerBot
        print("✅ BioScannerBot imported successfully")
    except Exception as e:
        print(f"❌ BioScannerBot import failed: {e}")
        return False
    
    try:
        from bots.message_bot import MessageBot
        print("✅ MessageBot imported successfully")
    except Exception as e:
        print(f"❌ MessageBot import failed: {e}")
        return False
    
    try:
        from bots.data_retriever_bot import DataRetrieverBot
        print("✅ DataRetrieverBot imported successfully")
    except Exception as e:
        print(f"❌ DataRetrieverBot import failed: {e}")
        return False
    
    try:
        from bots.account_warmup_bot import AccountWarmupBot
        print("✅ AccountWarmupBot imported successfully")
    except Exception as e:
        print(f"❌ AccountWarmupBot import failed: {e}")
        return False
    
    return True

def test_database():
    """Test database connection"""
    print("\nTesting database connection...")
    
    try:
        from core.database import DatabaseManager
        db = DatabaseManager()
        
        # Test connection
        connection = db.get_connection()
        if connection:
            connection.close()
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            return False
        
        # Test basic queries
        accounts = db.get_all_accounts()
        print(f"✅ Found {len(accounts)} accounts in database")
        
        messages = db.get_all_messages()
        print(f"✅ Found {len(messages)} messages in database")
        
        scraped_users = db.get_scraped_users()
        print(f"✅ Found {len(scraped_users)} scraped users in database")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def test_instagram_manager():
    """Test Instagram Manager"""
    print("\nTesting Instagram Manager...")
    
    try:
        from core.instagram_manager import InstagramManager
        im = InstagramManager()
        
        # Test getting accounts
        accounts = im.get_all_accounts()
        print(f"✅ Found {len(accounts)} accounts in Instagram Manager")
        
        # Test getting specific account
        if accounts:
            account = im.get_account(accounts[0]['id'])
            if account:
                print(f"✅ Successfully retrieved account {accounts[0]['id']}")
                print(f"   Username: {account.username}")
                print(f"   Is logged in: {account.is_logged_in()}")
            else:
                print("❌ Failed to retrieve account")
                return False
        else:
            print("⚠️  No accounts found to test")
        
        return True
        
    except Exception as e:
        print(f"❌ Instagram Manager test failed: {e}")
        return False

def test_bot_initialization():
    """Test bot initialization"""
    print("\nTesting bot initialization...")
    
    try:
        from core.instagram_manager import InstagramManager
        from core.database import DatabaseManager
        from bots.bio_scanner_bot import BioScannerBot
        from bots.message_bot import MessageBot
        from bots.data_retriever_bot import DataRetrieverBot
        from bots.account_warmup_bot import AccountWarmupBot
        
        im = InstagramManager()
        db = DatabaseManager()
        
        # Test BioScannerBot
        try:
            bot = BioScannerBot(account_id=5, csv_file_path="test.csv", 
                              instagram_manager=im, db_manager=db)
            print("✅ BioScannerBot initialized successfully")
        except Exception as e:
            print(f"⚠️  BioScannerBot initialization warning: {e}")
        
        # Test MessageBot
        try:
            bot = MessageBot(account_id=5, instagram_manager=im, db_manager=db)
            print("✅ MessageBot initialized successfully")
        except Exception as e:
            print(f"⚠️  MessageBot initialization warning: {e}")
        
        # Test DataRetrieverBot
        try:
            bot = DataRetrieverBot(account_id=5, instagram_manager=im, db_manager=db)
            print("✅ DataRetrieverBot initialized successfully")
        except Exception as e:
            print(f"⚠️  DataRetrieverBot initialization warning: {e}")
        
        # Test AccountWarmupBot
        try:
            bot = AccountWarmupBot(account_id=5, instagram_manager=im, db_manager=db)
            print("✅ AccountWarmupBot initialized successfully")
        except Exception as e:
            print(f"⚠️  AccountWarmupBot initialization warning: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization test failed: {e}")
        return False

def test_config_files():
    """Test configuration files"""
    print("\nTesting configuration files...")
    
    config_files = [
        'config.py',
        'proxies.txt',
        'templates/accounts/api_run.html',
        'templates/accounts/dashboard.html',
        'templates/accounts/account_list.html'
    ]
    
    all_exist = True
    for file_path in config_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def main():
    """Main test execution"""
    print("="*60)
    print("QUICK SYSTEM TEST")
    print("="*60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Module Imports", test_imports),
        ("Database Connection", test_database),
        ("Instagram Manager", test_instagram_manager),
        ("Bot Initialization", test_bot_initialization),
        ("Configuration Files", test_config_files)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
        print()
    
    # Summary
    print("="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! System is ready to use.")
        return 0
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

