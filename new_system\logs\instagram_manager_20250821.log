2025-08-21 22:34:29,454 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 22:34:30,837 - core.database - INFO - Database connection successful
2025-08-21 22:34:30,838 - core.database - INFO - Database Manager initialized
2025-08-21 22:35:37,853 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 22:35:38,122 - core.database - INFO - Database connection successful
2025-08-21 22:35:38,123 - core.database - INFO - Database Manager initialized
2025-08-21 22:39:22,775 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 22:39:23,061 - core.database - INFO - Database connection successful
2025-08-21 22:39:23,062 - core.database - INFO - Database Manager initialized
2025-08-21 22:39:23,068 - __main__ - INFO - Logging system initialized
2025-08-21 22:39:23,096 - core.database - INFO - Table message_logs created/verified
2025-08-21 22:39:23,112 - core.database - INFO - Table scraped_users created/verified
2025-08-21 22:39:23,126 - core.database - INFO - Table message_logs created/verified
2025-08-21 22:39:23,137 - core.database - INFO - Table scraped_users created/verified
2025-08-21 22:39:23,138 - __main__ - INFO - System initialization complete
2025-08-21 22:39:23,188 - app - INFO - Loading 5 accounts from database
2025-08-21 22:39:23,191 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 22:39:23,192 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 22:39:23,193 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 22:39:23,193 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 22:39:23,194 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 22:39:23,195 - app - INFO - Successfully loaded 5 accounts
2025-08-21 22:39:23,195 - app - INFO - Instagram Management Application initialized
2025-08-21 22:39:23,195 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 22:39:23,225 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-21 22:39:23,225 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-21 22:42:37,782 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 22:42:38,100 - core.database - INFO - Database connection successful
2025-08-21 22:42:38,102 - core.database - INFO - Database Manager initialized
2025-08-21 22:42:38,106 - __main__ - INFO - Logging system initialized
2025-08-21 22:42:38,152 - app - INFO - Loading 5 accounts from database
2025-08-21 22:42:38,153 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 22:42:38,154 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 22:42:38,155 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 22:42:38,155 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 22:42:38,156 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 22:42:38,156 - app - INFO - Successfully loaded 5 accounts
2025-08-21 22:42:38,156 - app - INFO - Instagram Management Application initialized
2025-08-21 22:42:38,156 - app - INFO - Starting Instagram Management System on 127.0.0.1:9001
2025-08-21 22:42:38,172 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9001
2025-08-21 22:42:38,172 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-21 22:42:55,880 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:42:55] "GET /api/v1/bots/status HTTP/1.1" 200 -
2025-08-21 22:43:17,666 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:43:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 22:43:47,655 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:43:47] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 22:43:51,749 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:43:51] "GET /api/v1/bots/status HTTP/1.1" 200 -
2025-08-21 22:44:00,387 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 22:44:00,400 - core.database - INFO - Database connection successful
2025-08-21 22:44:00,402 - core.database - INFO - Database Manager initialized
2025-08-21 22:44:00,402 - bots.data_retriever_bot - INFO - Data Retriever Bot initialized
2025-08-21 22:44:00,403 - bots.data_retriever_bot - INFO - Account ID: 5
2025-08-21 22:44:00,403 - bots.data_retriever_bot - INFO - Starting Data Retriever Bot
2025-08-21 22:44:00,403 - bots.data_retriever_bot - INFO - Proxy assignment for account 5: FAILED
2025-08-21 22:44:00,437 - bots.data_retriever_bot - INFO - Starting data extraction cycle
2025-08-21 22:44:00,458 - bots.data_retriever_bot - INFO - Extracted 0 records from database
2025-08-21 22:44:00,464 - bots.data_retriever_bot - INFO - Extracted 1 records from CSV files
2025-08-21 22:44:00,468 - bots.data_retriever_bot - INFO - Filtered 1 records to 1 unique records
2025-08-21 22:44:00,472 - bots.data_retriever_bot - INFO - Saved 1 records to failed_messages.csv
2025-08-21 22:44:00,472 - bots.data_retriever_bot - INFO - Added 1 new unique records
2025-08-21 22:44:17,658 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:44:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 22:44:47,660 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:44:47] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 22:45:17,655 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:45:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 22:45:47,654 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 22:45:47] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:07:04,955 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:07:05,832 - core.database - INFO - Database connection successful
2025-08-21 23:07:05,834 - core.database - INFO - Database Manager initialized
2025-08-21 23:07:05,845 - __main__ - INFO - Logging system initialized
2025-08-21 23:07:05,891 - app - INFO - Loading 5 accounts from database
2025-08-21 23:07:05,892 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:07:05,892 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:07:05,893 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:07:05,894 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:07:05,894 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:07:05,895 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:07:05,895 - app - INFO - Instagram Management Application initialized
2025-08-21 23:07:05,895 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:07:05,954 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-21 23:07:05,955 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-21 23:07:05,961 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:07:07,586 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:07:07,837 - core.database - INFO - Database connection successful
2025-08-21 23:07:07,838 - core.database - INFO - Database Manager initialized
2025-08-21 23:07:07,848 - __main__ - INFO - Logging system initialized
2025-08-21 23:07:07,892 - app - INFO - Loading 5 accounts from database
2025-08-21 23:07:07,894 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:07:07,895 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:07:07,895 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:07:07,896 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:07:07,898 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:07:07,898 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:07:07,899 - app - INFO - Instagram Management Application initialized
2025-08-21 23:07:07,899 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:07:07,917 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:07:07,922 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:07:07,953 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:07] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-21 23:07:08,057 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:08] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-21 23:07:08,462 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:08] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:08,465 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:08] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:08,469 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:08] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,818 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,820 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,820 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,827 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,829 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,830 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,866 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,967 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,970 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,970 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,984 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,985 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:09,986 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:09] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,076 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,091 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,095 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,097 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,099 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,194 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,194 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,195 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:10,549 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "GET /check_status/?_=1755788830473 HTTP/1.1" 200 -
2025-08-21 23:07:10,663 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:10] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,111 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:07:12,223 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,227 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,231 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,234 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,700 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,701 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,812 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,824 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,826 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,829 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,832 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,833 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,836 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,843 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,854 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,856 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,868 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,870 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,870 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,874 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,886 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,889 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,893 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,896 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:12,898 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:12] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:13,116 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:13] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:07:23,319 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:07:23,330 - core.database - INFO - Database connection successful
2025-08-21 23:07:23,331 - core.database - INFO - Database Manager initialized
2025-08-21 23:07:23,332 - bots.bio_scanner_bot - INFO - Bio Scanner Bot initialized for account 1
2025-08-21 23:07:23,332 - bots.bio_scanner_bot - INFO - Starting Bio Scanner Bot for account 1
2025-08-21 23:07:23,333 - bots.bio_scanner_bot - ERROR - Input CSV file not found: C:/files/account_data_1.csv
2025-08-21 23:07:23,333 - bots.bio_scanner_bot - ERROR - Critical error in bio scanner bot: No user data to process
2025-08-21 23:07:23,334 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:23] "POST /api/v1/bots/bio_scanner HTTP/1.1" 200 -
2025-08-21 23:07:43,122 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:43] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:07:50,248 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-21 23:07:50,333 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,342 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,350 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,351 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,850 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,856 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,931 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,941 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,943 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,946 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,946 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,947 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,952 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,958 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,970 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,971 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,977 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,977 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,982 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,983 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,990 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,991 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,996 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:50,998 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:50] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:51,252 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:51] "GET /check_status/?_=1755788871231 HTTP/1.1" 200 -
2025-08-21 23:07:52,648 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:52] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:07:52,697 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:52] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:52,707 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:52] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:52,713 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:52] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:52,713 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:52] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,185 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,188 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,299 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,320 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,321 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,321 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,323 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,323 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,325 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,343 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,348 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,350 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,352 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,355 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,356 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,367 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,375 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,383 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,384 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,386 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,386 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:53,505 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:53] "GET /check_status/?_=1755788873476 HTTP/1.1" 200 -
2025-08-21 23:07:55,102 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "GET /message_list/ HTTP/1.1" 200 -
2025-08-21 23:07:55,157 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,166 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,169 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,174 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,780 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,782 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,840 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,843 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/img/delete-icon.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,871 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,937 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,944 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,956 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,957 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,958 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,959 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,961 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,965 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,978 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,981 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,985 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,987 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,989 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,992 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:55,996 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:55] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,004 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,006 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,011 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,015 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,017 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,018 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,031 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,036 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,062 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,086 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,106 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,151 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,152 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,168 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:56,213 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:56] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:57,986 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:57] "GET /account_list/ HTTP/1.1" 200 -
2025-08-21 23:07:58,798 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:58] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:58,806 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:58] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:58,808 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:58] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:58,809 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:58] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,340 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,341 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,379 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,380 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,413 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,477 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,483 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,485 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,498 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,501 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,504 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,509 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,511 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,512 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,522 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,525 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,530 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,532 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,535 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,537 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,545 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,564 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,565 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,567 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,568 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,570 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,571 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,586 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,588 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,593 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,597 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,601 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,605 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,607 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,612 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,616 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,619 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,623 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,633 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-21 23:07:59,832 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:07:59] "GET /check_status/?_=1755788879716 HTTP/1.1" 200 -
2025-08-21 23:08:02,728 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:02] "GET /scanning/ HTTP/1.1" 200 -
2025-08-21 23:08:02,803 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:02] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:08:02,808 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:02] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:02,813 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:02] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:08:02,815 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:02] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,284 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,285 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,423 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,433 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,434 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,436 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,437 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,438 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,441 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,453 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,460 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,460 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,463 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,472 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,475 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,476 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,480 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,482 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,485 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,490 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,491 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,497 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,499 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,503 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,508 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,511 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,515 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,520 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,523 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,526 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,528 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,529 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,535 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:03,664 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:03] "GET /check_status/?_=1755788883575 HTTP/1.1" 200 -
2025-08-21 23:08:04,929 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:04] "GET /messaging/ HTTP/1.1" 200 -
2025-08-21 23:08:05,010 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,012 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,019 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,019 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,479 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/css/buttons.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,481 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/img/csv_exp2.png HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,517 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,517 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,543 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,592 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,595 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,609 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,610 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,613 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,615 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,620 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,622 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,632 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,634 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,640 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,643 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,645 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,650 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,650 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,667 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,671 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,674 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,679 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,681 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,684 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,687 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,692 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,695 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,700 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,703 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,706 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,708 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,710 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:05,854 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:05] "GET /check_status/?_=1755788885770 HTTP/1.1" 200 -
2025-08-21 23:08:35,872 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:35] "GET /check_status/?_=1755788885771 HTTP/1.1" 200 -
2025-08-21 23:08:55,735 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:55] "GET /scanning/ HTTP/1.1" 200 -
2025-08-21 23:08:55,830 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:55] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:08:55,837 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:55] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:08:55,838 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:55] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:55,840 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:55] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,549 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,551 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,756 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,763 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,773 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,773 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,774 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,774 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,796 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,803 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,809 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,810 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,810 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,811 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,815 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,821 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,828 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,832 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,838 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,841 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,843 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,850 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,851 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,862 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,864 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,866 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,868 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,872 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,875 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,879 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,887 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,888 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:57,897 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:57] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:08:58,358 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:08:58] "GET /check_status/?_=1755788938226 HTTP/1.1" 200 -
2025-08-21 23:09:06,626 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:09:06] "GET /check_status/?_=1755788885772 HTTP/1.1" 200 -
2025-08-21 23:09:18,020 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:09:18] "GET /api/v1/bots/status HTTP/1.1" 200 -
2025-08-21 23:09:20,322 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:09:20] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-21 23:09:28,629 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:09:28] "GET /check_status/?_=1755788938227 HTTP/1.1" 200 -
2025-08-21 23:09:36,634 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:09:36] "GET /check_status/?_=1755788885773 HTTP/1.1" 200 -
2025-08-21 23:09:58,628 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:09:58] "GET /check_status/?_=1755788938228 HTTP/1.1" 200 -
2025-08-21 23:10:06,637 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:06] "GET /check_status/?_=1755788885774 HTTP/1.1" 200 -
2025-08-21 23:10:28,633 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:28] "GET /check_status/?_=1755788938229 HTTP/1.1" 200 -
2025-08-21 23:10:36,624 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:36] "GET /check_status/?_=1755788885775 HTTP/1.1" 200 -
2025-08-21 23:10:51,518 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:51] "GET /scanning/ HTTP/1.1" 200 -
2025-08-21 23:10:51,570 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:51] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:10:51,583 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:51] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:10:51,586 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:51] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:10:51,587 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:51] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,197 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,201 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,348 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,357 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,367 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,370 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,372 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,373 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,374 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,379 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,387 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,395 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,398 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,403 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,404 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,406 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,411 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,413 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,423 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,428 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,431 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,432 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,440 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,441 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,442 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,449 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,452 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,457 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,462 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,466 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,468 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,472 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,474 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:10:52,944 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:52] "GET /check_status/?_=1755789052779 HTTP/1.1" 200 -
2025-08-21 23:10:53,023 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:10:53] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:11:11,653 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:11] "GET /check_status/?_=1755788885776 HTTP/1.1" 200 -
2025-08-21 23:11:12,797 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:12] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:11:12,847 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:12] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:11:12,849 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:12] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:11:12,869 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:12] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:11:12,869 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:12] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,421 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,422 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,536 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,538 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,547 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,557 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,557 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,559 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,561 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,567 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,569 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,575 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,577 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,595 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,597 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,600 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,604 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,607 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,609 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,614 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,621 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:13,806 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:13] "GET /check_status/?_=1755789073779 HTTP/1.1" 200 -
2025-08-21 23:11:16,108 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "GET /set_scheduler/ HTTP/1.1" 200 -
2025-08-21 23:11:16,238 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,242 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,243 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,747 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,748 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,878 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,883 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,889 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,889 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,893 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,898 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,909 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,915 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,928 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,930 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,932 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,939 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,940 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,945 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,957 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,961 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,963 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,967 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:11:16,969 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:16] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:11:17,187 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:17] "GET /check_status/?_=1755789077167 HTTP/1.1" 200 -
2025-08-21 23:11:47,201 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:11:47] "GET /check_status/?_=1755789077168 HTTP/1.1" 200 -
2025-08-21 23:12:01,632 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:01] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:12:01,699 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:01] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:12:01,710 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:01] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:12:01,713 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:01] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:12:01,714 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:01] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,297 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,298 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,413 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,421 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,424 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,425 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,425 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,426 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,447 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,448 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,451 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,454 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,455 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,457 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,467 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,469 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,471 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,475 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,477 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,479 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,482 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:02,675 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:02] "GET /check_status/?_=1755789122650 HTTP/1.1" 200 -
2025-08-21 23:12:11,665 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:11] "GET /check_status/?_=1755788885777 HTTP/1.1" 200 -
2025-08-21 23:12:28,107 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "GET /csv_files/ HTTP/1.1" 200 -
2025-08-21 23:12:28,188 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,192 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,198 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,201 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,800 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/img/csv_exp2.png HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,803 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/img/folder_export.jpg HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,849 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,850 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,961 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,967 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,970 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,973 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,976 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,978 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,982 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,985 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,991 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,995 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:28,998 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:28] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,002 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,003 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,026 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,029 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,031 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,034 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,036 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,042 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,049 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,057 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,058 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,063 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,064 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,070 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,070 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,076 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,081 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,083 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,087 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,088 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,312 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "GET /check_status/?_=1755789149208 HTTP/1.1" 200 -
2025-08-21 23:12:29,724 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-21 23:12:29,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,787 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:12:29,788 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:29] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,452 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,468 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,470 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,470 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,471 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,473 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,474 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,491 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,495 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,498 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,501 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,505 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,506 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,507 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,512 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,514 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,533 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,533 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,536 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,539 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,539 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:12:30,760 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:12:30] "GET /check_status/?_=1755789150732 HTTP/1.1" 200 -
2025-08-21 23:13:00,771 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:00] "GET /check_status/?_=1755789150733 HTTP/1.1" 200 -
2025-08-21 23:13:11,654 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:11] "GET /check_status/?_=1755788885778 HTTP/1.1" 200 -
2025-08-21 23:13:30,590 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:30] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-21 23:13:30,679 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:30] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:13:30,691 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:30] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:13:30,694 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:30] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:13:30,695 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:30] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,356 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,392 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,459 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,465 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,466 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,469 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,471 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,477 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,479 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,486 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,490 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,494 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,496 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,500 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,501 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,510 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,513 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,516 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,518 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,520 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:13:31,920 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "GET /check_status/?_=1755789211876 HTTP/1.1" 200 -
2025-08-21 23:13:31,996 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:13:31] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:14:01,925 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:01] "GET /check_status/?_=1755789211877 HTTP/1.1" 200 -
2025-08-21 23:14:11,638 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:11] "GET /check_status/?_=1755788885779 HTTP/1.1" 200 -
2025-08-21 23:14:31,543 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:31] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-21 23:14:31,605 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:31] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:14:31,615 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:31] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:14:31,623 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:31] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:14:31,624 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:31] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,166 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,173 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,287 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,296 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,301 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,302 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,302 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,304 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,348 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,348 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,359 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,360 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,360 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,360 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,389 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,390 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,391 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,393 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,397 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,399 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:14:32,700 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "GET /check_status/?_=1755789272675 HTTP/1.1" 200 -
2025-08-21 23:14:32,760 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:14:32] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:15:02,708 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:02] "GET /check_status/?_=1755789272676 HTTP/1.1" 200 -
2025-08-21 23:15:11,633 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:11] "GET /check_status/?_=1755788885780 HTTP/1.1" 200 -
2025-08-21 23:15:32,342 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-21 23:15:32,406 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:15:32,425 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:15:32,426 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:15:32,428 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:32,939 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:32,946 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:32] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,028 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,029 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,030 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,032 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,078 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,080 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,081 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,089 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,091 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,092 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,096 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,111 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,113 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,118 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,119 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,120 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,139 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,140 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:15:33,422 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "GET /check_status/?_=1755789333388 HTTP/1.1" 200 -
2025-08-21 23:15:33,487 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:15:33] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:16:03,424 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:03] "GET /check_status/?_=1755789333389 HTTP/1.1" 200 -
2025-08-21 23:16:07,195 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:16:07,248 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,251 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,260 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,261 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,805 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,807 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,938 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,949 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,951 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,955 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,955 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,958 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,961 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,973 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,976 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,983 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,983 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,984 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,986 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:07,990 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:07] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:08,003 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:08] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:08,007 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:08] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:08,009 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:08] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:08,014 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:08] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:16:08,014 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:08] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:16:08,234 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:08] "GET /check_status/?_=1755789368206 HTTP/1.1" 200 -
2025-08-21 23:16:11,627 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:11] "GET /check_status/?_=1755788885781 HTTP/1.1" 200 -
2025-08-21 23:16:26,677 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:26] "GET /messaging/ HTTP/1.1" 200 -
2025-08-21 23:16:38,235 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:16:38] "GET /check_status/?_=1755789368207 HTTP/1.1" 200 -
2025-08-21 23:17:08,240 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:17:08] "GET /check_status/?_=1755789368208 HTTP/1.1" 200 -
2025-08-21 23:17:11,639 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:17:11] "GET /check_status/?_=1755788885782 HTTP/1.1" 200 -
2025-08-21 23:17:38,236 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:17:38] "GET /check_status/?_=1755789368209 HTTP/1.1" 200 -
2025-08-21 23:18:08,625 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:18:08] "GET /check_status/?_=1755789368210 HTTP/1.1" 200 -
2025-08-21 23:18:11,637 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:18:11] "GET /check_status/?_=1755788885783 HTTP/1.1" 200 -
2025-08-21 23:18:38,624 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:18:38] "GET /check_status/?_=1755789368211 HTTP/1.1" 200 -
2025-08-21 23:19:07,098 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:07] "GET /check_status/?_=1755788885784 HTTP/1.1" 200 -
2025-08-21 23:19:10,544 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:10] "GET /check_status/?_=1755789368212 HTTP/1.1" 200 -
2025-08-21 23:19:13,224 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:19:13,293 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,294 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,306 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,307 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,869 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,871 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,994 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:13,998 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:13] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,004 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,005 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,007 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,007 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,024 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,030 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,030 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,040 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,041 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,045 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,051 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,052 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,058 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,058 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,070 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,071 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:14,410 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:19:14,498 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:14] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:19:21,328 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:19:21,340 - core.database - INFO - Database connection successful
2025-08-21 23:19:21,341 - core.database - INFO - Database Manager initialized
2025-08-21 23:19:21,342 - bots.bio_scanner_bot - INFO - Bio Scanner Bot initialized for account 5
2025-08-21 23:19:21,342 - bots.bio_scanner_bot - INFO - Starting Bio Scanner Bot for account 5
2025-08-21 23:19:21,351 - bots.bio_scanner_bot - INFO - Loaded 1 rows from input CSV
2025-08-21 23:19:21,352 - bots.bio_scanner_bot - ERROR - Missing required columns: ['id1', 'id3', 'title']
2025-08-21 23:19:21,353 - bots.bio_scanner_bot - INFO - Available columns: ['username']
2025-08-21 23:19:21,353 - bots.bio_scanner_bot - ERROR - Critical error in bio scanner bot: No user data to process
2025-08-21 23:19:21,353 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:21] "POST /api/v1/bots/bio_scanner HTTP/1.1" 200 -
2025-08-21 23:19:36,633 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:36] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:19:39,253 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "GET /scanning/ HTTP/1.1" 200 -
2025-08-21 23:19:39,312 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,318 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,329 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,329 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,850 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,851 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,968 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,972 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,981 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,984 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,987 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,988 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,991 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:39,993 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:39] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,004 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,008 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,013 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,015 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,019 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,020 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,034 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,035 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,043 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,046 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,048 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,056 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,062 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,064 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,069 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,071 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,074 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,121 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,122 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,128 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,132 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,135 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:40,250 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:40] "GET /check_status/?_=1755789580132 HTTP/1.1" 200 -
2025-08-21 23:19:42,410 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:19:42,478 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:19:42,483 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:19:42,494 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:19:42,496 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:19:42,930 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:42,931 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:42,993 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:42] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,009 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,010 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,013 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,015 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,016 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,017 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,034 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,038 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,039 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,045 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,047 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,051 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,053 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,058 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,063 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,070 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,071 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,073 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:19:43,217 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:19:43] "GET /check_status/?_=1755789583194 HTTP/1.1" 200 -
2025-08-21 23:20:02,479 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:02] "GET /scanning/ HTTP/1.1" 200 -
2025-08-21 23:20:02,531 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:02] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:20:02,547 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:02] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:20:02,551 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:02] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:20:02,552 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:02] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,394 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,398 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,699 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,706 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,710 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,722 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,724 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,726 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,729 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,731 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,734 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,742 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,747 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,759 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,760 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,761 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,764 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,770 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,772 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,783 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,786 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,791 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,793 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,794 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,795 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,797 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,803 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,811 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,818 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,821 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,824 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,826 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:03,827 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:03] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:20:04,097 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:04] "GET /check_status/?_=1755789603993 HTTP/1.1" 200 -
2025-08-21 23:20:06,640 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:06] "GET /check_status/?_=1755788885786 HTTP/1.1" 200 -
2025-08-21 23:20:34,099 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:20:34] "GET /check_status/?_=1755789603994 HTTP/1.1" 200 -
2025-08-21 23:21:04,636 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:21:04] "GET /check_status/?_=1755789603995 HTTP/1.1" 200 -
2025-08-21 23:21:11,645 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:21:11] "GET /check_status/?_=1755788885787 HTTP/1.1" 200 -
2025-08-21 23:21:34,620 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:21:34] "GET /check_status/?_=1755789603996 HTTP/1.1" 200 -
2025-08-21 23:22:04,632 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:22:04] "GET /check_status/?_=1755********* HTTP/1.1" 200 -
2025-08-21 23:22:11,642 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:22:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:22:34,637 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:22:34] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:23:05,889 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:05] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:23:07,608 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:07] "GET /account_list/ HTTP/1.1" 200 -
2025-08-21 23:23:07,665 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:07] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:23:07,670 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:07] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:23:07,678 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:07] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:23:07,679 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:07] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:23:08,854 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:08] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-21 23:23:08,855 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:08] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-21 23:23:08,885 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:08] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-21 23:23:08,885 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:08] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:08,933 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:08] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,023 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,029 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,038 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,039 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,249 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,254 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,262 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,262 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,275 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,277 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,278 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,284 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,285 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,287 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,295 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,302 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,304 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,320 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,322 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,323 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,326 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,326 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,327 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,337 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,345 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,346 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,352 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,356 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,360 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,361 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,364 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,370 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,370 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-21 23:23:09,776 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:09] "GET /check_status/?_=1755789789613 HTTP/1.1" 200 -
2025-08-21 23:23:11,663 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:11] "GET /check_status/?_=1755789603999 HTTP/1.1" 200 -
2025-08-21 23:23:39,777 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:23:39] "GET /check_status/?_=1755789789614 HTTP/1.1" 200 -
2025-08-21 23:24:09,782 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:24:09] "GET /check_status/?_=1755789789615 HTTP/1.1" 200 -
2025-08-21 23:24:10,169 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:24:10,341 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:24:12,053 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:24:12,451 - core.database - INFO - Database connection successful
2025-08-21 23:24:12,452 - core.database - INFO - Database Manager initialized
2025-08-21 23:24:12,469 - __main__ - INFO - Logging system initialized
2025-08-21 23:24:12,515 - app - INFO - Loading 5 accounts from database
2025-08-21 23:24:12,518 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:24:12,519 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:24:12,520 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:24:12,520 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:24:12,521 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:24:12,521 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:24:12,522 - app - INFO - Instagram Management Application initialized
2025-08-21 23:24:12,522 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:24:12,550 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:24:12,556 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:24:12,629 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:24:12] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:24:33,509 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:24:33,599 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:24:35,481 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:24:35,874 - core.database - INFO - Database connection successful
2025-08-21 23:24:35,875 - core.database - INFO - Database Manager initialized
2025-08-21 23:24:35,880 - __main__ - INFO - Logging system initialized
2025-08-21 23:24:35,923 - app - INFO - Loading 5 accounts from database
2025-08-21 23:24:35,924 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:24:35,924 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:24:35,925 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:24:35,926 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:24:35,927 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:24:35,927 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:24:35,927 - app - INFO - Instagram Management Application initialized
2025-08-21 23:24:35,928 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:24:35,945 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:24:35,952 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:24:39,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:24:39] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:24:56,556 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:24:56,649 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:24:58,576 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:24:58,878 - core.database - INFO - Database connection successful
2025-08-21 23:24:58,879 - core.database - INFO - Database Manager initialized
2025-08-21 23:24:58,884 - __main__ - INFO - Logging system initialized
2025-08-21 23:24:58,938 - app - INFO - Loading 5 accounts from database
2025-08-21 23:24:58,939 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:24:58,940 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:24:58,940 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:24:58,941 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:24:58,941 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:24:58,942 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:24:58,942 - app - INFO - Instagram Management Application initialized
2025-08-21 23:24:58,942 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:24:58,960 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:24:58,965 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:25:00,339 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\test_proxy_strategy.py', reloading
2025-08-21 23:25:00,436 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:25:02,134 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:25:02,415 - core.database - INFO - Database connection successful
2025-08-21 23:25:02,416 - core.database - INFO - Database Manager initialized
2025-08-21 23:25:02,422 - __main__ - INFO - Logging system initialized
2025-08-21 23:25:02,467 - app - INFO - Loading 5 accounts from database
2025-08-21 23:25:02,468 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:25:02,469 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:25:02,469 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:25:02,470 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:25:02,471 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:25:02,471 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:25:02,471 - app - INFO - Instagram Management Application initialized
2025-08-21 23:25:02,471 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:25:02,494 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:25:02,500 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:25:09,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:25:09] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:25:11,641 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:25:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:25:17,706 - core.instagram_manager - INFO - Account 5: Attempting fresh login for adelkatarina123
2025-08-21 23:25:17,727 - core.instagram_manager - INFO - Account 5: Generated 2FA code
2025-08-21 23:25:17,728 - instagrapi - INFO - https://i.instagram.com/api/v1/launcher/sync/
2025-08-21 23:25:18,409 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/launcher/sync/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-21 23:25:19,237 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/login/
2025-08-21 23:25:23,007 - private_request - INFO - adelkatarina123 [400] POST https://i.instagram.com/api/v1/accounts/login/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-21 23:25:23,008 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/two_factor_login/
2025-08-21 23:25:27,345 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/accounts/two_factor_login/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-21 23:25:28,350 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-21 23:25:29,877 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-21 23:25:30,898 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/timeline/
2025-08-21 23:25:33,562 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/feed/timeline/ (269.0.0.18.75, OnePlus 6T Dev)
2025-08-21 23:25:33,577 - core.instagram_manager - INFO - Account 5: Login successful
2025-08-21 23:25:33,579 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-21 23:25:33,605 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:25:33] "POST /api/v1/accounts/5/login HTTP/1.1" 200 -
2025-08-21 23:25:33,609 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:25:33] "POST /api/v1/accounts/5/login HTTP/1.1" 200 -
2025-08-21 23:25:39,790 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:25:39] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:26:09,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:26:09] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:26:11,638 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:26:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:26:39,789 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:26:39] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:27:09,777 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:27:09] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:27:11,641 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:27:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:27:39,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:27:39] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:28:09,789 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:28:09] "GET /check_status/?_=1755789789623 HTTP/1.1" 200 -
2025-08-21 23:28:11,641 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:28:11] "GET /check_status/?_=1755789604004 HTTP/1.1" 200 -
2025-08-21 23:28:39,790 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:28:39] "GET /check_status/?_=1755789789624 HTTP/1.1" 200 -
2025-08-21 23:29:10,632 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:29:10] "GET /check_status/?_=1755789789625 HTTP/1.1" 200 -
2025-08-21 23:29:11,637 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:29:11] "GET /check_status/?_=1755789604005 HTTP/1.1" 200 -
2025-08-21 23:29:40,638 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:29:40] "GET /check_status/?_=1755789789626 HTTP/1.1" 200 -
2025-08-21 23:30:11,648 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:30:11] "GET /check_status/?_=1755789789627 HTTP/1.1" 200 -
2025-08-21 23:30:11,651 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:30:11] "GET /check_status/?_=1755789604006 HTTP/1.1" 200 -
2025-08-21 23:31:11,641 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:31:11] "GET /check_status/?_=1755789604007 HTTP/1.1" 200 -
2025-08-21 23:31:11,644 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:31:11] "GET /check_status/?_=1755789789628 HTTP/1.1" 200 -
2025-08-21 23:31:22,077 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:31:22,187 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:31:23,882 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:31:24,370 - core.database - INFO - Database connection successful
2025-08-21 23:31:24,371 - core.database - INFO - Database Manager initialized
2025-08-21 23:31:24,376 - __main__ - INFO - Logging system initialized
2025-08-21 23:31:24,423 - app - INFO - Loading 5 accounts from database
2025-08-21 23:31:24,425 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:31:24,425 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:31:24,427 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:31:24,428 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:31:24,429 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:31:24,429 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:31:24,429 - app - INFO - Instagram Management Application initialized
2025-08-21 23:31:24,430 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:31:24,449 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:31:24,456 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:32:11,650 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:32:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:32:11,652 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:32:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:33:11,645 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:33:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:33:11,648 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:33:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:33:41,750 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:33:41] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:34:04,629 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:34:04] "GET /check_status/?_=1755789604011 HTTP/1.1" 200 -
2025-08-21 23:34:11,634 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:34:11] "GET /check_status/?_=1755789789631 HTTP/1.1" 200 -
2025-08-21 23:34:34,634 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:34:34] "GET /check_status/?_=1755789604012 HTTP/1.1" 200 -
2025-08-21 23:35:11,648 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:35:11] "GET /check_status/?_=1755789604013 HTTP/1.1" 200 -
2025-08-21 23:35:11,650 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:35:11] "GET /check_status/?_=1755789789632 HTTP/1.1" 200 -
2025-08-21 23:35:21,238 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:35:21,345 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:35:23,294 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:35:23,864 - core.database - INFO - Database connection successful
2025-08-21 23:35:23,865 - core.database - INFO - Database Manager initialized
2025-08-21 23:35:23,870 - __main__ - INFO - Logging system initialized
2025-08-21 23:35:23,916 - app - INFO - Loading 5 accounts from database
2025-08-21 23:35:23,917 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:35:23,918 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:35:23,919 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:35:23,920 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:35:23,921 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:35:23,921 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:35:23,921 - app - INFO - Instagram Management Application initialized
2025-08-21 23:35:23,922 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:35:23,940 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:35:23,946 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:35:42,914 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:35:43,035 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:35:45,139 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:35:45,475 - core.database - INFO - Database connection successful
2025-08-21 23:35:45,476 - core.database - INFO - Database Manager initialized
2025-08-21 23:35:45,481 - __main__ - INFO - Logging system initialized
2025-08-21 23:35:45,526 - app - INFO - Loading 5 accounts from database
2025-08-21 23:35:45,527 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:35:45,528 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:35:45,530 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:35:45,530 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:35:45,531 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:35:45,531 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:35:45,532 - app - INFO - Instagram Management Application initialized
2025-08-21 23:35:45,532 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:35:45,550 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:35:45,555 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:36:11,656 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:36:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:36:11,657 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:36:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:37:11,642 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:37:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:37:11,644 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:37:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:38:11,655 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:38:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:38:11,657 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:38:11] "GET /check_status/?_=1755789604016 HTTP/1.1" 200 -
2025-08-21 23:39:11,650 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:39:11] "GET /check_status/?_=1755789789636 HTTP/1.1" 200 -
2025-08-21 23:39:11,651 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:39:11] "GET /check_status/?_=1755789604017 HTTP/1.1" 200 -
2025-08-21 23:40:11,646 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:40:11] "GET /check_status/?_=1755789604018 HTTP/1.1" 200 -
2025-08-21 23:40:11,647 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:40:11] "GET /check_status/?_=1755789789637 HTTP/1.1" 200 -
2025-08-21 23:41:11,638 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:41:11] "GET /check_status/?_=1755789604019 HTTP/1.1" 200 -
2025-08-21 23:41:11,640 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:41:11] "GET /check_status/?_=1755789789638 HTTP/1.1" 200 -
2025-08-21 23:42:11,639 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:42:11] "GET /check_status/?_=1755789789639 HTTP/1.1" 200 -
2025-08-21 23:42:11,641 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:42:11] "GET /check_status/?_=1755789604020 HTTP/1.1" 200 -
2025-08-21 23:43:11,645 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:43:11] "GET /check_status/?_=1755789789640 HTTP/1.1" 200 -
2025-08-21 23:43:11,647 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:43:11] "GET /check_status/?_=1755789604021 HTTP/1.1" 200 -
2025-08-21 23:44:11,636 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:44:11] "GET /check_status/?_=1755789789641 HTTP/1.1" 200 -
2025-08-21 23:44:11,638 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:44:11] "GET /check_status/?_=1755789604022 HTTP/1.1" 200 -
2025-08-21 23:45:11,659 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:45:11] "GET /check_status/?_=1755789789642 HTTP/1.1" 200 -
2025-08-21 23:45:11,661 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:45:11] "GET /check_status/?_=1755789604023 HTTP/1.1" 200 -
2025-08-21 23:46:11,657 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:46:11] "GET /check_status/?_=1755789604024 HTTP/1.1" 200 -
2025-08-21 23:46:11,659 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:46:11] "GET /check_status/?_=1755789789643 HTTP/1.1" 200 -
2025-08-21 23:47:11,633 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:47:11] "GET /check_status/?_=1755789604025 HTTP/1.1" 200 -
2025-08-21 23:47:11,635 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:47:11] "GET /check_status/?_=1755789789644 HTTP/1.1" 200 -
2025-08-21 23:48:11,653 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:48:11] "GET /check_status/?_=1755789789645 HTTP/1.1" 200 -
2025-08-21 23:48:11,656 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:48:11] "GET /check_status/?_=1755789604026 HTTP/1.1" 200 -
2025-08-21 23:49:11,651 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:11] "GET /check_status/?_=1755789789646 HTTP/1.1" 200 -
2025-08-21 23:49:11,653 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:11] "GET /check_status/?_=1755789604027 HTTP/1.1" 200 -
2025-08-21 23:49:53,794 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:53] "GET /check_status/?_=1755789789647 HTTP/1.1" 200 -
2025-08-21 23:49:56,564 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:56] "GET /scanning/ HTTP/1.1" 200 -
2025-08-21 23:49:57,028 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,029 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,038 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,039 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,853 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,854 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,982 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:57,990 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:57] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,002 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,003 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,005 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,005 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,018 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,019 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,027 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,033 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,036 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,043 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,045 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,052 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,055 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,066 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,069 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,073 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,073 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,076 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,077 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,087 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,091 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,101 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,102 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,102 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,107 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,109 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,111 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,118 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:49:58,476 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:49:58] "GET /check_status/?_=1755791398347 HTTP/1.1" 200 -
2025-08-21 23:50:06,366 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:06] "GET /csv_files/ HTTP/1.1" 200 -
2025-08-21 23:50:06,422 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:06] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:06,429 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:06] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:50:06,431 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:06] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-21 23:50:06,433 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:06] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,018 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/img/csv_exp2.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,019 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/img/folder_export.jpg HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,055 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,055 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,136 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,142 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,159 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,161 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,161 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,163 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,165 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,170 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,188 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,190 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,190 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,193 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,194 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,195 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,218 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,228 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,228 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,230 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,230 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,232 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,235 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,248 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,252 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,254 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,258 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,259 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,263 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,266 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,271 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,274 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,276 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,283 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:07,483 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:07] "GET /check_status/?_=1755791407388 HTTP/1.1" 200 -
2025-08-21 23:50:09,362 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:50:09,419 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:09,428 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:50:09,437 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:09,438 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:09,906 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:09,907 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:09] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,023 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,030 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,037 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,041 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,047 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,048 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,048 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,050 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,055 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,064 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,066 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,073 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,079 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,081 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,083 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,085 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,090 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,092 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,098 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:10,294 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:10] "GET /check_status/?_=1755791410257 HTTP/1.1" 200 -
2025-08-21 23:50:11,623 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:11] "GET /check_status/?_=1755789604028 HTTP/1.1" 200 -
2025-08-21 23:50:40,303 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:40] "GET /check_status/?_=1755791410258 HTTP/1.1" 200 -
2025-08-21 23:50:51,172 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:50:51,246 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,250 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,263 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,263 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,796 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,800 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,873 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,895 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,897 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,897 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,900 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,900 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,902 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,923 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,924 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,934 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,934 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,934 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,937 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,942 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,944 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,951 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,997 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:51,999 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:51] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:52,000 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:52] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:52,231 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:52] "GET /check_status/?_=1755791452197 HTTP/1.1" 200 -
2025-08-21 23:50:52,316 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:52] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:53,721 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:53] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:50:53,771 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:53] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:53,776 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:53] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:50:53,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:53] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:53,781 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:53] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,303 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,304 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,338 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,360 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,362 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,365 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,371 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,373 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,374 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,385 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,394 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,401 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,405 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,410 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,413 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,413 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,419 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,421 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,425 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,430 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:50:54,698 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "GET /check_status/?_=1755791454669 HTTP/1.1" 200 -
2025-08-21 23:50:54,776 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:50:54] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:51:11,640 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:51:11] "GET /check_status/?_=1755789604029 HTTP/1.1" 200 -
2025-08-21 23:51:24,709 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:51:24] "GET /check_status/?_=1755791454670 HTTP/1.1" 200 -
2025-08-21 23:51:54,702 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:51:54] "GET /check_status/?_=1755791454671 HTTP/1.1" 200 -
2025-08-21 23:52:11,625 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:52:11] "GET /check_status/?_=1755789604030 HTTP/1.1" 200 -
2025-08-21 23:52:24,707 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:52:24] "GET /check_status/?_=1755791454672 HTTP/1.1" 200 -
2025-08-21 23:52:54,707 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:52:54] "GET /check_status/?_=1755791454673 HTTP/1.1" 200 -
2025-08-21 23:53:07,872 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:07] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:53:08,001 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,004 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,008 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,009 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,550 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,554 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,627 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,630 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,633 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,638 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,639 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,645 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,647 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,651 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,657 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,661 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,666 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,679 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,679 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,680 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,683 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,686 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,687 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,700 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,700 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:08,994 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:08] "GET /check_status/?_=1755791588950 HTTP/1.1" 200 -
2025-08-21 23:53:09,142 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:09] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:53:11,634 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:11] "GET /check_status/?_=1755789604031 HTTP/1.1" 200 -
2025-08-21 23:53:25,195 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:53:25,322 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,329 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,331 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,332 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,802 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,802 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,914 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,918 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,924 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,925 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,926 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,928 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,937 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,943 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,947 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,956 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,957 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,959 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,961 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,966 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,970 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,972 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,983 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,991 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:53:25,994 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:25] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:53:26,224 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:26] "GET /check_status/?_=1755791606193 HTTP/1.1" 200 -
2025-08-21 23:53:26,295 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:26] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-21 23:53:56,227 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:53:56] "GET /check_status/?_=1755791606194 HTTP/1.1" 200 -
2025-08-21 23:54:11,643 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:54:11] "GET /check_status/?_=1755789604032 HTTP/1.1" 200 -
2025-08-21 23:54:26,226 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:54:26] "GET /check_status/?_=1755791606195 HTTP/1.1" 200 -
2025-08-21 23:54:56,223 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:54:56] "GET /check_status/?_=1755791606196 HTTP/1.1" 200 -
2025-08-21 23:55:11,627 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:55:11] "GET /check_status/?_=1755789604033 HTTP/1.1" 200 -
2025-08-21 23:55:26,235 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:55:26] "GET /check_status/?_=1755791606197 HTTP/1.1" 200 -
2025-08-21 23:55:56,232 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:55:56] "GET /check_status/?_=1755791606198 HTTP/1.1" 200 -
2025-08-21 23:56:09,377 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:56:09] "GET /check_status/?_=1755789604034 HTTP/1.1" 200 -
2025-08-21 23:56:26,624 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:56:26] "GET /check_status/?_=1755791606199 HTTP/1.1" 200 -
2025-08-21 23:56:34,108 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:56:34] "GET /check_status/?_=1755789604035 HTTP/1.1" 200 -
2025-08-21 23:56:56,626 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:56:56] "GET /check_status/?_=1755791606200 HTTP/1.1" 200 -
2025-08-21 23:57:04,105 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:04] "GET /check_status/?_=1755789604036 HTTP/1.1" 200 -
2025-08-21 23:57:34,106 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:34] "GET /check_status/?_=1755789604037 HTTP/1.1" 200 -
2025-08-21 23:57:42,363 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:57:42,490 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:57:44,283 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:57:44,556 - core.database - INFO - Database connection successful
2025-08-21 23:57:44,557 - core.database - INFO - Database Manager initialized
2025-08-21 23:57:44,562 - __main__ - INFO - Logging system initialized
2025-08-21 23:57:44,618 - app - INFO - Loading 5 accounts from database
2025-08-21 23:57:44,619 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:57:44,619 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:57:44,620 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:57:44,622 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:57:44,622 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:57:44,623 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:57:44,623 - app - INFO - Instagram Management Application initialized
2025-08-21 23:57:44,623 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:57:44,641 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:57:44,647 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:57:48,800 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\database.py', reloading
2025-08-21 23:57:48,906 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:57:50,654 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:57:50,997 - core.database - INFO - Database connection successful
2025-08-21 23:57:50,998 - core.database - INFO - Database Manager initialized
2025-08-21 23:57:51,007 - __main__ - INFO - Logging system initialized
2025-08-21 23:57:51,053 - app - INFO - Loading 5 accounts from database
2025-08-21 23:57:51,054 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:57:51,055 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:57:51,056 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:57:51,057 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:57:51,057 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:57:51,058 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:57:51,058 - app - INFO - Instagram Management Application initialized
2025-08-21 23:57:51,058 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:57:51,106 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:57:51,112 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:57:51,265 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:51] "GET /api_run/ HTTP/1.1" 200 -
2025-08-21 23:57:51,751 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:51] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-21 23:57:51,753 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:51] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:57:51,754 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:51] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-21 23:57:51,757 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:51] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,521 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,527 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,711 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,725 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,726 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,727 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,729 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,729 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,734 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,751 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,763 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,768 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,770 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,771 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,773 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,774 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,787 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,788 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,793 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,804 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-21 23:57:52,805 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:52] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-21 23:57:53,215 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:57:53] "GET /check_status/?_=1755791873193 HTTP/1.1" 200 -
2025-08-21 23:58:11,640 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:58:11] "GET /check_status/?_=1755791606201 HTTP/1.1" 200 -
2025-08-21 23:58:17,952 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-21 23:58:18,043 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:58:19,751 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:58:20,015 - core.database - INFO - Database connection successful
2025-08-21 23:58:20,017 - core.database - INFO - Database Manager initialized
2025-08-21 23:58:20,021 - __main__ - INFO - Logging system initialized
2025-08-21 23:58:20,067 - app - INFO - Loading 5 accounts from database
2025-08-21 23:58:20,069 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:58:20,070 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:58:20,070 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:58:20,070 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:58:20,071 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:58:20,073 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:58:20,073 - app - INFO - Instagram Management Application initialized
2025-08-21 23:58:20,073 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:58:20,092 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:58:20,097 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:58:23,222 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:58:23] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:58:24,252 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\database.py', reloading
2025-08-21 23:58:24,357 - werkzeug - INFO -  * Restarting with stat
2025-08-21 23:58:26,153 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-21 23:58:26,511 - core.database - INFO - Database connection successful
2025-08-21 23:58:26,513 - core.database - INFO - Database Manager initialized
2025-08-21 23:58:26,521 - __main__ - INFO - Logging system initialized
2025-08-21 23:58:26,571 - app - INFO - Loading 5 accounts from database
2025-08-21 23:58:26,572 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-21 23:58:26,573 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-21 23:58:26,574 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-21 23:58:26,575 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-21 23:58:26,575 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-21 23:58:26,576 - app - INFO - Successfully loaded 5 accounts
2025-08-21 23:58:26,576 - app - INFO - Instagram Management Application initialized
2025-08-21 23:58:26,576 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-21 23:58:26,604 - werkzeug - WARNING -  * Debugger is active!
2025-08-21 23:58:26,609 - werkzeug - INFO -  * Debugger PIN: 129-************-08-21 23:58:53,229 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:58:53] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:59:11,642 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:59:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:59:23,223 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:59:23] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-21 23:59:53,233 - werkzeug - INFO - 127.0.0.1 - - [21/Aug/2025 23:59:53] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-22 00:00:11,639 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 00:00:11] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-22 00:00:23,625 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 00:00:23] "GET /check_status/?_=1755791873198 HTTP/1.1" 200 -
2025-08-22 00:00:53,629 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 00:00:53] "GET /check_status/?_=1755791873199 HTTP/1.1" 200 -
2025-08-22 00:01:11,646 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 00:01:11] "GET /check_status/?_=1755791606204 HTTP/1.1" 200 -
2025-08-22 00:02:11,641 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 00:02:11] "GET /check_status/?_=1755791873200 HTTP/1.1" 200 -
2025-08-22 00:02:11,644 - werkzeug - INFO - 127.0.0.1 - - [22/Aug/2025 00:02:11] "GET /check_status/?_=1755791606205 HTTP/1.1" 200 -
2025-08-22 00:02:16,237 - werkzeug - INFO -  * Detected change in 'E:\\instaadmin\\new_system\\core\\instagram_manager.py', reloading
2025-08-22 00:02:16,340 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:02:21,622 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:02:53,580 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:02:58,965 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:08:46,612 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:08:51,924 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:17:13,136 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:19:32,988 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:20:03,051 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:20:45,643 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:21:38,146 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:23:23,963 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:29:50,832 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:30:37,399 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:35:28,069 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:37:15,496 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:40:55,122 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:41:24,176 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:43:41,178 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:44:23,693 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:46:28,477 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:48:10,550 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:49:16,444 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:51:40,729 - werkzeug - INFO -  * Restarting with stat
2025-08-22 00:53:05,013 - werkzeug - INFO -  * Restarting with stat
2025-08-22 01:04:19,134 - werkzeug - INFO -  * Restarting with stat
2025-08-22 01:13:00,455 - werkzeug - INFO -  * Restarting with stat
2025-08-22 01:14:00,306 - werkzeug - INFO -  * Restarting with stat
2025-08-22 01:14:07,793 - werkzeug - INFO -  * Restarting with stat
