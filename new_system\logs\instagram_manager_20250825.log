2025-08-25 09:18:29,192 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:18:29,652 - core.database - INFO - Database connection successful
2025-08-25 09:18:29,654 - core.database - INFO - Database Manager initialized
2025-08-25 09:18:29,669 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:18:29,682 - core.database - INFO - Database connection successful
2025-08-25 09:18:29,683 - core.database - INFO - Database Manager initialized
2025-08-25 09:18:29,696 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:18:29,698 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:18:29,700 - core.instagram_manager - ERROR - Error getting user info for instagram: FbSearchMixin.search_users() got an unexpected keyword argument 'count'
2025-08-25 09:19:01,253 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:01,564 - core.database - INFO - Database connection successful
2025-08-25 09:19:01,565 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:01,570 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:01,584 - core.database - INFO - Database connection successful
2025-08-25 09:19:01,585 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:01,599 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:19:01,600 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:19:01,601 - core.instagram_manager - ERROR - Error getting user info for instagram: FbSearchMixin.search_users() got an unexpected keyword argument 'count'
2025-08-25 09:19:20,927 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:21,221 - core.database - INFO - Database connection successful
2025-08-25 09:19:21,222 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:21,227 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:21,239 - core.database - INFO - Database connection successful
2025-08-25 09:19:21,240 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:21,256 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:19:21,257 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:19:21,258 - core.instagram_manager - ERROR - Error getting user info for instagram: FbSearchMixin.search_users() got an unexpected keyword argument 'count'
2025-08-25 09:19:53,479 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:53,770 - core.database - INFO - Database connection successful
2025-08-25 09:19:53,771 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:53,773 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:53,787 - core.database - INFO - Database connection successful
2025-08-25 09:19:53,787 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:53,802 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:19:53,804 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:19:56,916 - public_request - INFO - [None] [200] GET https://www.instagram.com/api/v1/users/web_profile_info/?username=instagram
2025-08-25 09:19:56,924 - instagrapi - ERROR - extract_user_gql() got an unexpected keyword argument 'update_headers'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 203, in user_info_by_username
    user = self.user_info_by_username_gql(username)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 155, in user_info_by_username_gql
    data = extract_user_gql(json.loads(self.public_request(f'https://www.instagram.com/api/v1/users/web_profile_info/?username={username}', headers=temporary_public_headers))['data']['user'], update_headers=update_headers)
TypeError: extract_user_gql() got an unexpected keyword argument 'update_headers'
2025-08-25 09:19:57,937 - instagrapi - INFO - https://i.instagram.com/api/v1/users/instagram/usernameinfo/
2025-08-25 09:19:59,433 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/instagram/usernameinfo/ (269.*********, OnePlus 6T Dev)
2025-08-25 09:19:59,447 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:19:59,461 - core.database - INFO - Database connection successful
2025-08-25 09:19:59,462 - core.database - INFO - Database Manager initialized
2025-08-25 09:19:59,474 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:19:59,475 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:20:00,480 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 09:20:01,842 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 09:21:24,438 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:21:24,718 - core.database - INFO - Database connection successful
2025-08-25 09:21:24,720 - core.database - INFO - Database Manager initialized
2025-08-25 09:21:24,724 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:21:24,739 - core.database - INFO - Database connection successful
2025-08-25 09:21:24,740 - core.database - INFO - Database Manager initialized
2025-08-25 09:21:24,753 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:21:24,754 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:21:25,758 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 09:21:26,820 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 09:21:28,342 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 09:21:28,343 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 09:21:29,369 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 09:21:30,729 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 09:21:55,564 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:21:55,773 - core.database - INFO - Database connection successful
2025-08-25 09:21:55,774 - core.database - INFO - Database Manager initialized
2025-08-25 09:21:55,777 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 09:21:55,787 - core.database - INFO - Database connection successful
2025-08-25 09:21:55,788 - core.database - INFO - Database Manager initialized
2025-08-25 09:21:55,789 - bots.bio_scanner_bot - INFO - Bio Scanner Bot initialized for account 5
2025-08-25 09:21:55,789 - bots.bio_scanner_bot - INFO - Starting Bio Scanner Bot for account 5
2025-08-25 09:21:55,800 - bots.bio_scanner_bot - INFO - Loaded 3 rows from input CSV
2025-08-25 09:21:55,808 - bots.bio_scanner_bot - INFO - Valid data rows: 3
2025-08-25 09:21:55,808 - bots.bio_scanner_bot - INFO - Total users to process: 3
2025-08-25 09:21:55,817 - bots.bio_scanner_bot - INFO - Loaded 1 existing results
2025-08-25 09:21:55,818 - bots.bio_scanner_bot - INFO - Users remaining to process: 3
2025-08-25 09:21:55,832 - bots.bio_scanner_bot - INFO - Retrieved credentials for account: adelkatarina123
2025-08-25 09:21:55,832 - bots.bio_scanner_bot - INFO - Setting up fresh login for account adelkatarina123
2025-08-25 09:21:55,834 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 09:21:55,835 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 09:21:55,835 - bots.bio_scanner_bot - INFO - Fresh login successful for account adelkatarina123
2025-08-25 09:21:55,836 - bots.bio_scanner_bot - INFO - Processing batch 1/100 (3 profiles)
2025-08-25 09:21:57,859 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 09:21:58,993 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 09:22:00,551 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 09:22:00,552 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 09:22:01,563 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 09:22:02,685 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 09:22:02,710 - bots.bio_scanner_bot - INFO - Profile retrieved: instagram | Followers: 694469990
2025-08-25 09:22:04,724 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 09:22:05,764 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=cristiano (269.*********, OnePlus 6T Dev)
2025-08-25 09:22:07,076 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22173560420%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 09:22:07,077 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 09:22:08,083 - instagrapi - INFO - https://i.instagram.com/api/v1/users/173560420/info/
2025-08-25 09:22:09,140 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/173560420/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 09:22:09,159 - bots.bio_scanner_bot - INFO - Profile retrieved: cristiano | Followers: 663017430
2025-08-25 09:22:11,181 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 09:22:11,932 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=leomessi (269.*********, OnePlus 6T Dev)
2025-08-25 09:22:13,216 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22427553890%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 09:22:13,217 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 09:22:14,227 - instagrapi - INFO - https://i.instagram.com/api/v1/users/427553890/info/
2025-08-25 09:22:14,951 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/427553890/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 09:22:14,974 - bots.bio_scanner_bot - INFO - Profile retrieved: leomessi | Followers: 506266466
2025-08-25 09:22:14,994 - bots.bio_scanner_bot - INFO - Saved 3 new results to C:/files/instagram_profiles_data_5.csv
2025-08-25 09:22:14,997 - bots.bio_scanner_bot - INFO - Final processed data saved to: C:/files/bio_5.csv
2025-08-25 09:22:14,999 - bots.bio_scanner_bot - INFO - Batch 1 completed - Success: 3, Errors: 0
2025-08-25 09:22:15,000 - bots.bio_scanner_bot - INFO - Progress file removed - all users processed
2025-08-25 09:22:15,000 - bots.bio_scanner_bot - INFO - === Bio Scanning Complete ===
2025-08-25 09:22:15,000 - bots.bio_scanner_bot - INFO - Total time: 19.21 seconds
2025-08-25 09:22:15,000 - bots.bio_scanner_bot - INFO - Batches processed: 1
2025-08-25 09:22:15,000 - bots.bio_scanner_bot - INFO - Profiles processed: 3
2025-08-25 09:22:15,001 - bots.bio_scanner_bot - INFO - Successful: 3
2025-08-25 09:22:15,001 - bots.bio_scanner_bot - INFO - Failed: 0
2025-08-25 10:12:44,616 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:12:44,834 - core.database - INFO - Database connection successful
2025-08-25 10:12:44,834 - core.database - INFO - Database Manager initialized
2025-08-25 10:12:44,838 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:12:44,850 - core.database - INFO - Database connection successful
2025-08-25 10:12:44,851 - core.database - INFO - Database Manager initialized
2025-08-25 10:12:44,851 - bots.bio_scanner_bot - INFO - Bio Scanner Bot initialized for account 5
2025-08-25 10:12:44,852 - bots.bio_scanner_bot - INFO - Starting Bio Scanner Bot for account 5
2025-08-25 10:12:44,858 - bots.bio_scanner_bot - INFO - Loaded 3 rows from input CSV
2025-08-25 10:12:44,861 - bots.bio_scanner_bot - INFO - Valid data rows: 3
2025-08-25 10:12:44,862 - bots.bio_scanner_bot - INFO - Total users to process: 3
2025-08-25 10:12:44,864 - bots.bio_scanner_bot - INFO - Loaded 4 existing results
2025-08-25 10:12:44,865 - bots.bio_scanner_bot - INFO - Users remaining to process: 0
2025-08-25 10:12:44,866 - bots.bio_scanner_bot - INFO - All users already processed
2025-08-25 10:21:33,004 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:21:33,311 - core.database - INFO - Database connection successful
2025-08-25 10:21:33,315 - core.database - INFO - Database Manager initialized
2025-08-25 10:21:33,317 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:21:33,330 - core.database - INFO - Database connection successful
2025-08-25 10:21:33,331 - core.database - INFO - Database Manager initialized
2025-08-25 10:21:39,354 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:21:39,650 - core.database - INFO - Database connection successful
2025-08-25 10:21:39,651 - core.database - INFO - Database Manager initialized
2025-08-25 10:21:39,657 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:21:39,670 - core.database - INFO - Database connection successful
2025-08-25 10:21:39,671 - core.database - INFO - Database Manager initialized
2025-08-25 10:21:39,684 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 10:21:39,685 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:21:40,696 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:21:41,887 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:21:43,663 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:21:43,663 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:21:44,669 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 10:21:46,128 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:22:12,258 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:22:12,546 - core.database - INFO - Database connection successful
2025-08-25 10:22:12,547 - core.database - INFO - Database Manager initialized
2025-08-25 10:22:12,547 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:22:12,559 - core.database - INFO - Database connection successful
2025-08-25 10:22:12,563 - core.database - INFO - Database Manager initialized
2025-08-25 10:22:12,574 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 10:22:12,576 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:22:13,582 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:22:14,529 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:22:16,079 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:22:16,079 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:22:17,092 - instagrapi - INFO - https://i.instagram.com/api/v1/users/27983543105/info/
2025-08-25 10:22:17,741 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/27983543105/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:22:18,747 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:22:19,250 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:22:20,262 - instagrapi - INFO - https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/
2025-08-25 10:22:21,363 - private_request - INFO - None [200] POST https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:22:21,365 - core.instagram_manager - INFO - Message sent to adelkatarina123 from account 5
2025-08-25 10:22:54,370 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:22:54,633 - core.database - INFO - Database connection successful
2025-08-25 10:22:54,633 - core.database - INFO - Database Manager initialized
2025-08-25 10:22:54,637 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:22:54,651 - core.database - INFO - Database connection successful
2025-08-25 10:22:54,651 - core.database - INFO - Database Manager initialized
2025-08-25 10:22:54,667 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 10:22:54,667 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:22:55,677 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:22:56,624 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:22:56,628 - instagrapi - INFO - user_medias_gql: 5, None
2025-08-25 10:22:58,196 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22id%22%3A********%2C%22first%22%3A5%2C%22after%22%3Anull%7D&query_hash=e7e2f4da4b02303f74f0841279e52d76
2025-08-25 10:22:58,196 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 797, in user_medias
    medias = self.user_medias_gql(user_id, amount, sleep)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 520, in user_medias_gql
    medias_page, end_cursor = self.user_medias_paginated_gql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 467, in user_medias_paginated_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:22:59,225 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/user/********/
2025-08-25 10:23:00,105 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/feed/user/********/?max_id=&count=5&rank_token=27983543105_d5c7fe19-7de2-4303-b8e9-c61243088b3d&ranked_content=true (269.*********, OnePlus 6T Dev)
2025-08-25 10:23:00,115 - instagrapi - ERROR - 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 678, in user_medias_v1
    medias_page, next_max_id = self.user_medias_paginated_v1(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in user_medias_paginated_v1
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in <listcomp>
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\extractors.py", line 77, in extract_media_v1
    return Media(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-25 10:23:01,143 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:23:02,064 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:23:02,065 - core.instagram_manager - ERROR - Account 5: Error searching users - 'UserShort' object has no attribute 'is_verified'
2025-08-25 10:27:46,809 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:27:47,064 - core.database - INFO - Database connection successful
2025-08-25 10:27:47,064 - core.database - INFO - Database Manager initialized
2025-08-25 10:27:47,070 - __main__ - INFO - Logging system initialized
2025-08-25 10:27:47,117 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:27:47,131 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:27:47,143 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:27:47,155 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:27:47,155 - __main__ - INFO - System initialization complete
2025-08-25 10:27:47,204 - app - INFO - Loading 5 accounts from database
2025-08-25 10:27:47,205 - core.instagram_manager - INFO - Added account 1: kelisegoddard
2025-08-25 10:27:47,206 - core.instagram_manager - INFO - Added account 2: lilit_hheaton
2025-08-25 10:27:47,207 - core.instagram_manager - INFO - Added account 3: maliaangela26
2025-08-25 10:27:47,207 - core.instagram_manager - INFO - Added account 4: whitelakeonion
2025-08-25 10:27:47,209 - core.instagram_manager - INFO - Added account 5: adelkatarina123
2025-08-25 10:27:47,209 - app - INFO - Successfully loaded 5 accounts
2025-08-25 10:27:47,209 - app - INFO - Instagram Management Application initialized
2025-08-25 10:27:47,209 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 10:27:47,234 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 10:27:47,238 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 10:27:53,705 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:53] "[32mGET / HTTP/1.1[0m" 302 -
2025-08-25 10:27:53,748 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:53] "GET /login/ HTTP/1.1" 200 -
2025-08-25 10:27:54,366 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,376 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,382 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,393 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,393 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,394 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,396 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,396 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,396 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,431 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,432 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,433 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,434 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,435 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,435 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,478 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,480 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,481 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,482 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,482 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,482 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,588 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:27:54,589 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:27:54] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:28:06,307 - app - INFO - User admin logged in successfully
2025-08-25 10:28:06,312 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:28:06] "[32mPOST /login/ HTTP/1.1[0m" 302 -
2025-08-25 10:28:06,399 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:28:06,401 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:28:06] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:28:07,689 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:28:07] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-25 10:28:55,059 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:28:55,060 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:28:55] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:28:58,276 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:28:58,277 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:28:58] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:29:00,946 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:29:00,946 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:29:00] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:29:31,734 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:29:32,004 - core.database - INFO - Database connection successful
2025-08-25 10:29:32,006 - core.database - INFO - Database Manager initialized
2025-08-25 10:29:32,006 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:29:32,018 - core.database - INFO - Database connection successful
2025-08-25 10:29:32,018 - core.database - INFO - Database Manager initialized
2025-08-25 10:29:32,031 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (new session)
2025-08-25 10:29:32,033 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:29:33,035 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:29:34,033 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:29:35,606 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:29:35,606 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:29:36,615 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 10:29:37,546 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:29:37,552 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:29:37,568 - core.database - INFO - Database connection successful
2025-08-25 10:29:37,569 - core.database - INFO - Database Manager initialized
2025-08-25 10:29:37,583 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (new session)
2025-08-25 10:29:37,584 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:29:37,585 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:29:37,586 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (new session)
2025-08-25 10:30:10,020 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:30:10,021 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:30:10] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:30:19,517 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:19,823 - core.database - INFO - Database connection successful
2025-08-25 10:30:19,824 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:19,824 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:19,838 - core.database - INFO - Database connection successful
2025-08-25 10:30:19,840 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:19,852 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:30:19,853 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:30:20,862 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:30:21,891 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:30:23,508 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:30:23,509 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:30:24,519 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 10:30:25,700 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:30:25,708 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:25,721 - core.database - INFO - Database connection successful
2025-08-25 10:30:25,721 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:25,737 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:30:25,737 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:30:25,737 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:25,743 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:30:25,743 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:30:33,868 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:34,143 - core.database - INFO - Database connection successful
2025-08-25 10:30:34,144 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:34,145 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:34,161 - core.database - INFO - Database connection successful
2025-08-25 10:30:34,162 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:34,170 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:30:34,175 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:30:34,175 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:30:35,187 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:30:35,949 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:30:37,507 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:30:40,864 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:30:44,239 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:30:45,249 - instagrapi - INFO - https://i.instagram.com/api/v1/users/27983543105/info/
2025-08-25 10:30:45,776 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/27983543105/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:30:46,788 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:30:47,345 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:30:48,357 - instagrapi - INFO - https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/
2025-08-25 10:30:48,976 - private_request - INFO - None [200] POST https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:30:48,976 - core.instagram_manager - INFO - Message sent to adelkatarina123 from account 5
2025-08-25 10:30:56,535 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:30:56,537 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:30:56] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:30:59,208 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:59,486 - core.database - INFO - Database connection successful
2025-08-25 10:30:59,486 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:59,489 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:30:59,502 - core.database - INFO - Database connection successful
2025-08-25 10:30:59,502 - core.database - INFO - Database Manager initialized
2025-08-25 10:30:59,515 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:30:59,515 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:30:59,515 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:31:00,533 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:31:01,536 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:31:01,537 - instagrapi - INFO - user_medias_gql: 5, None
2025-08-25 10:31:03,086 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22id%22%3A********%2C%22first%22%3A5%2C%22after%22%3Anull%7D&query_hash=e7e2f4da4b02303f74f0841279e52d76
2025-08-25 10:31:03,086 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 797, in user_medias
    medias = self.user_medias_gql(user_id, amount, sleep)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 520, in user_medias_gql
    medias_page, end_cursor = self.user_medias_paginated_gql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 467, in user_medias_paginated_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:31:04,103 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/user/********/
2025-08-25 10:31:04,866 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/feed/user/********/?max_id=&count=5&rank_token=27983543105_d5c7fe19-7de2-4303-b8e9-c61243088b3d&ranked_content=true (269.*********, OnePlus 6T Dev)
2025-08-25 10:31:04,873 - instagrapi - ERROR - 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 678, in user_medias_v1
    medias_page, next_max_id = self.user_medias_paginated_v1(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in user_medias_paginated_v1
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in <listcomp>
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\extractors.py", line 77, in extract_media_v1
    return Media(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-25 10:31:05,883 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:31:06,884 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:31:06,884 - core.instagram_manager - ERROR - Account 5: Error searching users - 'UserShort' object has no attribute 'is_verified'
2025-08-25 10:34:08,505 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:08,715 - core.database - INFO - Database connection successful
2025-08-25 10:34:08,716 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:22,376 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:22,576 - core.database - INFO - Database connection successful
2025-08-25 10:34:22,579 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:22,581 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:22,595 - core.database - INFO - Database connection successful
2025-08-25 10:34:22,596 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:22,596 - __main__ - INFO - Bio Scanner Bot initialized for account 5
2025-08-25 10:34:22,597 - __main__ - INFO - Starting Bio Scanner Bot for account 5
2025-08-25 10:34:22,602 - __main__ - INFO - Loaded 3 rows from input CSV
2025-08-25 10:34:22,605 - __main__ - INFO - Valid data rows: 3
2025-08-25 10:34:22,607 - __main__ - INFO - Total users to process: 3
2025-08-25 10:34:22,609 - __main__ - INFO - Loaded 4 existing results
2025-08-25 10:34:22,609 - __main__ - INFO - Users remaining to process: 0
2025-08-25 10:34:22,610 - __main__ - INFO - All users already processed
2025-08-25 10:34:31,692 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:34:31,693 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:34:31] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:34:34,524 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:34,809 - core.database - INFO - Database connection successful
2025-08-25 10:34:34,810 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:34,812 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:34,826 - core.database - INFO - Database connection successful
2025-08-25 10:34:34,826 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:34,840 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:34:34,840 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:34:34,842 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:34:35,844 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:34:36,809 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:34:38,367 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:34:38,367 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 305, in user_info
    user = self.user_info_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 234, in user_info_gql
    self.username_from_user_id_gql(user_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 111, in username_from_user_id_gql
    return self.user_short_gql(user_id).username
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\user.py", line 84, in user_short_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:34:39,379 - instagrapi - INFO - https://i.instagram.com/api/v1/users/27983543105/info/
2025-08-25 10:34:39,941 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/27983543105/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:34:40,950 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:34:41,433 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:34:42,442 - instagrapi - INFO - https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/
2025-08-25 10:34:43,028 - private_request - INFO - None [200] POST https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:34:43,028 - core.instagram_manager - INFO - Message sent to adelkatarina123 from account 5
2025-08-25 10:34:53,862 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:54,128 - core.database - INFO - Database connection successful
2025-08-25 10:34:54,128 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:54,130 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:34:54,144 - core.database - INFO - Database connection successful
2025-08-25 10:34:54,144 - core.database - INFO - Database Manager initialized
2025-08-25 10:34:54,157 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:34:54,157 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:34:54,158 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:34:55,164 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:34:55,996 - private_request - INFO - None [403] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:34:55,996 - core.instagram_manager - ERROR - Error getting media for instagram: login_required
2025-08-25 10:34:57,006 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:34:57,298 - private_request - INFO - None [403] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:34:57,298 - core.instagram_manager - ERROR - Account 5: Error searching users - login_required
2025-08-25 10:40:18,264 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:40:18,544 - core.database - INFO - Database connection successful
2025-08-25 10:40:18,545 - core.database - INFO - Database Manager initialized
2025-08-25 10:40:18,546 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:40:18,557 - core.database - INFO - Database connection successful
2025-08-25 10:40:18,558 - core.database - INFO - Database Manager initialized
2025-08-25 10:40:18,571 - core.instagram_manager - INFO - Account 5: No existing session file found
2025-08-25 10:40:18,571 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (new session)
2025-08-25 10:40:18,572 - core.instagram_manager - INFO - Account 5: Attempting fresh login for adelkatarina123
2025-08-25 10:40:18,621 - core.instagram_manager - INFO - Account 5: Generated 2FA code
2025-08-25 10:40:18,622 - instagrapi - INFO - https://i.instagram.com/api/v1/launcher/sync/
2025-08-25 10:40:19,935 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/launcher/sync/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:21,154 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/login/
2025-08-25 10:40:24,588 - private_request - INFO - adelkatarina123 [400] POST https://i.instagram.com/api/v1/accounts/login/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:24,589 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/two_factor_login/
2025-08-25 10:40:28,689 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/accounts/two_factor_login/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:29,701 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-25 10:40:30,024 - private_request - INFO - adelkatarina123 [400] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:31,036 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 10:40:31,469 - private_request - INFO - adelkatarina123 [200] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:32,483 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 10:40:34,027 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:35,031 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-25 10:40:36,107 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:37,120 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/timeline/
2025-08-25 10:40:38,811 - private_request - INFO - adelkatarina123 [200] POST https://i.instagram.com/api/v1/feed/timeline/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:38,815 - core.instagram_manager - INFO - Account 5: Login successful
2025-08-25 10:40:39,831 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:40:40,550 - private_request - INFO - adelkatarina123 [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:42,149 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:40:45,455 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:40:48,762 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:40:49,770 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 10:40:50,630 - private_request - INFO - adelkatarina123 [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:40:50,638 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:40:50,648 - core.database - INFO - Database connection successful
2025-08-25 10:40:50,649 - core.database - INFO - Database Manager initialized
2025-08-25 10:40:50,665 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:40:50,665 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:40:50,667 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:40:50,668 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:40:50,669 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:41:12,015 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:41:12,301 - core.database - INFO - Database connection successful
2025-08-25 10:41:12,302 - core.database - INFO - Database Manager initialized
2025-08-25 10:41:12,304 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:41:12,317 - core.database - INFO - Database connection successful
2025-08-25 10:41:12,317 - core.database - INFO - Database Manager initialized
2025-08-25 10:41:12,330 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:41:12,330 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:41:12,332 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:41:13,335 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:41:14,071 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:15,654 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:41:19,017 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:41:22,326 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%*************%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:41:23,340 - instagrapi - INFO - https://i.instagram.com/api/v1/users/27983543105/info/
2025-08-25 10:41:23,878 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/27983543105/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:24,894 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:41:25,457 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:26,468 - instagrapi - INFO - https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/
2025-08-25 10:41:27,062 - private_request - INFO - None [200] POST https://i.instagram.com/api/v1/direct_v2/threads/broadcast/text/ (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:27,062 - core.instagram_manager - INFO - Message sent to adelkatarina123 from account 5
2025-08-25 10:41:34,054 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:41:34,350 - core.database - INFO - Database connection successful
2025-08-25 10:41:34,351 - core.database - INFO - Database Manager initialized
2025-08-25 10:41:34,352 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:41:34,365 - core.database - INFO - Database connection successful
2025-08-25 10:41:34,367 - core.database - INFO - Database Manager initialized
2025-08-25 10:41:34,381 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:41:34,382 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:41:34,383 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:41:35,393 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:41:36,472 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:36,473 - instagrapi - INFO - user_medias_gql: 5, None
2025-08-25 10:41:38,415 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22id%22%3A********%2C%22first%22%3A5%2C%22after%22%3Anull%7D&query_hash=e7e2f4da4b02303f74f0841279e52d76
2025-08-25 10:41:38,415 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 797, in user_medias
    medias = self.user_medias_gql(user_id, amount, sleep)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 520, in user_medias_gql
    medias_page, end_cursor = self.user_medias_paginated_gql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 467, in user_medias_paginated_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:41:39,423 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/user/********/
2025-08-25 10:41:40,188 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/feed/user/********/?max_id=&count=5&rank_token=27983543105_da259dc5-a534-48b6-a5ae-8bbda19683a2&ranked_content=true (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:40,195 - instagrapi - ERROR - 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 678, in user_medias_v1
    medias_page, next_max_id = self.user_medias_paginated_v1(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in user_medias_paginated_v1
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in <listcomp>
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\extractors.py", line 77, in extract_media_v1
    return Media(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-25 10:41:41,201 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:41:42,007 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:41:42,010 - core.instagram_manager - ERROR - Account 5: Error searching users - 'UserShort' object has no attribute 'is_verified'
2025-08-25 10:42:34,167 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:42:34,168 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:42:34] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:43:57,297 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:43:57,578 - core.database - INFO - Database connection successful
2025-08-25 10:43:57,579 - core.database - INFO - Database Manager initialized
2025-08-25 10:43:57,581 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:43:57,593 - core.database - INFO - Database connection successful
2025-08-25 10:43:57,594 - core.database - INFO - Database Manager initialized
2025-08-25 10:43:57,607 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:43:57,607 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:43:57,607 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:43:58,621 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:43:59,759 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:43:59,763 - instagrapi - INFO - user_medias_gql: 5, None
2025-08-25 10:44:01,342 - public_request - INFO - [None] [200] GET https://www.instagram.com/graphql/query/?variables=%7B%22id%22%3A********%2C%22first%22%3A5%2C%22after%22%3Anull%7D&query_hash=e7e2f4da4b02303f74f0841279e52d76
2025-08-25 10:44:01,344 - instagrapi - ERROR - 'data'
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 797, in user_medias
    medias = self.user_medias_gql(user_id, amount, sleep)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 520, in user_medias_gql
    medias_page, end_cursor = self.user_medias_paginated_gql(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 467, in user_medias_paginated_gql
    data = self.public_graphql_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\public.py", line 281, in public_graphql_request
    return body_json["data"]
KeyError: 'data'
2025-08-25 10:44:02,357 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/user/********/
2025-08-25 10:44:03,260 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/feed/user/********/?max_id=&count=5&rank_token=27983543105_da259dc5-a534-48b6-a5ae-8bbda19683a2&ranked_content=true (269.*********, OnePlus 6T Dev)
2025-08-25 10:44:03,265 - instagrapi - ERROR - 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 678, in user_medias_v1
    medias_page, next_max_id = self.user_medias_paginated_v1(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in user_medias_paginated_v1
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\mixins\media.py", line 655, in <listcomp>
    return ([extract_media_v1(media) for media in medias], next_max_id)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\instagrapi\extractors.py", line 77, in extract_media_v1
    return Media(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pydantic\main.py", line 253, in __init__
    validated_self = self.__pydantic_validator__.validate_python(data, self_instance=self)
pydantic_core._pydantic_core.ValidationError: 1 validation error for Media
clips_metadata.mashup_info
  Field required [type=missing, input_value={'clips_creation_entry_po...raction_settings': None}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-08-25 10:44:03,269 - core.instagram_manager - INFO - Account 5: Retrieved 0 media via GraphQL
2025-08-25 10:44:04,280 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:44:05,247 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:44:38,706 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:44:38,988 - core.database - INFO - Database connection successful
2025-08-25 10:44:38,989 - core.database - INFO - Database Manager initialized
2025-08-25 10:44:38,989 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:44:39,001 - core.database - INFO - Database connection successful
2025-08-25 10:44:39,002 - core.database - INFO - Database Manager initialized
2025-08-25 10:44:39,013 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:44:39,013 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:44:39,018 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:44:40,029 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:44:41,161 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:44:42,718 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:44:46,051 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:44:49,381 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:44:50,383 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 10:44:51,236 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:44:51,238 - core.instagram_manager - INFO - Account 5: Retrieved basic profile info for instagram
2025-08-25 10:44:51,238 - core.instagram_manager - WARNING - Account 5: Error processing media unknown: 'dict' object has no attribute 'pk'
2025-08-25 10:44:52,248 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:44:52,985 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:45:28,133 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:45:28,414 - core.database - INFO - Database connection successful
2025-08-25 10:45:28,415 - core.database - INFO - Database Manager initialized
2025-08-25 10:45:28,417 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:45:28,429 - core.database - INFO - Database connection successful
2025-08-25 10:45:28,430 - core.database - INFO - Database Manager initialized
2025-08-25 10:45:28,445 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:45:28,445 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:45:28,446 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 10:45:29,454 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:45:30,808 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:45:32,347 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:45:35,887 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:45:39,197 - public_request - INFO - [None] [401] GET https://www.instagram.com/graphql/query/?variables=%7B%22user_id%22%3A%22********%22%2C%22include_reel%22%3Atrue%7D&query_hash=ad99dd9d3646cc3c0dda65debcd266a7
2025-08-25 10:45:40,204 - instagrapi - INFO - https://i.instagram.com/api/v1/users/********/info/
2025-08-25 10:45:41,009 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/********/info/?is_prefetch=false&entry_point=self_profile&from_module=self_profile&is_app_start=False (269.*********, OnePlus 6T Dev)
2025-08-25 10:45:41,012 - core.instagram_manager - INFO - Account 5: Retrieved basic profile info for instagram
2025-08-25 10:45:42,020 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 10:45:42,735 - private_request - INFO - None [200] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 10:47:38,753 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:47:39,037 - core.database - INFO - Database connection successful
2025-08-25 10:47:39,039 - core.database - INFO - Database Manager initialized
2025-08-25 10:47:39,044 - __main__ - INFO - Logging system initialized
2025-08-25 10:47:39,079 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:47:39,091 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:47:39,104 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:47:39,116 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:47:39,116 - __main__ - INFO - System initialization complete
2025-08-25 10:47:39,164 - app - INFO - Loading 5 accounts from database
2025-08-25 10:47:39,165 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 10:47:39,165 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 10:47:39,166 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 10:47:39,167 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 10:47:39,167 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 10:47:39,168 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 10:47:39,168 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 10:47:39,170 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 10:47:39,172 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:47:39,173 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:47:39,173 - app - INFO - Successfully loaded 5 accounts
2025-08-25 10:47:39,174 - app - INFO - Instagram Management Application initialized
2025-08-25 10:47:39,174 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 10:47:39,188 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 10:47:39,189 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 10:47:52,372 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:47:52,374 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:47:52] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:48:16,068 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:48:16,069 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:48:16] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:48:20,364 - app - ERROR - Error in dashboard: Encountered unknown tag 'endcomment'.
2025-08-25 10:48:20,364 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:48:20] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:49:30,467 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:49:30,729 - core.database - INFO - Database connection successful
2025-08-25 10:49:30,730 - core.database - INFO - Database Manager initialized
2025-08-25 10:49:30,736 - __main__ - INFO - Logging system initialized
2025-08-25 10:49:30,765 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:49:30,777 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:49:30,790 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:49:30,803 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:49:30,804 - __main__ - INFO - System initialization complete
2025-08-25 10:49:30,851 - app - INFO - Loading 5 accounts from database
2025-08-25 10:49:30,853 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 10:49:30,853 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 10:49:30,853 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 10:49:30,855 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 10:49:30,856 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 10:49:30,856 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 10:49:30,857 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 10:49:30,858 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 10:49:30,859 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:49:30,859 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:49:30,859 - app - INFO - Successfully loaded 5 accounts
2025-08-25 10:49:30,861 - app - INFO - Instagram Management Application initialized
2025-08-25 10:49:30,861 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 10:49:30,871 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 10:49:30,873 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 10:49:37,658 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:37] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:49:38,023 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,025 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,028 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,427 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,428 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,429 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,430 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,430 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,431 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,461 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,467 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,469 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,470 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,470 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,473 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,476 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,488 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,490 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,494 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,496 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,497 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,504 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,512 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,514 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:49:38,981 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:38] "GET /check_status/?_=1756090178925 HTTP/1.1" 200 -
2025-08-25 10:49:41,088 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "GET /api_run/ HTTP/1.1" 200 -
2025-08-25 10:49:41,220 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,231 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,238 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,243 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/img/transformation.png HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,694 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,697 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,831 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,835 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,843 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,853 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,853 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,855 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,863 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,865 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,873 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,877 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,883 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,886 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,890 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,892 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,893 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,899 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,910 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,911 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:49:41,911 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:41] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:49:42,113 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:49:42] "GET /check_status/?_=1756090182088 HTTP/1.1" 200 -
2025-08-25 10:51:35,295 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:51:35,578 - core.database - INFO - Database connection successful
2025-08-25 10:51:35,579 - core.database - INFO - Database Manager initialized
2025-08-25 10:51:35,586 - __main__ - INFO - Logging system initialized
2025-08-25 10:51:35,612 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:51:35,625 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:51:35,639 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:51:35,651 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:51:35,651 - __main__ - INFO - System initialization complete
2025-08-25 10:51:35,697 - app - INFO - Loading 5 accounts from database
2025-08-25 10:51:35,697 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 10:51:35,697 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 10:51:35,697 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 10:51:35,697 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 10:51:35,702 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 10:51:35,702 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 10:51:35,702 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 10:51:35,702 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 10:51:35,705 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:51:35,706 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:51:35,706 - app - INFO - Successfully loaded 5 accounts
2025-08-25 10:51:35,706 - app - INFO - Instagram Management Application initialized
2025-08-25 10:51:35,707 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 10:51:35,717 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 10:51:35,717 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 10:51:42,126 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:51:42] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:52:12,131 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:52:12] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:52:42,126 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:52:42] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:53:12,126 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:12] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:53:42,118 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:42] "GET /check_status/?_=1756090182096 HTTP/1.1" 200 -
2025-08-25 10:53:46,085 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "GET /dashboard/ HTTP/1.1" 200 -
2025-08-25 10:53:46,521 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,542 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,548 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,549 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,716 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,816 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,821 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,826 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,828 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,830 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,831 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,856 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,865 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,866 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,866 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,869 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,870 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,871 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,878 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,885 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,893 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,894 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,895 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:46,895 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:46] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:47,086 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:47] "GET /check_status/?_=1756090427058 HTTP/1.1" 200 -
2025-08-25 10:53:48,876 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:48] "GET /message_list/ HTTP/1.1" 200 -
2025-08-25 10:53:48,933 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:48] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:48,942 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:48] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:48,957 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:48] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:48,958 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:48] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,515 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,516 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,553 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,560 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/img/delete-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,582 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,637 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,644 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,647 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,656 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,658 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,659 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,665 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,668 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,671 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,681 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,682 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,690 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,691 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,695 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,700 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,703 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,706 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,710 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,715 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,724 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,726 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,728 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,730 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,734 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,735 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,850 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,850 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,851 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,853 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,855 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:49,856 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:49] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:51,528 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:51] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:53:53,029 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:53:53,162 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,166 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,175 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,176 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,610 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,628 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,678 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,678 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,678 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,735 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,760 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,763 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,771 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,783 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,788 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,789 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,804 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,804 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,809 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,813 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,817 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,821 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,824 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,829 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,830 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,835 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,837 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,843 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,847 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:53:53,849 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,857 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,860 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,862 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,866 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,868 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,869 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,885 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,888 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,889 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,891 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,893 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,897 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,901 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:53,908 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:53] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:54,086 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:54] "GET /check_status/?_=1756090433971 HTTP/1.1" 200 -
2025-08-25 10:53:56,271 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "GET /messaging/ HTTP/1.1" 200 -
2025-08-25 10:53:56,367 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,372 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,380 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,380 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,799 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "GET /static/assets/plugins/datatable/css/buttons.bootstrap5.min.css HTTP/1.1" 200 -
2025-08-25 10:53:56,802 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/img/csv_exp2.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,830 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,830 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,857 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,922 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,943 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,944 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,945 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,950 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,951 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,952 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,957 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,973 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,980 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,982 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,988 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,989 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:56,996 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:56] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,006 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,009 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,013 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,014 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,015 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,016 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,021 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,032 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,033 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,038 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,040 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,043 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,044 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:53:57,230 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:53:57] "GET /check_status/?_=1756090437119 HTTP/1.1" 200 -
2025-08-25 10:54:02,618 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:02] "GET /message_list/ HTTP/1.1" 200 -
2025-08-25 10:54:02,679 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:02] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:54:02,692 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:02] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:54:02,696 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:02] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:54:02,700 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:02] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,304 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,306 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,325 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/img/delete-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,329 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,367 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,451 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,460 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,462 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,464 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,464 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,468 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,470 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,479 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,483 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,490 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,493 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,494 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,496 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,499 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,500 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,513 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,519 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,521 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,524 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,524 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,528 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,532 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,540 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,544 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,548 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,550 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,552 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,553 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,561 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,565 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:03,567 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:03] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:04,817 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:04] "GET /check_status/?_=1756090444589 HTTP/1.1" 200 -
2025-08-25 10:54:05,881 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:05] "GET /scanning/ HTTP/1.1" 200 -
2025-08-25 10:54:06,000 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,008 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,015 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,016 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,451 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,451 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,567 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,571 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,575 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,587 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,587 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,590 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,601 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,603 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,604 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,606 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,615 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,631 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,634 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,635 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,641 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,643 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,645 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,651 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,652 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,656 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,660 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,662 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,665 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,670 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,674 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,678 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,683 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,686 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,688 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,692 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,694 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:54:06,841 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:54:06] "GET /check_status/?_=1756090446763 HTTP/1.1" 200 -
2025-08-25 10:54:47,980 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:54:48,199 - core.database - INFO - Database connection successful
2025-08-25 10:54:48,200 - core.database - INFO - Database Manager initialized
2025-08-25 10:54:48,203 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:54:48,216 - core.database - INFO - Database connection successful
2025-08-25 10:54:48,217 - core.database - INFO - Database Manager initialized
2025-08-25 10:54:48,218 - __main__ - INFO - Bio Scanner Bot initialized for account 5
2025-08-25 10:54:48,218 - __main__ - INFO - Starting Bio Scanner Bot for account 5
2025-08-25 10:54:48,224 - __main__ - INFO - Loaded 3 rows from input CSV
2025-08-25 10:54:48,225 - __main__ - INFO - Valid data rows: 3
2025-08-25 10:54:48,229 - __main__ - INFO - Total users to process: 3
2025-08-25 10:54:48,234 - __main__ - INFO - Loaded 4 existing results
2025-08-25 10:54:48,235 - __main__ - INFO - Users remaining to process: 0
2025-08-25 10:54:48,235 - __main__ - INFO - All users already processed
2025-08-25 10:55:25,358 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 10:55:25,617 - core.database - INFO - Database connection successful
2025-08-25 10:55:25,618 - core.database - INFO - Database Manager initialized
2025-08-25 10:55:25,624 - __main__ - INFO - Logging system initialized
2025-08-25 10:55:25,645 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:55:25,657 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:55:25,669 - core.database - INFO - Table message_logs created/verified
2025-08-25 10:55:25,680 - core.database - INFO - Table scraped_users created/verified
2025-08-25 10:55:25,681 - __main__ - INFO - System initialization complete
2025-08-25 10:55:25,730 - app - INFO - Loading 5 accounts from database
2025-08-25 10:55:25,731 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 10:55:25,732 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 10:55:25,733 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 10:55:25,733 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 10:55:25,734 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 10:55:25,734 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 10:55:25,735 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 10:55:25,736 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 10:55:25,737 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 10:55:25,738 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 10:55:25,738 - app - INFO - Successfully loaded 5 accounts
2025-08-25 10:55:25,738 - app - INFO - Instagram Management Application initialized
2025-08-25 10:55:25,739 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 10:55:25,750 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 10:55:25,750 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 10:55:36,854 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:36] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:55:58,184 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:55:58,580 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,615 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,618 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,620 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,771 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,774 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,822 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,823 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,868 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,888 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,904 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,905 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,909 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,919 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:55:58,920 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,921 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,942 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,958 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,958 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,959 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,960 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,960 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,965 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,976 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,981 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,988 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,990 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,992 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:58,993 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:58] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,000 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,002 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,006 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,012 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,015 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,020 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,021 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,023 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,025 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,029 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,031 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,036 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,039 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,042 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 10:55:59,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:55:59] "GET /check_status/?_=1756090559200 HTTP/1.1" 200 -
2025-08-25 10:56:29,301 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:56:29] "GET /check_status/?_=1756090559201 HTTP/1.1" 200 -
2025-08-25 10:56:59,296 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:56:59] "GET /check_status/?_=1756********* HTTP/1.1" 200 -
2025-08-25 10:57:29,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:57:29] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:57:59,288 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:57:59] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:58:29,293 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:29] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:58:36,962 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:36] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:58:37,018 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,033 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,036 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,040 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,598 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,599 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,707 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,724 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,725 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,726 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,726 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,727 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,764 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:58:37,767 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,769 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,770 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,772 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,774 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,782 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,790 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,791 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,799 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,806 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,811 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,813 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,818 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,819 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,821 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,823 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,830 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,832 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,838 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,842 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,843 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,847 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,850 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,854 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,856 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,858 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,864 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,869 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,872 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,875 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:37,877 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:37] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:38,147 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:38] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:58:38,259 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:38] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:40,565 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:40] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:58:40,623 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:40] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:58:40,625 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:40] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:58:40,632 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:40] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:40,634 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:40] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,099 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,100 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,129 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,130 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,157 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,214 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,221 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,222 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,228 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,230 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,234 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,237 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,242 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:58:41,243 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,249 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,252 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,259 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,260 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,264 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,265 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,268 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,274 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,282 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,288 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,292 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,296 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,298 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,300 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,306 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,309 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,313 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,319 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,323 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,325 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,326 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,335 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,337 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,339 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,343 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:41,628 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:58:41,747 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:41] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,291 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:58:46,335 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,340 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,347 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,351 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,828 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,830 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,849 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,853 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,876 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,883 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,884 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,884 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,888 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,892 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,896 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,900 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:58:46,905 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,917 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,920 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,921 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,922 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,930 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,931 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,933 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,940 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,943 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,945 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,952 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,953 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,955 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,961 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,966 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,975 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,979 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,986 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,987 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,989 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,990 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,995 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:46,996 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:46] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:47,006 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:47] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:47,013 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:47] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:47,014 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:47] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:47,015 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:47] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 10:58:47,255 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:47] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:58:47,364 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:47] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 10:58:53,258 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:53] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:58:53,339 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:53] "GET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1" 200 -
2025-08-25 10:58:53,343 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:53] "GET /static/assets/demo/demo.css HTTP/1.1" 200 -
2025-08-25 10:58:53,345 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:53] "GET /static/assets/img/inst.png HTTP/1.1" 200 -
2025-08-25 10:58:53,346 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:53] "GET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1" 200 -
2025-08-25 10:58:54,171 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/img/csv-import.png HTTP/1.1" 200 -
2025-08-25 10:58:54,182 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/img/clear.png HTTP/1.1" 200 -
2025-08-25 10:58:54,188 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/img/all-delete.png HTTP/1.1" 200 -
2025-08-25 10:58:54,193 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/core/jquery.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,201 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/core/popper.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,236 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,312 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/moment.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,335 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/sweetalert2.js HTTP/1.1" 200 -
2025-08-25 10:58:54,363 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,396 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1" 200 -
2025-08-25 10:58:54,401 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1" 200 -
2025-08-25 10:58:54,425 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,468 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1" 200 -
2025-08-25 10:58:54,473 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,523 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,559 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,589 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1" 200 -
2025-08-25 10:58:54,677 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/nouislider.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,709 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/arrive.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,734 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/chartist.min.js HTTP/1.1" 200 -
2025-08-25 10:58:54,775 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1" 200 -
2025-08-25 10:58:54,801 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1" 200 -
2025-08-25 10:58:54,846 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:54] "GET /static/assets/demo/demo.js HTTP/1.1" 200 -
2025-08-25 10:58:55,140 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,156 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1" 200 -
2025-08-25 10:58:55,193 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,199 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,222 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,232 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,272 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1" 200 -
2025-08-25 10:58:55,465 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,514 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,541 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,542 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,561 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1" 200 -
2025-08-25 10:58:55,574 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/js/datatables.js HTTP/1.1" 200 -
2025-08-25 10:58:55,601 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/img/eye.png HTTP/1.1" 200 -
2025-08-25 10:58:55,613 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "GET /static/assets/img/edit-icon.png HTTP/1.1" 200 -
2025-08-25 10:58:55,623 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:55] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:58:57,970 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:57] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:58:58,448 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:58] "GET /check_status/?_=1756090738336 HTTP/1.1" 200 -
2025-08-25 10:58:58,784 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:58:58] "GET /static/assets/img/favicon.png HTTP/1.1" 200 -
2025-08-25 10:59:17,275 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:59:47,267 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:47] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:59:49,309 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:49] "GET /static/assets/img/eye.png HTTP/1.1" 200 -
2025-08-25 10:59:54,186 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 10:59:54,244 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,263 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,266 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,267 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,797 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,801 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,938 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,939 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,939 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,940 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,941 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,942 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,972 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 10:59:54,974 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,975 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,981 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,982 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,984 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,989 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,993 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:54,998 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:54] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,002 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,003 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,010 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,011 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,016 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,020 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,022 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,030 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,031 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,032 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,037 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,050 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,052 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,054 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,058 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,059 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,062 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,067 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,070 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,074 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,076 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,079 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,083 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 10:59:55,390 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 10:59:55,499 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 10:59:55] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,381 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:00:08,426 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,431 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,446 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,447 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,914 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,918 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,947 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,949 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,965 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,972 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,973 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:08,974 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:08] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,002 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 11:00:09,003 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,005 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,007 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,007 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,009 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,023 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,028 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,037 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,039 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,039 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,043 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,044 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,045 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,053 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,057 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,071 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,076 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,078 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,084 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,089 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,090 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,091 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,096 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,097 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,103 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,108 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,111 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,113 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,119 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,121 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,122 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:09,350 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:00:09,458 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:09] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,384 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:00:16,433 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,437 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,443 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,445 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,906 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,910 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,940 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,941 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,958 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,961 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,963 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,974 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,985 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,988 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,992 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 11:00:16,995 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,996 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:16,998 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:16] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,003 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,006 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,012 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,016 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,021 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,025 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,028 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,032 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,035 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,038 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,043 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,047 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,049 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,052 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,057 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,064 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,068 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,074 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,076 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,081 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,083 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,083 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,084 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,089 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,095 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,101 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,344 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:00:17,433 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:17,880 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:17] "GET /static/assets/img/edit-icon.png HTTP/1.1" 200 -
2025-08-25 11:00:19,226 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:00:19,276 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,279 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,289 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,737 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,741 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,788 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,789 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,816 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,873 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,889 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,890 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,890 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,894 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,895 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,898 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,910 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 11:00:19,912 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,920 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,922 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,923 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,926 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,939 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,940 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,943 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,946 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,948 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,949 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,959 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,963 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,970 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,971 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,971 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,976 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,978 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,979 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,986 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,991 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:19,994 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:19] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:20,000 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:20,002 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:20,006 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:20,011 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:20,013 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:20,224 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:00:20,329 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:20] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:23,964 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:23] "[33mGET /static/assets/img/loading.gif HTTP/1.1[0m" 404 -
2025-08-25 11:00:23,968 - core.instagram_manager - INFO - Account 1: Attempting fresh login for kelisegoddard
2025-08-25 11:00:23,980 - core.instagram_manager - INFO - Account 1: Generated 2FA code
2025-08-25 11:00:23,981 - instagrapi - INFO - https://i.instagram.com/api/v1/launcher/sync/
2025-08-25 11:00:24,569 - private_request - INFO - kelisegoddard [200] POST https://i.instagram.com/api/v1/launcher/sync/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:00:25,179 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/login/
2025-08-25 11:00:27,446 - private_request - INFO - kelisegoddard [400] POST https://i.instagram.com/api/v1/accounts/login/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:00:27,447 - core.instagram_manager - ERROR - Account 1: Login error - We can send you an email to help you get back into your account. If you are sure that the password is correct, then change your IP address, because it is added to the blacklist of the Instagram Server
2025-08-25 11:00:27,448 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:27] "POST /api/v1/accounts/1/login HTTP/1.1" 200 -
2025-08-25 11:00:29,401 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:29] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 11:00:48,098 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:00:48,182 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,183 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,187 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,188 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,936 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "GET /static/assets/img/csv-import.png HTTP/1.1" 200 -
2025-08-25 11:00:48,941 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "GET /static/assets/img/clear.png HTTP/1.1" 200 -
2025-08-25 11:00:48,954 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "GET /static/assets/img/all-delete.png HTTP/1.1" 200 -
2025-08-25 11:00:48,976 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,979 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,980 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:48,984 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:48] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,038 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,115 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[33mGET /static/assets/img/login.png HTTP/1.1[0m" 404 -
2025-08-25 11:00:49,117 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "GET /static/assets/img/eye.png HTTP/1.1" 200 -
2025-08-25 11:00:49,120 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "GET /static/assets/img/edit-icon.png HTTP/1.1" 200 -
2025-08-25 11:00:49,155 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,155 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,158 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,158 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,159 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,161 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,308 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,309 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,310 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,335 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,336 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,446 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,449 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,452 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,453 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,454 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,534 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,537 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,542 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,543 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,544 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,545 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,653 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,654 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,655 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,656 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,662 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,663 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:00:49,806 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:49] "GET /check_status/?_=1756090849605 HTTP/1.1" 200 -
2025-08-25 11:00:50,237 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:00:50] "GET /check_status/?_=1756090820144 HTTP/1.1" 200 -
2025-08-25 11:01:02,258 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:01:02,536 - core.database - INFO - Database connection successful
2025-08-25 11:01:02,536 - core.database - INFO - Database Manager initialized
2025-08-25 11:01:02,543 - __main__ - INFO - Logging system initialized
2025-08-25 11:01:02,570 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:01:02,585 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:01:02,597 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:01:02,611 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:01:02,612 - __main__ - INFO - System initialization complete
2025-08-25 11:01:02,658 - app - INFO - Loading 5 accounts from database
2025-08-25 11:01:02,660 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 11:01:02,660 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 11:01:02,661 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 11:01:02,661 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 11:01:02,662 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 11:01:02,663 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 11:01:02,664 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 11:01:02,664 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 11:01:02,666 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:01:02,666 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:01:02,667 - app - INFO - Successfully loaded 5 accounts
2025-08-25 11:01:02,667 - app - INFO - Instagram Management Application initialized
2025-08-25 11:01:02,667 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 11:01:02,701 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 11:01:02,702 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 11:01:05,203 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:01:05,618 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,633 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,642 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,643 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,831 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,832 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,923 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,924 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,926 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,932 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,937 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,938 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,966 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,973 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,974 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,975 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,977 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,977 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,987 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,989 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:05,992 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:05] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,000 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,003 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,008 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,010 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,013 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,017 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,020 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,021 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,028 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,033 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,038 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,040 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,045 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,046 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,051 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,051 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,055 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,057 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,062 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,067 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,068 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:01:06,366 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "GET /check_status/?_=1756090866276 HTTP/1.1" 200 -
2025-08-25 11:01:06,477 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:06] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:01:36,377 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:01:36] "GET /check_status/?_=17*********** HTTP/1.1" 200 -
2025-08-25 11:02:01,233 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:01] "GET /static/assets/img/clear.png HTTP/1.1" 200 -
2025-08-25 11:02:06,368 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:06] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:02:36,374 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:36] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:02:41,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:02:41,341 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,352 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,358 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,359 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,877 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,880 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,968 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,968 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,980 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,980 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:41,983 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:41] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,007 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,007 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,015 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,016 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,017 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,018 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,024 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,025 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,032 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,035 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,039 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,045 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,047 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,050 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,055 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,061 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,061 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,064 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,064 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,081 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,083 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,084 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,086 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,089 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,091 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,097 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,099 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,104 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,110 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,111 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,113 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:42,427 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:02:42,525 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:42] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:50,555 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:50] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:02:50,607 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:50] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:02:50,610 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:50] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:02:50,614 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:50] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:50,616 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:50] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,135 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,137 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,172 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,174 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,195 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,255 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,265 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,283 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,288 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,301 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,306 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,306 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,323 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,324 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,325 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,326 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,331 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,332 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,345 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,350 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,350 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,355 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,355 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,359 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,362 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,367 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,369 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,375 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,375 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,381 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,384 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,390 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,390 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,394 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,395 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:02:51,625 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "GET /check_status/?_=1756090971545 HTTP/1.1" 200 -
2025-08-25 11:02:51,739 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:51] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:02:57,418 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:02:57,706 - core.database - INFO - Database connection successful
2025-08-25 11:02:57,707 - core.database - INFO - Database Manager initialized
2025-08-25 11:02:57,716 - __main__ - INFO - Logging system initialized
2025-08-25 11:02:57,748 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:02:57,768 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:02:57,785 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:02:57,799 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:02:57,799 - __main__ - INFO - System initialization complete
2025-08-25 11:02:57,842 - app - INFO - Loading 5 accounts from database
2025-08-25 11:02:57,844 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 11:02:57,844 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 11:02:57,845 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 11:02:57,845 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 11:02:57,845 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 11:02:57,845 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 11:02:57,848 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 11:02:57,848 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 11:02:57,850 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:02:57,850 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:02:57,850 - app - INFO - Successfully loaded 5 accounts
2025-08-25 11:02:57,851 - app - INFO - Instagram Management Application initialized
2025-08-25 11:02:57,851 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 11:02:57,861 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 11:02:57,861 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 11:02:59,903 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:02:59] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:03:00,374 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,378 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,385 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,385 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,449 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,450 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,481 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,482 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,505 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,568 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,571 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,585 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,586 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,587 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,590 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,595 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,596 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,611 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,613 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,616 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,618 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,620 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,622 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,640 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,642 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,645 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,651 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,652 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,654 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,662 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,662 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,669 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,672 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,675 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,675 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,681 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,687 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,689 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,695 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,700 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,701 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,703 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,705 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:03:00,934 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:00] "GET /check_status/?_=1756090980854 HTTP/1.1" 200 -
2025-08-25 11:03:01,041 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:01] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:03:30,942 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:03:30] "GET /check_status/?_=1756090980855 HTTP/1.1" 200 -
2025-08-25 11:04:00,948 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:04:00] "GET /check_status/?_=1756090980856 HTTP/1.1" 200 -
2025-08-25 11:04:30,942 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:04:30] "GET /check_status/?_=1756090980857 HTTP/1.1" 200 -
2025-08-25 11:05:00,938 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:05:00] "GET /check_status/?_=1756090980858 HTTP/1.1" 200 -
2025-08-25 11:05:30,943 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:05:30] "GET /check_status/?_=1756090980859 HTTP/1.1" 200 -
2025-08-25 11:05:59,903 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:06:00,180 - core.database - INFO - Database connection successful
2025-08-25 11:06:00,180 - core.database - INFO - Database Manager initialized
2025-08-25 11:06:00,186 - __main__ - INFO - Logging system initialized
2025-08-25 11:06:00,212 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:06:00,222 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:06:00,239 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:06:00,251 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:06:00,251 - __main__ - INFO - System initialization complete
2025-08-25 11:06:00,297 - app - INFO - Loading 5 accounts from database
2025-08-25 11:06:00,299 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 11:06:00,299 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 11:06:00,300 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 11:06:00,300 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 11:06:00,301 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 11:06:00,302 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 11:06:00,302 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 11:06:00,303 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 11:06:00,305 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:06:00,305 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:06:00,305 - app - INFO - Successfully loaded 5 accounts
2025-08-25 11:06:00,306 - app - INFO - Instagram Management Application initialized
2025-08-25 11:06:00,306 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 11:06:00,316 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 11:06:00,318 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 11:06:00,943 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:00] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:06:03,016 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "GET /account_list/ HTTP/1.1" 200 -
2025-08-25 11:06:03,448 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/css/material-dashboard.css?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,448 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/demo/demo.css HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,452 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/css/dataTables.bootstrap5.min.css HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,457 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/img/inst.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,636 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/img/csv-import.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,636 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/img/clear.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,708 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/img/all-delete.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,720 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/core/jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,763 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/core/popper.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,782 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/moment.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,782 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/core/bootstrap-material-design.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,784 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/perfect-scrollbar.jquery.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,792 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/img/eye.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,794 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/img/edit-icon.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,795 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/sweetalert2.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,802 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/jquery.validate.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,803 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/jquery.bootstrap-wizard.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,807 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/bootstrap-selectpicker.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,809 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/bootstrap-datetimepicker.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,815 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,818 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/bootstrap-tagsinput.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,822 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/jasny-bootstrap.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,824 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/fullcalendar.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,831 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/jquery-jvectormap.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,834 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/nouislider.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,839 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/arrive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,842 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/chartist.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,845 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/plugins/bootstrap-notify.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,846 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/material-dashboard.js?v=2.1.2 HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,853 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/demo/demo.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,853 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/jquery.dataTables.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,857 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.bootstrap5.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,858 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/dataTables.buttons.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,862 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/buttons.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,864 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/jszip.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,869 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/pdfmake/pdfmake.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,871 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/pdfmake/vfs_fonts.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,875 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/buttons.html5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,878 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/buttons.print.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,881 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/js/buttons.colVis.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,885 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/dataTables.responsive.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,888 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/plugins/datatable/responsive.bootstrap5.min.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:03,889 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:03] "[36mGET /static/assets/js/datatables.js HTTP/1.1[0m" 304 -
2025-08-25 11:06:04,188 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:06:04,298 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:04] "[36mGET /static/assets/img/favicon.png HTTP/1.1[0m" 304 -
2025-08-25 11:06:08,936 - core.instagram_manager - INFO - Account 4: Attempting fresh login for whitelakeonion
2025-08-25 11:06:08,946 - core.instagram_manager - INFO - Account 4: Generated 2FA code
2025-08-25 11:06:08,948 - instagrapi - INFO - https://i.instagram.com/api/v1/launcher/sync/
2025-08-25 11:06:09,528 - private_request - INFO - whitelakeonion [200] POST https://i.instagram.com/api/v1/launcher/sync/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:10,151 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/login/
2025-08-25 11:06:12,799 - private_request - INFO - whitelakeonion [400] POST https://i.instagram.com/api/v1/accounts/login/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:12,799 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/two_factor_login/
2025-08-25 11:06:17,408 - private_request - INFO - whitelakeonion [200] POST https://i.instagram.com/api/v1/accounts/two_factor_login/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:18,417 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-25 11:06:18,708 - private_request - INFO - whitelakeonion [400] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:19,719 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:06:20,471 - private_request - INFO - whitelakeonion [200] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:21,480 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:06:22,205 - private_request - INFO - whitelakeonion [200] POST https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:23,216 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-25 11:06:23,502 - private_request - INFO - whitelakeonion [400] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:06:23,503 - core.instagram_manager - ERROR - Account 4: Challenge required - challenge_required
2025-08-25 11:06:23,505 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:06:23] "POST /api/v1/accounts/4/login HTTP/1.1" 200 -
2025-08-25 11:07:48,143 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:07:48] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:08:04,194 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:08:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:08:37,462 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:08:37,725 - core.database - INFO - Database connection successful
2025-08-25 11:08:37,725 - core.database - INFO - Database Manager initialized
2025-08-25 11:08:37,734 - __main__ - INFO - Logging system initialized
2025-08-25 11:08:37,767 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:08:37,781 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:08:37,796 - core.database - INFO - Table message_logs created/verified
2025-08-25 11:08:37,805 - core.database - INFO - Table scraped_users created/verified
2025-08-25 11:08:37,805 - __main__ - INFO - System initialization complete
2025-08-25 11:08:37,855 - app - INFO - Loading 5 accounts from database
2025-08-25 11:08:37,855 - core.instagram_manager - INFO - Account 1: No existing session file found
2025-08-25 11:08:37,856 - core.instagram_manager - INFO - Added account 1: kelisegoddard (new session)
2025-08-25 11:08:37,856 - core.instagram_manager - INFO - Account 2: No existing session file found
2025-08-25 11:08:37,857 - core.instagram_manager - INFO - Added account 2: lilit_hheaton (new session)
2025-08-25 11:08:37,858 - core.instagram_manager - INFO - Account 3: No existing session file found
2025-08-25 11:08:37,859 - core.instagram_manager - INFO - Added account 3: maliaangela26 (new session)
2025-08-25 11:08:37,859 - core.instagram_manager - INFO - Account 4: No existing session file found
2025-08-25 11:08:37,860 - core.instagram_manager - INFO - Added account 4: whitelakeonion (new session)
2025-08-25 11:08:37,861 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:08:37,861 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:08:37,862 - app - INFO - Successfully loaded 5 accounts
2025-08-25 11:08:37,863 - app - INFO - Instagram Management Application initialized
2025-08-25 11:08:37,863 - app - INFO - Starting Instagram Management System on 127.0.0.1:9000
2025-08-25 11:08:37,873 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:9000
2025-08-25 11:08:37,873 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-08-25 11:08:41,753 - core.instagram_manager - INFO - Account 4: Attempting fresh login for whitelakeonion
2025-08-25 11:08:41,764 - core.instagram_manager - INFO - Account 4: Generated 2FA code
2025-08-25 11:08:41,764 - instagrapi - INFO - https://i.instagram.com/api/v1/launcher/sync/
2025-08-25 11:08:42,418 - private_request - INFO - whitelakeonion [200] POST https://i.instagram.com/api/v1/launcher/sync/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:43,244 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/login/
2025-08-25 11:08:46,077 - private_request - INFO - whitelakeonion [400] POST https://i.instagram.com/api/v1/accounts/login/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:46,078 - instagrapi - INFO - https://i.instagram.com/api/v1/accounts/two_factor_login/
2025-08-25 11:08:49,466 - private_request - INFO - whitelakeonion [200] POST https://i.instagram.com/api/v1/accounts/two_factor_login/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:50,471 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-25 11:08:50,756 - private_request - INFO - whitelakeonion [400] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:51,769 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:08:52,324 - private_request - INFO - whitelakeonion [200] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:53,335 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:08:54,294 - private_request - INFO - whitelakeonion [200] POST https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:55,309 - instagrapi - INFO - https://i.instagram.com/api/v1/feed/reels_tray/
2025-08-25 11:08:55,622 - private_request - INFO - whitelakeonion [400] POST https://i.instagram.com/api/v1/feed/reels_tray/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:08:55,623 - core.instagram_manager - ERROR - Account 4: Challenge required - challenge_required
2025-08-25 11:08:55,626 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:08:55] "POST /api/v1/accounts/4/login HTTP/1.1" 200 -
2025-08-25 11:09:04,201 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:09:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:09:34,204 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:09:34] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:10:04,188 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:10:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:10:34,200 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:10:34] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:11:04,198 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:11:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:11:06,897 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:07,176 - core.database - INFO - Database connection successful
2025-08-25 11:11:07,181 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:07,182 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:07,196 - core.database - INFO - Database connection successful
2025-08-25 11:11:07,196 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:07,217 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:11:07,218 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:11:08,225 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:11:08,787 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 11:11:09,795 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:11:10,351 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:11:27,289 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:11:27,591 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 11:11:27,591 - core.instagram_manager - ERROR - Error getting user info for instagram: challenge_required
2025-08-25 11:11:27,595 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:27,612 - core.database - INFO - Database connection successful
2025-08-25 11:11:27,614 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:27,630 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:11:27,630 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:11:27,630 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:27,633 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:11:27,635 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:11:34,191 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:11:34] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:11:38,094 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:38,300 - core.database - INFO - Database connection successful
2025-08-25 11:11:38,302 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:38,302 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:38,318 - core.database - INFO - Database connection successful
2025-08-25 11:11:38,319 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:38,319 - __main__ - INFO - Bio Scanner Bot initialized for account 5
2025-08-25 11:11:38,319 - __main__ - INFO - Starting Bio Scanner Bot for account 5
2025-08-25 11:11:38,327 - __main__ - INFO - Loaded 3 rows from input CSV
2025-08-25 11:11:38,330 - __main__ - INFO - Valid data rows: 3
2025-08-25 11:11:38,331 - __main__ - INFO - Total users to process: 3
2025-08-25 11:11:38,334 - __main__ - INFO - Loaded 4 existing results
2025-08-25 11:11:38,334 - __main__ - INFO - Users remaining to process: 0
2025-08-25 11:11:38,335 - __main__ - INFO - All users already processed
2025-08-25 11:11:43,965 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:44,232 - core.database - INFO - Database connection successful
2025-08-25 11:11:44,233 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:44,235 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:11:44,248 - core.database - INFO - Database connection successful
2025-08-25 11:11:44,250 - core.database - INFO - Database Manager initialized
2025-08-25 11:11:44,265 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:11:44,267 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:11:44,269 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 11:11:45,282 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:11:45,879 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 11:11:46,883 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:11:47,363 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:04,190 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:12:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:12:04,261 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:12:04,558 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=adelkatarina123 (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:04,559 - core.instagram_manager - ERROR - Error getting user info for adelkatarina123: challenge_required
2025-08-25 11:12:12,359 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:12:12,632 - core.database - INFO - Database connection successful
2025-08-25 11:12:12,632 - core.database - INFO - Database Manager initialized
2025-08-25 11:12:12,632 - core.instagram_manager - INFO - Instagram Manager initialized
2025-08-25 11:12:12,650 - core.database - INFO - Database connection successful
2025-08-25 11:12:12,651 - core.database - INFO - Database Manager initialized
2025-08-25 11:12:12,661 - core.instagram_manager - INFO - Account 5: Session auto-loaded successfully
2025-08-25 11:12:12,661 - core.instagram_manager - INFO - Added account 5: adelkatarina123 (session already loaded)
2025-08-25 11:12:12,665 - core.instagram_manager - INFO - Account 5: Session loaded successfully
2025-08-25 11:12:13,673 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:12:14,234 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:15,238 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:12:15,554 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:32,445 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:12:32,762 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:32,762 - core.instagram_manager - ERROR - Error getting media for instagram: challenge_required
2025-08-25 11:12:33,773 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:12:34,066 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:34,200 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:12:34] "GET /check_status/?_=1756091164101 HTTP/1.1" 200 -
2025-08-25 11:12:35,072 - instagrapi - INFO - https://i.instagram.com/api/v1/challenge/
2025-08-25 11:12:35,382 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/challenge/ (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:52,253 - instagrapi - INFO - https://i.instagram.com/api/v1/users/search/
2025-08-25 11:12:52,544 - private_request - INFO - None [400] GET https://i.instagram.com/api/v1/users/search/?search_surface=user_search_page&timezone_offset=-14400&count=30&q=instagram (269.*********, OnePlus 6T Dev)
2025-08-25 11:12:52,549 - core.instagram_manager - ERROR - Account 5: Error searching users - challenge_required
2025-08-25 11:13:04,205 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:13:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:13:34,201 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:13:34] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:14:04,277 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:14:04] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:14:34,273 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:14:34] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:15:32,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:15:32] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:16:32,290 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:16:32] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:17:32,291 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:17:32] "GET /check_status/?_=************* HTTP/1.1" 200 -
2025-08-25 11:18:32,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:18:32] "GET /check_status/?_=1756091164109 HTTP/1.1" 200 -
2025-08-25 11:19:32,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:19:32] "GET /check_status/?_=1756091164110 HTTP/1.1" 200 -
2025-08-25 11:20:32,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:20:32] "GET /check_status/?_=1756091164111 HTTP/1.1" 200 -
2025-08-25 11:21:32,273 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:21:32] "GET /check_status/?_=1756091164112 HTTP/1.1" 200 -
2025-08-25 11:22:32,291 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:22:32] "GET /check_status/?_=1756091164113 HTTP/1.1" 200 -
2025-08-25 11:23:32,288 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:23:32] "GET /check_status/?_=1756091164114 HTTP/1.1" 200 -
2025-08-25 11:23:44,324 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:23:44] "GET /check_status/?_=1756091164115 HTTP/1.1" 200 -
2025-08-25 11:24:04,199 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:24:04] "GET /check_status/?_=1756091164116 HTTP/1.1" 200 -
2025-08-25 11:24:34,194 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:24:34] "GET /check_status/?_=1756091164117 HTTP/1.1" 200 -
2025-08-25 11:25:04,192 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:25:04] "GET /check_status/?_=1756091164118 HTTP/1.1" 200 -
2025-08-25 11:25:34,193 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:25:34] "GET /check_status/?_=1756091164119 HTTP/1.1" 200 -
2025-08-25 11:26:04,199 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:26:04] "GET /check_status/?_=1756091164120 HTTP/1.1" 200 -
2025-08-25 11:26:34,196 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:26:34] "GET /check_status/?_=1756091164121 HTTP/1.1" 200 -
2025-08-25 11:27:04,197 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:27:04] "GET /check_status/?_=1756091164122 HTTP/1.1" 200 -
2025-08-25 11:27:34,203 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:27:34] "GET /check_status/?_=1756091164123 HTTP/1.1" 200 -
2025-08-25 11:28:04,195 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:28:04] "GET /check_status/?_=1756091164124 HTTP/1.1" 200 -
2025-08-25 11:28:34,201 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:28:34] "GET /check_status/?_=1756091164125 HTTP/1.1" 200 -
2025-08-25 11:29:04,205 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:29:04] "GET /check_status/?_=1756091164126 HTTP/1.1" 200 -
2025-08-25 11:29:34,190 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:29:34] "GET /check_status/?_=1756091164127 HTTP/1.1" 200 -
2025-08-25 11:30:04,253 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:30:04] "GET /check_status/?_=1756091164128 HTTP/1.1" 200 -
2025-08-25 11:30:34,197 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:30:34] "GET /check_status/?_=1756091164129 HTTP/1.1" 200 -
2025-08-25 11:31:04,190 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:31:04] "GET /check_status/?_=1756091164130 HTTP/1.1" 200 -
2025-08-25 11:31:34,191 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:31:34] "GET /check_status/?_=1756091164131 HTTP/1.1" 200 -
2025-08-25 11:32:04,192 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:32:04] "GET /check_status/?_=1756091164132 HTTP/1.1" 200 -
2025-08-25 11:32:34,206 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:32:34] "GET /check_status/?_=1756091164133 HTTP/1.1" 200 -
2025-08-25 11:33:04,194 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:33:04] "GET /check_status/?_=1756091164134 HTTP/1.1" 200 -
2025-08-25 11:33:34,192 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:33:34] "GET /check_status/?_=1756091164135 HTTP/1.1" 200 -
2025-08-25 11:34:04,196 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:34:04] "GET /check_status/?_=1756091164136 HTTP/1.1" 200 -
2025-08-25 11:34:34,198 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:34:34] "GET /check_status/?_=1756091164137 HTTP/1.1" 200 -
2025-08-25 11:35:04,195 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:35:04] "GET /check_status/?_=1756091164138 HTTP/1.1" 200 -
2025-08-25 11:35:34,196 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:35:34] "GET /check_status/?_=1756091164139 HTTP/1.1" 200 -
2025-08-25 11:36:04,272 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:36:04] "GET /check_status/?_=1756091164140 HTTP/1.1" 200 -
2025-08-25 11:36:34,268 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:36:34] "GET /check_status/?_=1756091164141 HTTP/1.1" 200 -
2025-08-25 11:37:32,291 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:37:32] "GET /check_status/?_=1756091164142 HTTP/1.1" 200 -
2025-08-25 11:38:32,282 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:38:32] "GET /check_status/?_=1756091164143 HTTP/1.1" 200 -
2025-08-25 11:39:32,278 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:39:32] "GET /check_status/?_=1756091164144 HTTP/1.1" 200 -
2025-08-25 11:40:32,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:40:32] "GET /check_status/?_=1756091164145 HTTP/1.1" 200 -
2025-08-25 11:41:32,290 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:41:32] "GET /check_status/?_=1756091164146 HTTP/1.1" 200 -
2025-08-25 11:42:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:42:32] "GET /check_status/?_=1756091164147 HTTP/1.1" 200 -
2025-08-25 11:43:32,297 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:43:32] "GET /check_status/?_=1756091164148 HTTP/1.1" 200 -
2025-08-25 11:44:32,295 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:44:32] "GET /check_status/?_=1756091164149 HTTP/1.1" 200 -
2025-08-25 11:45:32,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:45:32] "GET /check_status/?_=1756091164150 HTTP/1.1" 200 -
2025-08-25 11:46:32,286 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:46:32] "GET /check_status/?_=1756091164151 HTTP/1.1" 200 -
2025-08-25 11:47:32,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:47:32] "GET /check_status/?_=1756091164152 HTTP/1.1" 200 -
2025-08-25 11:48:32,278 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:48:32] "GET /check_status/?_=1756091164153 HTTP/1.1" 200 -
2025-08-25 11:49:32,283 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:49:32] "GET /check_status/?_=1756091164154 HTTP/1.1" 200 -
2025-08-25 11:50:32,281 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:50:32] "GET /check_status/?_=1756091164155 HTTP/1.1" 200 -
2025-08-25 11:51:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:51:32] "GET /check_status/?_=1756091164156 HTTP/1.1" 200 -
2025-08-25 11:52:32,289 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:52:32] "GET /check_status/?_=1756091164157 HTTP/1.1" 200 -
2025-08-25 11:53:32,276 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:53:32] "GET /check_status/?_=1756091164158 HTTP/1.1" 200 -
2025-08-25 11:54:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:54:32] "GET /check_status/?_=1756091164159 HTTP/1.1" 200 -
2025-08-25 11:55:32,283 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:55:32] "GET /check_status/?_=1756091164160 HTTP/1.1" 200 -
2025-08-25 11:56:32,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:56:32] "GET /check_status/?_=1756091164161 HTTP/1.1" 200 -
2025-08-25 11:57:32,298 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:57:32] "GET /check_status/?_=1756091164162 HTTP/1.1" 200 -
2025-08-25 11:58:32,290 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:58:32] "GET /check_status/?_=1756091164163 HTTP/1.1" 200 -
2025-08-25 11:59:32,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 11:59:32] "GET /check_status/?_=1756091164164 HTTP/1.1" 200 -
2025-08-25 12:00:32,333 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:00:32] "GET /check_status/?_=1756091164165 HTTP/1.1" 200 -
2025-08-25 12:01:32,290 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:01:32] "GET /check_status/?_=1756091164166 HTTP/1.1" 200 -
2025-08-25 12:02:32,286 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:02:32] "GET /check_status/?_=1756091164167 HTTP/1.1" 200 -
2025-08-25 12:03:32,295 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:03:32] "GET /check_status/?_=1756091164168 HTTP/1.1" 200 -
2025-08-25 12:04:32,275 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:04:32] "GET /check_status/?_=1756091164169 HTTP/1.1" 200 -
2025-08-25 12:05:32,279 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:05:32] "GET /check_status/?_=1756091164170 HTTP/1.1" 200 -
2025-08-25 12:06:32,286 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:06:32] "GET /check_status/?_=1756091164171 HTTP/1.1" 200 -
2025-08-25 12:07:32,280 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:07:32] "GET /check_status/?_=1756091164172 HTTP/1.1" 200 -
2025-08-25 12:08:32,279 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:08:32] "GET /check_status/?_=1756091164173 HTTP/1.1" 200 -
2025-08-25 12:09:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:09:32] "GET /check_status/?_=1756091164174 HTTP/1.1" 200 -
2025-08-25 12:10:32,327 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:10:32] "GET /check_status/?_=1756091164175 HTTP/1.1" 200 -
2025-08-25 12:11:32,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:11:32] "GET /check_status/?_=1756091164176 HTTP/1.1" 200 -
2025-08-25 12:12:32,294 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:12:32] "GET /check_status/?_=1756091164177 HTTP/1.1" 200 -
2025-08-25 12:13:32,295 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:13:32] "GET /check_status/?_=1756091164178 HTTP/1.1" 200 -
2025-08-25 12:14:32,289 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:14:32] "GET /check_status/?_=1756091164179 HTTP/1.1" 200 -
2025-08-25 12:15:32,294 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:15:32] "GET /check_status/?_=1756091164180 HTTP/1.1" 200 -
2025-08-25 12:16:32,278 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:16:32] "GET /check_status/?_=1756091164181 HTTP/1.1" 200 -
2025-08-25 12:17:32,282 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:17:32] "GET /check_status/?_=1756091164182 HTTP/1.1" 200 -
2025-08-25 12:18:32,278 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:18:32] "GET /check_status/?_=1756091164183 HTTP/1.1" 200 -
2025-08-25 12:19:32,288 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:19:32] "GET /check_status/?_=1756091164184 HTTP/1.1" 200 -
2025-08-25 12:20:32,277 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:20:32] "GET /check_status/?_=1756091164185 HTTP/1.1" 200 -
2025-08-25 12:21:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:21:32] "GET /check_status/?_=1756091164186 HTTP/1.1" 200 -
2025-08-25 12:22:32,290 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:22:32] "GET /check_status/?_=1756091164187 HTTP/1.1" 200 -
2025-08-25 12:23:32,294 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:23:32] "GET /check_status/?_=1756091164188 HTTP/1.1" 200 -
2025-08-25 12:24:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:24:32] "GET /check_status/?_=1756091164189 HTTP/1.1" 200 -
2025-08-25 12:25:32,295 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:25:32] "GET /check_status/?_=1756091164190 HTTP/1.1" 200 -
2025-08-25 12:26:32,287 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:26:32] "GET /check_status/?_=1756091164191 HTTP/1.1" 200 -
2025-08-25 12:27:32,286 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:27:32] "GET /check_status/?_=1756091164192 HTTP/1.1" 200 -
2025-08-25 12:28:32,280 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:28:32] "GET /check_status/?_=1756091164193 HTTP/1.1" 200 -
2025-08-25 12:29:32,279 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:29:32] "GET /check_status/?_=1756091164194 HTTP/1.1" 200 -
2025-08-25 12:30:32,292 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:30:32] "GET /check_status/?_=1756091164195 HTTP/1.1" 200 -
2025-08-25 12:31:32,285 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:31:32] "GET /check_status/?_=1756091164196 HTTP/1.1" 200 -
2025-08-25 12:32:32,289 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:32:32] "GET /check_status/?_=1756091164197 HTTP/1.1" 200 -
2025-08-25 12:33:32,279 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:33:32] "GET /check_status/?_=1756091164198 HTTP/1.1" 200 -
2025-08-25 12:34:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:34:32] "GET /check_status/?_=1756091164199 HTTP/1.1" 200 -
2025-08-25 12:35:32,303 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:35:32] "GET /check_status/?_=1756091164200 HTTP/1.1" 200 -
2025-08-25 12:36:32,283 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:36:32] "GET /check_status/?_=1756091164201 HTTP/1.1" 200 -
2025-08-25 12:37:32,283 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:37:32] "GET /check_status/?_=1756091164202 HTTP/1.1" 200 -
2025-08-25 12:38:32,284 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:38:32] "GET /check_status/?_=1756091164203 HTTP/1.1" 200 -
2025-08-25 12:39:32,279 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:39:32] "GET /check_status/?_=1756091164204 HTTP/1.1" 200 -
2025-08-25 12:40:32,295 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:40:32] "GET /check_status/?_=1756091164205 HTTP/1.1" 200 -
2025-08-25 12:41:27,460 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:41:27] "GET /check_status/?_=1756091164206 HTTP/1.1" 200 -
2025-08-25 12:41:34,190 - werkzeug - INFO - 127.0.0.1 - - [25/Aug/2025 12:41:34] "GET /check_status/?_=1756091164207 HTTP/1.1" 200 -
