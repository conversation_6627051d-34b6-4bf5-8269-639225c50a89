"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7633],{19132:function(e,t,n){n.d(t,{BZ:function(){return _},Bl:function(){return Z},LZ:function(){return w},M1:function(){return p},QD:function(){return k},gb:function(){return N},hJ:function(){return m},j5:function(){return L},sf:function(){return y},tc:function(){return g},te:function(){return z},xm:function(){return F},xn:function(){return v}});var r=n(19170),a=n(5362),l=n(27218),i=n(27895),s=n(30947),c=n(14448),o=n(13262),u=n(10228),d=n(88755),f=n(77930),h=n(81695),x=n(7653);let m=e=>e.type===u.Fg.GITHUB?{sync_source_type:u.Fg.GITHUB,sync_source_config:e.config}:e.type===u.Fg.GDRIVE?{sync_source_type:u.Fg.GDRIVE,sync_source_config:e.config}:null,g=()=>{let{activeOrganization:e}=(0,l.t)(),t=null==e?void 0:e.uuid,{value:n}=(0,c.F)("nighthawk_enabled"),{value:r}=(0,c.F)("kingfisher_enabled"),{value:i}=(0,c.F)("spider_enabled_2"),{value:s}=(0,c.F)("cinnabon_enabled");return(0,a.WE)("/api/organizations/".concat(t,"/sync/settings"),{queryKey:[o.FU,{orgUUID:t}],enabled:!!(e&&(n||r||i||s))})},p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{activeOrganization:t}=(0,l.t)(),n=null==t?void 0:t.uuid,{value:r}=(0,c.F)("nighthawk_enabled"),{value:i}=(0,c.F)("kingfisher_enabled"),{value:s}=(0,c.F)("spider_enabled_2"),{value:u}=(0,c.F)("cinnabon_enabled"),d=(0,f.useQueryClient)();return(0,a.Ne)("/api/organizations/".concat(n,"/sync/settings"),"PUT",(e,t)=>{let{type:n,enabled:r}=e;t||(t=[]);let a=t.map(e=>e.type===n?{...e,enabled:r}:e);return a.some(e=>e.type===n)?a:[...t,{type:n,enabled:r}]},{queryKey:[o.FU,{orgUUID:n}],enabled:!!(t&&(r||i||s||u)),onSuccess:async function(){for(var t,r=arguments.length,a=Array(r),l=0;l<r;l++)a[l]=arguments[l];await d.invalidateQueries({queryKey:[o.FU,{orgUUID:n}]}),await (null===(t=e.onSuccess)||void 0===t?void 0:t.call(e,...a))},meta:{errorMessage:"There was an error changing your settings. Please try again."},...e})},v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{activeOrganization:t}=(0,l.t)(),n=null==t?void 0:t.uuid,r=(0,f.useQueryClient)(),{value:i}=(0,c.F)("kingfisher_enabled"),{value:s}=(0,c.F)("apps_use_bananagrams");return(0,a.Ne)("/api/organizations/".concat(n,"/sync/settings/config"),"POST",(e,t)=>{let{type:n,config:r}=e;return t||(t=[]),t.map(e=>e.type===n?{...e,config:{...e.config,...r}}:e)},{queryKey:[o.FU,{orgUUID:n}],enabled:!!(t&&i&&s),onSuccess:async function(){for(var t,a=arguments.length,l=Array(a),i=0;i<a;i++)l[i]=arguments[i];await r.invalidateQueries({queryKey:[o.FU,{orgUUID:n}]}),await (null===(t=e.onSuccess)||void 0===t?void 0:t.call(e,...l))},meta:{errorMessage:"There was an error changing your settings. Please try again."},...e})},b=(e,t)=>{let n=new URLSearchParams(Array.from(t.entries()));n.delete(e);let r=n.toString()?"?".concat(n.toString()):"",a=window.location.pathname+r;window.history.replaceState({...window.history.state,as:a,url:a},"",a)},_=e=>{let{callerIdentifier:t,successParam:n,handleOpen:r}=e,a=(0,h.useSearchParams)(),l=(0,x.useRef)(!1);(0,x.useEffect)(()=>{!a.has(n)||l.current||t&&t!==a.get(n)||(l.current=!0,b(n,a),r())},[a,t,r,n])},w=e=>{let{pathname:t,searchParams:n,queryString:r,isClaudeApp:a}=e,l="".concat(t,"?").concat(r),i=n.get("origin");if(i)l=i;else if(a){let e=/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/i,n=t.startsWith("/project/")&&e.test(t.slice(9)),a=t.startsWith("/chat/")&&e.test(t.slice(6));l="claude://claude.ai".concat(n||a||"/new"===t?t:"/new","?").concat(r)}return l},j=e=>[u.co.CREATED,u.co.PENDING,u.co.UPDATING].includes(e),y=e=>j(e.status.state),F=e=>[u.co.FAILED,u.co.UPDATE_FAILED].includes(e.status.state),C=e=>{if(e instanceof s.Hx)return e instanceof s.Hx&&"access_denied"===e.errorCode},N=()=>{let{addError:e}=(0,i.e)(),t=(0,f.useQueryClient)();return(n,r)=>{C(n)&&(e("Please try again. You may need to reconnect with your ".concat(u.hP[r.sync_source_type]," account.")),o.Su.forEach(e=>{t.invalidateQueries({queryKey:[e]})}),t.invalidateQueries({queryKey:[o.cF]}))}},E={[u.Fg.GCAL]:"enabled_foccacia",[u.Fg.GMAIL]:"enabled_sourdough"},A=e=>{let t=e===u.Fg.GCAL||e===u.Fg.GMAIL?"pumpernickel_enabled":"cinnabon_enabled",{value:n}=(0,c.F)(t);return n},M=e=>{let t=(0,l.Cf)(),{data:n}=g(),r=!!(null==n?void 0:n.find(t=>t.type===e&&t.enabled));return!t||r},z=e=>{let t=(0,l.rW)(),n=M(e);return!!(A(e)&&n&&t)},S=e=>"sfdc"===e?"salesforce":e,k=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{activeOrganization:n}=(0,l.t)(),r=null==n?void 0:n.uuid,i=(0,h.useRouter)(),s=(0,h.usePathname)(),c=(0,h.useSearchParams)(),{callerIdentifier:o,handleAuthSuccess:u}=t,m=z(e),g=(0,f.useQueryClient)(),p=(0,d.useIsClaudeApp)(),{data:v,isFetched:b}=(0,a.WE)("/api/organizations/".concat(r,"/sync/").concat(S(e),"/auth"),{queryKey:["sync_".concat(e,"_auth_status"),{orgUUID:r}],enabled:!!(n&&m)}),j=(0,x.useRef)(!1),{mutate:y}=(0,a.uC)("/api/organizations/".concat(r,"/sync/").concat(S(e),"/auth/start"),"POST",{enabled:!!(n&&m),onSuccess:e=>{i.push(e.redirect_url)}});_({callerIdentifier:o,successParam:"".concat(e,"_success"),handleOpen:()=>null==u?void 0:u(!0)});let{mutate:F}=(0,a.uC)("/api/organizations/".concat(r,"/sync/").concat(S(e),"/auth"),"DELETE",{enabled:!!(n&&m),onSuccess:()=>{g.invalidateQueries({queryKey:["sync_".concat(e,"_auth_status"),{orgUUID:r}]})}});return{isAuthenticated:m&&!!(null==v?void 0:v.is_authenticated),startAuth:()=>{if(!j.current&&m){j.current=!0;let t=w({pathname:s,searchParams:c,queryString:"".concat(e,"_success=").concat(o),isClaudeApp:p});if(p){window.open("/settings/profile?open_in_browser=1&auth_start=".concat(e,"&origin=").concat(t));return}y({pre_auth_state:{origin:t},redirect_uri:new URL("/connect/".concat(S(e),"/callback"),window.location.origin).toString()})}},disconnect:F,isFetched:b}},L=(e,t)=>{var n,r;let a=z(e),{account:i}=(0,l.t)(),s=E[e];return s?a&&(t?!!(null==t?void 0:null===(n=t.conversationSettings)||void 0===n?void 0:n[s]):!!(null==i?void 0:null===(r=i.settings)||void 0===r?void 0:r[s])):a},Z=()=>{let{account:e}=(0,l.t)(),{mutate:t}=(0,r.Ck)();return{toggleIntegrationEnabled:(n,r)=>{if(!e)return;let a=E[n];a&&t({...e.settings,[a]:r})}}}},95586:function(e,t,n){n.d(t,{H:function(){return i},U:function(){return l}});var r=n(27218),a=n(15992);let l=()=>{let e=(0,r.ZJ)(),t=(0,r.Cf)();return(0,a.useConfig)("claude_ai_projects_limits").config.get("max_free_projects",0)>0||t||e},i=()=>{let e=(0,r.ZJ)(),t=(0,r.Cf)(),n=(0,a.useConfig)("claude_ai_projects_limits");return e||t?void 0:n?n.config.get("max_free_projects",0):0}},4746:function(e,t,n){n.d(t,{FeaturePreview:function(){return z},b5:function(){return A},h9:function(){return M},vF:function(){return L}});var r=n(27573),a=n(19170),l=n(18916),i=n(3053),s=n(27218),c=n(27895),o=n(9677),u=n(69321),d=n(7276),f=n(52939),h=n(22523),x=n(32737),m=n(14448),g=n(10607),p=n(35919),v=n.n(p),b=n(50294),_=n(7653),w=n(45790),j=n(40950),y=n(15992),F=n(59016),C=n(95586),N=n(64014);let E=(0,_.createContext)({openFeaturePreviewModal:v()}),A=e=>{let{children:t}=e,[n,a]=(0,_.useState)(!1);return(0,r.jsxs)(E.Provider,{value:{openFeaturePreviewModal:()=>a(!0)},children:[t,(0,r.jsx)(f.u_,{modalSize:"xl",isOpen:n,onClose:()=>a(!1),hasCloseButton:!0,title:(0,r.jsx)(w.Z,{defaultMessage:"Feature preview",id:"3H/t1552xG"}),children:(0,r.jsx)(z,{})})]})},M=()=>(0,_.useContext)(E);function z(e){let{includeTitle:t}=e,n=L();if(!(Object.keys(n).length>0))return;let a=(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{id:"feature-preview",className:"text-text-400 text-sm pt-4 pb-8",children:(0,r.jsx)(w.Z,{defaultMessage:"Preview and provide feedback on upcoming enhancements to our platform. Please note: experimental features might influence Claude’s behavior and some interactions may differ from the standard experience.",id:"TJ0Nj7aQ5C"})}),(0,r.jsx)("div",{className:"flex flex-col gap-4",children:Object.entries(n).map((e,t)=>{let[n,a]=e;return(0,r.jsxs)(r.Fragment,{children:[t>0&&(0,r.jsx)("div",{className:"border-t border-border-300"}),(0,r.jsx)(S,{label:n,...a},n)]})})})]});return t?(0,r.jsxs)(h.QQ,{children:[(0,r.jsx)(d.NZ,{children:(0,r.jsx)(w.Z,{defaultMessage:"Feature preview",id:"3H/t1552xG"})}),a]}):a}function S(e){var t;let{label:n,...l}=e,d=(0,j.Z)(),{track:f}=(0,i.z$)(),{addSuccess:h}=(0,c.e)(),{account:m}=(0,s.t)(),{mutate:p}=(0,a.Ck)(),{checked:v,disabled:w,tooltipContent:y}=k(n),F=(0,_.useCallback)(()=>{m&&(f({event_key:"claudeai.settings.preview_feature.toggled",account_uuid:m.uuid,feature_id:n,action:!0===v?"disable":"enable"}),h(d.formatMessage({defaultMessage:"{title} {action}",id:"XuGv50sPs9"},{title:l.title,action:v?"disabled":"enabled"})),p({...m.settings,[n]:!v}))},[m,f,n,v,h,d,l.title,p]);return(0,r.jsxs)("div",{className:"flex flex-row w-full gap-4 items-center",children:[(0,r.jsx)(b.default,{src:"/images/feature-preview/square/".concat(null!==(t=Z[n])&&void 0!==t?t:"generic",".png"),style:{objectFit:"cover",width:62,height:62},className:"rounded-lg border border-border-300",width:248,height:248,alt:d.formatMessage({defaultMessage:"Preview image of feature",id:"6VliGZPeZA"})}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 flex-grow",children:[(0,r.jsx)("h3",{className:"text-text-100 text-md",children:l.titleKey?(0,r.jsx)(u.d7,{id:l.titleKey}):l.title}),(0,r.jsx)("p",{className:"text-text-100 text-sm",children:l.descriptionKey?(0,r.jsx)(u.d7,{id:l.descriptionKey}):l.description})]}),(0,r.jsx)(x.u,{tooltipContent:y?(0,r.jsx)("div",{className:"text-center",children:y}):null,children:(0,r.jsx)("label",{className:(0,g.Z)(!w&&"cursor-pointer"),children:(0,r.jsx)(o.Z,{disabled:w,checked:v,onChange:F},n)})})]})}let k=e=>{var t,n,r;let a=(0,j.Z)(),{account:i}=(0,s.t)(),{value:c}=(0,m.F)("force_harmony"),{value:o,source:u}=(0,l.h)(null!==(n=null==i?void 0:i.settings)&&void 0!==n?n:{}),{data:d}=(0,F.Kg)(),f=!(null==d?void 0:d.disabled_features.includes("web_search")),h=(0,s.Cf)(),x=null!==(r=null==i?void 0:null===(t=i.settings)||void 0===t?void 0:t[e])&&void 0!==r&&r,g="preview_feature_uses_harmony"===e&&c;switch(e){case"preview_feature_uses_harmony":return{checked:c||x,disabled:g,tooltipContent:null};case"enabled_artifacts_attachments":return{checked:"flag"===u&&!!o||x,disabled:g,tooltipContent:null};case"enabled_web_search":if(h&&!f)return{checked:!1,disabled:!0,tooltipContent:a.formatMessage({defaultMessage:"This feature is disabled for your team plan.",id:"YNQWei0I8o"})};return{checked:x,disabled:g,tooltipContent:null};default:return{checked:x,disabled:g,tooltipContent:null}}};function L(){var e;let{value:t}=(0,m.F)("latex_ga"),n=null===(e=(0,y.useConfig)("fp_menu").config)||void 0===e?void 0:e.get("features",{}),r=(0,N.i)(),a=(0,C.U)(),{value:i,source:s}=(0,l.h)({}),c={...n};return r&&delete c.preview_feature_uses_artifacts,i&&"experiment"===s&&delete c.enabled_artifacts_attachments,a||delete c.preview_feature_uses_harmony,t&&delete c.preview_feature_uses_latex,c}let Z={preview_feature_uses_artifacts:"artifacts",preview_feature_uses_latex:"latex",preview_feature_uses_citations:"generic",preview_feature_uses_harmony:"harmony",enabled_artifacts_attachments:"analyse",enabled_turmeric:"harmony",enabled_gdrive:"google-drive",enabled_web_search:"web-search",enabled_compass:"compass",enabled_bananagrams:"bananagrams",enabled_foccacia:"foccacia",enabled_sourdough:"sourdough"}},64014:function(e,t,n){n.d(t,{i:function(){return l}});var r=n(14448),a=n(15992);let l=()=>{let{layer:e}=(0,a.useLayer)("frontend"),{value:t}=(0,r.F)("artifacts_enabled");return!!t||e.get("artifacts_enabled_by_default",!1)}},18916:function(e,t,n){n.d(t,{h:function(){return l}});var r=n(14448),a=n(15992);function l(e){let t=function(){let{layer:e}=(0,a.useLayer)("frontend");return e.get("analysis_tool_experiment_enabled",!1)}(),{value:n}=(0,r.F)("rely_on_analysis_flag"),{value:l}=(0,r.F)("analysis_tool_launch_ga");if(n){var i;return{value:null!==(i=null==e?void 0:e.enabled_artifacts_attachments)&&void 0!==i?i:l,source:"flag"}}return{value:(null==e?void 0:e.enabled_artifacts_attachments)||t,source:"experiment"}}},69801:function(e,t,n){n.d(t,{K:function(){return d},_:function(){return u}});var r=n(27573),a=n(10607),l=n(91452),i=n.n(l),s=n(22942),c=n.n(s),o=n(7653);let u=(0,o.forwardRef)((e,t)=>{let{label:n,id:l,className:i}=e;return n?(0,r.jsx)("label",{htmlFor:l,className:(0,a.Z)("text-text-200 mb-1 block text-sm",i),ref:t,children:n}):null});u.displayName="Label";let d=e=>{let{label:t,id:n}=e;return(0,o.useMemo)(()=>n||(t&&"string"==typeof t?c()("".concat(i()(t),"_")):c()()),[t,n])}},5788:function(e,t,n){n.d(t,{P:function(){return c}});var r=n(27573),a=n(69801),l=n(49289),i=n(7653);let s=(0,l.j)("text-text-100 py-0 transition-colors can-focus cursor-pointer appearance-none w-full",{variants:{size:{sm:"h-8 pl-3 pr-8 text-sm tracking-tight rounded-md",normal:"h-9 pl-1.5 pr-6 rounded-lg",lg:"h-11 pl-2 pr-6 rounded-[0.6rem]"},variant:{outline:"bg-bg-000 border border-border-300 hover:border-border-200 shadow-none",ghost:"bg-transparent border-none shadow-none cursor-pointer",danger:"bg-danger-900 text-danger-000 shadow border-danger-200 hover:border-danger-200 focus:border-danger-200"}},defaultVariants:{size:"normal",variant:"outline"}}),c=(0,i.forwardRef)((e,t)=>{let{size:n,variant:l,className:i,label:c,id:o,...u}=e,d=(0,a.K)({id:o,label:c});return(0,r.jsxs)(r.Fragment,{children:[c&&(0,r.jsx)(a._,{label:c,id:d}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("select",{id:d,ref:t,className:s({size:n,className:i,variant:l}),...u}),(0,r.jsx)("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-text-500",children:(0,r.jsx)("svg",{className:"fill-current h-4 w-4",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"})})})]})]})});c.displayName="Select"},12776:function(e,t,n){n.d(t,{o:function(){return d},N:function(){return u}});var r=n(27573);function a(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!==e&&(e.current=t)})}}var l=n(49289),i=n(10607),s=n(7653),c=n(69037),o=n(69801);let u=(0,l.j)("bg-bg-000\n  border\n  border-border-300\n  hover:border-border-200\n  transition-colors\n  placeholder:text-text-500\n  can-focus\n  disabled:cursor-not-allowed\n  disabled:opacity-50",{variants:{size:{default:"h-9 px-3 py-2 rounded-lg",sm:"h-8 rounded-md px-3 text-xs",lg:"h-11 px-3 rounded-[0.6rem]"},error:{true:"!border-danger-200/50 hover:!border-danger-200/90 focus:!border-danger-200"}},defaultVariants:{size:"default"}}),d=(0,s.forwardRef)((e,t)=>{let{autoFocus:n,className:l,id:d,label:f,secondaryLabel:h,size:x="default",error:m,type:g,value:p,currencySymbol:v="$",labelClassName:b,onChange:_,onValueChange:w,automaticallyFocusAndSelect:j,prepend:y,append:F,...C}=e,N=(0,i.Z)(u({size:x,error:m,className:l}),l),E=(0,o.K)({id:d,label:f}),A=(0,s.useRef)(null);(0,s.useEffect)(()=>{if(j){var e,t;null===(e=A.current)||void 0===e||e.focus(),null===(t=A.current)||void 0===t||t.select()}},[]);let{defaultValue:M,step:z,...S}=C,k="currency"!==g&&!(y||F);return(0,r.jsxs)(r.Fragment,{children:[f&&(0,r.jsx)(o._,{label:f,id:E,className:b}),(y||F)&&(0,r.jsxs)("div",{className:(0,i.Z)(N,"inline-flex cursor-text items-stretch gap-2 can-focus-within"),onClick:()=>{var e;return null===(e=A.current)||void 0===e?void 0:e.focus()},children:[y&&(0,r.jsx)("div",{className:"flex items-center",children:y}),(0,r.jsx)("input",{id:E,autoFocus:n,type:g,className:"w-full placeholder:text-text-500 m-0 bg-transparent p-0 hide-focus-ring disabled:cursor-not-allowed disabled:opacity-50",ref:a([t,A]),value:p,onChange:e=>{null==_||_(e),null==w||w(e.target.value)},...C}),F&&(0,r.jsx)("div",{className:(0,i.Z)("flex items-center","default"===x&&"-mr-2","sm"===x&&"-mr-2","lg"===x&&"-mr-1.5"),children:F})]}),"currency"===g&&(0,r.jsx)(c.Z,{id:E,ref:t,autoFocus:n,prefix:v,placeholder:v,className:N,value:p,onValueChange:e=>null==w?void 0:w(e),allowDecimals:!1,...S}),k&&(0,r.jsx)("input",{id:E,autoFocus:n,type:g,className:N,ref:a([t,A]),value:p,onChange:e=>{null==_||_(e),null==w||w(e.target.value)},...C}),h&&(0,r.jsx)("div",{className:"text-text-400 mt-1 text-sm",children:h})]})});d.displayName="TextInput"},9677:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(27573),a=n(10607);function l(e){let{className:t,variant:n,width:l=36,height:s=20,...c}=e,o="number"==typeof s?"".concat(s,"px"):s,u="calc(".concat(o," - 4px");return(0,r.jsxs)("div",{className:(0,a.Z)("group/switch relative select-none cursor-pointer",t),children:[(0,r.jsx)("input",{type:"checkbox",className:"peer sr-only",...c}),(0,r.jsx)("div",{style:{width:l,height:o},className:(0,a.Z)("border-border-300 rounded-full peer:can-focus peer-disabled:opacity-50",i.pill[n||"default"])}),(0,r.jsx)("div",{style:{height:u,width:u},className:(0,a.Z)("absolute start-[2px] top-[2px] rounded-full transition-all peer-checked:translate-x-full rtl:peer-checked:-translate-x-full",i.circle[n||"default"])})]})}let i={pill:{default:"\n    bg-bg-500\n    transition-colors\n    peer-checked:bg-accent-secondary-100\n    "},circle:{default:"\n    group-hover/switch:opacity-80\n    bg-white\n    transition\n    "}}},9788:function(e,t,n){n.d(t,{q:function(){return i}});var r=n(10607),a=n(7653);function l(e){return function(t){for(var n=arguments.length,l=Array(n>1?n-1:0),i=1;i<n;i++)l[i-1]=arguments[i];let s=t.map(e=>e.replace(/\n/g,"").trim()),c=a.forwardRef((t,n)=>{let{className:i,...c}=t,o=l.map(e=>"function"==typeof e?e(t):e),u=Object.fromEntries(Object.entries(c).filter(e=>{let[t]=e;return!t.startsWith("$")}));return a.createElement(e,{...u,ref:n,className:(0,r.Z)(s,o,"string"==typeof i?i:"")})});return c.displayName="string"==typeof e?e:e.displayName,c}}function i(e){return l(e)}i.a=l("a"),i.aside=l("aside"),i.button=l("button"),i.main=l("main"),i.div=l("div"),i.form=l("form"),i.nav=l("nav"),i.fieldset=l("fieldset"),i.header=l("header"),i.h1=l("h1"),i.h2=l("h2"),i.h3=l("h3"),i.h4=l("h4"),i.h5=l("h5"),i.th=l("th"),i.td=l("td"),i.input=l("input"),i.label=l("label"),i.p=l("p"),i.section=l("section"),i.span=l("span"),i.li=l("li")},69321:function(e,t,n){n.d(t,{ClientIntlProvider:function(){return u},Gc:function(){return f},d7:function(){return d},lF:function(){return h}});var r=n(27573),a=n(13784),l=n(7653),i=n(22247),s=n(45790),c=n(72358);let o=()=>void 0;function u(e){let{locale:t,messages:n,children:s,hideErrors:c}=e;return(0,l.useEffect)(()=>{a.Zr.defaultLocale=t},[t]),(0,r.jsx)(i.Z,{locale:t,messages:n,onError:c?o:void 0,fallbackOnEmptyString:!1,children:s})}function d(e){let{id:t}=e;return(0,r.jsx)(s.Z,{id:(0,c.g2)(t),defaultMessage:" "})}function f(e){let{id:t,intl:n}=e;return n.formatMessage({id:(0,c.g2)(t),defaultMessage:" "})}function h(e){let{id:t,intl:n}=e;return n.formatMessage({id:(0,c.Y_)(t),defaultMessage:" "})}},72358:function(e,t,n){n.d(t,{GN:function(){return s},Y_:function(){return c},Yr:function(){return u},ZW:function(){return i},g2:function(){return o}});var r=n(79939),a=n.n(r),l=n(68571);a().join(l.cwd(),"public/i18n"),a().join(l.cwd(),"public/i18n/secret");let i="en-US",s={"xx-LS":{name:"Long stringsSSSSSSSS"},"xx-AC":{name:"ALL CAPS"},"xx-HA":{name:"[javascript] prefixed strings"},"en-XA":{name:"Ȧȧƈƈḗḗƞŧḗḗḓ Ḗḗƞɠŀīīşħ"},"en-XB":{name:"ɥsıʅƃuƎ ıpıԐ"}};function c(e){return"".concat("secret",":").concat(e)}function o(e){return"".concat("statsig",":").concat(e)}Object.keys(s);let u=["en-US","de-DE","fr-FR","ko-KR","ja-JP","es-419","es-ES","it-IT","hi-IN","pt-BR","id-ID"]},7276:function(e,t,n){n.d(t,{GR:function(){return l.QQ},M$:function(){return u},NZ:function(){return d},mH:function(){return o}});var r=n(56683),a=n(9788),l=n(22523);function i(){let e=(0,r._)(["p-4 bg-bg-300 rounded-xl"]);return i=function(){return e},e}function s(){let e=(0,r._)(["basis-0 flex-col gap-1 inline-flex"]);return s=function(){return e},e}function c(){let e=(0,r._)(["items-center gap-2 inline-flex text-text-000 font-styrene font-medium text-lg leading-7"]);return c=function(){return e},e}let o=a.q.div(i()),u=a.q.div(s()),d=a.q.div(c())},32006:function(e,t,n){n.d(t,{W9:function(){return h},cT:function(){return a},j0:function(){return v},k6:function(){return u},tB:function(){return x}});var r,a,l=n(27573),i=n(10228),s=n(54731),c=n(30070),o=n(10607);(r=a||(a={})).DOCS="docs",r.SHEETS="sheets",r.SLIDES="slides";let u=e=>{let{size:t,className:n,variant:r}=e;return"docs"===r?(0,l.jsx)(d,{size:t,className:n}):(0,l.jsxs)("svg",{width:t,height:t,viewBox:"0 0 87.3 78",xmlns:"http://www.w3.org/2000/svg",className:n,children:[(0,l.jsx)("path",{d:"m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z",fill:"#0066da"}),(0,l.jsx)("path",{d:"m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z",fill:"#00ac47"}),(0,l.jsx)("path",{d:"m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z",fill:"#ea4335"}),(0,l.jsx)("path",{d:"m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z",fill:"#00832d"}),(0,l.jsx)("path",{d:"m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z",fill:"#2684fc"}),(0,l.jsx)("path",{d:"m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z",fill:"#ffba00"})]})},d=e=>{let{size:t}=e;return(0,l.jsxs)("svg",{width:t,height:t,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 64 88",children:[(0,l.jsx)("path",{fill:"#3086F6",d:"M58,88H6c-3.3,0-6-2.7-6-6V6c0-3.3,2.7-6,6-6h36l22,22v60C64,85.3,61.3,88,58,88z"}),(0,l.jsx)("path",{fill:"#0C67D6",d:"M42,0l22,22H42V0z"}),(0,l.jsx)("path",{fill:"#FDFFFF",d:"M50,39H14v-5h36V39z M50,46H14v5h36V46z M40,58H14v5h26V58z"})]})},f=e=>{let{color:t="currentColor",size:n,className:r}=e;return(0,l.jsx)("svg",{className:(0,o.Z)("text-text-100",r),width:n,height:n,viewBox:"0 0 98 96",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{fill:t,fillRule:"evenodd",clipRule:"evenodd",d:"M48.854 0C21.839 0 0 22 0 49.217c0 21.756 13.993 40.172 33.405 46.69 2.427.49 3.316-1.059 3.316-2.362 0-1.141-.08-5.052-.08-9.127-13.59 2.934-16.42-5.867-16.42-5.867-2.184-5.704-5.42-7.17-5.42-7.17-4.448-3.015.324-3.015.324-3.015 4.934.326 7.523 5.052 7.523 5.052 4.367 7.496 11.404 5.378 14.235 4.074.404-3.178 1.699-5.378 3.074-6.6-10.839-1.141-22.243-5.378-22.243-24.283 0-5.378 1.94-9.778 5.014-13.2-.485-1.222-2.184-6.275.486-13.038 0 0 4.125-1.304 13.426 5.052a46.97 46.97 0 0 1 12.214-1.63c4.125 0 8.33.571 12.213 1.63 9.302-6.356 13.427-5.052 13.427-5.052 2.67 6.763.97 11.816.485 13.038 3.155 3.422 5.015 7.822 5.015 13.2 0 18.905-11.404 23.06-22.324 24.283 1.78 1.548 3.316 4.481 3.316 9.126 0 6.6-.08 11.897-.08 13.526 0 1.304.89 2.853 3.316 2.364 19.412-6.52 33.405-24.935 33.405-46.691C97.707 22 75.788 0 48.854 0z"})})},h=e=>{let{size:t,className:n}=e;return(0,l.jsxs)("svg",{className:(0,o.Z)("text-text-100",n),width:t,height:t,viewBox:"52 42 88 66",children:[(0,l.jsx)("path",{fill:"#4285f4",d:"M58 108h14V74L52 59v43c0 3.32 2.69 6 6 6"}),(0,l.jsx)("path",{fill:"#34a853",d:"M120 108h14c3.32 0 6-2.69 6-6V59l-20 15"}),(0,l.jsx)("path",{fill:"#fbbc04",d:"M120 48v26l20-15v-8c0-7.42-8.47-11.65-14.4-7.2"}),(0,l.jsx)("path",{fill:"#ea4335",d:"M72 74V48l24 18 24-18v26L96 92"}),(0,l.jsx)("path",{fill:"#c5221f",d:"M52 51v8l20 15V48l-5.6-4.2c-5.94-4.45-14.4-.22-14.4 7.2"})]})},x=e=>{let{size:t,className:n}=e;return(0,l.jsx)("svg",{className:(0,o.Z)("text-text-100",n),width:t,height:t,viewBox:"0 0 200 200",enableBackground:"new 0 0 200 200",children:(0,l.jsx)("g",{children:(0,l.jsxs)("g",{transform:"translate(3.75 3.75)",children:[(0,l.jsx)("path",{fill:"#FFFFFF",d:"M148.882,43.618l-47.368-5.263l-57.895,5.263L38.355,96.25l5.263,52.632l52.632,6.579l52.632-6.579 l5.263-53.947L148.882,43.618z"}),(0,l.jsx)("path",{fill:"#1A73E8",d:"M65.211,125.276c-3.934-2.658-6.658-6.539-8.145-11.671l9.132-3.763c0.829,3.158,2.276,5.605,4.342,7.342 c2.053,1.737,4.553,2.592,7.474,2.592c2.987,0,5.553-0.908,7.697-2.724s3.224-4.132,3.224-6.934c0-2.868-1.132-5.211-3.395-7.026 s-5.105-2.724-8.5-2.724h-5.276v-9.039H76.5c2.921,0,5.382-0.789,7.382-2.368c2-1.579,3-3.737,3-6.487 c0-2.447-0.895-4.395-2.684-5.855s-4.053-2.197-6.803-2.197c-2.684,0-4.816,0.711-6.395,2.145s-2.724,3.197-3.447,5.276 l-9.039-3.763c1.197-3.395,3.395-6.395,6.618-8.987c3.224-2.592,7.342-3.895,12.342-3.895c3.697,0,7.026,0.711,9.974,2.145 c2.947,1.434,5.263,3.421,6.934,5.947c1.671,2.539,2.5,5.382,2.5,8.539c0,3.224-0.776,5.947-2.329,8.184 c-1.553,2.237-3.461,3.947-5.724,5.145v0.539c2.987,1.25,5.421,3.158,7.342,5.724c1.908,2.566,2.868,5.632,2.868,9.211 s-0.908,6.776-2.724,9.579c-1.816,2.803-4.329,5.013-7.513,6.618c-3.197,1.605-6.789,2.421-10.776,2.421 C73.408,129.263,69.145,127.934,65.211,125.276z"}),(0,l.jsx)("path",{fill:"#1A73E8",d:"M121.25,79.961l-9.974,7.25l-5.013-7.605l17.987-12.974h6.895v61.197h-9.895L121.25,79.961z"}),(0,l.jsx)("path",{fill:"#EA4335",d:"M148.882,196.25l47.368-47.368l-23.684-10.526l-23.684,10.526l-10.526,23.684L148.882,196.25z"}),(0,l.jsx)("path",{fill:"#34A853",d:"M33.092,172.566l10.526,23.684h105.263v-47.368H43.618L33.092,172.566z"}),(0,l.jsx)("path",{fill:"#4285F4",d:"M12.039-3.75C3.316-3.75-3.75,3.316-3.75,12.039v136.842l23.684,10.526l23.684-10.526V43.618h105.263 l10.526-23.684L148.882-3.75H12.039z"}),(0,l.jsx)("path",{fill:"#188038",d:"M-3.75,148.882v31.579c0,8.724,7.066,15.789,15.789,15.789h31.579v-47.368H-3.75z"}),(0,l.jsx)("path",{fill:"#FBBC04",d:"M148.882,43.618v105.263h47.368V43.618l-23.684-10.526L148.882,43.618z"}),(0,l.jsx)("path",{fill:"#1967D2",d:"M196.25,43.618V12.039c0-8.724-7.066-15.789-15.789-15.789h-31.579v47.368H196.25z"})]})})})},m=e=>{let{size:t,className:n}=e;return(0,l.jsxs)("svg",{className:(0,o.Z)("text-text-100",n),width:t,height:t,viewBox:"0 0 127 127",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)("path",{d:"M27.2 80c0 7.3-5.9 13.2-13.2 13.2C6.7 93.2.8 87.3.8 80c0-7.3 5.9-13.2 13.2-13.2h13.2V80zm6.6 0c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2v33c0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V80z",fill:"#E01E5A"}),(0,l.jsx)("path",{d:"M47 27c-7.3 0-13.2-5.9-13.2-13.2C33.8 6.5 39.7.6 47 .6c7.3 0 13.2 5.9 13.2 13.2V27H47zm0 6.7c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H13.9C6.6 60.1.7 54.2.7 46.9c0-7.3 5.9-13.2 13.2-13.2H47z",fill:"#36C5F0"}),(0,l.jsx)("path",{d:"M99.9 46.9c0-7.3 5.9-13.2 13.2-13.2 7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H99.9V46.9zm-6.6 0c0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V13.8C66.9 6.5 72.8.6 80.1.6c7.3 0 13.2 5.9 13.2 13.2v33.1z",fill:"#2EB67D"}),(0,l.jsx)("path",{d:"M80.1 99.8c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2-7.3 0-13.2-5.9-13.2-13.2V99.8h13.2zm0-6.6c-7.3 0-13.2-5.9-13.2-13.2 0-7.3 5.9-13.2 13.2-13.2h33.1c7.3 0 13.2 5.9 13.2 13.2 0 7.3-5.9 13.2-13.2 13.2H80.1z",fill:"#ECB22E"})]})},g=e=>{let{size:t,className:n}=e;return(0,l.jsxs)("svg",{version:"1.1",className:(0,o.Z)("text-text-100",n),width:t,height:t,viewBox:"0 0 273 191",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)("defs",{children:(0,l.jsx)("path",{id:"a",d:"m0.06 0.5h272v190h-272z"})}),(0,l.jsxs)("g",{"fill-rule":"evenodd",children:[(0,l.jsx)("path",{d:"m113 21.3c8.78-9.14 21-14.8 34.5-14.8 18 0 33.6 10 42 24.9a58 58 0 0 1 23.7-5.05c32.4 0 58.7 26.5 58.7 59.2s-26.3 59.2-58.7 59.2c-3.96 0-7.82-0.398-11.6-1.15-7.35 13.1-21.4 22-37.4 22a42.7 42.7 0 0 1-18.8-4.32c-7.45 17.5-24.8 29.8-45 29.8-21.1 0-39-13.3-45.9-32a45.1 45.1 0 0 1-9.34 0.972c-25.1 0-45.4-20.6-45.4-45.9 0-17 9.14-31.8 22.7-39.8a52.6 52.6 0 0 1-4.35-21c0-29.2 23.7-52.8 52.9-52.8 17.1 0 32.4 8.15 42 20.8",fill:"#00A1E0",mask:"url(#b)"}),(0,l.jsx)("path",{d:"m39.4 99.3c-0.171 0.446 0.061 0.539 0.116 0.618 0.511 0.37 1.03 0.638 1.55 0.939 2.78 1.47 5.4 1.9 8.14 1.9 5.58 0 9.05-2.97 9.05-7.75v-0.094c0-4.42-3.92-6.03-7.58-7.18l-0.479-0.155c-2.77-0.898-5.16-1.68-5.16-3.5v-0.093c0-1.56 1.4-2.71 3.56-2.71 2.4 0 5.26 0.799 7.09 1.81 0 0 0.542 0.35 0.739-0.173 0.107-0.283 1.04-2.78 1.14-3.06 0.106-0.293-0.08-0.514-0.271-0.628-2.1-1.28-5-2.15-8-2.15l-0.557 2e-3c-5.11 0-8.68 3.09-8.68 7.51v0.095c0 4.66 3.94 6.18 7.62 7.23l0.592 0.184c2.68 0.824 5 1.54 5 3.42v0.094c0 1.73-1.51 3.02-3.93 3.02-0.941 0-3.94-0.016-7.19-2.07-0.393-0.229-0.617-0.394-0.92-0.579-0.16-0.097-0.56-0.272-0.734 0.252l-1.1 3.06m81.7 0c-0.171 0.446 0.061 0.539 0.118 0.618 0.509 0.37 1.03 0.638 1.55 0.939 2.78 1.47 5.4 1.9 8.14 1.9 5.58 0 9.05-2.97 9.05-7.75v-0.094c0-4.42-3.91-6.03-7.58-7.18l-0.479-0.155c-2.77-0.898-5.16-1.68-5.16-3.5v-0.093c0-1.56 1.4-2.71 3.56-2.71 2.4 0 5.25 0.799 7.09 1.81 0 0 0.542 0.35 0.74-0.173 0.106-0.283 1.04-2.78 1.13-3.06 0.107-0.293-0.08-0.514-0.27-0.628-2.1-1.28-5-2.15-8-2.15l-0.558 2e-3c-5.11 0-8.68 3.09-8.68 7.51v0.095c0 4.66 3.94 6.18 7.62 7.23l0.591 0.184c2.69 0.824 5 1.54 5 3.42v0.094c0 1.73-1.51 3.02-3.93 3.02-0.943 0-3.95-0.016-7.19-2.07-0.393-0.229-0.623-0.387-0.921-0.579-0.101-0.064-0.572-0.248-0.733 0.252l-1.1 3.06m55.8-9.36c0 2.7-0.504 4.83-1.49 6.34-0.984 1.49-2.47 2.22-4.54 2.22s-3.55-0.724-4.52-2.21c-0.977-1.5-1.47-3.64-1.47-6.34 0-2.7 0.496-4.82 1.47-6.31 0.968-1.48 2.44-2.19 4.52-2.19s3.56 0.717 4.54 2.19c0.992 1.49 1.49 3.61 1.49 6.31m4.66-5.01c-0.459-1.55-1.17-2.91-2.12-4.05-0.951-1.14-2.15-2.06-3.58-2.72-1.42-0.665-3.1-1-5-1s-3.57 0.337-5 1c-1.42 0.664-2.63 1.58-3.58 2.72-0.948 1.14-1.66 2.5-2.12 4.05-0.455 1.54-0.686 3.22-0.686 5.01 0 1.79 0.231 3.47 0.686 5.01 0.457 1.55 1.17 2.91 2.12 4.05 0.951 1.14 2.16 2.05 3.58 2.7 1.43 0.648 3.11 0.978 5 0.978 1.89 0 3.57-0.33 4.99-0.978 1.42-0.648 2.63-1.56 3.58-2.7 0.949-1.14 1.66-2.5 2.12-4.05 0.454-1.54 0.685-3.22 0.685-5.01 0-1.78-0.231-3.47-0.685-5.01m38.3 12.8c-0.153-0.453-0.595-0.282-0.595-0.282-0.677 0.259-1.4 0.499-2.17 0.619-0.776 0.122-1.64 0.183-2.55 0.183-2.25 0-4.05-0.671-5.33-2-1.29-1.33-2.01-3.47-2-6.37 7e-3 -2.64 0.645-4.62 1.79-6.14 1.13-1.5 2.87-2.28 5.17-2.28 1.92 0 3.39 0.223 4.93 0.705 0 0 0.365 0.159 0.54-0.322 0.409-1.13 0.711-1.94 1.15-3.18 0.124-0.355-0.18-0.505-0.291-0.548-0.604-0.236-2.03-0.623-3.11-0.786-1.01-0.154-2.18-0.234-3.5-0.234-1.96 0-3.7 0.335-5.19 0.999-1.49 0.663-2.75 1.58-3.75 2.72-1 1.14-1.76 2.5-2.27 4.05-0.505 1.54-0.76 3.23-0.76 5.02 0 3.86 1.04 6.99 3.1 9.28 2.06 2.3 5.16 3.46 9.2 3.46 2.39 0 4.84-0.483 6.6-1.18 0 0 0.336-0.162 0.19-0.554l-1.15-3.16m8.15-10.4c0.223-1.5 0.634-2.75 1.28-3.72 0.967-1.48 2.44-2.29 4.51-2.29 2.07 0 3.44 0.814 4.42 2.29 0.65 0.975 0.934 2.27 1.04 3.72l-11.3-2e-3zm15.7-3.3c-0.397-1.49-1.38-3-2.02-3.69-1.02-1.09-2.01-1.86-3-2.28a11.5 11.5 0 0 0-4.52-0.917c-1.97 0-3.76 0.333-5.21 1.01-1.45 0.682-2.67 1.61-3.63 2.77-0.959 1.16-1.68 2.53-2.14 4.1-0.46 1.55-0.692 3.25-0.692 5.03 0 1.82 0.241 3.51 0.715 5.04 0.479 1.54 1.25 2.89 2.29 4.01 1.04 1.13 2.37 2.01 3.97 2.63 1.59 0.615 3.52 0.934 5.73 0.927 4.56-0.015 6.96-1.03 7.94-1.58 0.175-0.098 0.34-0.267 0.134-0.754l-1.03-2.89c-0.158-0.431-0.594-0.275-0.594-0.275-1.13 0.422-2.73 1.18-6.48 1.17-2.45-4e-3 -4.26-0.727-5.4-1.86-1.16-1.16-1.74-2.85-1.83-5.25l15.8 0.012s0.416-4e-3 0.459-0.41c0.017-0.168 0.541-3.24-0.471-6.79zm-142 3.3c0.223-1.5 0.635-2.75 1.28-3.72 0.968-1.48 2.44-2.29 4.51-2.29 2.07 0 3.44 0.814 4.42 2.29 0.649 0.975 0.933 2.27 1.04 3.72l-11.3-2e-3zm15.7-3.3c-0.396-1.49-1.38-3-2.02-3.69-1.02-1.09-2.01-1.86-3-2.28a11.5 11.5 0 0 0-4.52-0.917c-1.97 0-3.76 0.333-5.21 1.01-1.45 0.682-2.67 1.61-3.63 2.77-0.957 1.16-1.68 2.53-2.14 4.1-0.459 1.55-0.69 3.25-0.69 5.03 0 1.82 0.239 3.51 0.716 5.04 0.478 1.54 1.25 2.89 2.28 4.01 1.04 1.13 2.37 2.01 3.97 2.63 1.59 0.615 3.51 0.934 5.73 0.927 4.56-0.015 6.96-1.03 7.94-1.58 0.174-0.098 0.34-0.267 0.133-0.754l-1.03-2.89c-0.159-0.431-0.595-0.275-0.595-0.275-1.13 0.422-2.73 1.18-6.48 1.17-2.44-4e-3 -4.26-0.727-5.4-1.86-1.16-1.16-1.74-2.85-1.83-5.25l15.8 0.012s0.416-4e-3 0.459-0.41c0.017-0.168 0.541-3.24-0.472-6.79zm-49.8 13.6c-0.619-0.494-0.705-0.615-0.91-0.936-0.313-0.483-0.473-1.17-0.473-2.05 0-1.38 0.46-2.38 1.41-3.05-0.01 2e-3 1.36-1.18 4.58-1.14a32 32 0 0 1 4.28 0.365v7.17h2e-3s-2 0.431-4.26 0.567c-3.21 0.193-4.63-0.924-4.62-0.921zm6.28-11.1c-0.64-0.047-1.47-0.07-2.46-0.07-1.35 0-2.66 0.168-3.88 0.498-1.23 0.332-2.34 0.846-3.29 1.53a7.63 7.63 0 0 0-2.29 2.6c-0.559 1.04-0.844 2.26-0.844 3.64 0 1.4 0.243 2.61 0.723 3.6a6.54 6.54 0 0 0 2.06 2.47c0.877 0.638 1.96 1.11 3.21 1.39 1.24 0.283 2.64 0.426 4.18 0.426 1.62 0 3.23-0.136 4.79-0.399a95.1 95.1 0 0 0 3.97-0.772c0.526-0.121 1.11-0.28 1.11-0.28 0.39-0.099 0.36-0.516 0.36-0.516l-9e-3 -14.4c0-3.16-0.844-5.51-2.51-6.96-1.66-1.45-4.09-2.18-7.24-2.18-1.18 0-3.09 0.16-4.23 0.389 0 0-3.44 0.668-4.86 1.78 0 0-0.312 0.192-0.142 0.627l1.12 3c0.139 0.389 0.518 0.256 0.518 0.256s0.119-0.047 0.259-0.13c3.03-1.65 6.87-1.6 6.87-1.6 1.7 0 3.02 0.345 3.9 1.02 0.861 0.661 1.3 1.66 1.3 3.76v0.667c-1.35-0.196-2.6-0.309-2.6-0.309zm127-8.13a0.428 0.428 0 0 0-0.237-0.568c-0.269-0.102-1.61-0.385-2.64-0.449-1.98-0.124-3.08 0.21-4.07 0.654-0.978 0.441-2.06 1.15-2.66 1.97l-2e-3 -1.92c0-0.264-0.187-0.477-0.453-0.477h-4.04c-0.262 0-0.452 0.213-0.452 0.477v23.5a0.48 0.48 0 0 0 0.479 0.479h4.14a0.479 0.479 0 0 0 0.478-0.479v-11.8c0-1.58 0.174-3.15 0.521-4.14 0.342-0.979 0.807-1.76 1.38-2.32a4.79 4.79 0 0 1 1.95-1.17 7.68 7.68 0 0 1 2.12-0.298c0.825 0 1.73 0.212 1.73 0.212 0.304 0.034 0.473-0.152 0.576-0.426 0.271-0.721 1.04-2.88 1.19-3.31",fill:"#FFFFFE"}),(0,l.jsx)("path",{d:"M162.201 67.548a13.258 13.258 0 0 0-1.559-.37 12.217 12.217 0 0 0-2.144-.166c-2.853 0-5.102.806-6.681 2.398-1.568 1.58-2.635 3.987-3.17 7.154l-.193 1.069h-3.581s-.437-.018-.529.459l-.588 3.28c-.041.314.094.51.514.508h3.486l-3.537 19.743c-.277 1.59-.594 2.898-.945 3.889-.346.978-.684 1.711-1.1 2.243-.403.515-.785.894-1.444 1.115-.544.183-1.17.267-1.856.267-.382 0-.89-.064-1.265-.139-.375-.074-.57-.158-.851-.276 0 0-.409-.156-.57.254-.131.335-1.06 2.89-1.17 3.206-.112.312.045.558.243.629.464.166.809.272 1.441.421.878.207 1.618.22 2.311.22 1.452 0 2.775-.204 3.872-.6 1.104-.399 2.065-1.094 2.915-2.035.919-1.015 1.497-2.078 2.05-3.528.547-1.437 1.013-3.221 1.386-5.3l3.554-20.109h5.196s.438.016.529-.459l.588-3.28c.041-.314-.093-.51-.515-.508h-5.043c.025-.114.254-1.888.833-3.558.247-.713.712-1.288 1.106-1.683a3.273 3.273 0 0 1 1.321-.822 5.48 5.48 0 0 1 1.693-.244c.475 0 .941.057 1.296.131.489.104.679.159.807.197.514.157.583.005.684-.244l1.206-3.312c.124-.356-.178-.506-.29-.55m-70.474 34.117c0 .264-.188.479-.452.479h-4.183c-.265 0-.453-.215-.453-.479V67.997c0-.263.188-.476.453-.476h4.183c.264 0 .452.213.452.476v33.668",fill:"#FFFFFE"})]})]})},p=e=>{let{size:t,className:n}=e;return(0,l.jsx)("svg",{className:(0,o.Z)("text-text-100",n),width:t,height:t,viewBox:"0 0 155 144",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{d:"M110.73 34.0139C110.73 52.5445 95.7048 67.581 77.1742 67.581C58.6319 67.581 43.607 52.5562 43.607 34.0139C43.607 15.4716 58.6319 0.446716 77.1742 0.446716C95.7048 0.446716 110.73 15.4716 110.73 34.0139ZM33.5671 75.967C15.0365 75.967 0 90.9919 0 109.523C0 128.053 15.0248 143.09 33.5671 143.09C52.1094 143.09 67.1343 128.065 67.1343 109.523C67.1343 90.9919 52.1094 75.967 33.5671 75.967ZM120.77 75.967C102.227 75.967 87.2024 90.9919 87.2024 109.534C87.2024 128.076 102.227 143.101 120.77 143.101C139.3 143.101 154.337 128.076 154.337 109.534C154.337 90.9919 139.312 75.967 120.77 75.967Z",fill:"#FF584A"})})};function v(e){let t,{type:n,size:r,className:a,variant:o,isError:d=!1}=e,v={size:r,className:a,variant:o};switch(n){case i.Fg.GITHUB:t=(0,l.jsx)(f,{...v});break;case i.Fg.GDRIVE:t=(0,l.jsx)(u,{...v});break;case i.Fg.SLACK:t=(0,l.jsx)(m,{...v});break;case i.Fg.SALESFORCE:t=(0,l.jsx)(g,{...v});break;case i.Fg.GMAIL:t=(0,l.jsx)(h,{...v});break;case i.Fg.GCAL:t=(0,l.jsx)(x,{...v});break;case i.Fg.ASANA:t=(0,l.jsx)(p,{...v});break;case i.Fg.OUTLINE:throw Error("Unknown sync source type");default:t=(0,l.jsx)(s.Z,{...v})}return(0,l.jsxs)(l.Fragment,{children:[t,d&&(0,l.jsx)("div",{className:"absolute bottom-0 left-0 border-0.5 border-border-300 rounded-md m-0.5 p-0.5 bg-danger-900 text-danger-000",children:(0,l.jsx)(c.v,{size:14})})]})}},11607:function(e,t,n){n.r(t),n.d(t,{Loading:function(){return s}});var r=n(27573),a=n(10607),l=n(7653),i=n(45790);let s=(0,l.memo)(function(e){let{size:t="md",fullscreen:n=!1,inheritColor:s,delay:c=0}=e,[o,u]=(0,l.useState)(c>0);return(0,l.useEffect)(()=>{if(!c)return;let e=setTimeout(()=>u(!1),c);return()=>clearTimeout(e)},[c]),(0,r.jsx)("div",{className:(0,a.Z)(n?"fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2":"m-auto"),children:(0,r.jsx)("div",{className:(0,a.Z)("sm"===t&&"h-4 w-4 border-2","md"===t&&"h-20 w-20 border-8",s?"border-current":"border-border-200","text-secondary inline-block animate-spin rounded-full border-solid border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]",o&&"hidden"),role:"status",children:(0,r.jsx)("span",{className:"sr-only",children:(0,r.jsx)(i.Z,{defaultMessage:"Loading...",id:"gjBiyjshwX"})})})})})},52939:function(e,t,n){n.d(t,{dm:function(){return h},mH:function(){return x},ol:function(){return m},u_:function(){return g}});var r=n(56683),a=n(27573),l=n(9788),i=n(80151),s=n(67905),c=n(10607),o=n(7653),u=n(70354);function d(){let e=(0,r._)(['\n  fixed\n  z-modal\n  inset-0\n  grid\n  items-center\n  justify-items-center\n  bg-black\n  bg-opacity-50\n  backdrop-brightness-75\n  overflow-y-auto\n  md:p-10\n  p-4\n  data-[state="open"]:animate-[fade_250ms_ease-in_forwards]\n  data-[state="closed"]:animate-[fade_125ms_ease-out_reverse_forwards]\n']);return d=function(){return e},e}function f(){let e=(0,r._)(['\n  flex\n  flex-col\n  focus:outline-none\n  relative\n  text-text-100\n  text-left\n  shadow-xl\n  border-0.5\n  border-border-300\n  rounded-2xl\n  md:p-6\n  p-4\n  align-middle\n  data-[state="open"]:animate-[zoom_250ms_ease-in_forwards]\n  data-[state="closed"]:animate-[zoom_125ms_ease-out_reverse_forwards]\n']);return f=function(){return e},e}function h(e){let{children:t,layout:n="right",className:r}=e;return(0,a.jsx)("div",{className:(0,c.Z)("mt-4 flex flex-col gap-2","left"===n&&"sm:flex-row","center"===n&&"justify-center sm:flex-row","right"===n&&"sm:flex-row-reverse","between"===n&&"justify-between sm:flex-row",r),children:t})}function x(e){let{...t}=e;return(0,a.jsx)(u.z,{...t})}function m(e){let{onClose:t}=e;return(0,a.jsx)("button",{className:"text-text-500 hover:text-text-400 hover:bg-bg-300 -ml-2 rounded-full px-2 py-2 transition-colors",onClick:()=>t(),children:(0,a.jsx)(i.X,{color:"currentColor",size:20})})}function g(e){let{title:t,subtitle:n,isOpen:r,className:l,children:i,onClose:u,icon:d,modalSize:f="md",backgroundColor:h,autoCloseOnFocusOut:x=!0,fullWidth:g=!0,fullHeight:b,hasCloseButton:_=!1,prependBeforeCloseButton:w,overlayChildren:j,overlayClassName:y}=e,[F,C]=(0,o.useState)(!1);(0,o.useEffect)(()=>{C(!0)},[]);let N=e=>{x||e.preventDefault()},[E,A]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{if(r)A(!0);else{let e=window.setTimeout(()=>{A(!1)},125);return setTimeout(()=>{document.body.style.pointerEvents="auto"},300),()=>{window.clearTimeout(e)}}},[r]),F)?(0,a.jsx)(s.fC,{open:r,onOpenChange:e=>{e||u()},children:E&&(0,a.jsx)(s.h_,{forceMount:!0,children:(0,a.jsxs)(p,{forceMount:!0,className:y,children:[(0,a.jsx)(v,{forceMount:!0,onOpenAutoFocus:e=>e.preventDefault(),onInteractOutside:N,onEscapeKeyDown:N,className:(0,c.Z)("min-w-0",l,g&&"w-full",b&&"h-full","sm"===f&&"max-w-sm","md"===f&&"max-w-md","lg"===f&&"max-w-lg","xl"===f&&"max-w-3xl","3xl"===f&&"max-w-6xl",null!=h?h:"bg-bg-200"),children:(0,a.jsxs)("div",{className:(0,c.Z)("flex flex-col min-h-full",b&&"w-full h-full"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[t&&(0,a.jsxs)(s.Dx,{className:"font-styrene-display text-text-100 flex w-full min-w-0 items-center text-lg font-medium leading-6 break-words",children:[d&&(0,a.jsx)("span",{className:"mr-2",children:d}),t]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[w,_&&(0,a.jsx)(m,{onClose:u})]})]}),n&&(0,a.jsx)("p",{className:"text-text-400 mb-2 text-sm",children:n}),i]})}),j]})})}):null}let p=(0,l.q)(s.aV)(d()),v=(0,l.q)(s.VY)(f())},22523:function(e,t,n){n.d(t,{LK:function(){return w},LZ:function(){return p},OT:function(){return _},QE:function(){return b},QQ:function(){return m},X2:function(){return g},pF:function(){return v}});var r=n(56683),a=n(9788);function l(){let e=(0,r._)(["\n  bg-bg-100\n  border-0.5\n  border-border-300\n  rounded-xl\n  p-6\n  md:p-8\n"]);return l=function(){return e},e}function i(){let e=(0,r._)(["\n  flex\n  flex-row\n  gap-8\n  justify-between\n  items-center\n  py-2\n"]);return i=function(){return e},e}function s(){let e=(0,r._)(["\n  flex flex-col\n  border-border-400\n  border-0.5\n"]);return s=function(){return e},e}function c(){let e=(0,r._)(["\n  text-text-100 \n  font-medium\n"]);return c=function(){return e},e}function o(){let e=(0,r._)(["\n  text-text-100 \n  font-medium\n"]);return o=function(){return e},e}function u(){let e=(0,r._)(["\n  text-text-300 \n  text-sm\n"]);return u=function(){return e},e}function d(){let e=(0,r._)(["py-8 px-10 bg-bg-100 border-0.5 border-border-300 rounded-xl"]);return d=function(){return e},e}function f(){let e=(0,r._)(["text-text-100 font-styrene text-lg font-medium tracking-tight"]);return f=function(){return e},e}function h(){let e=(0,r._)(["text-text-200 font-styrene tracking-tight"]);return h=function(){return e},e}function x(){let e=(0,r._)(["text-text-200 font-styrene text-sm tracking-tight"]);return x=function(){return e},e}let m=a.q.section(l()),g=a.q.div(i()),p=a.q.div(s()),v=a.q.p(c());a.q.p(o());let b=a.q.p(u());a.q.section(d());let _=a.q.h3(f()),w=a.q.p(h());a.q.p(x())},10228:function(e,t,n){var r,a,l,i;n.d(t,{Fg:function(){return r},co:function(){return a},hP:function(){return s},qC:function(){return c}}),(l=r||(r={})).GITHUB="github",l.GDRIVE="gdrive",l.OUTLINE="outlin",l.SALESFORCE="sfdc",l.GMAIL="gmail",l.GCAL="gcal",l.SLACK="slack",l.ASANA="asana";let s={github:"GitHub",gdrive:"Google Drive",outlin:"Outline",sfdc:"Salesforce",gmail:"Gmail",gcal:"Google Calendar",slack:"Slack",asana:"Asana"};(i=a||(a={})).CREATED="created",i.PENDING="pending",i.FAILED="failed",i.UPDATING="updating",i.UPDATE_FAILED="update_failed",i.READY="ready",i.UNAUTHENTICATED="unauthenticated",i.ACCESS_DENIED="access_denied",i.DISABLED="disabled";let c=["access_denied","failed","unauthenticated","update_failed"]}}]);