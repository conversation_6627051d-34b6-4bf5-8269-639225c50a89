"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8321],{48321:function(e,t,n){n.r(t),n.d(t,{AuthButton:function(){return d},ClaudeCodeWaitlist:function(){return y},DesktopAuthRedirect:function(){return el},Footer:function(){return N},GoogleAuthFlowKind:function(){return r},GoogleAuthLogin:function(){return X},LegalLink:function(){return et},MagicLinkForm:function(){return eC},MaxWidthContainer:function(){return C},OAuthConsentPage:function(){return eR},RedirectIfLoggedIn:function(){return eD},RefetchAndContinueButton:function(){return eF},SSOCallbackClientPage:function(){return eK},SSO_INITIATING_EMAIL_LOCAL_STORAGE_KEY:function(){return K},VerifyMagicLink:function(){return eb},setLastLoginMethod:function(){return B},webRedirectToDesktopAppURL:function(){return Q},withCurrentAccountRequired:function(){return eX}});var a,s,i,r,l=n(56683),o=n(9788);function c(){let e=(0,l._)(["\n  ","\n  flex\n  items-center\n  place-content-center\n  gap-2\n  font-medium\n  tracking-tight\n  ","\n"]);return c=function(){return e},e}let d=o.q.button(c(),"\n  px-4\n  py-2\n  rounded-lg\n  shadow-sm\n",e=>"primary"===e.$variant?"\n          bg-always-black\n          text-always-white/90\n          disabled:bg-always-black/50\n          disabled:text-always-white/50\n        ":"\n          bg-always-white\n          text-always-black/80\n        ");var u=n(27573),h=n(3053),f=n(27218),g=n(85689),x=n(7653),m=n(45790),p=n(5362),j=n(30947),v=n(13262);let b={"user:profile":{messageId:"oauth.scope.user.profile",description:(0,u.jsx)(m.Z,{defaultMessage:"Access your Anthropic profile information",id:"EvXAQK/bGO"})},"user:inference":{messageId:"oauth.scope.user.inference",description:(0,u.jsx)(m.Z,{defaultMessage:"Make API calls on your behalf",id:"n3ywBmpvAm"})},"org:profile":{messageId:"oauth.scope.org.profile",description:(0,u.jsx)(m.Z,{defaultMessage:"Access profile information for all members in your organization",id:"5CqC2vx+3K"})},"org:inference":{messageId:"oauth.scope.org.inference",description:(0,u.jsx)(m.Z,{defaultMessage:"Make API calls on behalf of your organization",id:"l/AJQVR0UT"})},"org:create_api_key":{messageId:"oauth.scope.org.create_api_key",description:(0,u.jsx)(m.Z,{defaultMessage:"Generate API keys on your behalf",id:"Li8bwfrwx/"})}};function y(e){let{onSignUpAllowed:t}=e,{account:n}=(0,f.t)(),a=(0,h.z$)(),{data:s,isLoading:i}=function(e){let{activeOrganization:t}=(0,f.t)(),n=null==t?void 0:t.uuid;return(0,p.WE)("/api/account/tengu_waitlist/".concat(n),{queryKey:[v.l8,n],enabled:!!n})}(),[r,l]=(0,x.useState)(!1);(0,x.useEffect)(()=>{(null==s?void 0:s.is_allowed)&&t()},[s,t]);let o=(null==n?void 0:n.email_address)||"";(0,x.useEffect)(()=>{(null==s?void 0:s.is_allowed)===!1&&a.track({email:o,event_key:"tengu.waitlist.update",is_subscribed:!0})},[a,o,null==s?void 0:s.is_allowed]);let c=(0,x.useCallback)(()=>{a.track({email:o,event_key:"tengu.waitlist.update",is_subscribed:!1}),l(!0)},[a,o]);return i||(null==s?void 0:s.is_allowed)?null:(0,u.jsx)("div",{className:"flex flex-col items-center justify-center p-6",children:(0,u.jsxs)("div",{className:"max-w-xl w-full",children:[(0,u.jsx)("h1",{className:"text-4xl font-medium font-styrene-display text-center mb-4",children:(0,u.jsx)(m.Z,{defaultMessage:"Claude Code is at capacity",id:"Ij/+1u4YwC"})}),(0,u.jsxs)("div",{className:"text-lg text-center text-text-300",children:[(0,u.jsx)("p",{className:"mb-8",children:(0,u.jsx)(m.Z,{defaultMessage:"Claude Code is currently experiencing high demand. Anthropic has paused sign ups to provide the best possible service to customers. We’ll notify you when we have a spot for you!",id:"AW28jlNhMw"})}),r?(0,u.jsxs)("p",{className:"text-text-500",children:[(0,u.jsx)(g.J,{className:"inline mr-1"}),"\xa0",(0,u.jsx)(m.Z,{defaultMessage:"You won’t be notified when signups resume",id:"x7yM13IhBB"})]}):(0,u.jsx)("button",{onClick:c,className:"text-text-500 underline hover:text-text-400 active:text-text-500",children:(0,u.jsx)(m.Z,{defaultMessage:"Don’t notify me when signups resume",id:"18uo0ORaen"})})]})]})})}var w=n(8571),k=n(50862),_=n(13737),M=n(10607);function Z(){let e=(0,l._)(["\n  max-w-6xl\n  mx-auto\n  p-2\n  px-6\n"]);return Z=function(){return e},e}let C=o.q.div(Z()),S=e=>{let{className:t,...n}=e;return(0,u.jsx)("li",{children:(0,u.jsx)("a",{className:(0,M.Z)("hover:text-oncolor-100",t),...n})})};function N(){let{applicationType:e,websiteBaseUrl:t}=(0,w.m)(),{openConsentBanner:n}=(0,k.C)();return(0,u.jsx)("footer",{className:"text-oncolor-200 bg-always-black py-4 text-sm",children:(0,u.jsx)(C,{className:"grid gap-8",children:(0,u.jsxs)("section",{className:"grid gap-4 sm:grid-cols-4",children:[(0,u.jsx)("div",{className:"text-always-white sm:col-span-2",children:(0,u.jsx)(_.V,{size:24})}),(0,u.jsxs)("ul",{className:"grid auto-rows-min gap-2 sm:row-span-2",children:[(0,u.jsx)(S,{href:"https://www.anthropic.com/product",children:(0,u.jsx)(m.Z,{defaultMessage:"Product",id:"x/ZVlUTP1H"})}),(0,u.jsx)(S,{href:"https://www.anthropic.com/research",children:(0,u.jsx)(m.Z,{defaultMessage:"Research",id:"JEe7dVso7F"})}),(0,u.jsx)(S,{href:"https://www.anthropic.com/careers",children:(0,u.jsx)(m.Z,{defaultMessage:"Careers",id:"yJ1ORHjbX8"})}),(0,u.jsx)(S,{href:"https://www.anthropic.com/company",children:(0,u.jsx)(m.Z,{defaultMessage:"Company",id:"9YazHG2WSm"})}),(0,u.jsx)(S,{href:"https://www.anthropic.com/index?subjects=announcements",children:(0,u.jsx)(m.Z,{defaultMessage:"News",id:"NZyhSXwwOq"})})]}),(0,u.jsxs)("ul",{className:"grid auto-rows-min gap-2 sm:row-span-2",children:[(0,u.jsx)(S,{href:"".concat(t,"/legal/").concat("claude-dot"===e?"consumer-terms":"commercial-terms"),children:"claude-dot"===e?(0,u.jsx)(m.Z,{defaultMessage:"Consumer Terms",id:"vPzYLV3Pyi"}):(0,u.jsx)(m.Z,{defaultMessage:"Commercial Terms",id:"vvH9eFKSOb"})}),(0,u.jsx)(S,{href:"https://www.anthropic.com/privacy",children:(0,u.jsx)(m.Z,{defaultMessage:"Privacy Policy",id:"vx0nkZ8xpy"})}),(0,u.jsx)("li",{children:(0,u.jsx)("button",{className:"hover:text-oncolor-100",onClick:n,children:(0,u.jsx)(m.Z,{defaultMessage:"Your Privacy Choices",id:"Hty8P+F08/"})})}),(0,u.jsx)(S,{href:"https://www.anthropic.com/responsible-disclosure-policy",children:(0,u.jsx)(m.Z,{defaultMessage:"Responsible Disclosure Policy",id:"XIAalfdbvx"})}),(0,u.jsx)(S,{href:"https://trust.anthropic.com/",children:(0,u.jsx)(m.Z,{defaultMessage:"Compliance",id:"NIi2K2ghec"})})]}),(0,u.jsx)("p",{className:"text-xs opacity-50 sm:col-span-2 sm:self-end",children:(0,u.jsx)(m.Z,{defaultMessage:"This site is protected by reCAPTCHA Enterprise. The Google <privacyLink>Privacy Policy</privacyLink> and <termsLink>Terms of Service</termsLink> apply.",id:"vKvtiZVDgg",values:{privacyLink:e=>(0,u.jsx)("a",{className:"underline",target:"_blank",rel:"noopener noreferrer",href:"https://policies.google.com/privacy",children:e}),termsLink:e=>(0,u.jsx)("a",{className:"underline",target:"_blank",rel:"noopener noreferrer",href:"https://policies.google.com/terms",children:e})}})})]})})})}var L=n(70354),E=n(95622),z=n(35807),A=n(5007),T=n(50294),I=n(88146),P=n(81695),H=n(40950),R=n(19170),O=n(8322);let q=["signups_disabled","region_missing","signups_disabled_in_region","signin_temporarily_disabled","no_console_account","invalid_recaptcha","account_banned","email_auth_disabled","bad_network"],V=e=>"string"==typeof e&&q.includes(e),D=e=>{let{type:t}=e,n=(0,w.Z)(),{websiteBaseUrl:a}=(0,w.m)();switch(t){case"signups_disabled":return(0,u.jsx)(u.Fragment,{children:(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Unfortunately, Claude is not available to new users right now. We’re working hard to expand our availability soon.",id:"X/BBHLVHoU"})})});case"region_missing":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"There was an error logging in. If you believe you received this message in error, please <link>contact support</link>.",id:"hHHVXQS6EX",values:{link:e=>(0,u.jsx)(I.default,{className:"underline",href:"https://support.anthropic.com/",target:"_blank",children:e})}})});case"signups_disabled_in_region":return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Unfortunately, {product} is only available in <link>certain regions</link> right now. We’re working hard to expand to other regions soon.",id:"ccGmik8Lac",values:{product:n?"Claude.ai":"Claude",link:e=>(0,u.jsx)("a",{className:"underline",href:n?"https://www.anthropic.com/claude-ai-locations":"https://www.anthropic.com/supported-countries",children:e})}})}),(0,u.jsx)("p",{className:"pt-2",children:(0,u.jsx)("a",{className:"underline",href:"https://docs.google.com/forms/d/e/1FAIpQLSdokCNPiZvSXohDuLVJdekevCTgMeZglKrauGmLkfwdLMCZaw/viewform?usp=sf_link",children:(0,u.jsx)(m.Z,{defaultMessage:"Get notified when Claude is available in your region.",id:"+7tWzJ3/oJ"})})})]});case"signin_temporarily_disabled":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Logging in is currently disabled. Please check back later.",id:"ltWJgvSQAd"})});case"invalid_recaptcha":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"We were unable to validate your browser. Please try again.",id:"qinEgHj3jD"})});case"no_console_account":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"There was an error logging in. If you believe you received this message in error, please <link>contact support</link>.",id:"hHHVXQS6EX",values:{link:e=>(0,u.jsx)(I.default,{className:"underline",href:"https://support.anthropic.com/",target:"_blank",children:e})}})});case"account_banned":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Your account has been disabled after an automatic review of your recent activities. Please take a look at our <tosLink>Terms of Service</tosLink> and <aupLink>Usage Policy</aupLink> for more information. If you wish to appeal your suspension, please visit our <supportLink>Trust & Safety Center</supportLink>.",id:"Amh83qRsB8",values:{tosLink:e=>(0,u.jsx)(I.default,{className:"underline",href:"".concat(a,"/legal/").concat(n?"consumer-terms":"commercial-terms"),target:"_blank",children:e}),aupLink:e=>(0,u.jsx)(I.default,{className:"underline",href:"https://claude.ai/legal/aup",target:"_blank",children:e}),supportLink:e=>(0,u.jsx)("a",{className:"underline",href:"https://support.anthropic.com/en/articles/8241253-trust-and-safety-warnings-and-appeals",children:e})}})});case"email_auth_disabled":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Login with email has been disabled for this account. Please choose another login method.",id:"80gn7FstL7"})});case"bad_network":return(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Your network has been blocked from accessing Claude. For assistance, please visit <link>support.anthropic.com</link>.",id:"1ZSD73HVtH",values:{link:e=>(0,u.jsx)("a",{href:"https://support.anthropic.com",children:e})}})})}},F=e=>(0,u.jsx)("div",{role:"alert",className:"bg-danger-900 border-danger-200 text-danger-000 rounded-lg border px-4 py-2",...e});var G=n(32737);function U(e){let{method:t,lastLoginMethod:n,children:a}=e;return t!==n?(0,u.jsx)(u.Fragment,{children:a}):(0,u.jsx)(G.u,{tooltipContent:"Last used",side:"left",children:(0,u.jsx)("div",{children:a})})}let K="ssoInitiatingEmail";function B(e){localStorage.setItem("lastLoginMethod",e)}let W=(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(m.Z,{defaultMessage:"If the problem persists <link>contact support</link> for assistance.",id:"rSHZ6kYY89",values:{link:e=>(0,u.jsx)(I.default,{className:"underline",href:"https://support.anthropic.com/",target:"_blank",children:e})}})}),J="claude://login/google-auth",Y="code";function Q(e){let t=new URL(J);return t.searchParams.set(Y,e),t.toString()}function X(e){let{googleOauthClientId:t}=(0,w.m)();return(0,u.jsx)(A.rg,{clientId:t,children:(0,u.jsx)($,{...e})})}function $(e){let{forceSelectAccount:t,joinToken:n,postAuthCallback:a,lastLoginMethod:s,authFlow:i=0,googleLoginSuccessCallback:r,disabled:l,onSsoRequiredError:o}=e,c=(0,H.Z)(),d=(0,O._A)(n,a),f=(0,P.useSearchParams)(),{track:g}=(0,h.z$)(),[p,v]=(0,x.useState)({kind:0}),b=(0,w.Z)(),{mutate:y}=(0,R.IR)({onSuccess:()=>{B("google"),d()},onError:e=>{let t=(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(m.Z,{defaultMessage:"There was an error logging you in. {emailSupport}",id:"E8pXZqQZw2",values:{emailSupport:W}})});if(e instanceof j.Hx&&"sso_required"===e.errorCode){var n;t=(0,u.jsx)(u.Fragment,{children:e.message});let a=null!==(n=e.extra.details.email)&&void 0!==n?n:void 0;o&&o(a)}e instanceof j.Hx&&V(e.message)&&(t=(0,u.jsx)(D,{type:e.message})),v({kind:0,inlineError:t})}}),{claudeAiAbsoluteUrl:k}=(0,w.m)(),_=(null==f?void 0:f.has("selectAccount"))||t,M={flow:"auth-code",onError:(0,x.useCallback)(e=>{let{error_description:t}=e;g({event_key:"login.google.error",error:null!=t?t:""}),v({kind:0,inlineError:t})},[v,g]),onNonOAuthError:(0,x.useCallback)(e=>{g({event_key:"login.google.error",error:e.type}),v({kind:0,inlineError:"popup_closed"===e.type?void 0:(0,u.jsx)(m.Z,{defaultMessage:"There was an error logging you in. {emailSupport}",id:"E8pXZqQZw2",values:{emailSupport:W}})})},[v,g]),select_account:_},Z=(0,x.useCallback)(e=>{let{code:t}=e;y({code:t,join_token:n,recaptcha_token:"DISABLED_FOR_TESTING",recaptcha_site_key:"DISABLED_FOR_TESTING",locale:b?c.locale:void 0})},[n,y,c.locale,b]),C=(0,A.Nq)({...M,onSuccess:Z}),S=(0,x.useCallback)(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let a=t[1];"object"==typeof a&&null!==a&&"code"in a&&"string"==typeof a.code?Z({code:a.code}):v({kind:0,inlineError:(0,u.jsx)(m.Z,{defaultMessage:"There was an error logging you in. {emailSupport}",id:"E8pXZqQZw2",values:{emailSupport:W}})})},[Z,v]);(0,E.H9)(E.LT.googleAuthCode,S);let N=(0,x.useCallback)(()=>{let e=new URL("/login/app-google-auth?open_in_browser=1",k);_&&e.searchParams.set("selectAccount","1"),window.location.href=e.toString()},[k,_]),I=(0,A.Nq)({...M,onSuccess:e=>{let{code:t}=e,n=new URL(J);n.searchParams.set(Y,t),console.log("Will open claude:// magic link:",n.toString()),window.location.href=n.toString(),r&&r(t)}}),q=(0,x.useCallback)(()=>{switch(i){case 0:C();break;case 1:N();break;case 2:I()}},[i,C,N,I]),G=(0,x.useCallback)(()=>{g({event_key:"login.google.started"}),v({kind:1}),q()},[q,g]),K=1===p.kind,Q=0===p.kind?p.inlineError:void 0;return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(U,{method:"google",lastLoginMethod:s,children:(0,u.jsx)(L.z,{variant:"ghost",size:"lg",className:"border-0.5 border-border-400 hover:border-border-300 w-full gap-2 !rounded-xl font-medium bg-always-white/90 hover:bg-always-white dark:bg-bg-100/50 dark:hover:bg-bg-100",onClick:G,disabled:K||l,"data-testid":"login-with-google",children:K?(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(z.U,{className:"mr-1 animate-spin",size:16}),(0,u.jsx)(m.Z,{defaultMessage:"Loading...",id:"gjBiyjshwX"})]}):(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(T.default,{alt:c.formatMessage({defaultMessage:"Google logo",id:"g+MI9/sOCK"}),width:16,height:16,src:"/images/google.svg"}),(0,u.jsx)(m.Z,{defaultMessage:"Continue with Google",id:"FJ6r9Eufij"})]})})}),Q&&(0,u.jsx)(F,{children:Q})]})}(a=i||(i={}))[a.Inactive=0]="Inactive",a[a.Active=1]="Active",(s=r||(r={}))[s.Browser=0]="Browser",s[s.DesktopApp=1]="DesktopApp",s[s.WebRedirectToDesktopApp=2]="WebRedirectToDesktopApp";var ee=n(18850);let et=e=>{let{type:t,children:n,className:a}=e,{websiteBaseUrl:s}=(0,w.m)(),i=(0,ee.q)(),r=null==i?void 0:i[t];return(0,u.jsx)(I.default,{target:"_blank",rel:"noopener",href:"".concat(s,"/legal/").concat(r?"archive/".concat(r):t),className:(0,M.Z)("underline",a),children:n})};function en(){let e=(0,P.useParams)(),[t,n]=(0,x.useState)("");return(0,x.useEffect)(()=>{n(window.location.hash.slice(1))},[e]),t}var ea=n(7992),es=n(77879),ei=n(67587);function er(e){let{heading:t,subheading:n,children:a}=e,s=(0,ei.F)();return(0,u.jsxs)("div",{className:"flex flex-col items-center gap-8",children:[(0,u.jsxs)("div",{className:"flex flex-col items-center gap-1 text-center",children:[(0,u.jsx)("h1",{className:(0,M.Z)("text-text-100 text-3xl sm:text-4xl","claude"===s.theme?"font-copernicus":"font-styrene-display"),children:t}),(0,u.jsx)("p",{className:(0,M.Z)("text-text-300 sm:text-md text-sm","claude"===s.theme?"font-tiempos":"font-styrene-display"),children:n})]}),a]})}function el(){let e=en(),t=e?"claude://claude.ai/magic-link#".concat(e):null;(0,x.useEffect)(()=>{t&&(console.log("Will open claude:// magic link:",t),window.location.href=t)},[t]);let[n,a]=(0,x.useState)(!1);(0,es.KS)(()=>{a(!0)},1e4);let s=(0,P.usePathname)(),i=(0,P.useSearchParams)();return e?(0,u.jsx)(er,{heading:(0,u.jsx)(m.Z,{defaultMessage:"Logging you in…",id:"BzrVCLxi6o"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Click the button below if the app does not open automatically.",id:"OrxlVBgRQC"}),children:(0,u.jsxs)("div",{className:"flex flex-col gap-4 items-center",children:[(0,u.jsx)(L.z,{size:"lg",variant:"secondary",onClick:()=>{t&&(window.location.href=t)},prepend:(0,u.jsx)(ea.O,{}),children:(0,u.jsx)(m.Z,{defaultMessage:"Open App",id:"Ya9IuD2AHE"})}),(0,u.jsx)(L.z,{variant:"underline",onClick:()=>{let e=new URLSearchParams(i.toString());e.delete("client");let t=e.toString(),n=window.location.hash,a="".concat(s).concat(t?"?".concat(t):"").concat(n);window.location.href=a},className:"transition-opacity",style:{opacity:n?1:0,visibility:n?"visible":"hidden",pointerEvents:n?"auto":"none"},children:(0,u.jsx)(m.Z,{defaultMessage:"Log In With Code",id:"20mr0misGY"})})]})}):null}var eo=n(40287),ec=n(11607),ed=n(68744),eu=n(6385),eh=n(93513);function ef(e){let{error:t}=e,n=(0,u.jsx)(m.Z,{defaultMessage:"We were unable to verify you with this link",id:"ZkoXKPVE1y"}),a="";if(t instanceof j.Hx){if("login_link_already_used"===t.message)n=(0,u.jsx)(m.Z,{defaultMessage:"This link has already been used",id:"JrJjWdFQsA"});else if("login_link_expired"===t.message)n=(0,u.jsx)(m.Z,{defaultMessage:"This link has expired",id:"GTybDvVcm0"});else if("login_link_not_found"===t.message)n=(0,u.jsx)(m.Z,{defaultMessage:"This link is not valid",id:"Eu2oG+nqCV"});else if("incomplete_sso"===t.errorCode)n=(0,u.jsx)(m.Z,{defaultMessage:"Your organization’s SSO setup is incomplete",id:"i125V+FMaU"});else if("no_new_orgs_for_parent"===t.errorCode)n=(0,u.jsx)(m.Z,{defaultMessage:"New organizations not allowed",id:"KDU2XXcW+D"}),a=(0,u.jsx)(m.Z,{defaultMessage:"Please contact your company’s IT administrator to be added to an existing organization. ",id:"KmKEc/9tf7"});else if("account_banned"===t.message)return(0,u.jsx)(eg,{})}return(0,u.jsx)(er,{heading:n,subheading:(0,u.jsxs)(u.Fragment,{children:[a,(0,u.jsx)(m.Z,{defaultMessage:"If the problem persists, please <link>contact support</link>.",id:"N9Z9S0uO71",values:{link:e=>(0,u.jsx)(I.default,{className:"underline",href:"https://support.anthropic.com/",children:e})}})]}),children:(0,u.jsx)("div",{children:(0,u.jsx)(L.z,{size:"lg",href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Try Again",id:"jsy7pkBETc"})})})})}function eg(){let{websiteBaseUrl:e}=(0,w.m)(),t=(0,w.Z)(),n="".concat(e,"/legal/").concat(t?"consumer-terms":"commercial-terms");return(0,u.jsx)(er,{heading:(0,u.jsx)(m.Z,{defaultMessage:"Your account has been disabled",id:"gH4qzoODAC"}),subheading:(0,u.jsxs)("p",{className:"max-w-lg",children:[(0,u.jsx)(m.Z,{defaultMessage:"Your account has been disabled after an automatic review of your recent activities.",id:"LvJHQlZLSB"}),(0,u.jsx)("br",{}),(0,u.jsx)(m.Z,{defaultMessage:"Please take a look at our <tosLink>Terms of Service</tosLink> and <aupLink>Usage Policy</aupLink> for more information.{br}If you wish to appeal your suspension, please visit our <trustLink>Trust & Safety Center</trustLink>.",id:"IxABwVosNB",values:{br:(0,u.jsx)("br",{}),tosLink:e=>(0,u.jsx)("a",{className:"inline-block underline",href:n,target:"_blank",children:e}),aupLink:e=>(0,u.jsx)("a",{className:"inline-block underline",href:"https://claude.ai/legal/aup",target:"_blank",children:e}),trustLink:e=>(0,u.jsx)("a",{className:"inline-block underline",href:"https://support.anthropic.com/en/articles/8241253-trust-and-safety-warnings-and-appeals",children:e})}})]}),children:(0,u.jsx)("div",{children:(0,u.jsx)(L.z,{size:"lg",href:n,children:(0,u.jsx)(m.Z,{defaultMessage:"Terms of Service",id:"32rBNKP0z1"})})})})}function ex(e){let{encodedEmailAddress:t,nonce:n}=e,{account:a,refetch:s}=(0,f.t)(),i=(0,eo.f)(),{recaptchaKeyId:r}=(0,w.m)(),{getRecaptchaToken:l}=(0,O.Z1)(),[o,c]=(0,x.useState)(null),[d,h]=(0,x.useState)("/"),{data:g,mutate:m}=(0,R.ml)({onSuccess:()=>{i.delete(eu.cn.JOIN_TOKEN);let e=i.get(eu.cn.RETURN_TO);e&&h((0,eh.G9)(e)),s().catch(e=>{c(e)})},onError:e=>{c(e)},meta:{noToast:!0}}),p=(0,H.Z)(),j=(0,w.Z)();return((0,O.Ql)(()=>{l(r,"VERIFY_MAGIC_LINK").catch(()=>"").then(e=>{let a={credentials:{method:"nonce",nonce:n,encoded_email_address:t},recaptcha_token:e,recaptcha_site_key:r,locale:j?p.locale:void 0},s=i.get(eu.cn.JOIN_TOKEN);s&&(a.join_token=s),m(a)})}),a&&g)?(0,u.jsx)(ed.Redirect,{to:null!=d?d:"/"}):o?(0,u.jsx)(ef,{error:o}):(0,u.jsx)(ec.Loading,{size:"md"})}var em=n(17582),ep=n(62168),ej=n(11379);function ev(e){let{nonce:t,encodedEmailAddress:n}=e,{recaptchaKeyId:a}=(0,w.m)(),{getRecaptchaToken:s}=(0,O.Z1)(),[i,r]=(0,x.useState)(null),{data:l,mutate:o}=(0,R.yU)({onError:e=>{r(e)},meta:{noToast:!0}}),{didCopy:c,copyToClipboard:d}=(0,em.m)();return((0,O.Ql)(()=>{s(a,"EXCHANGE_MAGIC_LINK").catch(()=>"").then(e=>{o({nonce:t,encoded_email_address:n,recaptcha_token:e,recaptcha_site_key:a})})}),i)?(0,u.jsx)(ef,{error:i}):l?(0,u.jsxs)(er,{heading:(0,u.jsx)(m.Z,{defaultMessage:"Use verification code to continue",id:"H5oyc838uC"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Enter this verification code where you first tried to sign in",id:"Gvwuso1lG1"}),children:[(0,u.jsx)("div",{className:"border-0.5 border-border-300 bg-bg-100 text-text-000 rounded-2xl px-5 py-5 text-5xl sm:px-8",children:l.code}),(0,u.jsx)("div",{children:(0,u.jsx)(L.z,{size:"lg",onClick:()=>{var e;d(null!==(e=null==l?void 0:l.code.toString())&&void 0!==e?e:"")},prepend:c?(0,u.jsx)(ep.f,{}):(0,u.jsx)(ej.C,{}),children:(0,u.jsx)(m.Z,{defaultMessage:"Copy Code",id:"BKfcE2cnNL"})})})]}):(0,u.jsx)(ec.Loading,{size:"md"})}function eb(e){let{isPendingLogin:t}=e,n=en(),[a,s]=n.split(":");return n?a&&s?t?(0,u.jsx)(ex,{encodedEmailAddress:s,nonce:a}):(0,u.jsx)(ev,{encodedEmailAddress:s,nonce:a}):(0,u.jsx)(ef,{}):null}var ey=n(94872),ew=n(5984),ek=n(14448),e_=n(13883),eM=n(45144);function eZ(){let e=(0,l._)(["flex flex-col gap-4"]);return eZ=function(){return e},e}function eC(e){let{forceEmail:t,email:n,setEmail:a,returnTo:s,joinToken:i,legalDocumentTypes:r=[],postAuthCallback:l,onLoginInitiated:o,lastLoginMethod:c,initialState:d="input_email",allowedLoginMethods:h,emailPlaceholder:f,continueButtonText:g}=e,m=(0,eo.f)(),[p,j]=(0,x.useState)(d),v=(0,O._A)(i,l),b=(0,ee.q)(),y=(0,x.useCallback)(()=>{if(null==o||o(!0),j("email_sent"),b){let e=r.map(e=>({document_id:b[e],accepted_via_checkbox:!1}));m.set(eu.cn.LEGAL_ACCEPTANCES,JSON.stringify(e),{maxAgeSeconds:3600})}i&&m.set(eu.cn.JOIN_TOKEN,i,{maxAgeSeconds:3600}),s&&m.set(eu.cn.RETURN_TO,s,{maxAgeSeconds:3600})},[o,b,i,s,r,m]);return"input_email"===p?(0,u.jsx)(eS,{email:n,setEmail:a,forceEmail:t,onSuccess:y,lastLoginMethod:c,allowedLoginMethods:h,placeholder:f,continueButtonText:g}):"email_sent"===p?(0,u.jsx)(eA,{email:n,onInputCode:()=>j("input_code"),onSendAgain:()=>{null==o||o(!1),j("input_email")}}):(0,u.jsx)(eN,{email:n,joinToken:i,onSendAgain:()=>{null==o||o(!1),j("input_email")},onSuccess:()=>{(0,ey.bq)("event","log_in"),B("magic_link"),v()}})}function eS(e){let{email:t,setEmail:n,forceEmail:a,onSuccess:s,lastLoginMethod:i,allowedLoginMethods:r,placeholder:l,continueButtonText:o}=e,c=(0,H.Z)(),d=(0,w.Z)(),[h,f]=(0,x.useState)(null),[g,p]=(0,x.useState)(!1),j=(0,P.useRouter)(),[v,b]=(0,x.useState)("primary"),{mutate:y,isPending:k}=(0,R.wO)({onSuccess:e=>{e.sso_url?(localStorage.setItem(K,t),j.push(e.sso_url),p(!0)):(f(null),s())},onError:e=>{f(e)},meta:{noToast:!0}}),{recaptchaKeyId:_}=(0,w.m)(),{getRecaptchaToken:M,isLoading:Z}=(0,O.Z1)(),C=async e=>{let n=await M(_,"SEND_MAGIC_LINK").catch(e=>((0,eM.Tb)(e),""));y({utc_offset:new Date().getTimezoneOffset(),email_address:t,recaptcha_token:n,recaptcha_site_key:_,login_intent:e,locale:d?c.locale:void 0})},S=Z||k||g,N=null!=l?l:c.formatMessage({defaultMessage:"Enter your personal or work email",description:"Please keep this short - if need be, can be translated as just “Enter your email”",id:"7F3FjLWGXi"}),E=r&&r.includes("sso"),z=E&&1===r.length;return(0,u.jsxs)(eT,{action:"",onSubmit:e=>{e.preventDefault(),b("primary");let t=null;E&&(t=z?"sso":"magic_link"),C(t)},children:[(0,u.jsx)(U,{method:"magic_link",lastLoginMethod:i,children:(0,u.jsx)(ew.oi,{"data-1p-ignore":!0,id:"email","data-testid":"email",value:t,type:"email",label:c.formatMessage({defaultMessage:"Email",id:"sy+pv5U9ls"}),size:"lg",className:"w-full",onChange:e=>n(e.target.value),placeholder:N,required:!0,readOnly:a,disabled:a,...!!h&&{"aria-invalid":!0,"aria-describedby":"email-error"}})}),!!h&&(0,u.jsx)(F,{className:"mt-2",id:"email-error",children:(0,u.jsx)(eL,{error:h})}),(0,u.jsx)(L.z,{size:"lg","data-testid":"continue",type:"submit",disabled:S,loading:"primary"===v&&S,children:z?(0,u.jsx)(m.Z,{defaultMessage:"Continue with SSO",id:"qank7+0V6w"}):null!=o?o:(0,u.jsx)(m.Z,{defaultMessage:"Continue with email",id:"l6yCDglZqT"})}),(0,u.jsx)("div",{className:"transition-all duration-200 ease-in-out ".concat(E&&!z?"opacity-100 translate-y-0":"opacity-0 -translate-y-4 pointer-events-none h-0"),children:(0,u.jsx)(L.z,{size:"lg",variant:"secondary",type:"submit",onClick:()=>{b("secondary"),C("sso")},disabled:S,loading:"secondary"===v&&S,className:"w-full",children:(0,u.jsx)(m.Z,{defaultMessage:"Continue with SSO",id:"qank7+0V6w"})})})]})}function eN(e){let{onSuccess:t,onSendAgain:n,email:a,joinToken:s}=e,i=(0,H.Z)(),[r,l]=(0,x.useState)(""),[o,c]=(0,x.useState)(null),{recaptchaKeyId:d}=(0,w.m)(),{getRecaptchaToken:h,isLoading:f}=(0,O.Z1)(),{mutate:g,isPending:p}=(0,R.ml)({onSuccess:()=>{c(null),t()},onError:e=>c(e),meta:{noToast:!0}}),j=(0,w.Z)(),v=async()=>{g({credentials:{method:"code",email_address:a,code:r},join_token:s,recaptcha_token:await h(d,"VERIFY_MAGIC_LINK").catch(e=>((0,eM.Tb)(e),"")),recaptcha_site_key:d,locale:j?i.locale:void 0})};return(0,u.jsxs)(eT,{action:"",onSubmit:e=>{e.preventDefault(),v()},children:[(0,u.jsxs)("div",{className:"text-text-400 text-center",children:[(0,u.jsx)("p",{className:"mb-4",children:(0,u.jsx)(m.Z,{defaultMessage:"Have a verification code instead?",id:"qhL6h9p/kx"})}),(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Enter the code generated from the link sent to",id:"gezCH0cDcD"})}),(0,u.jsx)("p",{className:"truncate font-medium",children:a})]}),(0,u.jsx)(ez,{}),(0,u.jsxs)("div",{children:[(0,u.jsx)(ew.oi,{id:"code","data-testid":"code",label:i.formatMessage({defaultMessage:"Login code",id:"2FuQjAui2U",description:"A field where the user enters a verification code sent to their email"}),size:"lg",className:"w-full text-center",value:r,onChange:e=>l(e.target.value.trim().toUpperCase()),placeholder:i.formatMessage({defaultMessage:"Enter verification code",id:"NSyuEP2pmX"}),type:"text",inputMode:"numeric",pattern:"[0-9]*",autoComplete:"one-time-code",autoFocus:!0,"data-1p-ignore":!0,...!!o&&{"aria-invalid":!0,"aria-describedby":"code-error"}}),!!o&&(0,u.jsx)(F,{className:"mt-2",id:"code-error",children:(0,u.jsx)(eE,{error:o})})]}),(0,u.jsx)(L.z,{size:"lg",variant:"primary","data-testid":"continue",type:"submit",className:"!rounded-xl",disabled:p||f,loading:p||f,children:(0,u.jsx)(m.Z,{defaultMessage:"Verify Email Address",id:"lwbklkLCpV"})}),(0,u.jsx)("p",{className:"text-text-400 text-sm tracking-tighter",children:(0,u.jsx)(m.Z,{defaultMessage:"Not seeing the email in your inbox? <link>Try sending again</link>.",id:"eVdgwMz2gs",values:{link:e=>(0,u.jsx)("button",{onClick:n,className:"underline",children:e})}})})]})}function eL(e){let{error:t}=e;if(t instanceof j.Hx){if(V(t.message))return(0,u.jsx)(D,{type:t.message});if(400===t.statusCode&&t.message.match(/email_address\b/))return(0,u.jsx)(m.Z,{defaultMessage:"Invalid email address. Please double-check the email entered and try again.",id:"L68uqJudwl"})}return(0,u.jsx)(m.Z,{defaultMessage:"There was an error sending you a login link. If the problem persists <link>contact support</link> for assistance.",id:"8q/0yT/ybe",values:{link:e=>(0,u.jsx)("a",{className:"underline",href:"https://support.anthropic.com/",target:"_blank",children:e})}})}function eE(e){let{error:t}=e;if(t instanceof j.Hx){if(V(t.message))return(0,u.jsx)(D,{type:t.message});if("already_used"===t.message)return(0,u.jsx)(m.Z,{defaultMessage:"Code already used. Please refresh the page and try again.",id:"J3lL/Wz4kn"});if("rate_limit_error"===t.type)return(0,u.jsx)(u.Fragment,{children:t.message});if(400===t.statusCode)return(0,u.jsx)(m.Z,{defaultMessage:"Incorrect code entered. Please check that the code matches what you received in your other browser or device and try again.",id:"WppuwpRf3J"})}return(0,u.jsx)(m.Z,{defaultMessage:"There was an error verifying your code. If the problem persists <link>contact support</link> for assistance.",id:"zzOZLy0ngS",values:{link:e=>(0,u.jsx)("a",{className:"underline",href:"https://support.anthropic.com/",target:"_blank",children:e})}})}let ez=()=>{let{value:e}=(0,ek.F)("email_login_deliverability_notice");return e?(0,u.jsxs)("div",{className:"text-sm text-danger-000 flex flex-col items-center gap-2 border rounded-xl p-3 border-danger-100",children:[(0,u.jsx)("div",{children:(0,u.jsx)(m.Z,{defaultMessage:"We are experiencing delivery issues with some email providers and are working to resolve this.",id:"bXkNSli+MX"})}),(0,u.jsx)("div",{children:(0,u.jsx)(m.Z,{defaultMessage:"Check your junk/spam and quarantine folders and ensure that <link><EMAIL></link> is on your allowed senders list.",id:"OOPO5NvL1R",values:{link:e=>(0,u.jsx)(I.default,{className:"font-medium",href:"mailto:<EMAIL>",children:e})}})})]}):null};function eA(e){let{onInputCode:t,onSendAgain:n,email:a}=e;return(0,es.KS)(t,5e3),(0,u.jsxs)(eT,{children:[(0,u.jsxs)("div",{className:"flex grow flex-col items-center justify-center gap-3 rounded-xl pb-10 pt-8",children:[(0,u.jsx)(e_.K,{size:32,className:"text-accent-main-200"}),(0,u.jsxs)("div",{className:"text-center",children:[(0,u.jsx)("p",{className:"text-text-400",children:(0,u.jsx)(m.Z,{defaultMessage:"To continue, click the link sent to",id:"V5IldMz1xL"})}),(0,u.jsx)("span",{className:"text-text-300 font-medium",children:a})]})]}),/@(outlook\.com|hotmail\.com|live\.(com|ca))$/i.test(a)&&(0,u.jsx)(ez,{}),(0,u.jsxs)("div",{className:"text-text-400 flex flex-col items-center gap-3 text-sm tracking-tighter",children:[(0,u.jsx)("p",{className:"text-center",children:(0,u.jsx)(m.Z,{defaultMessage:"Signing in from another browser? <link>Enter verification code</link>",id:"Q26S85nbUM",values:{link:e=>(0,u.jsx)("button",{onClick:t,className:"underline","data-testid":"enter-code",children:e})}})}),(0,u.jsx)("p",{children:(0,u.jsx)(m.Z,{defaultMessage:"Not seeing the email in your inbox? <link>Try sending again</link>",id:"+tRAUahcqW",values:{link:e=>(0,u.jsx)("button",{onClick:n,className:"underline",children:e})}})})]})]})}let eT=o.q.form(eZ());var eI=n(18013),eP=n(44602),eH=n(60983);function eR(e){let{productSurface:t}=e,{account:n,activeOrganization:a}=(0,f.t)(),{switchAndRefresh:s}=(0,O.z6)(),{track:i}=(0,h.z$)(),r=(0,P.useRouter)(),[l,o]=(0,eI.A)("oauth_org_selected",!1),[c,d]=x.useState(!1),[v,w]=(0,x.useState)(!0),k=(0,P.useSearchParams)(),_=k.get("client_id"),M=k.get("redirect_uri"),Z=k.get("response_type"),C=k.get("scope"),S=k.get("state"),N=k.get("code_challenge"),E=k.get("code_challenge_method"),{data:z,error:A}=function(e){let{activeOrganization:t}=(0,f.t)();return(0,p.WE)("/v1/oauth/".concat(null==t?void 0:t.uuid,"/authorize?client_id=").concat(e),{enabled:!!e})}(_),I=function(e){let{activeOrganization:t}=(0,f.t)(),{track:n}=(0,h.z$)();return(0,p.uC)("/v1/oauth/".concat(null==t?void 0:t.uuid,"/authorize"),"POST",{onSuccess:(t,a)=>{n({event_key:"oauth.authorize.success",client_id:a.client_id,client_name:e}),t.redirect_uri&&window.location.assign(t.redirect_uri)},onError:(t,a)=>{let s="unknown";t instanceof j.Hx&&(s=t.type),n({event_key:"oauth.authorize.failure",client_id:a.client_id,client_name:e,error_type:s})}})}((null==z?void 0:z.client_name)||""),H="console"===t?"api":"chat",R=n&&n.memberships.map(e=>e.organization).filter(e=>e.capabilities.includes(H));if(!_)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Missing client_id parameter",id:"hXuDfHDNMR"})});if(!M)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Missing redirect_uri parameter",id:"9Fmgf+Zkd1"})});if("code"!==Z)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Invalid response_type: {responseType}. Expected: ‘code’",id:"LGhUfWU8AS",values:{responseType:Z||(0,u.jsx)(m.Z,{defaultMessage:"missing",id:"UY3SnwUIrB",description:"A placeholder in an error message"})}})});if(!C)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Missing scope parameter",id:"bkF9le2jiA"})});if(!S)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Missing state parameter",id:"k100YXtNvw"})});if(!N)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Missing code_challenge parameter",id:"mB/AKgD/Hb"})});if("S256"!==E)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Invalid code_challenge_method: {method}. Expected: ‘S256’",id:"4TV2Xe9SOE",values:{method:E||(0,u.jsx)(m.Z,{defaultMessage:"missing",id:"UY3SnwUIrB",description:"A placeholder in an error message"})}})});let q=C.split(" ").find(e=>!b[e]);if(q)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid OAuth Request",id:"sx/MKHhEAT"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Unknown scope: {scope}",id:"HMtR826877",values:{scope:q}})});if(A)return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"OAuth Request Failed",id:"i1b4iShlwX"}),subheading:A.message||(0,u.jsx)(m.Z,{defaultMessage:"Failed to get client information",id:"7wzwJpSnIl"})});if(!z)return null;let V=R&&R.length>1;return V&&(!l||c)?(0,u.jsx)(eO,{orgs:R,clientName:z.client_name,onSelectOrg:e=>{d(!0),o(!0),s(e)}}):(null==z?void 0:z.client_name)==="Claude Code"&&v?(0,u.jsxs)("div",{className:"min-h-screen flex flex-col justify-between bg-bg-200",children:[(0,u.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center",children:(0,u.jsx)(y,{onSignUpAllowed:()=>{w(!1)}})}),(0,u.jsx)(eq,{onSwitchOrg:V?()=>o(!1):void 0})]}):(0,u.jsxs)("div",{className:"min-h-screen flex flex-col justify-between bg-bg-200",children:[(0,u.jsx)("div",{className:"flex-1 flex flex-col items-center justify-center",children:(0,u.jsxs)("div",{className:"bg-bg-100/20 max-w-xl rounded-2xl border border-border-300 shadow-sm gap-8 flex flex-col pt-12 p-8",children:[(0,u.jsxs)("div",{className:"flex items-center justify-center",children:[(0,u.jsx)("div",{className:"h-12 w-12 flex items-center justify-center border border-border-300 rounded-lg",children:(0,u.jsx)(T.default,{src:z.client_logo_uri||"",alt:z.client_name,width:26,height:26})}),(0,u.jsx)("div",{className:"w-8 border-t border-dashed border-border-300"}),(0,u.jsx)("div",{className:"h-12 w-12 flex items-center justify-center border border-border-300 rounded-lg",children:(0,u.jsx)(T.default,{src:"claude-ai"===t?"/images/claude_icon_clay.png":"/images/anthropic_icon_ivory.png",alt:"Anthropic",width:26,height:26})})]}),(0,u.jsx)("div",{className:"text-center",children:(0,u.jsx)("h1",{className:"text-xl text-text-100",children:(0,u.jsx)(m.Z,{defaultMessage:"{clientName} would like to connect to your {product} organization {orgName}",id:"4lK02128Hd",values:{clientName:(0,u.jsx)("span",{className:"font-medium",children:z.client_name}),product:"claude-ai"===t?"Claude":"Anthropic",orgName:(0,u.jsx)("span",{className:"font-medium",children:null==a?void 0:a.name})}})})}),(0,u.jsxs)("div",{className:"bg-bg-100 rounded-xl p-6",children:[(0,u.jsx)("div",{className:"text-text-100 text-sm font-medium",children:(0,u.jsx)(m.Z,{defaultMessage:"YOUR ACCOUNT WILL BE USED TO:",id:"j4bZ0NDstj"})}),(0,u.jsx)("div",{children:null==C?void 0:C.split(" ").map(e=>{let t=b[e];return t?(0,u.jsxs)("div",{className:"flex items-start pt-4",children:[(0,u.jsx)(g.J,{size:22,className:"text-accent-main-200 mr-3 flex-shrink-0"}),(0,u.jsx)("span",{className:"text-text-100 text-base",children:t.description})]},e):null})})]}),(0,u.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,u.jsx)(L.z,{variant:"primary",onClick:()=>{i({event_key:"oauth.authorize.approved",client_id:_,client_name:z.client_name}),I.mutate({response_type:"code",client_id:_,organization_uuid:a.uuid,redirect_uri:M,scope:C,state:S,code_challenge:N,code_challenge_method:E})},size:"lg",className:"w-full",children:(0,u.jsx)(m.Z,{defaultMessage:"Authorize",id:"QwnGVYPlwS"})}),(0,u.jsx)(L.z,{variant:"ghost",size:"lg",className:"text-text-300 w-full",onClick:()=>{i({event_key:"oauth.authorize.denied",client_id:_,client_name:z.client_name}),r.push("/")},children:(0,u.jsx)(m.Z,{defaultMessage:"Decline",id:"pvtgR26QWY"})})]})]})}),(0,u.jsx)(eq,{onSwitchOrg:V?()=>o(!1):void 0})]})}function eO(e){let{orgs:t,clientName:n,onSelectOrg:a}=e;return(0,u.jsxs)("div",{className:"min-h-screen flex flex-col justify-between bg-bg-200",children:[(0,u.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,u.jsxs)("div",{className:"flex flex-col items-center gap-8",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("div",{className:"text-center text-4xl font-medium mb-2",children:(0,u.jsx)("span",{className:"text-text-200",children:(0,u.jsx)(m.Z,{defaultMessage:"Select organization",id:"AtARAM//Fq"})})}),(0,u.jsx)("div",{className:"text-center text-lg",children:(0,u.jsx)("span",{className:"text-text-300",children:(0,u.jsx)(m.Z,{defaultMessage:"Select which organization you would like to connect with {clientName}",id:"R4rJldtNWQ",values:{clientName:n}})})})]}),(0,u.jsx)("div",{className:"w-full max-w-xl border border-border-300 rounded-2xl bg-bg-300 px-6 shadow-sm",children:(0,u.jsx)("div",{className:"divide-y divide-text-300/20",children:t.map(e=>(0,u.jsxs)("button",{onClick:()=>a(e.uuid),className:"w-full text-left py-6 text-lg font-medium text-text-200 hover:text-text-100 transition-colors flex items-center justify-between",children:[e.name,(0,u.jsx)(eH.T,{className:"text-text-400",size:20})]},e.uuid))})}),(0,u.jsx)("div",{className:"max-w-xl text-text-400 text-sm",children:(0,u.jsx)(m.Z,{defaultMessage:"Can’t find your organization? Check your email for an invitation to join or contact your organization’s admin to request access.",id:"nBSuONF32d"})})]})}),(0,u.jsx)(eq,{})]})}function eq(e){let{onSwitchOrg:t}=e,{account:n}=(0,f.t)(),a=(0,P.useSearchParams)(),s=(0,P.usePathname)(),i=(0,eh.C2)(s,a.toString(),"/logout");return(0,u.jsxs)("div",{className:"flex flex-col py-6 text-center text-text-400 text-sm gap-1",children:[(0,u.jsx)("div",{children:(0,u.jsx)(m.Z,{defaultMessage:"Logged in as {email}",id:"FnFSiUloOJ",values:{email:(0,u.jsx)("span",{className:"font-medium",children:null==n?void 0:n.email_address})}})}),t&&(0,u.jsx)("button",{onClick:t,className:"text-text-300 hover:text-text-200 transition-colors",children:(0,u.jsx)(m.Z,{defaultMessage:"Switch organization",id:"0lmKIf9uGH"})}),(0,u.jsx)(I.default,{href:i,className:"text-text-300 hover:text-text-200 transition-colors",children:(0,u.jsx)(m.Z,{defaultMessage:"Switch account",id:"qs1y2yKQaa"})})]})}var eV=n(20388);function eD(e){let{children:t}=e,{data:n,isFetching:a}=(0,f.dr)(),s=null==n?void 0:n.account,i=(0,O.nJ)();return((0,x.useEffect)(()=>{s&&i()},[s,i]),s||a)?(0,u.jsx)(eV.F,{cover:!0}):t}function eF(){let{refetch:e}=(0,f.t)(),t=(0,P.useRouter)();return(0,u.jsx)(L.z,{size:"lg",className:"w-36",onClick:()=>{e().then(()=>t.push("/"))},children:(0,u.jsx)(m.Z,{defaultMessage:"Continue",id:"acrOozm08x"})})}var eG=n(77930),eU=n(5068);function eK(e){let{productSurface:t}=e,n=(0,P.useSearchParams)(),a=n.get("code"),s=n.get("state"),i=n.get("error"),r=n.get("error_description");if(i&&r){if("idp_initiated_sso_disabled"===i){let e=n.get("organization_id");if(e)return(0,u.jsx)(eW,{organizationId:e,productSurface:t})}return(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"SSO Authentication Error",id:"rBjbD8bHKt"}),subheading:r,button:(0,u.jsx)(L.z,{href:"/login?sso=true",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})})}return s?a?(0,u.jsx)(eB,{productSurface:t,code:a,state:s}):(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Invalid SSO callback url",id:"4eChDp03mD"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Missing required <code>code</code> query parameter",id:"KmhZFUJPio",values:{code:e=>(0,u.jsx)("code",{className:"font-mono",children:e})}}),button:(0,u.jsx)(L.z,{href:"/login?sso=true",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})}):(0,u.jsx)(eP.C,{productSurface:t,headline:(0,u.jsx)(m.Z,{defaultMessage:"Authentication Error",id:"57VhQbitLE"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"There was an error processing your authentication request. Please try logging in again.",id:"A6m+PJb2BQ"}),button:(0,u.jsx)(L.z,{href:"/login?sso=true",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})})}function eB(e){var t,n;let{productSurface:a,code:s,state:i}=e,[r,l]=(0,x.useState)(""),o=(0,eG.useQueryClient)(),{data:c,error:d}=(0,R.fb)(s,i,a),h=!!(c&&((null===(t=c.state)||void 0===t?void 0:t.kind)==="authenticated"||c.success)),{data:f}=(0,eU.useQuery)({queryKey:["invalidate-bootstrap"],queryFn:async()=>(await o.invalidateQueries({queryKey:[v.aY]}),!0),enabled:h});if(f&&(null==c?void 0:c.account)){let e=localStorage.getItem(K);return e&&c.account.email_address.toLowerCase()!==e.toLowerCase()?(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"Please confirm your email",id:"pDIQIF8xMX"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"You entered <b>{initiatingEmail}</b> into claude.ai, but you are currently logged in as <b>{currentEmail}</b>",id:"vWx1s8O529",values:{b:e=>(0,u.jsx)("b",{className:"font-medium",children:e}),initiatingEmail:e,currentEmail:c.account.email_address}}),button:(0,u.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,u.jsx)(L.z,{href:"/",children:(0,u.jsx)(m.Z,{defaultMessage:"Continue as {email}",id:"nKlwk79Yxp",values:{email:c.account.email_address}})}),(0,u.jsx)(L.z,{href:"/logout",variant:"secondary",children:(0,u.jsx)(m.Z,{defaultMessage:"Log out",id:"PlBReUqqzW"})})]})}):(0,u.jsx)(ed.Redirect,{to:"/"})}if(c&&"state"in c&&(null===(n=c.state)||void 0===n?void 0:n.kind)==="magic_link")return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"Please check your email",id:"jGxZ/OgrqN"}),button:(0,u.jsx)("div",{className:"flex justify-center",children:(0,u.jsx)("div",{className:"max-w-sm rounded-3xl p-6 border-0.5 border-border-200",children:(0,u.jsx)(eC,{email:r,setEmail:l,initialState:"email_sent",postAuthCallback:e=>e(),initialEmail:c.state.email})})})});if(d){if(d instanceof j.Hx)switch(d.errorCode){case"expired_code":return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"Expired Code",id:"3elrtxarAr"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Your login code has expired. Please login again",id:"hFMFaLyghn"}),button:(0,u.jsx)(L.z,{href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})});case"mismatched_domain":return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"Your email domain does not match your organization",id:"3ZMclN3S73"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Please contact your account administrator to ensure you have an account for Claude.ai",id:"0r3lQRm+/i"}),button:(0,u.jsx)(L.z,{href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})});case"profile_not_found":return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"We couldn’t find your account information",id:"800lEfphhc"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Please contact your account administrator for access to Claude.ai",id:"ah0XEVn44m"}),button:(0,u.jsx)(L.z,{href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})});case"service_unavailable":return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"Enterprise authentication is currently down",id:"dfbLtW8ZzI"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Please try again later. Follow <link>status.anthropic.com</link> for updates.",id:"DGKE1znDOn",values:{link:e=>(0,u.jsx)("a",{className:"underline",href:"https://status.anthropic.com",children:e})}}),button:(0,u.jsx)(L.z,{href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})});case"would_exceed_max_members":return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"We were unable to log you in",id:"qFAJDEyl7+"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Please contact your account administrator",id:"uZrKUruiqS"}),button:(0,u.jsx)(L.z,{href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})})}return(0,u.jsx)(eP.C,{productSurface:a,headline:(0,u.jsx)(m.Z,{defaultMessage:"We were unable to authenticate you with your identity provider",id:"A9Ry6llaQo"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"Please try again. If that doesn’t work, contact your account administrator or <link>Anthropic support</link>.",id:"5v8CN3Z1GI",values:{link:e=>(0,u.jsx)("a",{href:"https://support.anthropic.com",children:e})}}),button:(0,u.jsx)(L.z,{href:"/login",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})})}return(0,u.jsx)(eV.F,{})}function eW(e){let{organizationId:t,productSurface:n}=e,{data:a,isLoading:s,error:i}=(0,R.MZ)(t);return s?(0,u.jsx)(eV.F,{}):i||!a?(0,u.jsx)(eP.C,{productSurface:n,headline:(0,u.jsx)(m.Z,{defaultMessage:"SSO Authentication Error",id:"rBjbD8bHKt"}),subheading:(0,u.jsx)(m.Z,{defaultMessage:"An error occurred during the authentication process. Please try again.",id:"Wpb5i1oWy0"}),button:(0,u.jsx)(L.z,{href:"/login?sso=true",children:(0,u.jsx)(m.Z,{defaultMessage:"Back to Login",id:"DUepuoilGy"})})}):(0,u.jsx)(ed.Redirect,{to:a.url})}var eJ=n(35228),eY=n(92841);let eQ=()=>(0,u.jsx)(eV.F,{}),eX=(e,t)=>{let{errorMessage:n,permission:a,capability:s,skipOrganizationChecks:i=!1,skipPathnameInReturnToUrl:r=!1,onLoading:l=eQ,productSurface:o}=t;return function(t){let{account:c,activeOrganization:d,isLoading:h,isLoggedOut:g}=(0,f.t)(),p=(0,P.useRouter)(),j=(0,w.Z)(),v=(0,P.usePathname)(),b=(0,P.useSearchParams)(),y=(0,x.useCallback)(()=>{p.push((0,eh.C2)(v,b.toString(),"/login",{selectAccount:"true"}))},[p,v,b]),k=(0,x.useCallback)(()=>{p.push((0,eh.C2)(v,b.toString(),"/logout",{selectAccount:"true"}))},[p,v,b]),_=(0,O.wy)();return((0,x.useEffect)(()=>{c&&(0,eJ.cG)(c,j)?r?_(b.get("returnTo")):_(window.location.pathname+window.location.search):g&&y()},[c,_,g,y,j,b]),h||i||d)?c&&(i||d)?(a&&d&&!(0,eY.Fs)(c,d,a)&&(0,P.notFound)(),s&&d&&!d.capabilities.includes(s)&&(0,P.notFound)(),(0,u.jsx)(e,{...t})):l():g||c&&(0,eJ.cG)(c,j)?(0,u.jsx)(eV.F,{}):(0,u.jsx)(eP.C,{productSurface:o,headline:(0,u.jsx)(m.Z,{defaultMessage:"Account Error",id:"VZe77ZaRv/"}),subheading:n,button:(0,u.jsx)(L.z,{onClick:k,children:(0,u.jsx)(m.Z,{defaultMessage:"Logout",id:"C81/uGekIK"})})})}}},8322:function(e,t,n){n.d(t,{K5:function(){return p},Ql:function(){return k},Z1:function(){return w},_A:function(){return v},nJ:function(){return b},rN:function(){return j},wy:function(){return y},z6:function(){return x}});var a=n(8571),s=n(40287),i=n(27218),r=n(27895),l=n(18850),o=n(35228),c=n(6385),d=n(14448);n(92841);var u=n(81695),h=n(7653),f=n(19170),g=n(93513);let x=()=>{let e=(0,u.useRouter)(),t=(0,s.f)();return{refresh:()=>{e.refresh()},switchAndRefresh:(e,n)=>{t.set(c.cn.LAST_ACTIVE_ORG,e),n?location.pathname=n:location.reload()}}},m=e=>{let{account:t}=(0,i.t)();return t&&((0,o.wJ)({account:t,isClaudeDot:e})||(0,o.c6)(t,e))?"/onboarding":t&&(0,o.kK)(t,e)?"/invites":t&&(0,o.D_)(t,e)?"/create":e?"/new":"/dashboard"};function p(e){let{value:t}=(0,d.F)("show_affirmative_consent"),{value:n}=(0,d.F)("show_affirmative_consent_for_privacy_policy");return(0,h.useMemo)(()=>[...t===e?["aup","consumer-terms"]:[],...n===e?["privacy"]:[]],[n,t,e])}let j=(e,t,n)=>{let a=(0,l.q)(),{mutateAsync:s}=(0,f.k7)();return(0,h.useCallback)(async()=>{if(!e||!a||0===t.length)return;let i=t.map(e=>({document_id:"v3:".concat(e,":").concat(a[e]),accepted_via_checkbox:n}));await s({acceptances:i})},[e,a,t,s,n])},v=(e,t)=>{let{account:n,refetch:a,setActiveOrganizationUUID:s}=(0,i.t)(),[l,o]=(0,h.useState)(!1),[c,d]=(0,h.useState)(!1),u=b(),{addSuccess:g}=(0,r.e)(),{data:x}=(0,f.gq)(e);return(0,h.useEffect)(()=>{n&&l&&c&&(t?t(u):u())},[c,u,l,n,t]),(0,h.useCallback)(async()=>{o(!0),await a(),(null==x?void 0:x.organization_uuid)&&(s(x.organization_uuid),g("Successfully joined ".concat(x.organization_name))),d(!0)},[x,g,a,s])},b=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=(0,u.useSearchParams)().get("returnTo"),n=y(e);return(0,h.useCallback)(()=>{n(t)},[n,t])},y=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],{account:t}=(0,i.t)(),[n,s]=(0,h.useState)(!1),r=(0,u.useRouter)(),[l,o]=(0,h.useState)(null),c=(0,u.usePathname)(),d=(0,u.useSearchParams)(),f=(0,a.Z)(),x=m(f);return(0,h.useEffect)(()=>{(n||e)&&!t&&r.push((0,g.C2)(c,d.toString()))},[t,r,n,e,c,d]),(0,h.useEffect)(()=>{if(n&&t){let e=(0,g.eX)(t,l,x,f);e!==window.location.href.replace(window.location.origin,"")&&(r.push(e),r.refresh())}},[t,r,n,l,x,f]),(0,h.useCallback)(e=>{e&&o(e),s(!0)},[])};function w(){let[e,t]=(0,h.useState)(!1);return{getRecaptchaToken:(0,h.useCallback)(async(e,n)=>(t(!0),new Promise((a,s)=>{if("undefined"==typeof grecaptcha)return t(!1),s(Error("Recaptcha failed to load"));grecaptcha.enterprise.ready(()=>{grecaptcha.enterprise.execute(e,{action:n}).then(e=>{a(e),t(!1)},e=>{t(!1),s(e)})})})),[]),isLoading:e}}function k(e){let[t,n]=(0,h.useState)(!1);(0,h.useEffect)(()=>{if(!t){let t=setInterval(()=>{window.grecaptcha&&(n(!0),e())},10);return()=>clearInterval(t)}},[t,n,e])}},93513:function(e,t,n){n.d(t,{$6:function(){return l},C2:function(){return i},G9:function(){return r},eX:function(){return s},kY:function(){return o}});var a=n(35228);let s=(e,t,n,s)=>{if(!t||!t.startsWith("/"))return n;if((0,a.cG)(e,s)){let e=new URLSearchParams({returnTo:t});return"".concat(n,"?").concat(e.toString())}return r(t)},i=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/login",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s="".concat(e,"?").concat(t),i=new URLSearchParams(a);return i.append("returnTo",s),"".concat(n,"?").concat(i.toString())};function r(e){let t=new URL(e,"http://example.com");return t.pathname.replace(/^\/+/,"/")+t.search}function l(e){return e.reduce((e,t)=>{let n=localStorage.getItem(t);return null!==n&&e.push([t,n]),e},[])}function o(e){e.forEach(e=>{let[t,n]=e;localStorage.setItem(t,n)})}},27895:function(e,t,n){n.d(t,{ErrorsProvider:function(){return p},e:function(){return j},v:function(){return v}});var a=n(27573),s=n(35228),i=n(97975),r=n.n(i),l=n(55854),o=n.n(l),c=n(78646),d=n.n(c),u=n(81695),h=n(7653),f=n(15992),g=n(27218),x=n(18013);let m=(0,h.createContext)(void 0);function p(e){let{children:t}=e,n=(0,h.useRef)(1),[s,i]=(0,h.useState)([]),r=(0,h.useCallback)(e=>{i(t=>t.filter(t=>t.id!==e))},[]),l=(0,h.useCallback)((e,t)=>{let a=n.current++;return i(t=>[...t,{id:a,message:e instanceof Error?e.message:e,toastType:"error"}]),t&&setTimeout(()=>r(a),t),a},[r]),o=(0,h.useCallback)((e,t)=>{let a=n.current++;return i(t=>[...t,{id:a,message:e,toastType:"success"}]),t&&setTimeout(()=>r(a),t),a},[r]),c=(0,h.useMemo)(()=>({toasts:s,addError:l,addSuccess:o,clearToast:r}),[s,l,o,r]);return(0,a.jsx)(m.Provider,{value:c,children:t})}function j(){let e=(0,h.useContext)(m);if(!e)throw Error("Must be called within ErrorsProvider");return e}function v(){var e;let{config:t}=(0,f.useConfig)("claude_system_message"),n=t.get("id",null),a=t.get("title",null),i=t.get("message",null),l=t.get("displayFrequencyHours",null),{account:c}=(0,g.t)(),m=null!==(e=null==c?void 0:c.uuid)&&void 0!==e?e:"logged-out",p=(0,u.usePathname)(),[j,v]=(0,x.R)("dismissed-system-messages",{}),b=(0,h.useMemo)(()=>()=>v(e=>{let t=r()(e);return n&&d()(t,[m,n],Date.now()),t}),[v,m,n]);return(0,h.useMemo)(()=>{if(!c||(0,s.cG)(c,!0)||"/download"===p||!n)return!1;let e=o()(j,[m,n]);return!e||"boolean"==typeof e||!!l&&Date.now()-e>=36e5*l},[c,n,m,j,l,p])?{currentSystemMessageId:n,currentSystemMessageTitle:a,currentSystemMessageContent:i,dismissCurrentSystemMessage:b}:{currentSystemMessageId:null,currentSystemMessageTitle:null,currentSystemMessageContent:null,dismissCurrentSystemMessage:()=>null}}},18850:function(e,t,n){n.d(t,{LegalDocsProvider:function(){return r},q:function(){return l}});var a=n(27573),s=n(7653);let i=(0,s.createContext)(void 0),r=e=>{let{value:t,children:n}=e;return(0,a.jsx)(i.Provider,{value:t,children:n})};function l(){return(0,s.useContext)(i)}},18013:function(e,t,n){n.d(t,{A:function(){return s},R:function(){return i}});var a=n(77879);function s(e,t){return(0,a.Xs)("SSS-".concat(e),t,{serializer:JSON.stringify,deserializer:JSON.parse})}function i(e,t){return(0,a._)("LSS-".concat(e),t,{serializer:JSON.stringify,deserializer:JSON.parse})}},13737:function(e,t,n){n.d(t,{V:function(){return s}});var a=n(27573);function s(e){let{size:t=16}=e;return(0,a.jsx)("svg",{width:t,height:t,viewBox:"0 0 28 28",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M20 4h-4.3l7.7 19.5h4.2L20 4ZM7.6 4 0 23.5h4.3L6 19.4h8l1.6 4h4.3L12.1 4H7.7Zm-.4 11.8 2.6-6.9 2.7 6.9H7.3Z"})})}},64483:function(e,t,n){n.d(t,{I:function(){return s}});var a=n(27573);function s(e){let{height:t=14,className:n}=e;return(0,a.jsxs)("svg",{height:t,className:n,viewBox:"0 0 110 12",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg","aria-label":"Anthropic",children:[(0,a.jsx)("path",{d:"M26.92 2.43646H30.929V11.8011H33.4879V2.43646H37.4969V0.198895H26.92V2.43646Z"}),(0,a.jsx)("path",{d:"M22.3992 8.32044L17.0254 0.198895H14.1253V11.8011H16.5989V3.67956L21.9727 11.8011H24.8728V0.198895H22.3992V8.32044Z"}),(0,a.jsx)("path",{d:"M47.7326 4.8232H42.103V0.198895H39.544V11.8011H42.103V7.06077H47.7326V11.8011H50.2916V0.198895H47.7326V4.8232Z"}),(0,a.jsx)("path",{d:"M4.75962 0.198895L0 11.8011H2.66129L3.63471 9.36464H8.61422L9.58747 11.8011H12.2488L7.48914 0.198895H4.75962ZM4.49553 7.20994L6.12438 3.1326L7.75323 7.20994H4.49553Z"}),(0,a.jsx)("path",{d:"M71.4966 0C68.0506 0 65.611 2.48619 65.611 6.01657C65.611 9.51381 68.0506 12 71.4966 12C74.9256 12 77.348 9.51381 77.348 6.01657C77.348 2.48619 74.9256 0 71.4966 0ZM71.4966 9.67956C69.4836 9.67956 68.2553 8.28729 68.2553 6.01657C68.2553 3.71271 69.4836 2.32044 71.4966 2.32044C73.4926 2.32044 74.7038 3.71271 74.7038 6.01657C74.7038 8.28729 73.4926 9.67956 71.4966 9.67956Z"}),(0,a.jsx)("path",{d:"M107.27 7.90608C106.827 9.03315 105.94 9.67956 104.729 9.67956C102.716 9.67956 101.487 8.28729 101.487 6.01657C101.487 3.71271 102.716 2.32044 104.729 2.32044C105.94 2.32044 106.827 2.96685 107.27 4.09392H109.983C109.318 1.60773 107.322 0 104.729 0C101.283 0 98.843 2.48619 98.843 6.01657C98.843 9.51381 101.283 12 104.729 12C107.339 12 109.335 10.3757 110 7.90608H107.27Z"}),(0,a.jsx)("path",{d:"M90.9615 0.198895L95.7212 11.8011H98.3313L93.5717 0.198895H90.9615Z"}),(0,a.jsx)("path",{d:"M85.5707 0.198895H79.7364V11.8011H82.2953V7.59116H85.5707C88.2832 7.59116 89.938 6.19889 89.938 3.89503C89.938 1.59116 88.2832 0.198895 85.5707 0.198895ZM85.4513 5.35359H82.2953V2.43646H85.4513C86.7137 2.43646 87.379 2.9337 87.379 3.89503C87.379 4.85635 86.7137 5.35359 85.4513 5.35359Z"}),(0,a.jsx)("path",{d:"M63.6492 3.72928C63.6492 1.54144 61.9944 0.198895 59.2819 0.198895H53.4476V11.8011H56.0065V7.25967H58.8553L61.4144 11.8011H64.2463L61.4127 6.91376C62.8349 6.38254 63.6492 5.26392 63.6492 3.72928ZM56.0065 2.43646H59.1625C60.4249 2.43646 61.0903 2.88398 61.0903 3.72928C61.0903 4.57459 60.4249 5.0221 59.1625 5.0221H56.0065V2.43646Z"})]})}},22769:function(e,t,n){n.d(t,{s:function(){return i}});var a=n(27573),s=n(10607);function i(e){let{className:t}=e;return(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 184 40",className:(0,s.Z)("text-text-000",t),fill:"currentColor",children:[(0,a.jsx)("path",{shapeRendering:"optimizeQuality",fill:"#D97757",d:"m7.75 26.27 7.77-4.36.13-.38-.13-.21h-.38l-1.3-.08-4.44-.12-3.85-.16-3.73-.2-.94-.2L0 19.4l.09-.58.79-.53 1.13.1 2.5.17 3.75.26 2.72.16 4.03.42h.64l.09-.26-.22-.16-.17-.16-3.88-2.63-4.2-2.78-2.2-1.6L3.88 11l-.6-.76-.26-1.66L4.1 7.39l1.45.1.37.1 1.47 1.13 3.14 2.43 4.1 3.02.6.5.24-.17.03-.12-.27-.45L13 9.9l-2.38-4.1-1.06-1.7-.28-1.02c-.1-.42-.17-.77-.17-1.2L10.34.21l.68-.22 1.64.22.69.6 1.02 2.33 1.65 3.67 2.56 4.99.75 1.48.4 1.37.15.42h.26v-.24l.21-2.81.39-3.45.38-4.44.13-1.25.62-1.5L23.1.57l.96.46.79 1.13-.11.73-.47 3.05-.92 4.78-.6 3.2h.35l.4-.4 1.62-2.15 2.72-3.4 1.2-1.35 1.4-1.49.9-.71h1.7l1.25 1.86-.56 1.92-1.75 2.22-1.45 1.88-2.08 2.8-1.3 2.24.12.18.31-.03 4.7-1 2.54-.46 3.03-.52 1.37.64.15.65-.54 1.33-3.24.8-3.8.76-5.66 1.34-.07.05.08.1 2.55.24 1.09.06h2.67l4.97.37 1.3.86.78 1.05-.13.8-2 1.02-2.7-.64-6.3-1.5-2.16-.54h-.3v.18l1.8 1.76 3.3 2.98 4.13 3.84.21.95-.53.75-.56-.08-3.63-2.73-1.4-1.23-3.17-2.67h-.21v.28l.73 1.07 3.86 5.8.2 1.78-.28.58-1 .35-1.1-.2L26 33.14l-2.33-3.57-1.88-3.2-.23.13-1.11 11.95-.52.61-1.2.46-1-.76-.53-1.23.53-2.43.64-3.17.52-2.52.47-3.13.28-1.04-.02-.07-.23.03-2.36 3.24-3.59 4.85-2.84 3.04-.68.27-1.18-.61.11-1.09.66-.97 3.93-5 2.37-3.1 1.53-1.79-.01-.26h-.09L6.8 30.56l-1.86.24-.8-.75.1-1.23.38-.4 3.14-2.16Z"}),(0,a.jsx)("path",{shapeRendering:"optimizeQuality",d:"M64.48 33.54c-5.02 0-8.45-2.8-10.07-7.11a19.19 19.19 0 0 1-1.23-7.03c0-7.23 3.24-12.25 10.4-12.25 4.81 0 7.78 2.1 9.47 7.11h2.06l-.28-6.91c-2.88-1.86-6.48-2.8-10.86-2.8-6.17 0-11.42 2.76-14.34 7.74a16.77 16.77 0 0 0-2.22 8.65c0 5.53 2.61 10.43 7.51 13.15a17.51 17.51 0 0 0 8.73 2.06c4.78 0 8.57-.91 11.93-2.5l.87-7.62h-2.1c-1.26 3.48-2.76 5.57-5.25 6.68-1.22.55-2.76.83-4.62.83ZM86.13 7.15l.2-3.4h-1.42l-6.32 1.9v1.03l2.8 1.3v23.78c0 1.62-.83 1.98-3 2.25v1.74h10.75v-1.74c-2.18-.27-3-.63-3-2.25V7.16Zm42.75 29h.83l7.27-1.38v-1.78l-1.02-.08c-1.7-.16-2.14-.51-2.14-1.9V18.33l.2-4.07h-1.15l-6.87.99v1.74l.67.12c1.86.27 2.41.79 2.41 2.09v11.3c-1.78 1.38-3.48 2.25-5.5 2.25-2.24 0-3.63-1.14-3.63-3.8V18.34l.2-4.07h-1.18l-6.88.99v1.74l.71.12c1.86.27 2.41.79 2.41 2.09v10.43c0 4.42 2.5 6.52 6.48 6.52 3.04 0 5.53-1.62 7.4-3.87l-.2 3.87ZM108.9 22.08c0-5.65-3-7.82-8.42-7.82-4.78 0-8.25 1.98-8.25 5.26 0 .98.35 1.73 1.06 2.25l3.64-.48c-.16-1.1-.24-1.77-.24-2.05 0-1.86.99-2.8 3-2.8 2.97 0 4.47 2.09 4.47 5.45v1.1l-7.5 2.25c-2.5.68-3.92 1.27-4.87 2.65a5 5 0 0 0-.7 2.8c0 3.2 2.2 5.46 5.96 5.46 2.72 0 5.13-1.23 7.23-3.56.75 2.33 1.9 3.56 3.95 3.56 1.66 0 3.16-.67 4.5-1.98l-.4-1.38c-.58.16-1.14.24-1.73.24-1.15 0-1.7-.91-1.7-2.69v-8.26Zm-9.6 10.87c-2.05 0-3.32-1.19-3.32-3.28 0-1.42.67-2.25 2.1-2.73l6.08-1.93v5.84c-1.94 1.47-3.08 2.1-4.86 2.1Zm63.3 1.82v-1.78l-1.03-.08c-1.7-.16-2.13-.51-2.13-1.9V7.15l.2-3.4h-1.43l-6.32 1.9v1.03l2.8 1.3v7.82a8.83 8.83 0 0 0-5.37-1.54c-6.28 0-11.18 4.78-11.18 11.93 0 5.89 3.52 9.96 9.32 9.96 3 0 5.61-1.46 7.23-3.72l-.2 3.72h.84l7.27-1.38Zm-13.16-18.14c3 0 5.25 1.74 5.25 4.94v9a7.2 7.2 0 0 1-5.21 2.1c-4.3 0-6.48-3.4-6.48-7.94 0-5.1 2.49-8.1 6.44-8.1Zm28.53 4.5c-.56-2.64-2.18-4.14-4.43-4.14-3.36 0-5.69 2.53-5.69 6.16 0 5.37 2.84 8.85 7.43 8.85a8.6 8.6 0 0 0 7.39-4.35l1.34.36c-.6 4.66-4.82 8.14-10 8.14-6.08 0-10.27-4.5-10.27-10.9 0-6.45 4.55-10.99 10.63-10.99 4.54 0 7.74 2.73 8.77 7.47l-15.84 4.86v-2.14l10.67-3.31Z"})]})}},70354:function(e,t,n){n.d(t,{z:function(){return h}});var a=n(27573),s=n(11607),i=n(88755),r=n(49289),l=n(10607),o=n(88146),c=n(7653);let d="\n    text-text-000\n    border-0.5\n    border-border-300\n    relative\n    overflow-hidden\n    font-styrene\n    font-medium\n    transition\n    duration-100\n    hover:border-border-300/0\n    bg-bg-300/0\n    hover:bg-bg-400\n    backface-hidden",u=(0,r.j)("inline-flex\n  items-center\n  justify-center\n  relative\n  shrink-0\n  can-focus\n  select-none\n  disabled:pointer-events-none\n  disabled:opacity-50\n  disabled:shadow-none\n  disabled:drop-shadow-none",{variants:{variant:{primary:"bg-text-000\n        text-bg-000\n        relative\n        overflow-hidden\n        font-medium\n        font-styrene\n        transition-transform\n        will-change-transform\n        ease-[cubic-bezier(0.165,0.85,0.45,1)]\n        duration-150\n        hover:scale-y-[1.015]\n        hover:scale-x-[1.005]\n        backface-hidden\n        after:absolute\n        after:inset-0\n        after:bg-[radial-gradient(at_bottom,hsla(var(--bg-000)/20%),hsla(var(--bg-000)/0%))]\n        after:opacity-0\n        after:transition\n        after:duration-200\n        after:translate-y-2\n        hover:after:opacity-100\n        hover:after:translate-y-0",flat:"bg-accent-main-000\n          text-oncolor-100\n          font-styrene\n          font-medium\n          transition-colors\n          hover:bg-accent-main-200",secondary:d,outline:d,ghost:"text-text-300\n          border-transparent\n          transition\n          font-styrene\n          duration-300\n          ease-[cubic-bezier(0.165,0.85,0.45,1)]\n          hover:bg-bg-400\n          aria-pressed:bg-bg-400\n          aria-checked:bg-bg-400\n          aria-expanded:bg-bg-300\n          hover:text-text-100\n          aria-pressed:text-text-100\n          aria-checked:text-text-100\n          aria-expanded:text-text-100",underline:"opacity-80\n          transition-all\n          active:scale-[0.985]\n          hover:opacity-100\n          hover:underline\n          underline-offset-3",danger:"bg-danger-200\n          text-oncolor-100\n          font-styrene\n          font-medium\n          transition\n          hover:scale-y-[1.015]\n          hover:scale-x-[1.005]\n          hover:opacity-95",unstyled:""},size:{default:"h-9 px-4 py-2 rounded-lg min-w-[5rem] active:scale-[0.985] whitespace-nowrap text-sm",sm:"h-8 rounded-md px-3 text-xs min-w-[4rem] active:scale-[0.985] whitespace-nowrap",lg:"h-11 rounded-[0.6rem] px-5 min-w-[6rem] active:scale-[0.985] whitespace-nowrap",icon:"h-9 w-9 rounded-md active:scale-95 shrink-0",icon_xs:"h-6 w-6 rounded-md active:scale-95",icon_sm:"h-8 w-8 rounded-md active:scale-95",icon_lg:"h-11 w-11 rounded-[0.6rem] active:scale-95",inline:"px-0.5 rounded-[0.25rem]",unset:""},option:{rounded:"!rounded-full",prepend:"",append:""},state:{active:""}},compoundVariants:[{size:"default",option:"prepend",class:"pl-2 pr-3 gap-1"},{size:"lg",option:"prepend",class:"pl-2.5 pr-3.5 gap-1"},{size:"sm",option:"prepend",class:" pl-2 pr-2.5 gap-1"},{size:"default",option:"append",class:"pl-3 pr-2 gap-1"},{size:"lg",option:"append",class:"pl-3.5 pr-2.5 gap-1"},{size:"sm",option:"append",class:"pl-2.5 pr-2 gap-1"},{variant:"ghost",state:"active",class:"!bg-bg-400"}],defaultVariants:{variant:"primary",size:"default"}}),h=(0,c.forwardRef)((e,t)=>{let{className:n,variant:r,size:c,option:d,loading:h,href:f,onLinkClick:g,target:x,prepend:m,append:p,state:j,disabled:v,children:b,type:y="button",...w}=e;m&&(d="prepend"),p&&(d="append");let k=(0,i.useIsClaudeApp)(),_=(0,l.Z)(u({variant:r,size:c,option:d,state:j,className:n}),h&&"text-transparent ![text-shadow:_none]",k&&"cursor-default"),M=(0,a.jsxs)(a.Fragment,{children:[h&&(0,a.jsx)("div",{className:(0,l.Z)("absolute inset-0 flex items-center justify-center",r&&"flat"!==r&&"danger"!==r?"text-bg-300":"text-oncolor-100"),children:(0,a.jsx)(s.Loading,{size:"sm",inheritColor:!0,delay:0})}),m,b,p]});return f?(0,a.jsx)(o.default,{href:f,target:x||"_self",className:_,"aria-label":w["aria-label"],onClick:g,children:M}):(0,a.jsx)("button",{className:_,ref:t,disabled:v||h,type:y,...w,children:M})});h.displayName="Button"},32737:function(e,t,n){n.d(t,{u:function(){return f}});var a=n(56683),s=n(27573),i=n(9788),r=n(85638),l=n(10607),o=n(68425);function c(){let e=(0,a._)(["\n  px-2\n  py-1\n  text-xs\n  font-medium\n  font-sans\n  leading-tight\n  rounded-md\n  shadow-md\n  text-white\n  bg-black/80\n  backdrop-blur\n  break-words\n  z-tooltip\n  max-w-[13rem]\n  [*:disabled_&]:hidden\n"]);return c=function(){return e},e}function d(){let e=(0,a._)(["\n  max-w-[310px]\n  z-tooltip\n  [*:disabled_&]:hidden\n"]);return d=function(){return e},e}function u(){let e=(0,a._)(["\n  p-1\n  text-xs\n  font-medium\n  font-sans\n  leading-tight\n  rounded-lg\n  shadow-md\n  text-white\n  bg-black/80\n  backdrop-blur\n  break-words\n  z-tooltip\n  max-w-[13rem]\n  [*:disabled_&]:hidden\n"]);return u=function(){return e},e}function h(){let e=(0,a._)(["\n  p-1\n  text-sm\n  font-normal\n  font-styrene\n  leading-tight\n  border-border-300\n  border-0.5\n  rounded-lg\n  shadow-diffused \n  shadow-[hsl(var(--always-black)/4%)]\n  text-text-100\n  bg-bg-100\n  break-words\n  z-tooltip\n  max-w-[13rem]\n  [*:disabled_&]:hidden\n"]);return h=function(){return e},e}function f(e){let{children:t,tooltipContent:n,contentStyle:a="default",side:i="top",sideOffset:o,align:c,className:d,delayDuration:u=200,...h}=e,f=m[a];return(0,s.jsx)(r.zt,{delayDuration:u,children:(0,s.jsxs)(r.fC,{...h,children:[(0,s.jsx)(r.xz,{asChild:!0,children:t}),(0,s.jsx)(r.h_,{children:(0,s.jsx)("span",{children:(0,s.jsx)(f,{className:(0,l.Z)(d,!n&&"hidden"),sideOffset:null!=o?o:5,side:i,align:c,children:n})})})]})})}let g=(0,i.q)(r.VY)(c()),x=(0,i.q)(r.VY)(d()),m={default:g,citation:e=>{let{children:t,...n}=e;return(0,s.jsx)(x,{...n,children:(0,s.jsx)(o.E.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.2},className:"p-3 text-sm font-normal font-styrene leading-tight rounded-lg border-0.5 shadow-diffused shadow-[hsl(var(--always-black)/4%)] border-border-300/25 text-text-200 bg-bg-100 break-words",children:t})})},inputMenu:(0,i.q)(r.VY)(u()),sampleImage:(0,i.q)(r.VY)(h())}},17582:function(e,t,n){n.d(t,{f:function(){return l},m:function(){return o}});var a=n(27573),s=n(62168),i=n(89404),r=n(7653);function l(e){let{didCopy:t,...n}=e;return t?(0,a.jsx)(s.f,{...n}):(0,a.jsx)(i.T,{...n})}function o(){let[e,t]=(0,r.useState)(!1),[n,a]=(0,r.useState)(void 0);return{didCopy:e,copyToClipboard:(0,r.useCallback)(async e=>{let s,i;let r=()=>{t(!0),a(setTimeout(()=>t(!1),2e3))};if(n&&clearTimeout(n),"string"==typeof e)s=e.trim();else if(e&&"object"==typeof e)s=e.text.trim(),i=e.html;else throw Error("Invalid clipboard input; no plain text provided");try{if(i)try{await navigator.clipboard.write([new ClipboardItem({"text/plain":new Blob([s],{type:"text/plain"}),"text/html":new Blob([i],{type:"text/html"})})]),r();return}catch(e){try{let e=new ClipboardEvent("copy",{bubbles:!0,cancelable:!0}),t=new DataTransfer;if(t.setData("text/plain",s),t.setData("text/html",i),Object.defineProperty(e,"clipboardData",{value:t,writable:!1}),document.dispatchEvent(e),e.defaultPrevented){r();return}}catch(e){}}await navigator.clipboard.writeText(s),r()}catch(e){console.error("Clipboard copy failed:",e)}},[n])}}},20388:function(e,t,n){n.d(t,{F:function(){return i}});var a=n(27573),s=n(44602);function i(e){let{cover:t}=e,n=(0,a.jsx)(s.C,{productSurface:"console",loading:!0,compact:!0});return t?(0,a.jsx)("div",{className:"bg-bg-200 fixed inset-0 z-10 grid place-items-center",children:n}):n}},44602:function(e,t,n){n.d(t,{C:function(){return f}});var a=n(56683),s=n(27573),i=n(9788),r=n(64483),l=n(22769),o=n(11607);function c(){let e=(0,a._)(["\n  mx-0\n  mt-4\n  min-w-[16rem]\n"]);return c=function(){return e},e}function d(){let e=(0,a._)(["\n  grid\n  place-content-center\n  min-h-min\n  text-center\n  gap-2\n  pt-24\n  pb-32\n  px-4\n  mx-auto\n  h-screen\n"]);return d=function(){return e},e}function u(){let e=(0,a._)(["\n  font-copernicus\n  font-medium\n  tracking-tighter\n  text-4xl\n"]);return u=function(){return e},e}function h(){let e=(0,a._)(["\n  font-styrene\n  text-text-300\n  text-lg\n"]);return h=function(){return e},e}function f(e){let{productSurface:t,headline:n,subheading:a,button:i,testId:c,compact:d,loading:u}=e,h="claude-ai"===t?l.s:r.I;return(0,s.jsx)(x,{"data-testid":c,className:d?"max-w-min":"w-fit",children:u?(0,s.jsx)(o.Loading,{delay:1e3}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-10 text-center",children:(0,s.jsx)(h,{className:"inline-block h-6 -ml-1"})}),(0,s.jsx)(m,{children:n}),a&&(0,s.jsx)(p,{children:a}),(0,s.jsx)(g,{children:i})]})})}n(7653);let g=i.q.div(c()),x=i.q.div(d()),m=i.q.h2(u()),p=i.q.h3(h())},67587:function(e,t,n){n.d(t,{ThemeProvider:function(){return d},F:function(){return x}});var a=n(27573),s=n(40287),i=n(18013),r=n(6385),l=n(7653);let o="(prefers-color-scheme: dark)",c=(0,l.createContext)(void 0);function d(e){let{initialTheme:t,children:n}=e,[r,d]=(0,l.useState)(t),f=(0,s.f)(),[x,m]=(0,i.R)("userThemeMode","auto"),[p,j]=(0,l.useState)(g(x));(0,l.useEffect)(()=>{j(g(x))},[x]),(0,l.useEffect)(()=>u(r),[r]),(0,l.useEffect)(()=>h(x,f),[f,x]);let v=(0,l.useCallback)(()=>{h(x,f),j(g(x))},[f,x]);return(0,l.useEffect)(()=>{if("auto"!==x)return;let e=window.matchMedia(o);return e.addEventListener("change",v),()=>e.removeEventListener("change",v)},[x,v]),(0,l.useEffect)(()=>{var e,t;null===(t=window.electronWindowControl)||void 0===t||null===(e=t.setThemeMode)||void 0===e||e.call(t,"auto"===x?"system":x)},[x]),(0,a.jsx)(c.Provider,{value:{theme:r,mode:x,setMode:m,setTheme:d,resolvedMode:p},children:n})}let u=e=>{"undefined"!=typeof document&&(document.documentElement.dataset.theme=e,f())},h=(e,t)=>{if("undefined"==typeof document)return;let n=g(e);t.set(r.cn.COLOR_MODE,n),document.documentElement.dataset.mode=n,f()},f=()=>{let[e,t,n]=getComputedStyle(document.documentElement).getPropertyValue("--bg-200").split(" "),a="hsl(".concat(e,",").concat(t,",").concat(n,")"),s=document.querySelector('meta[name="theme-color"]');s||((s=document.createElement("meta")).setAttribute("name","theme-color"),document.head.appendChild(s)),s.setAttribute("content",a)},g=e=>{var t;return"auto"!==e?"auto"===e?"light":e:(null===(t=window)||void 0===t?void 0:t.matchMedia(o).matches)?"dark":"light"},x=()=>{let e=(0,l.useContext)(c);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},68744:function(e,t,n){n.d(t,{Redirect:function(){return i}});var a=n(81695),s=n(7653);function i(e){let{to:t,mode:n="replace"}=e,i=(0,a.useRouter)();return(0,s.useEffect)(()=>{"push"===n?i.push(t):i.replace(t)},[i,t,n]),null}},35228:function(e,t,n){n.d(t,{D_:function(){return r},c6:function(){return s},cG:function(){return l},kK:function(){return i},wJ:function(){return a}});let a=e=>{let{account:t,isClaudeDot:n}=e;return n?!1===t.settings.has_finished_claudeai_onboarding:!t.full_name||!t.display_name},s=(e,t)=>t&&!e.is_verified,i=(e,t)=>!!function(e,t){for(let n of e.invites)if(n.organization.capabilities&&n.organization.capabilities.includes(t))return n}(e,t?"raven":"api"),r=(e,t)=>!t&&0===e.invites.length&&0===e.memberships.filter(e=>e.organization.capabilities.includes("api")).length,l=(e,t)=>a({account:e,isClaudeDot:t})||s(e,t)||i(e,t)||r(e,t)}}]);