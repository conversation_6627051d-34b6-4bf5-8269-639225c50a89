(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{64134:function(e,t,n){"use strict";n.d(t,{P:function(){return u}});var a=n(27573),r=n(11607),s=n(82083),i=n(50106),l=n(64047),o=n(22027),c=n(86518),d=n(10607);let u=e=>{let{type:t,size:n,className:u,isLoading:m=!1}=e;return(0,a.jsx)("div",{className:(0,d.Z)("bg-bg-100 text-text-500 flex items-center justify-center rounded-l-[inherit]","default"===n&&"w-14","sm"===n&&"w-11",u),children:m?(0,a.jsx)(r.Loading,{size:"sm"}):(0,a.jsx)(()=>{switch(t){case s.JP.Markdown:return(0,a.jsx)(i.X,{size:24});case s.JP.Svg:return(0,a.jsx)(l.j,{size:24});case s.JP.Html:return(0,a.jsx)(o.T,{size:24});case s.JP.Code:case s.JP.Text:default:return(0,a.jsx)(c.E,{size:24})}},{})})}},84575:function(e,t,n){"use strict";n.d(t,{w:function(){return i}});var a=n(27573),r=n(32737),s=n(10607);function i(e){let{Icon:t,children:n,isAssistant:i=!0,tooltipContent:l,...o}=e;return(0,a.jsx)(r.u,{className:"mt-1",side:"bottom",delayDuration:100,tooltipContent:l,children:(0,a.jsxs)("button",{...o,className:(0,s.Z)("flex flex-row items-center gap-1 rounded-md p-1 py-0.5 text-xs transition-opacity delay-100 text-text-300 active:scale-95 select-none",i?"hover:bg-bg-200":"hover:bg-bg-400",o.className),children:[t&&(0,a.jsx)(t,{size:16}),n]})})}n(7653)},60879:function(e,t,n){"use strict";n.d(t,{d8:function(){return el},EY:function(){return ea},J$:function(){return eo},ux:function(){return er}});var a=n(56683),r=n(27573);n(94006);var s=n(82083),i=n(57895);let l=RegExp("<".concat(s.Km,"\\s+(.*?)\\s*\\/>"),"g"),o=()=>e=>{(0,i.Vn)(e,"html",(e,t,n)=>{let a=e.value.match(l);if(a){let e=a.map(e=>{let t=e.replace("<".concat(s.Km," "),"").replace(/\s*\/>/,"").trim(),{id:n,type:a,title:r,language:i,isClosed:l}=(0,s.l2)(t),o={id:n,title:r,type:a,isClosed:l};return i&&(o.language=i),{type:"artifactComponent",data:{hName:"artifactComponent",hProperties:o}}});n.children.splice(t,1,...e)}})};var c=n(405);let d=()=>e=>{(0,i.Vn)(e,"list",e=>{e.spread=!1}),(0,i.Vn)(e,"listItem",e=>{e.spread=!1})};var u=n(87683),m=n(7653),p=n(87376).Buffer;function f(){return function(e,t){let n=p.isBuffer(t.value)?t.value.toString("utf8"):"string"==typeof t.value?t.value:String(t.value);(0,i.Vn)(e,(e,t,a)=>{if(void 0===t||void 0===a||void 0===e.children||0===e.children.length)return;let r=!1,s=[],i=e.children.some(e=>"emphasis"===e.type),l=e.children[0].position,o=e.children[e.children.length-1].position;if(i&&l&&o){let[e,t]=g(n.slice(l.start.offset,o.end.offset));e&&(r=!0,s.push(...t))}else for(let t of e.children)if("text"===t.type){let[e,a]=g(t.position?n.slice(t.position.start.offset,t.position.end.offset):t.value);e?(r=!0,s.push(...a)):s.push(t)}else s.push(t);if(r){if(s.some(e=>"math"===e.type)){let e=function(e){let t=[],n=[],a=()=>{let e={type:"paragraph",children:t};n.push(e)};for(let r of e)"math"===r.type?(t.length>0&&(a(),t=[]),n.push(r)):t.push(r);return t.length>0&&(a(),t=[]),n}(s);a.children.splice(t,1,...e)}else e.children=s}})}}let x=/^\s*\$\$/,h=/\$\$\s*$/;function g(e){let t=!1,n=[],a=e.split("\n"),r=!1,s=[];for(let e of a){if(x.test(e)&&h.test(e)&&e.trim().length>4){n.push({type:"math",meta:null,value:e.slice(2,-2),data:{hName:"div",hProperties:{className:["math","math-display"]},hChildren:[{type:"text",value:e.slice(2,-2)}]}}),t=!0;continue}if(!r&&x.test(e)){r=!0;continue}if(r&&x.test(e)){if(r=!1,s){let e=s.join("\n");n.push({type:"math",meta:null,value:e,data:{hName:"div",hProperties:{className:["math","math-display"]},hChildren:[{type:"text",value:e}]}}),t=!0,s=[]}continue}if(r){s.push(e);continue}for(let a=0;a<e.length;a++){let r=function(e,t){let n=!1;do{let a=(n=(t=e.indexOf("$",t))>-1&&function(e,t){let n=!v(e,t-1,"$"),a=!v(e,t+1," "),r=!v(e,t+1,"$");return!b(e,t)&&n&&a&&r}(e,t))?function(e,t){let n=function(e,t){if(t<=0)return t;let n=e.lastIndexOf(" ",t);return n>-1?n+1:0}(e,t),a=/^(\$\w+!?)?\$?[A-Z]+\$?[0-9]+(:(\$\w+!?)?\$?[A-Z]+\$?[0-9]+)?/g.exec(e.slice(n)),r=a?n+a[0].length-1:-1;return r>-1&&v(e,n,"$")&&v(e,r+1,"$")?-1:r}(e,t):-1;a>-1&&a>t?(n=!1,t=a+1):!n&&t>-1&&(t+=1)}while(t>-1&&!n);return t}(e,a),s=r>-1?function(e,t){let n=!1;do(n=(t=e.indexOf("$",t))>-1&&function(e,t){var n;let a=!v(e,t-1," "),r=(n=t+1)<0||n>=e.length||!(e[n]>="0")||!(e[n]<="9");return!b(e,t)&&a&&r}(e,t))||!(t>-1)||(t+=1);while(t>-1&&!n);return t}(e,r+1):-1;if(r>-1&&s>-1){r>a&&n.push({type:"text",value:e.substring(a,r)});let i=e.substring(r+1,s);n.push({type:"inlineMath",value:i,data:{hName:"span",hProperties:{className:["math","math-inline"]},hChildren:[{type:"text",value:i+"\n"}]}}),a=s,t=!0}else{a<e.length&&n.push({type:"text",value:e.substring(a)+"\n"});break}}}return[t,n]}function v(e,t,n){return!(t<0)&&!(t>=e.length)&&e[t]===n}function b(e,t){return v(e,t-1,"\\")&&!b(e,t-1)}let j=()=>{let[e,t]=(0,m.useState)({remarkMath:void 0,rehypeKatex:void 0,inHouseLatexPlugin:f});return(0,m.useEffect)(()=>{(async()=>{let[e,a]=await Promise.all([n.e(2171).then(n.bind(n,82171)),Promise.all([n.e(8287),n.e(2982)]).then(n.bind(n,82982))]);t({remarkMath:e.default,rehypeKatex:a.default,inHouseLatexPlugin:f})})()},[]),e};var y=n(1812),w=n(9788),N=n(98555),k=n(43566),M=n(96707),_=n(14448),C=n(14575),P=n(75502),Z=n(38148),S=n(42187),J=n(10607),z=n(45790),A=n(40950),E=n(2717),$=n(69389),O=n(19541),D=n(64134),I=n(3053),T=n(32006),F=n(32737),R=n(71580),L=n(16963),H=n(19984),K=n(35919);let U={highlightedCitation:null,setHighlightedCitation:n.n(K)()},q=(0,m.createContext)(U);function V(e){let{children:t,blockCitations:n}=e,[a,s]=(0,m.useState)(null);return(0,r.jsx)(q.Provider,{value:{highlightedCitation:a,setHighlightedCitation:s,blockCitations:n},children:t})}var G=n(41198);function W(e){let{citationUuids:t,messageUuid:n}=e,{blockCitations:a}=(0,m.useContext)(q),s=(0,M.R)(t,a),i=(0,M.X)(),l=(0,C.d)(),o=(0,I.z$)(),c=(0,m.useCallback)(e=>{let t,a;"google_doc_metadata"===e.metadata.type?(t="drive_search",a=""):"webpage_metadata"===e.metadata.type?(t="web_search",a=e.metadata.site_domain):(t=e.metadata.source,a=""),o.track({event_key:"claudeai.conversation.citation_clicked",messageUuid:n,citationSourceTool:t,webDomainName:a})},[o,n]);return s.length&&i?s.map(e=>(0,r.jsxs)(m.Fragment,{children:[(0,r.jsx)("span",{className:"inline-flex w-1"}),(0,r.jsx)(F.u,{tooltipContent:(0,r.jsx)(Y,{citation:e,handleClick:c}),contentStyle:"citation",side:"right",delayDuration:0,disableHoverableContent:l,children:(0,r.jsx)("span",{className:"inline-flex",children:(0,r.jsx)(B,{citation:e,handleClick:c,cardamomEnabled:l})})}),(0,r.jsx)("span",{className:"inline-flex w-1"})]},e.uuid)):null}let B=e=>{var t,n;let a,{citation:s,handleClick:i,cardamomEnabled:l}=e;switch(s.metadata.type){case"webpage_metadata":a=null!==(t=s.metadata.site_name)&&void 0!==t?t:(0,r.jsx)(z.Z,{defaultMessage:"Untitled",id:"3kbIhS7KZS"});break;case"google_doc_metadata":a=l?(0,r.jsx)(z.Z,{defaultMessage:"Google Doc",id:"7qYF66c9/s"}):s.title;break;default:a=s.metadata.source}return(0,r.jsxs)("a",{href:null!==(n=s.url)&&void 0!==n?n:"",target:"_blank",className:"group/tag relative h-[18px] cursor-pointer rounded-full inline-flex items-center overflow-hidden -translate-y-px",onClick:()=>{i(s)},children:[(0,r.jsx)("span",{className:(0,J.Z)("relative transition-colors h-[18px] max-w-[180px] overflow-hidden px-1.5 inline-flex items-center font-styrene text-xs tracking-tight rounded-full border-0.5 border-border-300 bg-bg-200",s.url&&"group-hover/tag:bg-accent-secondary-900 group-hover/tag:border-accent-secondary-100/60"),children:(0,r.jsx)("span",{className:"text-nowrap text-text-300 group-hover/tag:text-text-200 break-all truncate",children:a})}),(0,r.jsx)("span",{className:"transition-all group-hover/tag:opacity-[100%] opacity-[0%] h-[17px] absolute right-[0.5px] inline rounded-r-full flex items-center px-1.5 bg-gradient-to-r from-accent-secondary-900/0 via-accent-secondary-900/100 via-30% to-accent-secondary-900/100",children:(0,r.jsx)(L.G,{size:14,className:"transition-all group-hover/tag:ease-out duration-[500ms] ease-in text-accent-secondary-100 group-hover/tag:scale-[100%] scale-[80%] group-hover/tag:opacity-[100%] opacity-[0%] -mr-[2px]"})})]})};function Y(e){var t;let n,a,{citation:s,handleClick:i}=e;switch(s.metadata.type){case"webpage_metadata":n=(0,r.jsx)(G.c,{faviconUrl:s.metadata.favicon_url}),a=s.metadata.site_name;break;case"google_doc_metadata":n=(0,r.jsx)(T.k6,{size:14,variant:T.cT.DOCS}),a=(0,r.jsx)(z.Z,{defaultMessage:"Google Doc",id:"7qYF66c9/s"});break;default:{let e=(0,r.jsx)(H.J,{size:14,className:"text-text-500"});n=s.metadata.favicon_url?(0,r.jsx)(R.J,{style:{maxWidth:14,maxHeight:14},className:"object-contain",width:14,height:14,src:s.metadata.favicon_url,fallbackSvg:e,alt:"favicon"}):e,a=s.metadata.source}}return(0,r.jsxs)("a",{href:null!==(t=s.url)&&void 0!==t?t:"",target:"_blank",className:"flex flex-col gap-2",onClick:()=>i(s),children:[(0,r.jsx)("h3",{className:"font-styrene text-sm font-medium",children:s.title}),(0,r.jsxs)("span",{className:"flex flex-row items-center gap-1 text-text-500 text-xs",children:[n,(0,r.jsx)("p",{children:a})]})]})}var X=n(70354),Q=n(52939);let ee=e=>{let{isOpen:t=!1,url:n,onClose:a,onConfirm:s}=e,i=(0,A.Z)();return(0,r.jsxs)(Q.u_,{title:i.formatMessage({defaultMessage:"Open a link from the web",id:"EImP5C2Oec"}),isOpen:t,onClose:a,children:[(0,r.jsx)("p",{className:"break-words",children:(0,r.jsx)(z.Z,{defaultMessage:"You’re about to be navigated to {url}. Is that okay?",id:"CPD3nYSJ0P",values:{url:n}})}),(0,r.jsxs)(Q.dm,{children:[(0,r.jsx)(X.z,{variant:"outline",onClick:s,children:(0,r.jsx)(z.Z,{defaultMessage:"Continue",id:"acrOozm08x"})}),(0,r.jsx)(X.z,{variant:"secondary",onClick:a,children:(0,r.jsx)(z.Z,{defaultMessage:"Cancel",id:"47FYwba+bI"})})]})]})};var et=n(61995);function en(){let e=(0,a._)(["\n  tracking-tighter\n  flex-1\n  text-left\n  mr-4\n  [&_a]:font-medium\n  [&_a]:hover:underline\n"]);return en=function(){return e},e}let ea=[],er=(0,m.memo)(e=>{var t,n;let{text:a,blockCitations:s=[],hasLinkRef:i,hasCodeRef:l,className:p,linkTarget:f="_blank",isStreaming:x=!1,flagShowCodeHeader:h,unsafeAllowNavigationWithoutConfirmation:g,onOpenArtifact:v,isLastMessage:b,publishedArtifacts:w,useInHouseLatexPlugin:N,messageUuid:k}=e,C=(0,m.useRef)(null),P=null!=w?w:ea,Z=(0,M.X)();(0,m.useEffect)(()=>{let e=e=>{var t,n,a;let r=window.getSelection();if(r&&(null===(t=C.current)||void 0===t?void 0:t.contains(r.anchorNode))){let t=r.getRangeAt(0).cloneContents(),s=document.createElement("div");for(let e of(s.appendChild(t),s.getElementsByTagName("*")))e.removeAttribute("color"),e.removeAttribute("fontFamily");null===(n=e.clipboardData)||void 0===n||n.setData("text/html",s.innerHTML),null===(a=e.clipboardData)||void 0===a||a.setData("text/plain",s.textContent||""),e.preventDefault()}};return document.addEventListener("copy",e),()=>{document.removeEventListener("copy",e)}},[]);let{value:S}=(0,_.F)("latex_ga"),z=null!==(n=null===(t=(0,y.T)())||void 0===t?void 0:t.preview_feature_uses_latex)&&void 0!==n&&n,A=S||z,D=!!v,{remarkMath:I,rehypeKatex:T,inHouseLatexPlugin:F}=j(),R=(0,m.useMemo)(()=>(0,u.jA)(a),[a]),L=(0,m.useMemo)(()=>({h1:e=>{let{node:t,children:n,...a}=e;return(0,r.jsx)("h1",{className:"text-2xl font-bold mt-1 text-text-100",...a,children:n})},h2:e=>{let{node:t,children:n,...a}=e;return(0,r.jsx)("h2",{className:"text-xl font-bold text-text-100 mt-1 -mb-0.5",...a,children:n})},h3:e=>{let{node:t,children:n,...a}=e;return(0,r.jsx)("h3",{className:"text-lg font-bold text-text-100 mt-1 -mb-1.5",...a,children:n})},h4:e=>{let{node:t,children:n,...a}=e;return(0,r.jsx)("h4",{className:"text-base font-bold text-text-100 mt-1",...a,children:n})},p:e=>{let{node:t,children:n,...a}=e;return(0,r.jsx)(et.M.p,{...a,children:n})},blockquote:e=>{let{node:t,...n}=e;return(0,r.jsx)("blockquote",{className:"border-border-200 border-l-4 pl-4",...n})},li:e=>{let{node:t,ordered:n,children:a,...s}=e;return(0,r.jsx)(et.M.li,{...s,children:a})},ul:e=>{let{node:t,ordered:n,...a}=e;return(0,r.jsx)(et.M.ul,{...a})},ol:e=>{let{node:t,ordered:n,...a}=e;return(0,r.jsx)(et.M.ol,{...a})},img:e=>{let{node:t,...n}=e;return g?(0,r.jsx)("img",{...n}):(0,r.jsx)(es,{...n})},code(e){let{node:t,...n}=e;return l&&!n.inline&&(l.current=!0),(0,r.jsx)(et.M.code,{...n,flagShowCodeHeader:h})},a(e){let{node:t,...n}=e;return i&&(i.current=!0),g?(0,r.jsx)("a",{target:f,...n}):(0,r.jsx)(ei,{linkTarget:f,...n})},table(e){let{node:t,...n}=e;return(0,r.jsx)("pre",{className:"font-styrene border-border-100/50 overflow-x-scroll w-full rounded border-[0.5px] shadow-[0_2px_12px_hsl(var(--always-black)/5%)]",children:(0,r.jsx)("table",{className:"bg-bg-100 min-w-full border-separate border-spacing-0 text-sm leading-[1.88888] whitespace-normal",...n})})},thead(e){let{node:t,...n}=e;return(0,r.jsx)("thead",{className:"border-b-border-100/50 border-b-[0.5px] text-left",...n})},tr(e){let{node:t,isHeader:n,...a}=e;return(0,r.jsx)("tr",{className:"[tbody>&]:odd:bg-bg-500/10",...a})},td(e){let{node:t,isHeader:n,children:a,...s}=e;return(0,r.jsx)("td",{className:"border-t-border-100/50 [&:not(:first-child)]:-x-[hsla(var(--border-100) / 0.5)] border-t-[0.5px] px-2 [&:not(:first-child)]:border-l-[0.5px]",...s,children:a})},th(e){let{node:t,isHeader:n,children:a,...s}=e;return(0,r.jsx)("th",{className:"text-text-000 [&:not(:first-child)]:-x-[hsla(var(--border-100) / 0.5)] font-400 px-2 [&:not(:first-child)]:border-l-[0.5px]",...s,children:a})},artifactComponent(e){let{node:t}=e;return(0,r.jsx)(el,{properties:t.properties,isStreaming:x,isLastMessage:b,onOpenArtifact:v,publishedArtifacts:P})},...Z?{[c.KF]:e=>{let{node:t,...n}=e;return(0,r.jsx)(W,{...n,citationUuids:t.properties.citationUuids,messageUuid:k})}}:null}),[Z,g,l,h,i,f,x,b,v,P,k]),H=(0,m.useMemo)(()=>{let e=[O.Z,d];return D&&e.push(o),A&&I&&(N?e.push(F):e.push([I,{singleDollarTextMath:!1}])),Z&&(e.push($.Z),e.push(c.Su)),e},[D,A,I,Z,N,F]),K=(0,m.useMemo)(()=>{let e=[];return A&&T&&e.push([T,{errorColor:"inherit"}]),e},[T,A]);return(0,r.jsx)("div",{ref:C,children:(0,r.jsx)(V,{blockCitations:s,children:(0,r.jsx)(E.D,{className:(0,J.Z)("grid-cols-1 grid gap-2.5 [&_>_*]:min-w-0",p),remarkPlugins:H,rehypePlugins:K,components:L,children:R})})})});er.displayName="StandardMarkDown";let es=e=>{let{src:t,...n}=e,[a,s]=(0,m.useState)(!1),i=()=>{window.open(t,"_blank")};return t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>s(!0),className:"bg-bg-300 border-border-300 font-styrene text-text-300 inline-block grid h-32 w-40 items-center justify-items-center rounded-xl border p-4",children:(0,r.jsx)(z.Z,{defaultMessage:"Show Image",id:"eFsJ8k8yFm"})}),(0,r.jsx)(ee,{isOpen:a,url:t,onClose:()=>s(!1),onConfirm:()=>{i(),s(!1)}})]}):(0,r.jsx)("img",{...n})},ei=e=>{let{linkTarget:t,href:n,className:a,...s}=e,[i,l]=(0,m.useState)(!1),o=()=>{window.open(n,t)};return n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("a",{className:(0,J.Z)("underline",a),onClick:e=>{e.preventDefault(),l(!0)},href:n,...s}),(0,r.jsx)(ee,{isOpen:i,url:n,onClose:()=>l(!1),onConfirm:()=>{o(),l(!1)}})]}):(0,r.jsx)("a",{...s})},el=e=>{let{properties:t,isStreaming:n,isEdit:a,isNotLastEdit:s,onOpenArtifact:i,isLastMessage:l,publishedArtifacts:o}=e,c=(0,A.Z)(),d=(0,N.t)(),u=o.some(e=>e.artifact_identifier===t.id&&!0!==e.deleted);return(0,C.d)()?(0,r.jsx)(ec,{...e}):(0,r.jsxs)("button",{className:(0,J.Z)("font-styrene relative border-0.5 border-border-200 bg-bg-100 h-16 hover:border-border-100 flex flex-1 items-stretch rounded-lg text-left transition hover:drop-shadow-sm active:scale-[0.99] w-full max-w-[28rem]",s&&a&&"opacity-0 pointer-events-none transition-none"),onClick:()=>{i&&t.id&&i(t.id)},"aria-label":c.formatMessage({defaultMessage:"Preview contents",id:"hmKJSoIQ4a"}),children:[(0,r.jsx)(D.P,{size:"default",type:t.type,isLoading:n&&l&&!t.isClosed,className:"bg-bg-200 border-border-200 border-r-[0.5px] min-w-16"}),(0,r.jsxs)("div",{className:"padding min-w-0 flex flex-col justify-top items-start pl-4 pr-3 py-3 gap-[3px] w-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("div",{className:"break-all text-sm font-medium leading-tight line-clamp-1",children:t.title&&t.id&&!n?(0,r.jsx)(r.Fragment,{children:t.title}):a&&n?(0,r.jsx)(z.Z,{defaultMessage:"Making edits...",id:"0HUNCOubmp"}):t.title&&t.id?(0,r.jsx)(r.Fragment,{children:t.title}):(0,r.jsx)(z.Z,{defaultMessage:"Generating...",id:"A/lLjOnjl0"})}),a&&t.id&&t.title&&!n&&(0,r.jsx)("div",{className:"inline-block rounded-full px-2 py-px text-xs shadow-inset-border-0.5 shadow-border-300",children:(0,r.jsx)(z.Z,{defaultMessage:"Edited",id:"OhIqLr524S"})})]}),(0,r.jsx)("div",{className:"text-text-400 text-xs min-h-5 flex items-center gap-1 empty:hidden",children:t.type?(0,r.jsxs)("div",{className:(0,J.Z)("transition-opacity duration-200",t.type?n&&a?"opacity-0":"opacity-100":"opacity-0"),children:[(0,r.jsx)("div",{className:"mr-2 sm:block hidden text-text-500",children:(0,r.jsx)(z.Z,{defaultMessage:"Click to open {type}",id:"FxLCImHbz4",values:{type:d[t.type]}})}),(0,r.jsx)("div",{className:"mr-1 sm:hidden",children:(0,r.jsx)(z.Z,{defaultMessage:"Tap to open",id:"8NaLrLgJeK"})}),u&&(0,r.jsxs)("div",{className:"text-accent-secondary-100 bg-accent-secondary-900 inline-flex items-center rounded-full pl-2 pr-2 py-px text-xs shadow-inset-border-0.5 shadow-accent-secondary-200",children:[(0,r.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-accent-secondary-200 mr-2"}),(0,r.jsx)(z.Z,{defaultMessage:"Published",id:"w7tf2zwi6+"})]})]}):null})]})]})},eo=w.q.div(en());function ec(e){let{properties:t,isStreaming:n,isEdit:a,isNotLastEdit:i,onOpenArtifact:l,preview:o,selectedArtifactId:c,selectedArtifactVersion:d}=e,[u,p]=(0,m.useState)(!!t.type);(0,m.useEffect)(()=>{t.type&&p(!0)},[t.type]);let f=(0,A.Z)(),x=(0,N.i)(),h=t.type?x[t.type]:null,g=e.versionNum&&e.versionCount&&e.versionNum>1&&e.versionCount>1,v=t.title&&t.id&&!n?"generated":a&&n?"editing":t.title&&t.id?"partial":"generating",b=t.type===s.JP.Markdown||t.type===s.JP.Text,j=b||t.type===s.JP.Code;return(0,r.jsx)("button",{className:(0,J.Z)("flex text-left font-styrene rounded-xl mb-1 -mx-0.5 overflow-hidden border transition duration-300 w-full hover:bg-bg-000/50",i&&a&&"opacity-0 pointer-events-none transition-none",c===t.id&&d===t.version_uuid?"border-accent-secondary-200 duration-500":"border-border-300 hover:border-border-200"),onClick:()=>{l&&t.id&&l(t.id)},"aria-label":f.formatMessage({defaultMessage:"Preview contents",id:"hmKJSoIQ4a"}),children:(0,r.jsxs)("div",{className:"artifact-block-cell group/artifact-block flex flex-1 align-start justify-between w-full pl-0.5 py-0.5",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-1 py-4 px-4",children:[(0,r.jsx)("div",{className:(0,J.Z)("leading-tight text-sm","generating"===v?"text-text-500":null),children:"generated"===v?(0,r.jsx)(r.Fragment,{children:t.title}):"editing"===v?(0,r.jsx)(z.Z,{defaultMessage:"Making edits...",id:"0HUNCOubmp"}):"partial"===v?(0,r.jsx)(r.Fragment,{children:t.title}):(0,r.jsx)(k.g,{children:(0,r.jsx)(z.Z,{defaultMessage:"Drafting artifact...",id:"8NQAeTqGOl"})})}),(0,r.jsxs)("div",{className:(0,J.Z)("text-sm","generating"===v?"text-text-500":"text-text-300",h?"opacity-100":"opacity-0","transition-opacity duration-200"),children:[h,h&&g?(0,r.jsx)(r.Fragment,{children:" ∙ "}):null,g?(0,r.jsx)(z.Z,{defaultMessage:"Version {versionNum}",id:"iMMX+ZCJ/I",values:{versionNum:e.versionNum}}):null,"\xa0"]})]}),(0,r.jsx)("div",{className:"flex items-end artifact-block-cell-preview relative",children:(0,r.jsx)("div",{className:(0,J.Z)("absolute inset-0 flex flex-1 overflow-hidden","rounded-t-lg border-0.5 border-border-200 select-none","scale-[1] group-hover/artifact-block:scale-[1.035]","rotate-[0.1rad] group-hover/artifact-block:rotate-[0.065rad]","duration-300 ease-out group-hover/artifact-block:duration-400 group-hover/artifact-block:ease-[cubic-bezier(0,0.9,0.5,1.35)]","transition-transform backface-hidden will-change-transform",u?"translate-y-[19%]":"translate-y-[120%]",t.type===s.JP.Code?"bg-always-black text-always-white":b?"bg-bg-000 text-text-500":"bg-gradient-to-b from-bg-000 to-bg-000/0",j?"whitespace-pre-wrap text-[0.35rem] leading-none p-2 font-mono":"text-accent-brand pt-1 items-center justify-center h-full"),children:j?o:t.type===s.JP.Svg?(0,r.jsx)(P.E,{size:32,weight:"light"}):t.type===s.JP.React||t.type===s.JP.Html?(0,r.jsx)(Z.h,{size:32,weight:"light"}):t.type===s.JP.Code?(0,r.jsx)(S.E,{size:32,weight:"light"}):(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",width:"36",height:"36",children:(0,r.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",d:"M2 8a5 5 0 0 0 4-2 5 5 0 0 0 4 2 5 5 0 0 0 4-2 5 5 0 0 0 4 2 5 5 0 0 0 4-2M2 18a5 5 0 0 0 4-2 5 5 0 0 0 4 2 5 5 0 0 0 4-2 5 5 0 0 0 4 2 5 5 0 0 0 4-2"})})})})]})})}},41198:function(e,t,n){"use strict";n.d(t,{c:function(){return i}});var a=n(27573),r=n(71580),s=n(22027);let i=e=>{let{faviconUrl:t,fadeInOnLoad:n=!1,size:i=12}=e,l=(0,a.jsx)(s.T,{size:i,className:"text-text-500"});return t?(0,a.jsx)(r.J,{style:{maxWidth:i,maxHeight:i},className:"object-contain",width:i,height:i,src:t,alt:"favicon",fallbackSvg:l,fadeInOnLoad:n}):l}},61995:function(e,t,n){"use strict";n.d(t,{M:function(){return g},S:function(){return h}});var a=n(27573);n(21953);var r=n(84575),s=n(18013),i=n(17582),l=n(10607),o=n(5e3),c=n(7653),d=n(45790),u=n(68280),m=n(41668),p=n(35756),f=n(15992);let x=(0,c.memo)(e=>{let{node:t,stylesheet:n,style:a,useInlineStyles:r,index:s}=e;return(0,u.ZP)({node:t,stylesheet:n,style:a,useInlineStyles:r,key:s})},(e,t)=>(0,o.vZ)(e.node,t.node));x.displayName="Row";let h=e=>{let{rows:t,stylesheet:n,useInlineStyles:r}=e;return t.map((e,t)=>(0,a.jsx)(x,{index:t,node:e,stylesheet:n,useInlineStyles:r},t))},g={h1:e=>(0,a.jsx)("h1",{className:"mt-2 mb-2 font-bold",...e}),p:e=>(0,a.jsx)("p",{className:"whitespace-pre-wrap break-words",...e}),li:e=>(0,a.jsx)("li",{className:"whitespace-normal break-words",...e}),ul:e=>(0,a.jsx)("ul",{className:"[&:not(:last-child)_ul]:pb-1 [&:not(:last-child)_ol]:pb-1 list-disc space-y-1.5 pl-7",...e}),ol:e=>(0,a.jsx)("ol",{className:"[&:not(:last-child)_ul]:pb-1 [&:not(:last-child)_ol]:pb-1 list-decimal space-y-1.5 pl-7",...e}),blockQuote:e=>(0,a.jsx)("blockquote",{className:"border-l-2 border-border-100 pl-2",...e}),code:e=>{var t;let{inline:n,className:o,children:u,flagShowCodeHeader:x=!0}=e,g=/language-(\w+)/.exec(o||""),v=(null==g?void 0:null===(t=g[1])||void 0===t?void 0:t.toLowerCase())||"",b=String(u).trimEnd(),{didCopy:j,copyToClipboard:y}=(0,i.m)(),{config:w}=(0,f.useConfig)("claude_code_spotlight_code_viewed_signal"),N=w.get("languages",[]),[,k]=(0,s.R)("claude-code-view-timestamp",null);return(0,c.useEffect)(()=>{!n&&v&&N.includes(v)&&k(Date.now())},[]),n?(null==o?void 0:o.includes("math"))?(0,a.jsx)("span",{className:o,children:u}):(0,a.jsx)("code",{className:(0,l.Z)("bg-text-200/5 border border-0.5 border-border-300 text-danger-000 whitespace-pre-wrap rounded-[0.3rem] px-1 py-px text-[0.9rem]",o),children:u}):(0,a.jsxs)("div",{className:"relative flex flex-col rounded-lg",children:[!0===x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"text-text-300 absolute pl-3 pt-2.5 text-xs",children:g?g[1]:""}),(0,a.jsx)("div",{className:"pointer-events-none sticky my-0.5 ml-0.5 flex items-center justify-end px-1.5 py-1 mix-blend-luminosity top-0",children:(0,a.jsx)("div",{className:"from-bg-300/90 to-bg-300/70 pointer-events-auto rounded-md bg-gradient-to-b p-0.5 backdrop-blur-md",children:(0,a.jsxs)(r.w,{className:"opacity-60 hover:opacity-100",onClick:()=>{y(b)},children:[(0,a.jsx)(i.f,{didCopy:j,size:14,className:"text-text-500 mr-px -translate-y-[0.5px]"}),(0,a.jsx)("span",{className:"text-text-200 pr-0.5",children:(0,a.jsx)(d.Z,{defaultMessage:"Copy",id:"4l6vz1/eZ5"})})]})})})]}):null,(0,a.jsx)("div",{children:(0,a.jsx)(m.Z,{className:"code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed",language:g?g[1]:"",useInlineStyles:!1,PreTag:"div",style:p.Z,renderer:h,children:b})})]})}}},405:function(e,t,n){"use strict";n.d(t,{KF:function(){return i},Su:function(){return d},NM:function(){return c}});var a=n(57895),r=n(45144),s=n(41270);let i="antCitation",l=s.z.object({citations:s.z.string()}),o=e=>{let t={};return e.forEach(e=>{let n=e.end_index;t[n]||(t[n]=[]),t[n].push(e)}),Object.values(t).sort((e,t)=>t[0].end_index-e[0].end_index)},c=(e,t)=>{let n=t;for(let t of o(e)){let e=t[0].end_index;if(e>n.length)continue;let a=[...n].slice(0,e).join(""),r=[...n].slice(e).join(""),s=t.length?function(e){let{tagName:t,inlineContent:n,attributes:a}=e,r=a?"{"+Object.entries(a).map(e=>{let[t,n]=e;return"".concat(t,'="').concat(n,'"')}).join(" ")+"}":"";return":".concat(t,"[").concat(n,"]").concat(r)}({tagName:i,inlineContent:"",attributes:{citations:t.map(e=>e.uuid).join(",")}}):"",l="";a.endsWith("```")?l="\n":a&&!a.match(/\s$/)&&(l=" "),n=a+l+s+r}return n},d=function(e,t){let n=n=>{(0,a.Vn)(n,void 0,n=>{if(("textDirective"===n.type||"leafDirective"===n.type||"containerDirective"===n.type)&&n.name===e){let r=t(n.attributes);if(n.data={hName:e,hProperties:r},"containerDirective"===n.type&&n.children){let e=[...n.children];for(;e.length;){let t=e.shift();if(t){var a;t.data={...t.data,hProperties:{...(null===(a=t.data)||void 0===a?void 0:a.hProperties)||null,...r}},"children"in t&&e.push(...t.children)}}}}})};return()=>n}(i,e=>{try{return{citationUuids:l.parse(e).citations.split(",")}}catch(e){return(0,r.Tb)(e),{citationUuids:[]}}})},87683:function(e,t,n){"use strict";function a(e){return e.replace(/(^|\n)(\s?)•(\s?)/g,"$1$2- ")}n.d(t,{Yl:function(){return s},jA:function(){return a}});let r=/((([A-Za-z]{3,9}:(?:\/\/)?)(?:[-;:&=+$,\w]+@)?[A-Za-z0-9.-]+|(?:www.|[-;:&=+$,\w]+@)[A-Za-z0-9.-]+)((?:\/[+~%/.\w-_]*)?\??(?:[-+=&;%@.\w_]*)#?(?:[\w]*))?)/gi;function s(e){if(!e)return[];let t=e.matchAll(r),n=new Set;for(let e of t)Array.isArray(e)&&e[0]&&n.add(e[0]);return Array.from(n)}},98555:function(e,t,n){"use strict";n.d(t,{i:function(){return l},t:function(){return i}});var a=n(82083),r=n(7653),s=n(40950);function i(){let e=(0,s.Z)();return(0,r.useMemo)(()=>({[a.JP.Text]:e.formatMessage({defaultMessage:"text",description:"A description of the document type",id:"yaG66hygru"}),[a.JP.Markdown]:e.formatMessage({defaultMessage:"document",description:"A description of the document type",id:"Nk23/rndUZ"}),[a.JP.Html]:e.formatMessage({defaultMessage:"website",description:"A description of the document type",id:"O2ntnbm9yZ"}),[a.JP.Svg]:e.formatMessage({defaultMessage:"image",description:"A description of the document type",id:"3hvGd81pGE"}),[a.JP.Code]:e.formatMessage({defaultMessage:"code",description:"A description of the document type",id:"NsTmZ4s6sv"}),[a.JP.Mermaid]:e.formatMessage({defaultMessage:"diagram",description:"A description of the document type",id:"kfe+Wnolct"}),[a.JP.React]:e.formatMessage({defaultMessage:"component",description:"A description of the document type (a React component)",id:"X83cfiK8ri"})}),[e])}function l(){let e=(0,s.Z)();return(0,r.useMemo)(()=>({[a.JP.Text]:e.formatMessage({defaultMessage:"Text",id:"aA8bDwsPeJ"}),[a.JP.Markdown]:e.formatMessage({defaultMessage:"Document",id:"wmirkPk7bp"}),[a.JP.Html]:e.formatMessage({defaultMessage:"Interactive artifact",id:"zfnwA1wU0m"}),[a.JP.Svg]:e.formatMessage({defaultMessage:"Image",id:"+0zv6gS/c6"}),[a.JP.Code]:e.formatMessage({defaultMessage:"Code",id:"h2vipunVVd"}),[a.JP.Mermaid]:e.formatMessage({defaultMessage:"Diagram",id:"PBUs06BBR7"}),[a.JP.React]:e.formatMessage({defaultMessage:"Interactive artifact",id:"zfnwA1wU0m"})}),[e])}},43566:function(e,t,n){"use strict";n.d(t,{g:function(){return r}});var a=n(27573);let r=e=>{let{children:t}=e;return(0,a.jsx)("span",{className:"bg-text-400 text-center text-always-white/0 bg-gradient-to-r bg-[length:400%_100%] from-text-400 from-30% via-always-white/70 to-text-400 to-80% bg-clip-text bg-no-repeat animate-[shimmertext_1.5s_infinite]",children:t})}},71580:function(e,t,n){"use strict";n.d(t,{J:function(){return l}});var a=n(27573),r=n(10607),s=n(50294),i=n(7653);let l=e=>{let{src:t,fallbackSrc:n,fallbackSvg:l,fadeInOnLoad:o=!1,alt:c,...d}=e,[u,m]=(0,i.useState)(!1),[p,f]=(0,i.useState)(t),[x,h]=(0,i.useState)(!1);return x&&l?l:(0,a.jsx)(s.default,{...d,alt:c,src:p,onError:()=>{n?f(n):l&&h(!0)},onLoad:()=>m(!0),className:(0,r.Z)("transition duration-400",u?"opacity-1":o&&"opacity-0 blur-sm")})}},82083:function(e,t,n){"use strict";n.d(t,{An:function(){return N},HZ:function(){return M},JP:function(){return r},Km:function(){return b},VA:function(){return m},kE:function(){return d},l2:function(){return j},tE:function(){return k},w0:function(){return w},yc:function(){return u}});var a,r,s,i=n(41270),l=n(98731),o=n(53462);(a=r||(r={})).Text="text/plain",a.Markdown="text/markdown",a.Html="text/html",a.Code="application/vnd.ant.code",a.Svg="image/svg+xml",a.Mermaid="application/vnd.ant.mermaid",a.React="application/vnd.ant.react",(s||(s={})).Repl="application/vnd.ant.repl";let c=i.z.string().nullish().transform(e=>null!=e?e:void 0),d=i.z.object({version_uuid:c,command:i.z.enum(["create","update","rewrite"]).optional().catch(void 0),id:c,title:c,type:i.z.nativeEnum(r).optional().catch(void 0),language:c,content:c,old_str:c,new_str:c,md_citations:i.z.unknown()}),u={"text/plain":{syntaxName:"plaintext",extension:()=>"txt"},"text/markdown":{syntaxName:"markdown",extension:()=>"md"},"text/html":{syntaxName:"html",extension:()=>"html"},"image/svg+xml":{syntaxName:"svg",extension:()=>"svg"},"application/vnd.ant.code":{syntaxName:"plaintext",extension:e=>{switch(null==e?void 0:e.language){case"python":return"py";case"javascript":return"js";case"typescript":return"ts";case"java":return"java";case"csharp":return"cs";case"go":return"go";case"rust":return"rs";case"ruby":return"rb";case"php":return"php";case"swift":return"swift";case"kotlin":return"kt";case"dart":return"dart";case"scala":return"scala";case"perl":return"pl";case"elixir":return"ex";case"haskell":return"hs";case"clojure":return"clj";case"r":return"r";case"groovy":return"groovy";case"bash":return"sh";case"css":return"css";case"scss":return"scss";case"less":return"less";case"sql":return"sql";case"graphql":return"graphql";case"json":return"json";case"html":return"html";default:return"txt"}}},"application/vnd.ant.mermaid":{syntaxName:"mermaid",extension:()=>"mermaid"},"application/vnd.ant.react":{syntaxName:"tsx",extension:()=>"tsx"}},m=e=>{let t=u[e.type];return"application/vnd.ant.code"!==e.type?t.extension():t.extension(e)},p="antArtifact",f="<".concat(p,"(?:\\s+([^>]*)>|(?!>)\\S*)"),x="</".concat(p,">"),h=RegExp("".concat(f,"([\\s\\S]*?)(?:").concat(x,"|$)"),"g"),g=RegExp("<(?:a(?:n(?:t(?:A(?:r(?:t(?:i(?:f(?:a(?:c(?:t)?)?)?)?)?)?)?)?)?)?)?$","gm"),v=RegExp("".concat(f,"[\\s\\S]*?(?:").concat(x,"|$)"),"g"),b="ANTARTIFACTLINK";function j(e){var t;let n=e.match(/identifier="([^"]*)"/),a=n?n[1]:"",s=e.match(/type="([^"]*)"/),i=s?(t=s[1],Object.values(r).includes(t)?t:"text/plain"):"text/plain",l=e.match(/title="([^"]*)"/),o=l?l[1]:"Untitled artifact",c=e.match(/language="([^"]*)"/),d=c?c[1]:void 0,u=e.match(/isClosed="(true|false)"/);return{id:a,type:i,title:o,language:d,isClosed:u?"true"===u[1]:void 0}}function y(e){return e.replace(g,"")}function w(e){return S(y(e)).replace(h,(e,t,n)=>{if(!t||"string"!=typeof t)return"";{let{type:e,language:a}=j(t);return"text/markdown"!==e&&"string"==typeof n?"```".concat(a||u[e].syntaxName,"\n").concat(n.trimStart(),"\n```"):"\n".concat(n)}})}function N(e){return y(S(e).replace(v,(e,t)=>{let n=e.endsWith(x);return"<".concat(b," ").concat(t," isClosed=“").concat(n,"” />")}))}let k=(e,t)=>"create"===e.command||"rewrite"===e.command?e.content:"update"===e.command?void 0===t?e.new_str:t.includes(e.old_str)?t.replace(e.old_str,e.new_str):t.replace(e.old_str.trim(),e.new_str):null!=t?t:"";function M(e){var t;let n=(0,l.K)(e).matchAll(h),a=[];for(let t of n){let[n,r,s]=t,i=n.endsWith(x);if(!r||!s)continue;let{id:l,type:o,title:c,language:d}=j(r),u={command:"create",message:e.uuid||"",created_at:e.created_at,content:s.trimStart(),isClosed:i};a.push({id:l,currentVersion:u,type:o,title:c,language:d})}return null===(t=e.content)||void 0===t||t.map((t,n,r)=>{if("tool_use"===t.type&&"artifacts"===t.name&&t.input){let s;let i=(0,o.n)(t),l=n===r.length-1,{version_uuid:c,command:u,id:m,type:p="text/plain",title:f="",language:x,content:h="",old_str:g="",new_str:v="",md_citations:b}=d.parse(i),j=(null==b?void 0:b.length)?h:h.trimStart();"create"===u||"rewrite"===u?s={uuid:c,command:u,citations:b,content:j,message:e.uuid||"",created_at:e.created_at,isClosed:!l}:"update"===u&&""!==h?s={uuid:c,command:"rewrite",message:e.uuid||"",created_at:e.created_at,isClosed:!l,content:h}:"update"===u&&(s={uuid:c,command:"update",message:e.uuid||"",created_at:e.created_at,isClosed:!l,old_str:g,new_str:v}),void 0!==m&&void 0!==s&&a.push({id:m,currentVersion:s,type:p,title:f,language:x})}}),a}let _="antThinking",C="<".concat(_,">"),P=RegExp("".concat(C,"[\\s\\S]*?(?:</").concat(_,">|$)"),"g"),Z=RegExp("<(?:a(?:n(?:t(?:T(?:h(?:i(?:n(?:k(?:i(?:n(?:g)?)?)?)?)?)?)?)?)?)?)?$","gm");function S(e){return e.replace(Z,"").replace(P,"")}},96707:function(e,t,n){"use strict";n.d(t,{R:function(){return s},X:function(){return r}});var a=n(14448);let r=()=>{let{value:e}=(0,a.F)("applejacks"),{value:t}=(0,a.F)("cheerios"),{value:n}=(0,a.F)("fruitypebbles");return e||t||n};function s(e,t){return e.map(e=>null==t?void 0:t.find(t=>t.uuid===e)).filter(e=>!!e)}},37458:function(e,t,n){"use strict";function a(e){let t=function(e){let t=[],n=0;for(;n<e.length;){let a=e[n];if(/\s/.test(a)){n++;continue}if("{[]}:,".includes(a)){t.push(a),n++;continue}if('"'===a){let r=a;n++;let s=!1,i=!1;for(;n<e.length;){if(r+=a=e[n],'"'===a&&!s){i=!0;break}s="\\"===a&&!s,n++}if(!i){if(r.slice(-5).includes("\\"))for(r=r.slice(0,r.lastIndexOf("\\"));r.endsWith("\\");)r=r.slice(0,-1);r+='"'}t.push(r),n++;continue}if(/[-0-9]/.test(a)){let a="";for(;n<e.length&&/[-0-9.eE+]/.test(e[n]);)a+=e[n],n++;if(!a.match(/\d$/))for(;a.length>0&&!a[a.length-1].match(/\d/);)a=a.slice(0,-1);t.push(a);continue}if(/[tfn]/.test(a)){let r="",s=["true","false","null"],i=s.filter(e=>e.startsWith(a));if(1===i.length)n+=(r=i[0]).length;else for(;n<e.length&&/[a-z]/.test(e[n])&&(r+=e[n],n++,!s.includes(r)););s.includes(r)&&t.push(r);continue}n++}return t}(e),n=[];for(let e of t)"{"===e||"["===e?n.push("{"===e?"}":"]"):("}"===e||"]"===e)&&n.length>0&&n.pop();n.reverse(),","===t[t.length-1]&&t.pop();let a=t.concat(n).join("");if(""===a)return null;try{return JSON.parse(a)}catch(a){let e=Math.max(t.lastIndexOf(","),t.lastIndexOf("{"),t.lastIndexOf("["));if(-1!==e){let a=t.slice(0,e+1);return","===a[a.length-1]&&a.pop(),JSON.parse(a.concat(n).join(""))}throw Error("Invalid JSON")}}n.d(t,{g:function(){return a}})},53462:function(e,t,n){"use strict";n.d(t,{n:function(){return r}});var a=n(37458);let r=e=>{if(e.input&&Object.keys(e.input).length>0)return e.input;if(e.partial_json){let t={};try{t=(0,a.g)(e.partial_json)}catch(e){}return null!=t?t:{}}return{}}},14575:function(e,t,n){"use strict";n.d(t,{d:function(){return r}});var a=n(14448);let r=()=>(0,a.F)("cardamom_enabled").value},21953:function(){}}]);